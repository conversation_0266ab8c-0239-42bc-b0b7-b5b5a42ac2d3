# 微信小程序评论API接口使用说明

## 概述

本文档介绍了微信小程序中评论相关功能的API接口调整和使用方法。我们重新整理了API接口结构，提供了统一的服务管理和错误处理机制。

## 文件结构

```
weapp/
├── config/
│   └── api.js                    # API配置文件
├── services/
│   ├── apiManager.js            # API管理器
│   ├── commentService.js        # 评论服务
│   └── postService.js           # 帖子服务
└── utils/
    └── request.js               # 请求工具
```

## API配置 (config/api.js)

### 评论相关接口
```javascript
comment: {
  add: '/blade-chat/feedback/comment/add',        // 添加评论
  reply: '/blade-chat/feedback/comment/reply',    // 回复评论
  list: '/blade-chat/feedback/comment/list',      // 获取评论列表
  replies: '/blade-chat/feedback/comment/replies', // 获取回复列表
  remove: '/blade-chat/feedback/comment/remove',  // 删除评论
  like: '/blade-chat/feedback/comment/like',      // 点赞评论
  detail: '/blade-chat/feedback/comment/detail'   // 获取评论详情
}
```

### 反馈相关接口
```javascript
feedback: {
  submit: '/blade-chat/feedback/submit',           // 提交反馈
  page: '/blade-chat/feedback/page',              // 获取反馈页面（帖子详情页使用）
  list: '/blade-chat/feedback/list',              // 获取反馈列表（通用）
  helpful: '/blade-chat/feedback/helpful',        // 标记有帮助
  tags: '/blade-chat/feedback/getTagsByCategory', // 获取反馈标签
  hotTags: '/blade-chat/feedback/getHotTags'      // 获取热门标签
}
```

### 帖子相关接口
```javascript
post: {
  detail: '/blade-chat/post/detail',              // 帖子详情
  like: '/blade-chat/post/like',                  // 点赞帖子
  favorite: '/blade-chat/post/favorite',          // 收藏帖子
  list: '/blade-chat/post/list',                  // 获取帖子列表
  publish: '/blade-chat/post/publish'             // 发布帖子
}
```

## 使用方法

### 1. 在页面中引入API管理器

```javascript
// 方法1: 直接引入服务
const commentService = require('../../services/commentService');
const postService = require('../../services/postService');

// 方法2: 使用全局API管理器
const { api } = getApp().globalData;
```

### 2. 评论功能使用示例

#### 添加评论
```javascript
async addComment() {
  try {
    const commentData = {
      postId: '123',
      content: '这是一条评论',
      image: 'https://example.com/image.jpg', // 可选
      contactInfo: '联系方式', // 可选
      mentionedUserIds: ['user1', 'user2'], // 提及的用户ID
      mentionedUsers: [
        { userId: 'user1', nickname: '用户1' },
        { userId: 'user2', nickname: '用户2' }
      ]
    };
    
    const result = await commentService.addComment(commentData);
    if (result.code === 200) {
      wx.showToast({ title: '评论成功', icon: 'success' });
    }
  } catch (error) {
    console.error('添加评论失败:', error);
  }
}
```

#### 回复评论
```javascript
async replyComment() {
  try {
    const replyData = {
      postId: '123',
      parentId: '456', // 父评论ID
      content: '这是一条回复',
      mentionedUserIds: ['user1'],
      mentionedUsers: [{ userId: 'user1', nickname: '用户1' }]
    };
    
    const result = await commentService.replyComment(replyData);
    if (result.code === 200) {
      wx.showToast({ title: '回复成功', icon: 'success' });
    }
  } catch (error) {
    console.error('回复评论失败:', error);
  }
}
```

#### 获取评论列表
```javascript
async loadComments() {
  try {
    const result = await commentService.getCommentList('123', {
      current: 1,
      size: 10
    });
    
    if (result.code === 200) {
      this.setData({
        commentList: result.data.records
      });
    }
  } catch (error) {
    console.error('获取评论列表失败:', error);
  }
}
```

#### 点赞评论
```javascript
async likeComment(commentId) {
  try {
    const result = await commentService.likeComment(commentId, 'LIKE');
    if (result.code === 200) {
      wx.showToast({ title: '点赞成功', icon: 'success' });
    }
  } catch (error) {
    console.error('点赞失败:', error);
  }
}
```

### 3. 使用API管理器的便捷方法

```javascript
const { api } = getApp().globalData;

// 显示加载状态
api.showLoading('正在提交...');

// 隐藏加载状态
api.hideLoading();

// 显示成功提示
api.showSuccess('操作成功');

// 显示错误提示
api.showError('操作失败');

// 确认对话框
const confirmed = await api.confirm('确定要删除这条评论吗？');
if (confirmed) {
  // 执行删除操作
}

// 检查网络状态
const networkAvailable = await api.checkNetwork();
if (networkAvailable) {
  // 执行网络请求
}
```

### 4. 错误处理

所有API调用都包含统一的错误处理机制：

```javascript
try {
  const result = await commentService.addComment(commentData);
  // 处理成功结果
} catch (error) {
  // 错误会自动显示toast提示
  // 可以根据需要添加额外的错误处理逻辑
  console.error('操作失败:', error);
}
```

## 数据结构

### 评论数据结构 (CommentDTO)
```javascript
{
  postId: String,           // 帖子ID
  parentId: String,         // 父评论ID（回复时使用）
  content: String,          // 评论内容
  image: String,            // 图片URL（可选）
  contactInfo: String,      // 联系信息（可选）
  mentionedUserIds: Array,  // 提及的用户ID列表
  mentionedUsers: Array     // 提及的用户信息列表
}
```

### 用户提及数据结构
```javascript
{
  userId: String,           // 用户ID
  nickname: String          // 用户昵称
}
```

## 注意事项

1. **网络请求**: 所有API调用都是异步的，需要使用async/await或Promise处理
2. **错误处理**: 建议在每个API调用外包装try-catch进行错误处理
3. **加载状态**: 对于耗时操作，建议显示加载状态提升用户体验
4. **数据验证**: 在提交数据前进行必要的验证
5. **权限检查**: 某些操作需要用户登录，确保在调用前检查登录状态

## 迁移指南

如果你的代码中使用了旧的API调用方式，可以按以下步骤迁移：

1. 引入新的服务文件
2. 替换API调用方法
3. 更新错误处理逻辑
4. 测试功能是否正常

### 迁移示例

**旧代码:**
```javascript
const { request } = getApp().globalData;
const result = await request({
  url: '/blade-chat/feedback/comment/add',
  method: 'POST',
  data: commentData
});
```

**新代码:**
```javascript
const commentService = require('../../services/commentService');
const result = await commentService.addComment(commentData);
```

这样的调整使代码更加清晰、易维护，并提供了更好的错误处理和用户体验。
