.category-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 200rpx;
  height: 180rpx;
  border-radius: 24rpx;
  background: #fff;
  box-shadow: 0 4rpx 24rpx rgba(255,107,107,0.08);
  margin: 20rpx;
  position: relative;
  transition: box-shadow 0.2s, transform 0.2s, border 0.2s;
  border: 2rpx solid transparent;
}
.category-card.selected {
  border: 2rpx solid #ff6b6b;
  background: linear-gradient(to bottom, #fff 60%, #ffeaea 100%);
  box-shadow: 0 8rpx 32rpx rgba(255,107,107,0.10);
  transform: scale(1.04);
}
.category-card.selected .name {
  color: #ff6b6b;
}
.icon {
  width: 64rpx;
  height: 64rpx;
  margin-bottom: 12rpx;
  filter: drop-shadow(0 2rpx 8rpx #ff6b6b22);
}
.name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
.badge {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  background: linear-gradient(to right, #ff6b6b, #ff8585);
  color: #fff;
  font-size: 20rpx;
  border-radius: 20rpx;
  padding: 4rpx 16rpx;
  box-shadow: 0 2rpx 8rpx #ff6b6b33;
}
.category-card:active {
  box-shadow: 0 12rpx 32rpx rgba(255,107,107,0.18);
  transform: scale(0.98);
} 