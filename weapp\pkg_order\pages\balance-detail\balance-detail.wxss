/* 余额明细页面样式 */
.balance-detail-container {
  min-height: 100vh;
  background-color: #f5f6f7;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 余额卡片 */
/* .balance-card {
  background: linear-gradient(135deg, #ff7d7d 0%, #ff6b6b 100%);
  margin: 24rpx;
  border-radius: 24rpx;
  padding: 40rpx 32rpx;
  color: white;
}

.balance-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.balance-title {
  font-size: 28rpx;
  opacity: 0.9;
}

.balance-amount {
  display: flex;
  align-items: baseline;
}

.currency {
  font-size: 32rpx;
  margin-right: 8rpx;
}

.amount {
  font-size: 56rpx;
  font-weight: bold;
} */

/* 筛选标签 */
.filter-tabs-scroll {
  background: white;
  border-radius: 16rpx;
}

.filter-tabs {
  display: flex;
  padding: 14rpx 8rpx;
  gap: 8rpx;
}

.filter-tab {
  flex: 1;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  color: #666;
  background: #f8f9fa;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.filter-tab.active {
  background: #ff7d7d;
  color: white;
}

/* 明细列表 */
.detail-list {
  height: calc(100vh - 400rpx);
}

.detail-item {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.detail-info {
  flex: 1;
}

.detail-title {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.detail-time {
  font-size: 24rpx;
  color: #999;
}

.detail-amount {
  display: flex;
  align-items: baseline;
  font-weight: 600;
}

.detail-amount.income {
  color: #52c41a;
}

.detail-amount.expense {
  color: #ff4d4f;
}

.amount-sign {
  font-size: 24rpx;
  margin-right: 4rpx;
}

.amount-value {
  font-size: 32rpx;
}

.detail-content {
  margin-bottom: 16rpx;
}

.detail-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.detail-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-status {
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  font-size: 22rpx;
}

.detail-status.success {
  background: #f6ffed;
  color: #52c41a;
}

.detail-status.pending {
  background: #fff7e6;
  color: #fa8c16;
}

.detail-status.failed {
  background: #fff2f0;
  color: #ff4d4f;
}

.status-text {
  font-size: 22rpx;
}

.detail-order {
  flex: 1;
  text-align: right;
}

.order-text {
  font-size: 22rpx;
  color: #999;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #ff7d7d;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 26rpx;
  color: #999;
}

/* 没有更多数据 */
.no-more-container {
  text-align: center;
  padding: 40rpx;
}

.no-more-text {
  font-size: 26rpx;
  color: #999;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 120rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 24rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
  text-align: center;
  line-height: 1.5;
}
