# 登录过期自动跳转优化

## 概述

本次优化主要解决了登录过期时的用户体验问题，实现了自动跳转到登录页面的功能，避免了用户需要手动重新登录的困扰。

## 主要改进

### 1. 统一登录过期处理

- **创建了统一的登录处理工具** (`utils/loginHandler.js`)
- **防止重复跳转**：使用全局状态管理，避免多个请求同时触发跳转
- **正确的跳转路径**：修正了登录页面路径为 `/pkg_common/pages/login/login`

### 2. 用户体验优化

- **友好的提示信息**：显示"登录已过期，正在跳转登录页面"的提示
- **延迟跳转**：给用户1.5秒时间看到提示信息
- **智能页面检查**：避免重复跳转到登录页面
- **失败处理**：如果跳转失败，会尝试使用 `reLaunch` 跳转到首页

### 3. 代码结构优化

- **消除代码重复**：所有包的 `request.js` 文件都使用统一的登录处理逻辑
- **模块化设计**：将登录过期处理逻辑独立成工具函数
- **易于维护**：集中管理登录相关的逻辑

## 文件修改说明

### 新增文件

1. **`utils/loginHandler.js`**
   - 统一的登录过期处理函数
   - 登录状态检查函数
   - 强制登录跳转函数

### 修改文件

1. **`utils/request.js`**
   - 使用统一的 `handleLoginExpired` 函数
   - 移除重复的登录处理逻辑

2. **`pkg_common/utils/request.js`**
   - 使用统一的登录处理逻辑
   - 修正跳转路径

3. **`pkg_user/utils/request.js`**
   - 使用统一的登录处理逻辑
   - 修正跳转路径

4. **`pkg_merchant/utils/request.js`**
   - 使用统一的登录处理逻辑
   - 修正跳转路径

5. **`components/custom-tabbar/index.js`**
   - 使用统一的 `forceLogin` 函数
   - 简化登录检查逻辑

6. **`pkg_common/pages/login/login.js`**
   - 优化登录成功后的返回逻辑
   - 添加 `onShow` 方法的调用

7. **`app.js`**
   - 使用统一的登录过期处理函数
   - 优化token自动刷新逻辑

## 功能特性

### 1. 防重复跳转
```javascript
let isRedirectingToLogin = false;

const handleLoginExpired = () => {
  if (isRedirectingToLogin) {
    return; // 防止重复跳转
  }
  isRedirectingToLogin = true;
  // ... 处理逻辑
};
```

### 2. 智能页面检查
```javascript
const pages = getCurrentPages();
const currentPage = pages[pages.length - 1];
const currentRoute = currentPage ? currentPage.route : '';

if (currentRoute !== 'pkg_common/pages/login/login') {
  // 只有不在登录页面时才跳转
  wx.navigateTo({ url: '/pkg_common/pages/login/login' });
}
```

### 3. 失败处理机制
```javascript
wx.navigateTo({
  url: '/pkg_common/pages/login/login',
  success: () => {
    console.log('成功跳转到登录页面');
  },
  fail: (err) => {
    // 如果跳转失败，尝试使用reLaunch
    wx.reLaunch({ url: '/pages/index/index' });
  }
});
```

### 4. 完整的存储清理
```javascript
const authKeys = ['token', 'refreshToken', 'userInfo', 'hasUserInfo', 'openId'];
authKeys.forEach(key => {
  wx.removeStorageSync(key);
});
```

## 使用方式

### 1. 在请求拦截器中使用
```javascript
// 响应拦截器
const responseInterceptor = (response) => {
  const { data } = response;
  if (data.code === 401) {
    handleLoginExpired();
    return Promise.reject(new Error('登录已过期，请重新登录'));
  }
  return data;
};
```

### 2. 在需要登录检查的地方使用
```javascript
const { forceLogin } = require('../../utils/loginHandler');

// 检查登录状态
if (!checkLoginStatus()) {
  forceLogin();
  return false;
}
```

## 测试建议

1. **模拟登录过期**：修改token使其过期，测试自动跳转功能
2. **网络异常测试**：在网络异常情况下测试跳转失败的处理
3. **重复请求测试**：同时发送多个请求，验证防重复跳转功能
4. **页面状态测试**：在不同页面测试登录过期的处理

## 注意事项

1. **路径一致性**：确保所有跳转都使用正确的登录页面路径
2. **状态管理**：注意全局状态的重置时机
3. **错误处理**：确保所有可能的错误情况都有相应的处理
4. **用户体验**：保持提示信息的友好性和及时性

## 后续优化建议

1. **添加登录状态监听**：可以添加全局的登录状态变化监听
2. **优化跳转动画**：可以考虑添加页面跳转的过渡动画
3. **增加重试机制**：对于网络异常导致的跳转失败，可以添加重试逻辑
4. **本地化支持**：为提示信息添加多语言支持 