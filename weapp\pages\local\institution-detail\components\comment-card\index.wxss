/* 评论卡片样式 */
.comment-card {
  padding: 24rpx;
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.comment-card:last-child {
  border-bottom: none;
}

/* 评论头部 */
.comment-header {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  flex-shrink: 0;
}

.user-info {
  flex: 1;
}

.user-name-line {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 4rpx;
  flex-wrap: wrap;
}

.nickname {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.reply-to {
  font-size: 24rpx;
  color: #007aff;
  background: #f0f8ff;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
}

.time {
  font-size: 24rpx;
  color: #999;
}

/* 点赞区域 */
.like-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rpx;
  padding: 8rpx;
  border-radius: 8rpx;
  transition: background-color 0.2s;
}

.like-area:active {
  background-color: #f5f5f5;
}

.like-icon {
  width: 32rpx;
  height: 32rpx;
}

.like-icon.helpful {
  filter: none;
}

.like-count {
  font-size: 20rpx;
  color: #666;
  min-width: 20rpx;
  text-align: center;
}

.like-count.helpful {
  color: #ff6b6b;
  font-weight: 600;
}

/* 评论内容 */
.comment-content {
  margin-left: 96rpx;
}

.content-text {
  margin-bottom: 12rpx;
}

.reply-inline-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16rpx;
}

.inline-reply-btn {
  color: #007aff;
  background: #f0f8ff;
  border-radius: 12rpx;
  flex-shrink: 0;
  font-size: 24rpx;
  height: 32rpx;
  line-height: 32rpx;
  padding: 0 10rpx;
  transition: all 0.2s;
}

.inline-reply-btn:active {
  background: #e1f0ff;
  transform: scale(0.95);
}

.comment-image {
  margin-top: 12rpx;
}

.comment-image image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 8rpx;
  object-fit: cover;
}

/* 展开回复区域 */
.expand-replies-section {
  margin-left: 96rpx;
  margin-top: 16rpx;
}

.expand-content {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  border-left: 4rpx solid #007aff;
  transition: background-color 0.2s;
}

.expand-content:active {
  background: #e9ecef;
}

.expand-content.compact {
  padding: 8rpx 12rpx;
}

.expand-text {
  font-size: 24rpx;
  color: #007aff;
  font-weight: 500;
}

.comment-rich-text {
  word-break: break-all;
  white-space: pre-wrap;
  overflow-wrap: break-word;
  display: block;
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 8rpx;
}


/* 回复列表样式 */
.replies-container {
  margin-top: 24rpx;
  border-radius: 12rpx;
  padding: 20rpx;
  border-left: 4rpx solid #ff6b6b;
}

.reply-item {
  /* margin-bottom: 20rpx; */
  padding: 16rpx;
  background: #fff;
  border-radius: 8rpx;
  /* box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05); */
}

.reply-item:last-child {
  margin-bottom: 0;
}

.reply-content {
  display: flex;
  gap: 16rpx;
}

.reply-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  flex-shrink: 0;
}

.reply-info {
  flex: 1;
}

.reply-header {
  margin-bottom: 8rpx;
}

.reply-nickname {
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
}

.reply-content-wrapper {
  margin-bottom: 8rpx;
}

.reply-text-with-actions {
  display: flex;
  align-items: flex-start;
  gap: 8rpx;
  flex-wrap: wrap;
}

.reply-text-front {
  font-size: 24rpx;
  color: #666;
}

.reply-text-front-name {
  font-size: 24rpx;
  color: #007aff;
  font-weight: 500;
}

.reply-text-content {
  font-size: 24rpx;
  color: #333;
  line-height: 1.5;
  flex: 1;
  word-break: break-all;
}

.reply-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-left: auto;
}

.reply-action-item {
  display: flex;
  align-items: center;
  gap: 4rpx;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  transition: background-color 0.2s;
}

.reply-action-item:active {
  background: #f5f5f5;
}

.reply-action-text {
  font-size: 20rpx;
  color: #007aff;
}

.reply-like-icon {
  width: 24rpx;
  height: 24rpx;
}

.reply-like-icon.liked {
  filter: none;
}

.reply-like-count {
  font-size: 20rpx;
  color: #666;
  min-width: 16rpx;
}

.reply-like-count.liked {
  color: #ff6b6b;
  font-weight: 600;
}

.reply-time {
  font-size: 20rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.reply-image {
  margin-top: 8rpx;
}

.reply-image image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 6rpx;
  object-fit: cover;
}

/* 无回复提示 */
.no-replies {
  text-align: center;
  padding: 32rpx;
}

.no-replies-text {
  font-size: 24rpx;
  color: #999;
}

/* 回复状态 */
.reply-status {
  margin-top: 16rpx;
  text-align: center;
}

.load-more-replies {
  padding: 12rpx 24rpx;
  background: #f8f9fa;
  border-radius: 20rpx;
  display: inline-block;
  transition: background-color 0.2s;
}

.load-more-replies:active {
  background: #e9ecef;
}

.load-more-text {
  font-size: 24rpx;
  color: #007aff;
}

.all-loaded-tip {
  padding: 8rpx 16rpx;
}

.all-loaded-text {
  font-size: 22rpx;
  color: #999;
}
