# 本地页面去掉sortType参数修改说明

## 修改概述

按照要求，从本地页面的请求参数中去掉了 `sortType` 参数。

## 修改内容

### 修改前的请求参数
```javascript
buildRequestParams(isRefresh) {
  const params = {
    current: isRefresh ? 1 : this.data.currentPage,
    size: this.data.pageSize,
    sortType: this.data.currentTab === 0 ? 'latest' : 'nearby'  // ❌ 已删除
  };
  // ... 其他参数
}
```

### 修改后的请求参数
```javascript
buildRequestParams(isRefresh) {
  const params = {
    current: isRefresh ? 1 : this.data.currentPage,
    size: this.data.pageSize
    // ✅ 不再传递 sortType 参数
  };
  // ... 其他参数
}
```

## 现在的请求参数格式

### 最新查询 (currentTab === 0)
```javascript
{
  current: 1,
  size: 10,
  latitude: 用户纬度,           // 用户自己的位置
  longitude: 用户经度,          // 用户自己的位置
  typeId: 分类ID,              // 可选：分类筛选
  name: '搜索关键词'            // 可选：搜索关键词
  // ✅ 不传 sortType、searchLatitude、searchLongitude
}
```

### 附近查询 (currentTab === 1)
```javascript
{
  current: 1,
  size: 10,
  latitude: 用户纬度,           // 用户自己的位置
  longitude: 用户经度,          // 用户自己的位置
  searchLatitude: 用户纬度,     // 查询的经纬度
  searchLongitude: 用户经度,    // 查询的经纬度
  typeId: 分类ID,              // 可选：分类筛选
  name: '搜索关键词'            // 可选：搜索关键词
  // ✅ 不传 sortType
}
```

## 影响说明

1. **后端处理**：后端需要根据是否传递 `searchLatitude/searchLongitude` 来判断是附近查询还是最新查询
2. **排序逻辑**：排序逻辑完全由后端根据参数自动判断
3. **兼容性**：保持了位置参数的传递规格，只是去掉了显式的排序类型标识

## 判断逻辑

后端可以通过以下方式判断查询类型：
- 如果请求中包含 `searchLatitude` 和 `searchLongitude` 参数 → 附近查询
- 如果请求中只包含 `latitude` 和 `longitude` 参数 → 最新查询

这样的设计更加简洁，减少了冗余参数。
