# 本地页面传参规格修改说明

## 修改概述

按照名片库页面的相同规格，对本地页面的分页查询传参进行了修改，同时添加了分类id传参查询支持。

## 主要修改内容

### 1. 添加用户位置数据

**新增数据字段：**
```javascript
data: {
  // 用户位置信息
  userLocation: null,
  // ... 其他字段
}
```

### 2. 优化位置获取逻辑

**修改前：**
```javascript
async getCurrentLocation() {
  try {
    console.log('开始获取位置信息...');
    await this.getLocation();

    // 实际项目中应该根据获取的位置信息设置具体地址
    this.setData({
      currentLocation: '当前位置'
    });
    console.log('位置获取完成');
  } catch (error) {
    // 错误处理...
  }
}
```

**修改后：**
```javascript
async getCurrentLocation() {
  try {
    console.log('开始获取位置信息...');
    const location = await this.getLocation();

    // 保存用户位置信息
    this.setData({
      userLocation: {
        latitude: location.latitude,
        longitude: location.longitude
      },
      currentLocation: '当前位置'
    });
    console.log('位置获取完成:', location);
  } catch (error) {
    // 错误处理...
  }
}
```

### 3. 修改请求参数构建逻辑

**修改前：**
```javascript
buildRequestParams(isRefresh) {
  const params = {
    current: isRefresh ? 1 : this.data.currentPage,
    size: this.data.pageSize,
    sortType: this.data.currentTab === 0 ? 'latest' : 'nearby'
  };

  // 添加筛选条件
  if (this.data.selectedCategory !== 'all' && this.data.selectedCategoryId) {
    params.typeId = this.data.selectedCategoryId;
  }

  if (this.data.searchKeyword) {
    params.name = this.data.searchKeyword;
  }

  return params;
}
```

**修改后：**
```javascript
buildRequestParams(isRefresh) {
  const params = {
    current: isRefresh ? 1 : this.data.currentPage,
    size: this.data.pageSize,
    sortType: this.data.currentTab === 0 ? 'latest' : 'nearby'
  };

  // 添加位置参数
  if (this.data.userLocation) {
    // 所有查询都传用户自己的位置到latitude、longitude
    params.latitude = this.data.userLocation.latitude;
    params.longitude = this.data.userLocation.longitude;

    // 附近查询时，传查询的经纬度到searchLatitude、searchLongitude
    if (this.data.currentTab === 1) { // 附近查询
      params.searchLatitude = this.data.userLocation.latitude;
      params.searchLongitude = this.data.userLocation.longitude;
    }
    // 最新查询时，不传searchLatitude、searchLongitude
  }

  // 添加分类筛选条件（支持分类id传参查询）
  if (this.data.selectedCategory !== 'all' && this.data.selectedCategoryId) {
    params.typeId = this.data.selectedCategoryId;
  }

  // 添加搜索关键词
  if (this.data.searchKeyword) {
    params.name = this.data.searchKeyword;
  }

  return params;
}
```

## 参数规格说明

### 参数定义

| 参数名 | 说明 | 使用场景 |
|--------|------|----------|
| `latitude` | 用户自己的纬度 | 所有查询都传递 |
| `longitude` | 用户自己的经度 | 所有查询都传递 |
| `searchLatitude` | 查询目标的纬度 | 附近查询时传递 |
| `searchLongitude` | 查询目标的经度 | 附近查询时传递 |
| `typeId` | 机构分类ID | 分类筛选时传递 |
| `sortType` | 排序类型 | latest(最新) / nearby(附近) |
| `name` | 搜索关键词 | 搜索时传递 |

### 查询场景

#### 1. 最新查询 (currentTab === 0)
```javascript
{
  current: 1,
  size: 10,
  sortType: 'latest',
  latitude: 用户纬度,           // 用户自己的位置
  longitude: 用户经度,          // 用户自己的位置
  typeId: 分类ID,              // 可选：分类筛选
  name: '搜索关键词'            // 可选：搜索关键词
  // 注意：不传 searchLatitude 和 searchLongitude
}
```

#### 2. 附近查询 (currentTab === 1)
```javascript
{
  current: 1,
  size: 10,
  sortType: 'nearby',
  latitude: 用户纬度,           // 用户自己的位置
  longitude: 用户经度,          // 用户自己的位置
  searchLatitude: 用户纬度,     // 查询的经纬度（当前为用户位置）
  searchLongitude: 用户经度,    // 查询的经纬度（当前为用户位置）
  typeId: 分类ID,              // 可选：分类筛选
  name: '搜索关键词'            // 可选：搜索关键词
}
```

#### 3. 分类筛选支持
- 支持通过 `typeId` 参数传递分类ID进行筛选
- 当 `selectedCategory !== 'all'` 且 `selectedCategoryId` 有值时传递
- 与位置参数可以同时使用

## 新增功能

### 1. 分类ID传参查询
- ✅ 支持通过分类ID进行精确筛选
- ✅ 与现有的分类选择逻辑兼容
- ✅ 可与位置查询组合使用

### 2. 位置参数规范化
- ✅ 统一位置参数传递规格
- ✅ 区分用户位置和查询位置
- ✅ 为未来功能扩展预留接口

## 业务逻辑说明

### Tab切换逻辑
1. **最新 (currentTab === 0)**：
   - 按创建时间排序
   - 传递用户位置用于统计分析
   - 不传递查询位置参数

2. **附近 (currentTab === 1)**：
   - 按距离排序
   - 传递用户位置和查询位置
   - 当前查询位置与用户位置相同

### 分类筛选逻辑
- 支持通过分类ID进行筛选
- "全部"选项不传递typeId参数
- 其他分类传递对应的分类ID

### 扩展性考虑
1. **搜索其他位置**：
   - 未来可以让用户选择其他位置进行查询
   - searchLatitude/searchLongitude 为目标位置
   - latitude/longitude 仍为用户实际位置

2. **多条件组合查询**：
   - 位置 + 分类 + 关键词可以同时使用
   - 提供灵活的筛选能力

## 与名片库页面的一致性

| 功能 | 名片库页面 | 本地页面 | 一致性 |
|------|------------|----------|--------|
| 用户位置传递 | ✅ latitude/longitude | ✅ latitude/longitude | ✅ 一致 |
| 查询位置传递 | ✅ searchLatitude/searchLongitude | ✅ searchLatitude/searchLongitude | ✅ 一致 |
| 最新查询 | ✅ 不传查询位置 | ✅ 不传查询位置 | ✅ 一致 |
| 附近查询 | ✅ 传查询位置 | ✅ 传查询位置 | ✅ 一致 |
| 分类筛选 | ❌ 不支持 | ✅ 支持typeId | ➕ 增强功能 |

## 注意事项

1. **位置权限**：确保用户已授权位置权限
2. **网络异常**：处理获取位置失败的情况
3. **参数验证**：后端需要验证位置参数的有效性
4. **性能优化**：避免频繁获取位置信息
5. **分类数据**：确保分类ID的有效性

## 测试建议

1. 测试最新查询功能，验证不传递 searchLatitude/searchLongitude
2. 测试附近查询功能，验证 searchLatitude/searchLongitude 参数传递
3. 测试分类筛选功能，验证 typeId 参数传递
4. 测试组合查询（位置+分类+关键词）
5. 测试位置权限被拒绝的情况
6. 测试网络异常情况下的降级处理
