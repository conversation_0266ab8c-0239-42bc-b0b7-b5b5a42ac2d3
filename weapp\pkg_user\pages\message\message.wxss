/* 消息通知页面设计稿样式 */
.msg-container {
  background: #fafafa;
  min-height: 100vh;
  padding-bottom: 32rpx;
}

/* 顶部Tab */
.msg-tabs {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 32rpx 0 16rpx 0;
  background: #fff;
  border-radius: 0 0 32rpx 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.03);
}

.msg-tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
}

.msg-tab-icon-wrap {
  width: 80rpx;
  height: 80rpx;
  background: #fff5f5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.msg-tab-icon {
  width: 44rpx;
  height: 44rpx;
  filter: brightness(0) saturate(100%) invert(56%) sepia(41%) saturate(749%) hue-rotate(-10deg) brightness(101%) contrast(101%);
}

.msg-tab-dot {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 16rpx;
  height: 16rpx;
  background: #ff8283;
  border-radius: 50%;
  border: 4rpx solid #fff5f5;
}

.msg-tab-label {
  margin-top: 12rpx;
  font-size: 26rpx;
  color: #333;
}

/* 消息列表 */
.msg-list {
  margin-top: 24rpx;
  padding: 0 24rpx;
}

.msg-list-item {
  display: flex;
  align-items: flex-start;
  background: #fff;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  padding: 24rpx 24rpx 24rpx 0;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.03);
  position: relative;
}

.msg-list-icon-wrap {
  width: 64rpx;
  height: 64rpx;
  background: #fff5f5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  position: relative;
}

.msg-list-icon {
  width: 36rpx;
  height: 36rpx;
  filter: brightness(0) saturate(100%) invert(56%) sepia(41%) saturate(749%) hue-rotate(-10deg) brightness(101%) contrast(101%);
}

.msg-list-dot {
  position: absolute;
  top: 4rpx;
  right: 4rpx;
  width: 14rpx;
  height: 14rpx;
  background: #ff8283;
  border-radius: 50%;
  border: 3rpx solid #fff5f5;
}

.msg-list-content {
  flex: 1;
  min-width: 0;
}

.msg-list-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.msg-list-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.msg-list-time {
  font-size: 24rpx;
  color: #999;
}

.msg-list-desc {
  font-size: 26rpx;
  color: #666;
  margin-top: 2rpx;
}

/* 分隔线 */
.msg-list-item:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 88rpx;
  right: 0;
  bottom: -12rpx;
  height: 1rpx;
  background: #eee;
  border-radius: 1rpx;
} 

/* 对话卡片样式 */
.msg-chat-item {
  display: flex;
  align-items: flex-start;
  background: #fff;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  padding: 24rpx 24rpx 24rpx 0;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.03);
  position: relative;
}

.msg-chat-avatar {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  margin: 8rpx 20rpx 0 16rpx;
  flex-shrink: 0;
}

.msg-chat-content-wrap {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.msg-chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4rpx;
}

.msg-chat-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
  margin-right: 16rpx;
}

.msg-chat-time {
  font-size: 24rpx;
  color: #bbb;
}

.msg-chat-content {
  font-size: 26rpx;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 420rpx;
} 