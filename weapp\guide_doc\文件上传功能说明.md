# 小程序文件上传功能说明

## 概述

本小程序已集成完整的文件上传功能，支持单文件上传、批量上传、进度显示等功能。所有文件上传相关的接口调用都封装在 `stores/fileUploadStore.js` 中，遵循小程序开发规范。

## 文件结构

```
weapp/
├── stores/
│   ├── fileUploadStore.js          # 文件上传数据层
│   └── publishStore.js             # 发布相关数据层（已集成文件上传）
├── utils/
│   ├── upload.js                   # 文件上传工具类
│   └── request.js                  # 网络请求工具
├── components/
│   └── upload-progress/            # 上传进度组件
└── pages/
    └── publish/
        └── publish.js              # 发布页面（已集成文件上传）
```

## 核心功能

### 1. 文件上传Store (`stores/fileUploadStore.js`)

封装了所有文件上传相关的API调用：

- `uploadFile()` - 上传单个文件
- `uploadFiles()` - 批量上传文件
- `getFileUrl()` - 获取文件访问URL
- `downloadFile()` - 下载文件
- `removeFile()` - 删除文件
- `getFileList()` - 获取文件列表
- `getFilesByBusiness()` - 根据业务类型获取文件
- `getFileStats()` - 获取文件统计信息
- `cleanExpiredFiles()` - 清理过期文件
- `getStorageConfig()` - 获取存储配置
- `updateStorageConfig()` - 更新存储配置

### 2. 上传工具类 (`utils/upload.js`)

提供便捷的文件上传方法：

- `uploadFile()` - 上传单个文件到服务器
- `uploadFiles()` - 批量上传文件到服务器
- `chooseAndUploadImages()` - 选择并上传图片
- `chooseAndUploadVideo()` - 选择并上传视频
- `previewFile()` - 预览文件
- `formatFileSize()` - 格式化文件大小
- `getFileIcon()` - 获取文件类型图标

### 3. 发布页面集成

发布页面 (`pages/publish/publish.js`) 已集成文件上传功能：

- 支持选择多张图片
- 自动上传图片到服务器
- 显示上传进度
- 支持删除已选择的图片
- 支持预览图片

## 使用方法

### 1. 在页面中使用文件上传

```javascript
const uploadHelper = require('../../utils/upload');

// 选择并上传图片
async function uploadImages() {
  const result = await uploadHelper.chooseAndUploadImages(
    6, 'miniapp', 'post', null
  );
  
  if (result.success) {
    console.log('上传成功:', result.data);
  }
}
```

### 2. 使用文件上传Store

```javascript
const FileUploadStore = require('../../stores/fileUploadStore');

// 上传单个文件
async function uploadSingleFile(filePath) {
  const result = await FileUploadStore.uploadFile(
    filePath,
    'miniapp',
    'post',
    '123'
  );
  
  if (result.success) {
    console.log('文件上传成功:', result.data);
  }
}

// 获取文件列表
async function getFiles() {
  const result = await FileUploadStore.getFileList({
    current: 1,
    size: 10
  });
  
  if (result.success) {
    console.log('文件列表:', result.data);
  }
}
```

### 3. 在发布页面中使用

发布页面已经集成了完整的文件上传流程：

1. 用户选择图片
2. 点击发布时自动上传图片
3. 上传成功后提交帖子数据
4. 显示上传进度和结果

## API接口说明

### 上传文件接口

- **URL**: `/blade-system/file-upload/upload`
- **方法**: POST
- **参数**:
  - `file`: 文件对象
  - `uploadSource`: 上传来源（默认：miniapp）
  - `businessType`: 业务类型
  - `businessId`: 业务ID

### 批量上传接口

- **URL**: `/blade-system/file-upload/upload-batch`
- **方法**: POST
- **参数**:
  - `files`: 文件数组
  - `uploadSource`: 上传来源
  - `businessType`: 业务类型
  - `businessId`: 业务ID

### 获取文件URL接口

- **URL**: `/blade-system/file-upload/url`
- **方法**: GET
- **参数**:
  - `fileId`: 文件ID

## 配置说明

### 1. 基础配置

在 `utils/request.js` 中配置API基础地址：

```javascript
const BASE_URL = 'http://localhost'; // 替换为实际的API地址
```

### 2. 认证配置

文件上传需要以下认证头：

```javascript
header: {
  'Tenant-Id': '000000',
  'Authorization': 'Basic d2VhcHA6c2FkZXF3ZXh6Y2Nhc2Rxd2U=',
  'Blade-Auth': `Bearer ${token}`
}
```

### 3. 文件大小限制

- 单文件最大：10MB
- 图片格式：jpg, jpeg, png, gif
- 视频格式：mp4, avi, mov

## 错误处理

所有上传方法都返回统一的结果格式：

```javascript
{
  success: true/false,
  data: {}, // 成功时返回数据
  message: '错误信息' // 失败时返回错误信息
}
```

## 注意事项

1. **网络权限**: 确保小程序已申请网络访问权限
2. **文件大小**: 注意文件大小限制，避免上传失败
3. **并发控制**: 批量上传时注意控制并发数量
4. **错误重试**: 建议实现上传失败重试机制
5. **进度显示**: 大文件上传时建议显示进度条

## 扩展功能

### 1. 添加文件类型验证

```javascript
// 在upload.js中添加文件类型验证
validateFileType(filePath) {
  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
  // 实现文件类型验证逻辑
}
```

### 2. 添加文件压缩

```javascript
// 在upload.js中添加图片压缩
async compressImage(filePath) {
  // 实现图片压缩逻辑
}
```

### 3. 添加上传重试机制

```javascript
// 在fileUploadStore.js中添加重试逻辑
async uploadFileWithRetry(file, maxRetries = 3) {
  // 实现重试逻辑
}
```

## 更新日志

- **v1.0.0**: 初始版本，支持基础文件上传功能
- **v1.1.0**: 添加批量上传和进度显示
- **v1.2.0**: 集成到发布页面，完善错误处理 