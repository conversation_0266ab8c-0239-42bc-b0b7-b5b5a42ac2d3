.favorite-tags-card {
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 24rpx rgba(255,107,107,0.08);
  padding: 24rpx 0 0 0;
  margin-bottom: 32rpx;
}
.favorite-tags-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 32rpx 24rpx 32rpx;
}
.favorite-tags-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #222;
}
.favorite-tags-more {
  font-size: 24rpx;
  color: #ff6b6b;
  cursor: pointer;
}
.favorite-tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx 20rpx;
  padding: 0 32rpx 24rpx 32rpx;
}
.favorite-tag {
  font-size: 24rpx;
  padding: 8rpx 28rpx;
  border-radius: 20rpx;
  background: #f5f5f5;
  color: #666;
  transition: all 0.2s;
}
.favorite-tag.selected {
  background: #ff6b6b;
  color: #fff;
}

/* 收藏帖子样式 */
.favorite-post-item {
  display: flex;
  align-items: flex-start;
  padding: 24rpx 32rpx;
  border-bottom: 1px solid #f5f5f5;
  transition: all 0.3s ease;
}

.favorite-post-item:active {
  background: #f8f8f8;
  transform: scale(0.98);
}

.favorite-post-item:last-child {
  border-bottom: none;
}

/* 左侧图片容器 */
.post-image-container {
  width: 120rpx;
  height: 120rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.post-image {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
  background: #f5f5f5;
}

/* 图片占位符 */
.post-image-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
  background: linear-gradient(135deg, #ff6b6b, #ff8585);
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-text {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
}

/* 右侧内容区域 */
.post-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0; /* 防止内容溢出 */
}

.post-title {
  font-size: 28rpx;
  color: #222;
  font-weight: 500;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.post-desc {
  font-size: 26rpx;
  color: #444;
  margin-bottom: 12rpx;
  word-break: break-all;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.4;
}

.post-meta {
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin-top: auto;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6rpx;
  color: #999;
  font-size: 22rpx;
}

.meta-icon {
  width: 28rpx;
  height: 28rpx;
}

/* 空状态样式 */
.favorite-tags-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 32rpx 40rpx 32rpx;
  text-align: center;
}

.empty-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 24rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.empty-desc {
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
} 