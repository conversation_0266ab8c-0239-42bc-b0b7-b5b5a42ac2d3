// pkg_user/pages/cooperation-policy/cooperation-policy.js
Page({
  data: {
    // 政策内容
    policies: [
      {
        title: '代理商加盟政策',
        content: [
          '• 区域独家代理权，保护代理商利益',
          '• 提供完整的培训体系和技术支持',
          '• 灵活的分成比例，最高可达70%',
          '• 免费提供营销物料和推广支持',
          '• 定期举办代理商大会和培训活动'
        ]
      },
      {
        title: '广告合作政策',
        content: [
          '• 多种广告位选择，精准投放',
          '• 按效果付费，ROI可控',
          '• 专业的数据分析和报告',
          '• 灵活的投放周期和预算',
          '• 专属客户经理一对一服务'
        ]
      },
      {
        title: '赞助合作政策',
        content: [
          '• 品牌露出机会丰富',
          '• 活动定制化服务',
          '• 多渠道宣传推广',
          '• 数据反馈及时准确',
          '• 长期合作优惠政策'
        ]
      },
      {
        title: '联合推广政策',
        content: [
          '• 资源互换，互利共赢',
          '• 用户数据共享（合规前提下）',
          '• 联合营销活动策划',
          '• 品牌联名机会',
          '• 技术对接支持'
        ]
      }
    ]
  },

  onLoad(options) {
    console.log('合作政策页面加载');
  },

  // 联系客服
  onContactService() {
    wx.makePhoneCall({
      phoneNumber: '************',
      fail: () => {
        wx.showModal({
          title: '联系客服',
          content: '客服电话：************\n工作时间：9:00-18:00',
          showCancel: false
        });
      }
    });
  },

  // 立即申请
  onApplyNow() {
    wx.navigateBack();
  }
});
