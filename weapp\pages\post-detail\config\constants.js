/**
 * 帖子详情页常量配置
 */

// 分页配置
const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_PAGE_SIZE: 10,
  FEEDBACK_PAGE_SIZE: 20,
  REPLY_PAGE_SIZE: 1000 // 回复一次性加载所有
};

// 默认值
const DEFAULTS = {
  AVATAR: '/assets/images/common/default-avatar.png',
  POST_IMAGE: '/assets/images/common/default-post.png',
  LOADING_TEXT: '加载中...',
  EMPTY_TEXT: '暂无数据',
  NO_MORE_REPLIES: '没有更多回复了',
  ALL_REPLIES_LOADED: '已显示全部回复'
};

// 主题色配置
const THEME_COLORS = {
  PRIMARY: '#ff6b6b',
  PRIMARY_LIGHT: '#ff8a8a',
  PRIMARY_DARK: '#e55555',
  SECONDARY: '#007aff',
  SUCCESS: '#52c41a',
  WARNING: '#faad14',
  ERROR: '#f5222d',
  TEXT_PRIMARY: '#333',
  TEXT_SECONDARY: '#666',
  TEXT_DISABLED: '#999',
  BORDER: '#e0e0e0',
  BACKGROUND: '#f8f9fa'
};

// 消息提示文本
const MESSAGES = {
  // 成功消息
  SUCCESS: {
    LIKE_POST: '点赞成功',
    UNLIKE_POST: '取消点赞成功',
    FAVORITE_POST: '收藏成功',
    UNFAVORITE_POST: '取消收藏成功',
    SUBMIT_FEEDBACK: '反馈已提交',
    SUBMIT_COMMENT: '评论成功',
    SUBMIT_REPLY: '回复成功',
    SUBMIT_REPORT: '举报已提交',
    COPY_LINK: '链接已复制'
  },

  // 错误消息
  ERROR: {
    NETWORK: '网络连接失败，请检查网络',
    POST_NOT_FOUND: '帖子不存在',
    LOAD_POST_FAILED: '加载帖子失败',
    LOAD_FEEDBACK_FAILED: '加载反馈失败',
    LOAD_REPLIES_FAILED: '加载回复失败',
    SUBMIT_FEEDBACK_FAILED: '提交反馈失败',
    SUBMIT_COMMENT_FAILED: '评论失败',
    SUBMIT_REPLY_FAILED: '回复失败',
    SUBMIT_REPORT_FAILED: '举报失败',
    LIKE_FAILED: '点赞失败',
    FAVORITE_FAILED: '收藏失败',
    SHARE_FAILED: '分享失败',
    COPY_FAILED: '复制失败'
  },

  // 验证消息
  VALIDATION: {
    FEEDBACK_CONTENT_REQUIRED: '请输入反馈内容',
    FEEDBACK_TAG_REQUIRED: '请选择反馈标签',
    COMMENT_CONTENT_REQUIRED: '请输入评论内容',
    REPLY_CONTENT_REQUIRED: '请输入回复内容',
    REPORT_REASON_REQUIRED: '请选择举报原因',
    CONTENT_TOO_LONG: '内容过长，请精简后重试',
    PARENT_COMMENT_NOT_FOUND: '父评论不存在'
  },

  // 加载消息
  LOADING: {
    POST_DETAIL: '加载帖子详情中...',
    FEEDBACK_LIST: '加载反馈中...',
    REPLY_LIST: '加载回复中...',
    SUBMITTING: '提交中...',
    PROCESSING: '处理中...'
  }
};

// 时间格式化配置
const TIME_FORMAT = {
  JUST_NOW: '刚刚',
  MINUTES_AGO: '分钟前',
  HOURS_AGO: '小时前',
  DAYS_AGO: '天前',
  DATE_FORMAT: 'MM-DD HH:mm'
};

// 内容限制
const LIMITS = {
  FEEDBACK_CONTENT_MAX: 500,
  COMMENT_CONTENT_MAX: 200,
  REPLY_CONTENT_MAX: 200,
  REPORT_REASON_MAX: 200
};

// 动画配置
const ANIMATION = {
  DURATION: 300,
  EASING: 'ease-in-out'
};

// 存储键名
const STORAGE_KEYS = {
  USER_INFO: 'userInfo',
  POST_HISTORY: 'postHistory',
  DRAFT_FEEDBACK: 'draftFeedback',
  DRAFT_COMMENT: 'draftComment'
};

module.exports = {
  PAGINATION,
  DEFAULTS,
  THEME_COLORS,
  MESSAGES,
  TIME_FORMAT,
  LIMITS,
  ANIMATION,
  STORAGE_KEYS
};
