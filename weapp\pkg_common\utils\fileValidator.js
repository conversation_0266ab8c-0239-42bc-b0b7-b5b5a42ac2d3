const config = require('../config/api');

/**
 * 文件验证工具类
 */
class FileValidator {
  /**
   * 验证文件类型
   * @param {string} filePath 文件路径
   * @param {Array} allowedTypes 允许的文件类型
   * @returns {boolean} 是否通过验证
   */
  static validateFileType(filePath, allowedTypes = config.upload.allowedTypes) {
    const extension = filePath.split('.').pop().toLowerCase();
    const mimeType = this.getMimeType(extension);
    
    return allowedTypes.includes(mimeType);
  }

  /**
   * 验证文件大小
   * @param {number} fileSize 文件大小（字节）
   * @param {number} maxSize 最大文件大小
   * @returns {boolean} 是否通过验证
   */
  static validateFileSize(fileSize, maxSize = config.upload.maxFileSize) {
    return fileSize <= maxSize;
  }

  /**
   * 验证图片文件
   * @param {string} filePath 文件路径
   * @param {number} fileSize 文件大小
   * @returns {Object} 验证结果
   */
  static validateImage(filePath, fileSize) {
    const errors = [];
    
    // 验证文件类型
    if (!this.validateFileType(filePath)) {
      errors.push('不支持的文件类型，请选择jpg、png、gif或webp格式的图片');
    }
    
    // 验证文件大小
    if (!this.validateFileSize(fileSize)) {
      const maxSizeMB = config.upload.maxFileSize / (1024 * 1024);
      errors.push(`文件大小不能超过${maxSizeMB}MB`);
    }
    
    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }

  /**
   * 获取文件MIME类型
   * @param {string} extension 文件扩展名
   * @returns {string} MIME类型
   */
  static getMimeType(extension) {
    const mimeTypes = {
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'webp': 'image/webp',
      'mp4': 'video/mp4',
      'avi': 'video/avi',
      'mov': 'video/quicktime'
    };
    
    return mimeTypes[extension] || 'application/octet-stream';
  }

  /**
   * 格式化文件大小
   * @param {number} bytes 字节数
   * @returns {string} 格式化后的文件大小
   */
  static formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 获取文件扩展名
   * @param {string} filePath 文件路径
   * @returns {string} 文件扩展名
   */
  static getFileExtension(filePath) {
    return filePath.split('.').pop().toLowerCase();
  }

  /**
   * 验证文件名
   * @param {string} fileName 文件名
   * @returns {boolean} 是否有效
   */
  static validateFileName(fileName) {
    // 检查文件名是否包含非法字符
    const invalidChars = /[<>:"/\\|?*]/;
    return !invalidChars.test(fileName);
  }

  /**
   * 生成安全的文件名
   * @param {string} originalName 原始文件名
   * @returns {string} 安全的文件名
   */
  static generateSafeFileName(originalName) {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 9);
    const extension = this.getFileExtension(originalName);
    
    return `file_${timestamp}_${random}.${extension}`;
  }
}

module.exports = FileValidator; 