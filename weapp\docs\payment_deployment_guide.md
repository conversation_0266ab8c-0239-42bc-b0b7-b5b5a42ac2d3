# 支付系统部署指南

## 🚀 快速部署

### 1. 环境准备

#### 前端环境
- 微信开发者工具
- Node.js 16+ (如需要)
- 小程序基础库 3.8.9+

#### 后端环境
- Java 8+
- Spring Boot 2.x+
- MySQL 8.0+
- Redis (可选)

### 2. 配置文件修改

#### 前端配置 (weapp/config/payConfig.js)

```javascript
const payConfig = {
  wechatPay: {
    // 替换为你的小程序AppID
    appId: 'wx1234567890abcdef',
    
    // 替换为你的商户号
    mchId: '1234567890',
    
    // 替换为你的商户密钥
    mchKey: 'your_merchant_key_32_characters',
    
    // 替换为你的回调地址
    notifyUrl: 'https://yourdomain.com/api/pay/notify',
    
    // 开发环境使用沙箱
    currentEnv: 'sandbox'
  }
};
```

#### 后端配置 (application.yml)

```yaml
# 微信支付配置
wechat:
  pay:
    app-id: wx1234567890abcdef
    mch-id: 1234567890
    mch-key: your_merchant_key_32_characters
    key-path: classpath:cert/apiclient_cert.p12
    notify-url: https://yourdomain.com/api/pay/notify
    
# 数据库配置
spring:
  datasource:
    url: **************************************
    username: root
    password: your_password
```

### 3. 数据库初始化

#### 创建数据库表

```sql
-- 支付订单表
CREATE TABLE `payment_orders` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `out_trade_no` varchar(64) NOT NULL COMMENT '商户订单号',
  `transaction_id` varchar(64) DEFAULT NULL COMMENT '微信订单号',
  `business_type` varchar(32) NOT NULL COMMENT '业务类型',
  `business_id` varchar(64) NOT NULL COMMENT '业务ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `amount` decimal(10,2) NOT NULL COMMENT '订单金额(元)',
  `total_fee` int NOT NULL COMMENT '订单金额(分)',
  `description` varchar(255) NOT NULL COMMENT '订单描述',
  `status` varchar(16) NOT NULL DEFAULT 'PENDING' COMMENT '订单状态',
  `payment_method` varchar(16) NOT NULL COMMENT '支付方式',
  `prepay_id` varchar(64) DEFAULT NULL COMMENT '预支付ID',
  `paid_at` datetime DEFAULT NULL COMMENT '支付时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_out_trade_no` (`out_trade_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_business` (`business_type`, `business_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付订单表';

-- 退款记录表
CREATE TABLE `payment_refunds` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `out_trade_no` varchar(64) NOT NULL COMMENT '原订单号',
  `out_refund_no` varchar(64) NOT NULL COMMENT '退款单号',
  `refund_id` varchar(64) DEFAULT NULL COMMENT '微信退款单号',
  `total_fee` int NOT NULL COMMENT '原订单金额(分)',
  `refund_fee` int NOT NULL COMMENT '退款金额(分)',
  `refund_desc` varchar(255) DEFAULT NULL COMMENT '退款原因',
  `status` varchar(16) NOT NULL DEFAULT 'PROCESSING' COMMENT '退款状态',
  `refund_time` datetime DEFAULT NULL COMMENT '退款时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_out_refund_no` (`out_refund_no`),
  KEY `idx_out_trade_no` (`out_trade_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='退款记录表';
```

### 4. 后端代码示例

#### 支付控制器 (PaymentController.java)

```java
@RestController
@RequestMapping("/api/payment")
public class PaymentController {
    
    @Autowired
    private PaymentService paymentService;
    
    @PostMapping("/wechat/unifiedorder")
    public ResponseEntity<String> unifiedOrder(@RequestBody UnifiedOrderRequest request) {
        try {
            String xmlResult = paymentService.unifiedOrder(request.getXmlData());
            return ResponseEntity.ok(xmlResult);
        } catch (Exception e) {
            return ResponseEntity.status(500).body(e.getMessage());
        }
    }
    
    @PostMapping("/wechat/notify")
    public String paymentNotify(HttpServletRequest request) {
        try {
            String xmlData = IOUtils.toString(request.getInputStream(), "UTF-8");
            return paymentService.handlePaymentNotify(xmlData);
        } catch (Exception e) {
            return "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[" + e.getMessage() + "]]></return_msg></xml>";
        }
    }
}
```

#### 支付服务 (PaymentService.java)

```java
@Service
public class PaymentService {
    
    @Autowired
    private WxPayService wxPayService;
    
    public String unifiedOrder(String xmlData) throws WxPayException {
        WxPayUnifiedOrderRequest request = WxPayUnifiedOrderRequest.fromXML(xmlData);
        WxPayUnifiedOrderResult result = wxPayService.unifiedOrder(request);
        return result.toXML();
    }
    
    public String handlePaymentNotify(String xmlData) throws WxPayException {
        WxPayOrderNotifyResult result = wxPayService.parseOrderNotifyResult(xmlData);
        
        // 处理业务逻辑
        processPaymentSuccess(result);
        
        return "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
    }
    
    private void processPaymentSuccess(WxPayOrderNotifyResult result) {
        // 更新订单状态
        // 处理业务逻辑
    }
}
```

### 5. 证书配置

#### 下载商户证书
1. 登录微信商户平台
2. 进入"账户中心" -> "API安全"
3. 下载商户证书 (apiclient_cert.p12)
4. 将证书放置到后端资源目录

#### 配置证书路径
```java
@Configuration
public class WxPayConfig {
    
    @Bean
    public WxPayService wxPayService() {
        WxPayService wxPayService = new WxPayServiceImpl();
        WxPayConfig payConfig = new WxPayConfig();
        
        payConfig.setAppId("wx1234567890abcdef");
        payConfig.setMchId("1234567890");
        payConfig.setMchKey("your_merchant_key");
        payConfig.setKeyPath("classpath:cert/apiclient_cert.p12");
        
        wxPayService.setConfig(payConfig);
        return wxPayService;
    }
}
```

### 6. 域名配置

#### 小程序后台配置
1. 登录微信公众平台
2. 进入"开发" -> "开发管理" -> "开发设置"
3. 配置服务器域名：
   - request合法域名：https://yourdomain.com
   - uploadFile合法域名：https://yourdomain.com
   - downloadFile合法域名：https://yourdomain.com

#### 微信支付配置
1. 登录微信商户平台
2. 进入"产品中心" -> "开发配置"
3. 配置支付授权目录：https://yourdomain.com/

### 7. 测试验证

#### 沙箱环境测试
```javascript
// 修改配置为沙箱环境
const payConfig = {
  wechatPay: {
    currentEnv: 'sandbox',
    // 使用沙箱商户号和密钥
  }
};
```

#### 测试用例
1. **创建订单测试**
   - 测试不同业务类型
   - 测试不同金额
   - 测试参数验证

2. **支付流程测试**
   - 测试微信支付
   - 测试支付取消
   - 测试支付失败

3. **回调处理测试**
   - 测试支付成功回调
   - 测试签名验证
   - 测试业务逻辑处理

### 8. 生产环境部署

#### 环境切换
```javascript
// 生产环境配置
const payConfig = {
  wechatPay: {
    currentEnv: 'production',
    // 使用正式商户号和密钥
  }
};
```

#### 安全检查
- [ ] 商户密钥安全存储
- [ ] HTTPS证书配置
- [ ] 回调地址验证
- [ ] 日志记录配置
- [ ] 错误监控配置

### 9. 监控和维护

#### 日志监控
```java
@Slf4j
@Component
public class PaymentMonitor {
    
    @EventListener
    public void handlePaymentSuccess(PaymentSuccessEvent event) {
        log.info("支付成功: 订单号={}, 金额={}", event.getOrderNo(), event.getAmount());
    }
    
    @EventListener
    public void handlePaymentFailed(PaymentFailedEvent event) {
        log.error("支付失败: 订单号={}, 原因={}", event.getOrderNo(), event.getReason());
    }
}
```

#### 性能监控
- 支付成功率统计
- 支付响应时间监控
- 异常订单告警
- 资金对账检查

### 10. 常见问题

#### Q: 支付时提示"商户号配置错误"
A: 检查商户号和AppID是否匹配，确认在微信商户平台已关联小程序。

#### Q: 回调接收不到
A: 检查回调地址是否可访问，确认是HTTPS协议，检查防火墙设置。

#### Q: 签名验证失败
A: 检查商户密钥是否正确，确认参数编码格式，检查签名算法实现。

#### Q: 证书相关错误
A: 确认证书文件路径正确，检查证书是否过期，验证证书密码。

---

**部署完成后，请进行充分的测试验证，确保支付功能正常运行。**
