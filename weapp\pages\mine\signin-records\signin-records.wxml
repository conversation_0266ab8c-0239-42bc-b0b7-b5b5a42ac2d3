<!--签到记录页面-->
<view class="signin-records-container">
  <!-- 页面头部 -->
  <view class="header">
    <view class="title">签到记录</view>
    <view class="subtitle">查看您的签到历史和获得的积分</view>
  </view>

  <!-- 统计汇总卡片 -->
  <view class="summary-card" wx:if="{{summary}}">
    <view class="summary-row">
      <view class="summary-item">
        <view class="summary-value">{{summary.totalAllDays || 0}}</view>
        <view class="summary-label">累计签到</view>
      </view>
      <view class="summary-item">
        <view class="summary-value">{{summary.totalPoints || 0}}</view>
        <view class="summary-label">获得积分</view>
      </view>
      <view class="summary-item">
        <view class="summary-value">{{summary.maxContinuousDays || 0}}</view>
        <view class="summary-label">最长连续</view>
      </view>
      <view class="summary-item">
        <view class="summary-value">{{summary.avgPoints || 0}}</view>
        <view class="summary-label">平均积分</view>
      </view>
    </view>
  </view>

  <!-- 筛选栏 -->
  <view class="filter-bar">
    <view class="filter-item" bindtap="showDatePicker">
      <text class="filter-text">{{dateRangeText}}</text>
      <text class="iconfont icon-arrow-down"></text>
    </view>
    <view class="filter-item" bindtap="resetFilter">
      <text class="filter-text">重置</text>
    </view>
  </view>

  <!-- 记录列表 -->
  <view class="records-list">
    <!-- 加载状态 -->
    <view class="loading-container" wx:if="{{loading && records.length === 0}}">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 空状态 -->
    <view class="empty-container" wx:if="{{!loading && records.length === 0}}">
      <view class="empty-icon">📝</view>
      <view class="empty-text">暂无签到记录</view>
      <view class="empty-desc">快去签到获得积分吧！</view>
      <button class="empty-button" bindtap="goToSignin">立即签到</button>
    </view>

    <!-- 记录项 -->
    <view class="record-item" wx:for="{{records}}" wx:key="id">
      <view class="record-left">
        <view class="record-date">
          <view class="date-main">{{item.displayDate}}</view>
          <view class="date-time">{{item.displayTime}}</view>
        </view>
      </view>
      
      <view class="record-center">
        <view class="record-title">
          <text class="signin-type">{{item.signinTypeText}}</text>
          <text class="weekday">{{item.weekDay}}</text>
        </view>
        <view class="record-desc" wx:if="{{item.displayContinuous}}">
          {{item.displayContinuous}}
        </view>
      </view>
      
      <view class="record-right">
        <view class="points-main">{{item.displayPoints}}</view>
        <view class="points-detail" wx:if="{{item.continuousReward > 0}}">
          <text class="base-points">基础+{{item.points}}</text>
          <text class="reward-points">奖励+{{item.continuousReward}}</text>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{records.length > 0}}">
      <view class="load-more-loading" wx:if="{{loadingMore}}">
        <view class="loading-spinner small"></view>
        <text class="loading-text">加载中...</text>
      </view>
      <view class="load-more-button" wx:elif="{{canLoadMore}}" bindtap="loadMore">
        <text>加载更多</text>
      </view>
      <view class="load-more-end" wx:else>
        <text>没有更多记录了</text>
      </view>
    </view>
  </view>

  <!-- 日期选择器弹窗 -->
  <view class="date-picker-modal" wx:if="{{showDatePicker}}" bindtap="hideDatePicker">
    <view class="date-picker-content" catchtap="stopPropagation">
      <view class="date-picker-header">
        <text class="date-picker-title">选择时间范围</text>
        <text class="date-picker-close" bindtap="hideDatePicker">×</text>
      </view>
      <view class="date-picker-body">
        <view class="date-input-group">
          <view class="date-input-item">
            <text class="date-label">开始日期</text>
            <picker mode="date" value="{{startDate}}" bindchange="onStartDateChange">
              <view class="date-input">{{startDate || '请选择'}}</view>
            </picker>
          </view>
          <view class="date-input-item">
            <text class="date-label">结束日期</text>
            <picker mode="date" value="{{endDate}}" bindchange="onEndDateChange">
              <view class="date-input">{{endDate || '请选择'}}</view>
            </picker>
          </view>
        </view>
        <view class="quick-select">
          <text class="quick-select-title">快速选择</text>
          <view class="quick-select-buttons">
            <button class="quick-button" bindtap="selectLastWeek">最近一周</button>
            <button class="quick-button" bindtap="selectLastMonth">最近一月</button>
            <button class="quick-button" bindtap="selectLastThreeMonths">最近三月</button>
          </view>
        </view>
      </view>
      <view class="date-picker-footer">
        <button class="date-picker-cancel" bindtap="hideDatePicker">取消</button>
        <button class="date-picker-confirm" bindtap="confirmDateFilter">确定</button>
      </view>
    </view>
  </view>
</view>
