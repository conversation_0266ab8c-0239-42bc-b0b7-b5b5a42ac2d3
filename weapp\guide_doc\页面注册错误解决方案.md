# 微信小程序页面注册错误解决方案

## 错误信息
```
Page "pkg_common/pages/login/login" has not been registered yet.
```

## 常见原因
1. **开发者工具缓存问题**（最常见）
2. **页面文件路径错误**
3. **页面文件缺失**
4. **app.json 配置错误**
5. **编译问题**

## 解决步骤

### 第一步：检查页面注册
确认 `app.json` 中已正确注册页面：
```json
{
  "subpackages": [
    {
      "root": "pkg_common",
      "name": "common",
      "pages": [
        "pages/login/login"  // ← 确认此路径存在
      ]
    }
  ]
}
```

### 第二步：检查文件结构
确认以下文件都存在：
```
pkg_common/
└── pages/
    └── login/
        ├── login.js
        ├── login.json
        ├── login.wxml
        └── login.wxss
```

### 第三步：清理缓存并重新编译
1. **关闭开发者工具**
2. **删除项目目录下的临时文件**：
   - 删除 `node_modules/.cache`（如果有）
   - 删除开发者工具的项目缓存
3. **重新打开开发者工具**
4. **重新编译项目**

### 第四步：检查语法错误
确认 `login.js` 文件语法正确：
```js
Page({
  data: {
    // 数据
  },
  onLoad() {
    // 生命周期
  }
  // 其他方法
});
```

### 第五步：强制刷新
1. 在开发者工具中点击"清缓存"
2. 点击"编译"
3. 如果还不行，尝试"重新编译"

## 预防措施
1. **修改 app.json 后必须重新编译**
2. **新增页面后检查文件完整性**
3. **定期清理开发者工具缓存**
4. **使用相对路径，避免绝对路径**

## 常见踩坑点
- 页面路径大小写敏感
- 分包页面路径要包含 root 前缀
- 修改 app.json 后必须重新编译
- 开发者工具缓存问题最常见

---

**如果以上步骤都无效，请检查：**
1. 微信开发者工具版本是否最新
2. 项目基础库版本是否支持
3. 是否有其他语法错误影响编译 