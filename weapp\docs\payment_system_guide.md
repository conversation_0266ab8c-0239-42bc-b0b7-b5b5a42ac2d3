# 微信支付系统使用指南

## 📋 概述

本支付系统基于 **WxJava** 框架的最佳实践，为小程序提供完整的支付解决方案。支持微信支付、余额支付等多种支付方式，具备订单管理、支付回调、退款处理等完整功能。

## 🏗️ 系统架构

### 核心组件

```
weapp/
├── config/
│   └── payConfig.js          # 支付配置管理
├── utils/
│   └── wechatPayUtil.js      # 微信支付工具类
├── services/
│   └── paymentService.js     # 支付服务类
├── stores/
│   └── paymentStore.js       # 支付数据存储
└── pages/
    ├── payment/              # 支付页面
    └── payment-demo/         # 支付演示页面
```

### 设计模式

- **配置管理模式**：统一管理支付配置
- **工厂模式**：支持多种支付方式
- **观察者模式**：支付状态变化通知
- **策略模式**：不同业务类型的支付策略

## ⚙️ 配置说明

### 1. 支付配置 (config/payConfig.js)

```javascript
const payConfig = {
  wechatPay: {
    appId: 'your_mini_program_app_id',        // 小程序AppID
    mchId: 'your_merchant_id',                // 商户号
    mchKey: 'your_merchant_key',              // 商户密钥
    notifyUrl: 'https://your-domain.com/api/pay/notify',
    // ... 其他配置
  }
};
```

### 2. 环境配置

支持沙箱和生产环境：

```javascript
// 开发环境使用沙箱
currentEnv: 'sandbox',

// 生产环境切换
currentEnv: 'production',
```

## 🚀 快速开始

### 1. 基础支付流程

```javascript
// 1. 引入支付存储
const paymentStore = require('../../stores/paymentStore');

// 2. 创建支付订单
const paymentData = {
  businessType: 'post_promotion',    // 业务类型
  businessId: 'post_123',           // 业务ID
  amount: '9.90',                   // 支付金额
  description: '帖子推广服务',       // 支付描述
  paymentMethod: 'wechat'           // 支付方式
};

const result = await paymentStore.createPayment(paymentData);

// 3. 发起支付
if (result.success) {
  const paymentResult = await paymentStore.requestWechatPayment(result.data.payParams);
  console.log('支付结果:', paymentResult);
}
```

### 2. 跳转支付页面

```javascript
// 构建支付参数
const params = new URLSearchParams({
  businessType: 'vip_upgrade',
  businessId: 'user_456',
  amount: '29.90',
  description: 'VIP会员升级'
});

// 跳转支付页面
wx.navigateTo({
  url: `/pages/payment/payment?${params.toString()}`
});
```

## 💳 支付方式

### 1. 微信支付

```javascript
// 微信小程序支付
const wechatPayment = {
  paymentMethod: 'wechat',
  // 自动获取用户openid
};
```

### 2. 余额支付

```javascript
// 账户余额支付
const balancePayment = {
  paymentMethod: 'balance',
  // 需要验证用户余额
};
```

## 📊 业务类型

系统支持多种业务场景：

| 业务类型 | 说明 | 示例金额 |
|---------|------|---------|
| `post_promotion` | 帖子推广 | ¥9.90 |
| `vip_upgrade` | VIP升级 | ¥29.90 |
| `reward` | 打赏功能 | ¥5.00 |
| `service_fee` | 服务费 | ¥19.90 |
| `deposit` | 保证金 | ¥100.00 |

### 添加新业务类型

```javascript
// 1. 在 payConfig.js 中添加
businessType: {
  new_business: 'new_business',
}

// 2. 在 paymentStore.js 中添加配置
getBusinessTypeConfig() {
  return {
    new_business: {
      name: '新业务',
      description: '新业务描述',
      icon: '/images/business/new.png'
    }
  };
}

// 3. 在 paymentService.js 中添加处理逻辑
async handleBusinessLogic(paymentData) {
  switch (businessType) {
    case 'new_business':
      await this.handleNewBusiness(businessId, paymentData);
      break;
  }
}
```

## 🔄 订单管理

### 1. 查询订单状态

```javascript
const result = await paymentStore.queryOrderStatus('EF1641234567890123');
if (result.success) {
  console.log('订单状态:', result.data.status);
}
```

### 2. 获取用户订单列表

```javascript
const orders = await paymentStore.getUserOrders({
  page: 1,
  pageSize: 10,
  status: 'PAID',
  businessType: 'post_promotion'
});
```

### 3. 订单状态说明

| 状态 | 说明 | 颜色 |
|------|------|------|
| `PENDING` | 待支付 | 橙色 |
| `PAID` | 已支付 | 绿色 |
| `CANCELLED` | 已取消 | 灰色 |
| `REFUNDED` | 已退款 | 蓝色 |
| `EXPIRED` | 已过期 | 红色 |

## 💰 退款处理

### 1. 申请退款

```javascript
const refundData = {
  outTradeNo: 'EF1641234567890123',    // 原订单号
  outRefundNo: 'RF1641234567890123',   // 退款单号
  totalFee: 990,                       // 原订单金额(分)
  refundFee: 990,                      // 退款金额(分)
  refundDesc: '用户申请退款'            // 退款原因
};

const result = await paymentStore.requestRefund(refundData);
```

### 2. 退款状态查询

退款通常需要1-3个工作日到账，可通过回调或主动查询获取退款状态。

## 🔔 支付回调

### 1. 回调处理流程

```javascript
// 后端接收微信支付回调
app.post('/api/pay/notify', async (req, res) => {
  const xmlData = req.body;
  const paymentService = new PaymentService();
  
  const result = await paymentService.handlePaymentNotify(xmlData);
  
  res.set('Content-Type', 'application/xml');
  res.send(result.response);
});
```

### 2. 业务逻辑处理

支付成功后自动触发相应的业务逻辑：

- **帖子推广**：增加帖子曝光度
- **VIP升级**：开通VIP权限
- **打赏**：增加作者收益
- **服务费**：记录平台收入

## 🛡️ 安全措施

### 1. 签名验证

所有支付请求都进行签名验证：

```javascript
// 验证微信支付回调签名
const isValid = wechatPayUtil.verifySign(notifyData);
if (!isValid) {
  return { success: false, message: '签名验证失败' };
}
```

### 2. 金额验证

```javascript
// 验证支付金额范围
validateAmount(amount);  // 1分 - 1000万分

// 防止重复支付
const existingOrder = await checkOrderExists(outTradeNo);
```

### 3. 环境隔离

- 开发环境使用沙箱配置
- 生产环境使用正式配置
- 敏感信息环境变量管理

## 📱 页面组件

### 1. 支付页面 (pages/payment/)

完整的支付页面，包含：
- 订单信息展示
- 支付方式选择
- 优惠券使用
- 支付协议确认
- 支付结果处理

### 2. 支付演示页面 (pages/payment-demo/)

演示各种支付场景：
- 预设支付场景
- 自定义金额支付
- 支付记录查看
- 功能特性展示

## 🔧 开发调试

### 1. 日志输出

```javascript
// 开启详细日志
console.log('支付参数:', paymentData);
console.log('支付结果:', paymentResult);
```

### 2. 错误处理

```javascript
try {
  const result = await paymentStore.createPayment(paymentData);
} catch (error) {
  console.error('支付失败:', error.message);
  wx.showToast({
    title: error.message || '支付失败',
    icon: 'none'
  });
}
```

### 3. 测试建议

- 使用微信开发者工具的支付调试功能
- 在沙箱环境进行充分测试
- 测试各种异常情况和边界条件

## 📈 性能优化

### 1. 缓存策略

```javascript
// 缓存订单信息
paymentStore.cacheOrderInfo(orderNo, orderData);

// 获取缓存信息
const cachedOrder = paymentStore.getCachedOrderInfo(orderNo);
```

### 2. 异步处理

```javascript
// 异步处理业务逻辑
async handleBusinessLogic(paymentData) {
  // 不阻塞支付回调响应
  setTimeout(() => {
    this.processBusinessLogic(paymentData);
  }, 0);
}
```

## 🚨 注意事项

### 1. 证书配置

- 退款等接口需要商户证书
- 证书文件需要安全存储
- 定期更新证书

### 2. 回调地址

- 回调地址必须是HTTPS
- 确保回调地址可访问
- 正确处理回调重试

### 3. 金额处理

- 微信支付金额单位是分
- 避免浮点数精度问题
- 统一使用整数计算

### 4. 订单号生成

- 确保订单号唯一性
- 包含时间戳和随机数
- 符合微信支付规范

## 📞 技术支持

如有问题，请参考：

1. **WxJava 官方文档**：https://github.com/binarywang/WxJava
2. **微信支付官方文档**：https://pay.weixin.qq.com/wiki/doc/api/
3. **小程序支付指南**：https://developers.weixin.qq.com/miniprogram/dev/api/payment/

---

**版本信息**：v1.0.0  
**更新时间**：2024-01-15  
**维护者**：开发团队
