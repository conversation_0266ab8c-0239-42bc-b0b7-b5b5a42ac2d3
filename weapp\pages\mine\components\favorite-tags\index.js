Component({
  properties: {
    tags: {
      type: Array,
      value: []
    },
    posts: {
      type: Array,
      value: []
    }
  },
  methods: {
    onMore() {
      this.triggerEvent('more');
    },
    onTagTap(e) {
      const index = e.currentTarget.dataset.index;
      this.triggerEvent('tagtap', { index });
    },
    onPostTap(e) {
      const id = e.currentTarget.dataset.id;
      this.triggerEvent('posttap', { id });
    }
  }
});