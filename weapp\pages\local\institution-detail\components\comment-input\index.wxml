<view class="comment-input-container" wx:if="{{show}}">
  <view class="input-header" wx:if="{{replyToComment}}">
    <text class="reply-hint">回复 @{{replyToComment.nickname}}</text>
    <text class="cancel-btn" bindtap="onCancel">取消</text>
  </view>

  <view class="input-content">
    <!-- 文本输入区域 -->
    <view class="text-input-area">
      <textarea
        id="comment-input"
        class="comment-textarea"
        placeholder="{{placeholder}}"
        value="{{content}}"
        bindinput="onInput"
        maxlength="{{maxLength}}"
        auto-height
        show-confirm-bar="{{false}}"
        cursor-spacing="20"
      />
      <view class="input-counter">
        <text class="counter-text">{{content.length}}/{{maxLength}}</text>
      </view>
    </view>

    <!-- 图片预览 -->
    <view wx:if="{{image}}" class="image-preview">
      <view class="preview-container">
        <image
          src="{{image}}"
          mode="aspectFill"
          class="preview-image"
          bindtap="onPreviewImage"
        />
        <view class="remove-image" bindtap="onRemoveImage">
          <text class="remove-icon">×</text>
        </view>
      </view>
    </view>

    <!-- 上传中状态 -->
    <view wx:if="{{uploading}}" class="uploading-status">
      <view class="loading-spinner"></view>
      <text class="uploading-text">图片上传中...</text>
    </view>
  </view>

  <!-- 工具栏 -->
  <view class="input-toolbar">
    <view class="toolbar-left">
      <view class="tool-item" bindtap="onChooseImage" wx:if="{{!image && !uploading}}">
        <image class="tool-icon" src="/assets/images/common/camera.png" mode="aspectFit" />
        <text class="tool-text">图片</text>
      </view>
      <view class="tool-item" bindtap="onToggleEmoji">
        <text class="tool-icon emoji-icon">😊</text>
        <text class="tool-text">表情</text>
      </view>
    </view>

    <view class="toolbar-right">
      <button
        class="submit-btn {{content.trim() ? 'active' : 'disabled'}}"
        bindtap="onSubmit"
        disabled="{{!content.trim() || submitting}}"
      >
        <text wx:if="{{submitting}}" class="submitting-text">发布中...</text>
        <text wx:else class="submit-text">{{parentId ? '回复' : '发布'}}</text>
      </button>
    </view>
  </view>

  <!-- 表情面板 -->
  <view wx:if="{{showEmojiPanel}}" class="emoji-panel">
    <view class="emoji-grid">
      <text
        wx:for="{{['😊', '😂', '😍', '😘', '😜', '😎', '😢', '😭', '😡', '😱', '👍', '👎', '❤️', '💔', '🎉', '🎊']}}"
        wx:key="*this"
        class="emoji-item"
        data-emoji="{{item}}"
        bindtap="onInsertEmoji"
      >
        {{item}}
      </text>
    </view>
  </view>
</view>

<!-- 遮罩层 -->
<view wx:if="{{show}}" class="input-mask" bindtap="onCancel"></view>
