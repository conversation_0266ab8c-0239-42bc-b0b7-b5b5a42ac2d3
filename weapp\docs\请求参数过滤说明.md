# 请求参数过滤功能说明

## 功能描述
在发送网络请求时，自动过滤掉空值参数（null、undefined、空字符串），避免向后端传递无效参数。

## 过滤规则

### 会被过滤的值
- `null`
- `undefined` 
- `''` (空字符串)

### 会被保留的值
- `0` (数字零)
- `false` (布尔值假)
- `[]` (空数组)
- `{}` (空对象)
- 其他所有有效值

## 实现原理

### 核心过滤函数
```javascript
const filterEmptyParams = (data) => {
  if (!data || typeof data !== 'object') {
    return data;
  }

  const filtered = {};
  for (const key in data) {
    const value = data[key];
    
    // 过滤条件：过滤 null、undefined、空字符串
    // 保留：数字0、布尔值false、其他有效值
    if (value !== null && value !== undefined && value !== '') {
      filtered[key] = value;
    }
  }
  
  return filtered;
};
```

### 集成到请求拦截器
```javascript
const requestInterceptor = (config) => {
  // ... 其他逻辑

  // 过滤请求参数中的空值
  if (config.data) {
    config.data = filterEmptyParams(config.data);
  }
  
  return config;
};
```

## 使用示例

### 示例1：基本过滤
```javascript
// 原始参数
const params = {
  latitude: 39.9042,
  longitude: 116.4074,
  searchLatitude: null,        // 会被过滤
  searchLongitude: undefined,  // 会被过滤
  scope: 5,
  category: '',               // 会被过滤
  keyword: 'restaurant',
  current: 1,
  size: 0,                   // 保留（数字0）
  isActive: false            // 保留（布尔值false）
};

// 过滤后参数
{
  latitude: 39.9042,
  longitude: 116.4074,
  scope: 5,
  keyword: 'restaurant',
  current: 1,
  size: 0,
  isActive: false
}
```

### 示例2：地图API调用
```javascript
// 调用前
const apiParams = {
  latitude: 39.9042,
  longitude: 116.4074,
  searchLatitude: this.data.searchLatitude, // 可能为null
  searchLongitude: this.data.searchLongitude, // 可能为null
  scope: 5,
  category: this.data.category, // 可能为空字符串
  keyword: this.data.keyword,   // 可能为空字符串
  current: 1,
  size: 50
};

// 自动过滤后，只传递有效参数给后端
```

### 示例3：分类筛选
```javascript
// 未选择分类时
const params = {
  latitude: 39.9042,
  longitude: 116.4074,
  scope: 5,
  category: '',  // 空字符串，会被过滤
  keyword: null, // null，会被过滤
  current: 1,
  size: 50
};

// 过滤后只传递有效参数
{
  latitude: 39.9042,
  longitude: 116.4074,
  scope: 5,
  current: 1,
  size: 50
}
```

## 优势

### 1. 减少无效参数传递
- 避免向后端传递空值参数
- 减少网络传输数据量
- 提高API调用效率

### 2. 简化后端处理
- 后端无需处理空值参数
- 减少参数验证逻辑
- 避免空值引起的错误

### 3. 提升代码质量
- 前端无需手动检查每个参数
- 统一的参数处理逻辑
- 减少重复代码

### 4. 增强健壮性
- 自动处理可选参数
- 避免意外的空值传递
- 提高系统稳定性

## 特殊情况处理

### 数字0的保留
```javascript
const params = {
  latitude: 0,    // 保留，可能是有效的纬度
  longitude: 0,   // 保留，可能是有效的经度
  page: 0,        // 保留，可能是有效的页码
  count: 0        // 保留，可能是有效的计数
};
// 所有参数都会被保留
```

### 布尔值false的保留
```javascript
const params = {
  isActive: false,    // 保留，明确的布尔值
  showDeleted: false, // 保留，明确的布尔值
  enableCache: false  // 保留，明确的布尔值
};
// 所有参数都会被保留
```

### 空数组和空对象的保留
```javascript
const params = {
  tags: [],           // 保留，可能是有效的空数组
  filters: {},        // 保留，可能是有效的空对象
  metadata: null      // 过滤，明确的空值
};

// 过滤后
{
  tags: [],
  filters: {}
}
```

## 测试验证

### 测试用例1：混合参数
```javascript
const input = {
  latitude: 39.9042,
  longitude: null,
  category: '',
  keyword: 'test',
  page: 0,
  isActive: false
};

const expected = {
  latitude: 39.9042,
  keyword: 'test',
  page: 0,
  isActive: false
};
```

### 测试用例2：全空参数
```javascript
const input = {
  category: '',
  keyword: null,
  searchLatitude: undefined
};

const expected = {};
```

### 测试用例3：全有效参数
```javascript
const input = {
  latitude: 39.9042,
  longitude: 116.4074,
  scope: 5,
  category: 'food'
};

const expected = {
  latitude: 39.9042,
  longitude: 116.4074,
  scope: 5,
  category: 'food'
};
```

## 注意事项

### 1. 不影响原始数据
- 过滤操作不会修改原始参数对象
- 返回新的过滤后对象
- 保证数据安全性

### 2. 只处理第一层属性
- 当前实现只过滤对象的第一层属性
- 嵌套对象内的空值不会被过滤
- 如需深度过滤，需要扩展实现

### 3. 类型检查
- 只对对象类型进行过滤
- 非对象类型直接返回原值
- 确保函数的健壮性

### 4. 性能考虑
- 过滤操作在每次请求时执行
- 对于大量参数的请求，会有轻微性能开销
- 但相比网络传输的优化，开销可忽略

## 兼容性
- ✅ 支持所有微信小程序版本
- ✅ 兼容现有API调用方式
- ✅ 不影响现有业务逻辑
- ✅ 向后兼容，可安全升级
