# 宣传栏目优化说明

## 优化概述

本次优化主要针对小程序首页的宣传栏目进行了以下改进：

1. **动态数据接入**：将原本的静态数据改为从后端API动态获取
2. **分类滚动支持**：分类标签支持横向滚动，适应更多分类
3. **分页加载**：支持上拉加载更多数据
4. **状态管理优化**：使用stores模式统一管理数据请求

## 主要改动

### 1. 新增文件

#### `weapp/stores/postStore.js`
- 负责帖子数据的获取和处理
- 支持分页查询和分类过滤
- 提供模拟数据作为备用方案
- 统一的数据格式转换

### 2. 修改文件

#### `weapp/stores/indexStore.js`
- 集成categoryStore和postStore
- 优化数据获取逻辑
- 添加错误处理机制

#### `weapp/stores/categoryStore.js`
- 修正API接口路径
- 优化分类数据处理

#### `weapp/pages/index/index.js`
- 添加分页相关状态管理
- 实现分类切换功能
- 优化数据加载逻辑
- 支持上拉加载更多

#### `weapp/pages/index/index.wxml`
- 为分类标签添加横向滚动
- 添加加载状态显示
- 优化UI结构

#### `weapp/pages/index/index.wxss`
- 添加滚动相关样式
- 优化加载状态样式
- 保持原有UI风格

## 功能特性

### 1. 动态数据加载
- 优先从后端API获取数据
- 接口失败时自动使用模拟数据
- 支持实时数据更新

### 2. 分类切换
- 支持按分类筛选帖子
- 分类标签支持横向滚动
- 切换时自动重新加载数据

### 3. 分页加载
- 支持上拉加载更多
- 显示加载状态和结束提示
- 优化内存使用

### 4. 下拉刷新
- 支持下拉刷新数据
- 重置分页状态
- 保持用户体验

## API接口

### 分类接口
```
GET /blade-miniapp/post/categories
```

### 帖子列表接口
```
GET /blade-miniapp/post/list
参数：
- current: 页码
- size: 每页大小
- categoryId: 分类ID（可选）
- keyword: 搜索关键词（可选）
```

## 数据结构

### 分类数据格式
```javascript
{
  id: 1,
  name: "分类名称",
  icon: "图标路径",
  description: "描述",
  tip: "提示信息"
}
```

### 帖子数据格式
```javascript
{
  id: 1,
  avatar: "头像",
  title: "标题",
  time: "时间",
  description: "描述",
  address: "地址",
  images: ["图片1", "图片2"],
  views: 浏览量,
  comments: 评论数,
  likes: 点赞数,
  tag: "分类标签"
}
```

## 使用说明

### 1. 分类切换
点击分类标签可以切换不同的分类，系统会自动加载对应分类的帖子数据。

### 2. 加载更多
滚动到页面底部时会自动加载更多数据，直到没有更多数据为止。

### 3. 下拉刷新
下拉页面可以刷新当前分类的数据。

## 注意事项

1. **接口兼容性**：确保后端API接口与文档一致
2. **错误处理**：所有接口调用都有错误处理机制
3. **性能优化**：使用分页加载避免一次性加载大量数据
4. **用户体验**：保持原有的UI风格和交互体验

## 后续优化建议

1. **缓存机制**：添加数据缓存减少重复请求
2. **搜索功能**：支持关键词搜索
3. **排序功能**：支持按时间、热度等排序
4. **图片懒加载**：优化图片加载性能
5. **骨架屏**：添加加载时的骨架屏效果 