/* 页面整体样式 - 与项目主题保持一致 */
.group-page {
  background: #f5f6f7;
  min-height: 100vh;
}

.scroll-container {
  background: #f5f6f7;
}

.page-content {
  background: #f5f6f7;
  min-height: 100vh;
  padding-bottom: 32rpx;
}
.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 24rpx 0 24rpx;
}
/* 标题样式 - 使用项目标准字体规范 */
.group-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #333333;
  line-height: 1.4;
}
.group-header-actions {
  display: flex;
  gap: 12rpx;
}
.group-header-btn {
  background: #f5f6f7;
  border: none;
  font-size: 28rpx;
  color: #666666;
  margin-left: 8rpx;
}
/* 搜索栏样式 - 与项目主题色保持一致 */
.group-search-bar {
  display: flex;
  align-items: center;
  margin: 24rpx;
  background: #ffffff;
  border-radius: 40rpx;
  padding: 0 20rpx;
  height: 80rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid #f0f0f0;
}
.search-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 16rpx;
  opacity: 0.6;
}
.search-input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 28rpx;
  padding: 0;
  color: #333333;
  line-height: 1.4;
}
.search-input::placeholder {
  color: #999999;
}
.search-btn {
  background: #ff6b6b;
  color: #fff;
  border: none;
  border-radius: 32rpx;
  font-size: 28rpx;
  padding: 0 32rpx;
  margin-left: 12rpx;
  height: 56rpx;
  line-height: 56rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}
.search-btn:active {
  background: #e55555;
  transform: scale(0.95);
}
/* 分类列表样式 - 优化字体和颜色 */
.group-category-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  padding: 0 24rpx;
  margin-bottom: 32rpx;
  gap: 0;
}
.category-item {
  width: 20%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 32rpx;
  transition: all 0.3s ease;
  position: relative;
  box-sizing: border-box;
  padding: 8rpx;
}

.category-item.active {
  transform: scale(1.05);
}

.category-item.active .category-icon {
  background: #ff6b6b;
  border-radius: 50%;
  padding: 12rpx;
  box-shadow: 0 4rpx 16rpx rgba(255, 107, 107, 0.3);
}

.category-item.active .category-label {
  color: #ffffff;
  background: #ff6b6b;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-weight: 600;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
}
.category-icon {
  width: 64rpx;
  height: 64rpx;
  margin-bottom: 12rpx;
  transition: all 0.3s ease;
  border-radius: 50%;
}
.category-label {
  font-size: 24rpx;
  color: #333333;
  transition: all 0.3s ease;
  text-align: center;
  min-height: 32rpx;
  line-height: 1.4;
  font-weight: 500;
}
/* 群组列表样式 - 优化卡片设计和字体 */
.group-list {
  padding: 0 24rpx;
}
.group-card {
  display: flex;
  align-items: center;
  background: #ffffff;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  padding: 32rpx 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}
.group-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}
.group-card-icon {
  width: 96rpx;
  height: 96rpx;
  background: #ff6b6b;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}
.group-card-img {
  width: 64rpx;
  height: 64rpx;
  border-radius: 12rpx;
}
.group-card-content {
  flex: 1;
  min-width: 0;
}
.group-card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
  line-height: 1.4;
}
.group-card-desc {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.4;
}
.group-card-btn {
  background: #ff6b6b;
  color: #fff;
  border: none;
  border-radius: 32rpx;
  font-size: 28rpx;
  font-weight: 500;
  padding: 16rpx 32rpx;
  margin-left: 16rpx;
  transition: all 0.3s ease;
  flex-shrink: 0;
}
.group-card-btn:active {
  background: #e55555;
  transform: scale(0.95);
}
/* 二维码弹窗样式 - 优化视觉效果 */
.qr-modal-mask {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,0.75);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(4rpx);
}
.qr-modal {
  background: transparent;
  border-radius: 0;
  box-shadow: none;
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.qr-image {
  width: 520rpx;
  height: 520rpx;
  margin-bottom: 40rpx;
  border-radius: 24rpx;
  background: #fff;
  box-shadow: 0 12rpx 48rpx rgba(0,0,0,0.2);
  border: 8rpx solid #fff;
}
.qr-tip {
  color: #fff;
  font-size: 30rpx;
  font-weight: 500;
  margin-bottom: 32rpx;
  text-align: center;
  text-shadow: 0 2rpx 8rpx rgba(0,0,0,0.3);
  line-height: 1.4;
}
/* 文字图标样式 */
.group-card-text-icon {
  width: 64rpx;
  height: 64rpx;
  background: #ff6b6b;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 600;
  border-radius: 16rpx;
}
/* 二维码弹窗按钮样式 */
.qr-save-btn {
  background: #ff6b6b;
  color: #fff;
  border: none;
  border-radius: 32rpx;
  font-size: 30rpx;
  font-weight: 500;
  padding: 20rpx 64rpx;
  margin-bottom: 16rpx;
  transition: all 0.3s ease;
}
.qr-save-btn:active {
  background: #e55555;
  transform: scale(0.95);
}

.qr-close-btn{
  background: #666666;
  color: #ffffff;
  border: none;
  border-radius: 32rpx;
  font-size: 30rpx;
  font-weight: 500;
  padding: 20rpx 64rpx;
  margin-bottom: 16rpx;
  transition: all 0.3s ease;
}
.qr-close-btn:active {
  background: #555555;
  transform: scale(0.95);
}

/* 状态提示样式 - 统一字体和颜色规范 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #666666;
  font-weight: 500;
}

/* 空状态样式 */
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 120rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: #666666;
  font-weight: 500;
}

/* 加载更多样式 */
.load-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 32rpx 0;
}

.load-more-text {
  font-size: 26rpx;
  color: #666666;
  font-weight: 500;
}

/* 没有更多数据样式 */
.no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 32rpx 0;
}

.no-more-text {
  font-size: 26rpx;
  color: #999999;
  font-weight: 400;
}
