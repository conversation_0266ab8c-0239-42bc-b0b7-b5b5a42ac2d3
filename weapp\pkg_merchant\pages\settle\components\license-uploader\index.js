const FileValidator = require('../../../../utils/fileValidator');

Component({
  properties: {
    license: String
  },
  methods: {
    onChooseLicense() {
      wx.chooseMedia({
        count: 1,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          if (res.tempFiles && res.tempFiles.length > 0) {
            const file = res.tempFiles[0];
            const validation = FileValidator.validateImage(file.tempFilePath, file.size);
            if (!validation.isValid) {
              wx.showToast({ title: validation.errors[0], icon: 'none' });
              return;
            }
            this.triggerEvent('change', { license: file.tempFilePath });
          }
        }
      });
    },
    onDeleteLicense() {
      this.triggerEvent('change', { license: '' });
    }
  }
}); 