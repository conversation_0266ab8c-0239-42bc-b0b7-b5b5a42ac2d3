<layout
  title="详情信息"
  showSearch="{{false}}"
  showLocation="{{false}}"
  background="linear-gradient(to bottom, #ff6b6b, #ff8585)"
  showBack="{{true}}"
>
  <view class="main-content">
    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 帖子详情内容 -->
    <view wx:elif="{{post}}">
      <user-card 
        avatar="{{post.avatar}}" 
        nickname="{{post.nickname}}" 
        status="活跃" 
        time="{{post.time}}" 
        bind:tap="onUserCardTap"
        data-userid="{{post.userId}}"
      />
      <view class="post-content-card">
        <text class="post-content">{{post.description}}</text>
        <image-grid images="{{post.images}}" bind:imageTap="onImageTap" />
        <view class="post-tags">
          <view class="post-name-tag">{{post.category.name}}</view>
          <!-- <tag-group tags="{{post.tags}}" /> -->
          <view
          class="post-tag-item"
            wx:for="{{post.tags}}"
            wx:key="*this"
            >{{item}}
          </view>
        </view>
        <contact-card 
          contact-type="{{post.contactType === 0 || post.contactType === '0' ? 'phone' : 'wechat'}}" 
          contact-value="{{post.contactType === 0 || post.contactType === '0' ? post.contactPhone : post.contactNumber}}" 
          bind:call="onContactTap" 
        />
      </view>
      <view class="stats-bar">
        <stats-bar
          views="{{post.views}}"
          likes="{{post.likes}}"
          comments="{{post.comments}}"
          isLiked="{{isLiked}}"
          isFavorited="{{isFavorited}}"
          likeLoading="{{likeLoading}}"
          bind:like="onLikeTap"
          bind:favorite="onFavoriteTap"
          bind:more="onMoreTap"
        />
      </view>
      <map-card 
        wx:if="{{post.latitude && post.longitude}}" 
        latitude="{{post.latitude}}" 
        longitude="{{post.longitude}}" 
        address="{{post.address}}" 
      />
      <!-- 操作按钮区 -->
      <view class="action-btns">
        <button
          class="btn-like {{isLiked ? 'liked' : ''}}"
          bindtap="onLikeTap"
          disabled="{{likeLoading}}"
        >
          <image
            class="btn-icon"
            src="{{isLiked ? '/assets/images/detail/heart-filled.png' : '/assets/images/detail/heart-icon.png'}}"
          />
          {{isLiked ? '已点赞' : '点赞'}}
        </button>

        <button class="btn-primary comment-btn" bindtap="onFeedbackTap">评论</button>

        <button class="btn-outline share-btn" bindtap="onShareTap">分享</button>
      </view>
      <view class="comment-section">
        <text class="section-title">用户反馈</text>
        <block wx:if="{{feedbackList.length}}">
          <block wx:for="{{feedbackList}}" wx:key="id">
            <comment-card
              id="{{item.id}}"
              feedbackId="{{item.id}}"
              avatar="{{item.avatar}}"
              nickname="{{item.nickname}}"
              content="{{item.content}}"
              time="{{item.createTime}}"
              likes="{{item.helpfulCount}}"
              isHelpful="{{item.isHelpful}}"
              replyToUserName="{{item.replyToUserName}}"
              image="{{item.image}}"
              replyCount="{{item.replyCount}}"
              expanded="{{item.expanded}}"
              replies="{{item.replies}}"
              hasMoreReplies="{{item.hasMoreReplies}}"
              allRepliesLoaded="{{item.allRepliesLoaded}}"
              totalReplyCount="{{item.totalReplyCount}}"
              commentId="{{item.id}}"
              postId="{{post.id}}"
              userId="{{item.userId}}"
              formattedContent="{{item.formattedContent}}"
              bind:like="onFeedbackLikeTap"
              bind:reply="onCommentReply"
              bind:toggleReplies="onToggleReplies"
              bind:loadReplies="onLoadReplies"
              bind:likeReply="onReplyLike"
              bind:loadMoreReplies="onLoadMoreReplies"
            />
          </block>
        </block>
        <block wx:else>
          <view class="empty-feedback">暂无用户反馈</view>
        </block>
      </view>
    </view>

    <!-- 错误状态 -->
    <view wx:else class="error-container">
      <image class="error-icon" src="/assets/images/404.svg" />
      <text class="error-text">帖子不存在或已被删除</text>
    </view>
  </view>

  <!-- 回复输入框 -->
  <view wx:if="{{showReplyInput}}" class="reply-input-container">
    <view class="reply-input-header">
      <text class="reply-target">回复 @{{replyTarget.nickname}}</text>
      <image class="close-reply" src="/assets/images/common/delete.png" bindtap="onCancelReply" />
    </view>
    <view class="reply-input-content">
      <textarea
        class="reply-textarea"
        placeholder="{{replyPlaceholder}}"
        value="{{replyContent}}"
        bindinput="onReplyInput"
        auto-focus="{{true}}"
        maxlength="500"
      />
      <view class="reply-actions">
        <text class="char-count">{{replyContent.length}}/500</text>
        <button class="reply-submit-btn" bindtap="onSubmitReply" disabled="{{replySubmitDisabled || replySubmitting}}">
          {{replySubmitting ? '发送中...' : '发送'}}
        </button>
      </view>
    </view>
  </view>
</layout>

<!-- 反馈标签弹窗组件 -->
<feedback-dialog
  visible="{{showFeedbackDialog}}"
  tags="{{feedbackTags}}"
  loading="{{feedbackDialogLoading}}"
  bind:submit="onFeedbackDialogSubmit"
  bind:cancel="onFeedbackDialogCancel"
/>

<!-- 举报弹窗组件 -->
<report-dialog
  visible="{{showReportDialog}}"
  reason="{{reportReason}}"
  loading="{{reportDialogLoading}}"
  bind:submit="onReportDialogSubmit"
  bind:cancel="onReportDialogCancel"
/>

<!-- 更多功能弹窗 -->
<view class="more-modal" wx:if="{{showMoreModal}}" bindtap="hideMoreModal">
  <view class="more-content" catchtap="stopPropagation">
    <view class="more-item" bindtap="onReportTap">
      <image class="more-icon-item" src="/assets/images/common/report.png" mode="aspectFit"></image>
      <text class="more-text">举报</text>
  </view>
    <view class="more-item" bindtap="onFavoriteTap">
      <image class="more-icon-item" src="/assets/images/detail/star-icon.png" mode="aspectFit"></image>
      <text class="more-text">{{isFavorited ? '取消收藏' : '收藏'}}</text>
  </view>
  </view>
</view>

<custom-tabbar show="{{showTabbar}}" /> 