const { getUserInfo } = require('../../utils/auth');
const SharePointsStore = require('../../stores/sharePointsStore');
const InviteStore = require('../../stores/inviteStore');
const QRCodeStore = require('../../stores/qrcodeStore');

Page({
  data: {
    // 用户信息
    userInfo: null,

    // 积分统计
    stats: {},

    // 邀请统计
    inviteStats: {},

    // 今日分享统计
    todayStats: [],

    // 分享配置
    shareConfig: {},

    // 邀请配置
    inviteConfig: {
      registerReward: 10,
      firstPostReward: 5
    },

    // 积分记录
    records: [],

    // 邀请记录
    recentInviteRecords: [],
    showInviteRecords: true,

    // 分页信息
    currentPage: 1,
    pageSize: 10,
    hasMore: true,
    loading: false,

    // 筛选器
    currentFilter: 'all',

    // 规则显示
    showRules: false,

    // 邀请二维码
    showQRCodeModal: false,
    inviteCode: '',
    qrcodeSize: 200
  },

  onLoad(options) {
    this.initPage();
  },

  onShow() {
    // 页面显示时刷新数据
    this.refreshData();
  },

  onPullDownRefresh() {
    this.refreshData().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMore();
    }
  },

  /**
   * 初始化页面
   */
  async initPage() {
    try {
      // 获取用户信息
      const userInfo = await getUserInfo();
      if (!userInfo) {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
        return;
      }

      this.setData({ userInfo });

      // 加载页面数据
      await this.loadPageData();
    } catch (error) {
      console.error('初始化页面失败:', error);
      wx.showToast({
        title: '页面加载失败',
        icon: 'none'
      });
    }
  },

  /**
   * 加载页面数据
   */
  async loadPageData() {
    const { userInfo } = this.data;
    if (!userInfo) return;

    try {
      // 并行加载各种数据
      const [stats, shareConfig, todayCount, inviteStats, inviteConfig, recentInvites] = await Promise.all([
        SharePointsStore.getSharePointsStats(userInfo.id),
        SharePointsStore.getSharePointsConfig(),
        SharePointsStore.getTodayShareCount(userInfo.id),
        InviteStore.getInviteStats(userInfo.id),
        InviteStore.getInviteConfig(),
        InviteStore.getRecentInviteRecords(userInfo.id)
      ]);

      // 处理今日分享统计
      const todayStats = this.processTodayStats(shareConfig, todayCount);

      this.setData({
        stats,
        shareConfig,
        todayStats,
        inviteStats,
        inviteConfig,
        recentInviteRecords: recentInvites
      });

      // 加载积分记录
      await this.loadRecords(true);
    } catch (error) {
      console.error('加载页面数据失败:', error);
    }
  },

  /**
   * 处理今日分享统计数据
   */
  processTodayStats(shareConfig, todayCount) {
    const todayStats = [];
    const shareTypes = shareConfig.shareTypes || {};
    const sharePoints = shareConfig.sharePoints || {};
    const dailyLimit = shareConfig.dailyLimit || {};

    for (const [type, typeName] of Object.entries(shareTypes)) {
      todayStats.push({
        type,
        typeName,
        points: sharePoints[type] || 0,
        count: todayCount[type] || 0,
        limit: dailyLimit[type] || 0
      });
    }

    return todayStats;
  },

  /**
   * 加载积分记录
   */
  async loadRecords(reset = false) {
    const { userInfo, currentFilter, currentPage, pageSize, loading } = this.data;

    if (loading) return;
    if (!userInfo) return;

    this.setData({ loading: true });

    try {
      const page = reset ? 1 : currentPage;

      const result = await SharePointsStore.getSharePointsRecords(
        userInfo.id,
        currentFilter,
        page,
        pageSize
      );

      const newRecords = result.records || [];
      const records = reset ? newRecords : [...this.data.records, ...newRecords];

      this.setData({
        records,
        currentPage: page + 1,
        hasMore: newRecords.length >= pageSize,
        loading: false
      });
    } catch (error) {
      console.error('加载积分记录失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    this.setData({
      currentPage: 1,
      hasMore: true,
      records: []
    });
    
    await this.loadPageData();
  },

  /**
   * 刷新统计数据
   */
  async refreshStats() {
    const { userInfo } = this.data;
    if (!userInfo) return;

    try {
      wx.showLoading({ title: '刷新中...' });

      const [stats, todayCount, inviteStats] = await Promise.all([
        this.getSharePointsStats(userInfo.id),
        this.getTodayShareCount(userInfo.id),
        this.getInviteStats(userInfo.id)
      ]);

      const todayStats = this.processTodayStats(this.data.shareConfig, todayCount);

      this.setData({
        stats,
        todayStats,
        inviteStats
      });

      wx.hideLoading();
      wx.showToast({
        title: '刷新成功',
        icon: 'success'
      });
    } catch (error) {
      console.error('刷新统计失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '刷新失败',
        icon: 'none'
      });
    }
  },

  /**
   * 切换筛选器
   */
  switchFilter(e) {
    const filter = e.currentTarget.dataset.filter;
    if (filter === this.data.currentFilter) return;

    this.setData({
      currentFilter: filter,
      currentPage: 1,
      hasMore: true,
      records: []
    });

    this.loadRecords(true);
  },

  /**
   * 加载更多
   */
  loadMore() {
    this.loadRecords(false);
  },

  /**
   * 切换规则显示
   */
  toggleRules() {
    this.setData({
      showRules: !this.data.showRules
    });
  },

  /**
   * 快捷分享小程序
   */
  async quickShareApp() {
    const { userInfo } = this.data;
    if (!userInfo) return;

    try {
      // 触发分享
      wx.showShareMenu({
        withShareTicket: true,
        success: () => {
          // 分享成功后记录分享行为
          this.recordShareAction(userInfo.id, 'qrcode', 'wechat').then(result => {
            if (result.success) {
              // 刷新数据
              this.refreshData();
              wx.showToast({
                title: `获得${result.points}积分`,
                icon: 'success'
              });
            }
          });
        }
      });
    } catch (error) {
      console.error('快捷分享失败:', error);
      wx.showToast({
        title: '分享失败',
        icon: 'none'
      });
    }
  },

  /**
   * 分享给好友
   */
  onShareAppMessage() {
    const { userInfo, inviteCode } = this.data;

    // 如果有邀请码，分享邀请链接
    if (inviteCode && userInfo) {
      // 记录邀请分享行为
      this.recordInviteShare(userInfo.id, 'wechat', inviteCode);

      return {
        title: `${userInfo.nickName || '朋友'}邀请你加入，注册即可获得积分奖励！`,
        path: `/pages/index/index?inviteCode=${inviteCode}&from=${userInfo.id}`,
        imageUrl: '/images/invite-share.png'
      };
    }

    // 记录普通分享行为
    if (userInfo) {
      this.recordShareAction(userInfo.id, 'qrcode', 'wechat');
    }

    return {
      title: '分享积分记录 - 查看我的分享积分获得情况',
      path: '/pages/share-points/share-points',
      imageUrl: '/images/share-points.png'
    };
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    const { userInfo, inviteCode } = this.data;

    // 如果有邀请码，分享邀请链接
    if (inviteCode && userInfo) {
      // 记录邀请分享行为
      this.recordInviteShare(userInfo.id, 'moments', inviteCode);

      return {
        title: `${userInfo.nickName || '朋友'}邀请你加入，注册即可获得积分奖励！`,
        query: `inviteCode=${inviteCode}&from=${userInfo.id}`,
        imageUrl: '/images/invite-share.png'
      };
    }

    // 记录普通分享行为
    if (userInfo) {
      this.recordShareAction(userInfo.id, 'qrcode', 'moments');
    }

    return {
      title: '分享积分记录 - 查看我的分享积分获得情况',
      query: 'from=timeline',
      imageUrl: '/images/share-points.png'
    };
  },

  // ==================== 邀请相关方法 ====================



  /**
   * 生成邀请二维码
   */
  async generateInviteQRCode() {
    const { userInfo } = this.data;
    if (!userInfo) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    try {
      // 生成邀请码
      const inviteCode = await InviteStore.generateInviteCode(userInfo.id);

      // 使用后端生成二维码
      const qrCodeResult = await QRCodeStore.getOrGenerateQRCode('invite', userInfo.id, { inviteCode });

      if (qrCodeResult.success !== false && qrCodeResult.qrCodeUrl) {
        this.setData({
          inviteCode,
          qrCodeUrl: qrCodeResult.qrCodeUrl,
          qrCodeContent: qrCodeResult.path || `pages/index/index?inviteCode=${inviteCode}&from=${userInfo.id}`,
          showQRCodeModal: true
        });

        wx.showToast({
          title: '二维码生成成功',
          icon: 'success'
        });
      } else {
        throw new Error(qrCodeResult.message || '生成二维码失败');
      }
    } catch (error) {
      console.error('生成邀请二维码失败:', error);
      wx.showToast({
        title: '生成失败',
        icon: 'none'
      });
    }
  },



  /**
   * 绘制二维码
   */
  drawQRCode(content) {
    const { qrcodeSize } = this.data;
    const ctx = wx.createCanvasContext('inviteQRCode', this);

    // 这里需要引入二维码生成库
    // 简化实现，实际项目中需要使用专门的二维码库
    try {
      // 清空画布
      ctx.clearRect(0, 0, qrcodeSize, qrcodeSize);

      // 绘制背景
      ctx.setFillStyle('#ffffff');
      ctx.fillRect(0, 0, qrcodeSize, qrcodeSize);

      // 绘制二维码占位符
      ctx.setFillStyle('#000000');
      ctx.setTextAlign('center');
      ctx.setFontSize(12);
      ctx.fillText('二维码', qrcodeSize / 2, qrcodeSize / 2);
      ctx.fillText(content.substring(0, 20) + '...', qrcodeSize / 2, qrcodeSize / 2 + 20);

      ctx.draw();
    } catch (error) {
      console.error('绘制二维码失败:', error);
    }
  },

  /**
   * 分享邀请链接
   */
  async shareInviteLink() {
    const { userInfo } = this.data;
    if (!userInfo) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    try {
      wx.showLoading({ title: '生成中...' });

      // 生成邀请码
      const inviteCode = await this.generateInviteCode(userInfo.id);

      wx.hideLoading();

      // 分享邀请链接
      wx.showShareMenu({
        withShareTicket: true,
        menus: ['shareAppMessage', 'shareTimeline']
      });

      // 设置分享内容
      this.setData({ inviteCode });

    } catch (error) {
      console.error('分享邀请链接失败:', error);
      wx.showToast({
        title: '生成失败',
        icon: 'none'
      });
      wx.hideLoading();
    }
  },

  /**
   * 保存二维码到相册
   */
  async saveQRCodeToAlbum() {
    const { qrCodeUrl } = this.data;

    if (!qrCodeUrl) {
      wx.showToast({
        title: '没有可保存的二维码',
        icon: 'none'
      });
      return;
    }

    const result = await QRCodeStore.saveQRCodeToAlbum(qrCodeUrl);

    if (!result.success && result.error && result.error.includes('auth deny')) {
      wx.showModal({
        title: '提示',
        content: '需要您授权保存图片到相册',
        success: (res) => {
          if (res.confirm) {
            wx.openSetting();
          }
        }
      });
    }
  },

  /**
   * 分享二维码给好友
   */
  shareQRCodeToFriend() {
    // 触发分享
    wx.showShareMenu({
      withShareTicket: true
    });
  },

  /**
   * 生成分享积分二维码
   */
  async generateShareQRCode(shareType = 'qrcode') {
    const { userInfo } = this.data;
    if (!userInfo) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    try {
      // 使用后端生成分享积分二维码
      const qrCodeResult = await this.getOrGenerateQRCode('sharePoints', userInfo.id, { shareType });

      if (qrCodeResult.success !== false && qrCodeResult.qrCodeUrl) {
        this.setData({
          shareType,
          qrCodeUrl: qrCodeResult.qrCodeUrl,
          qrCodeContent: qrCodeResult.path || `pages/index/index?shareUserId=${userInfo.id}&shareType=${shareType}`,
          showQRCodeModal: true
        });

        wx.showToast({
          title: '二维码生成成功',
          icon: 'success'
        });

        // 记录分享行为
        this.recordShareAction(userInfo.id, shareType, 'qrcode');
      } else {
        throw new Error(qrCodeResult.message || '生成二维码失败');
      }
    } catch (error) {
      console.error('生成分享积分二维码失败:', error);
      wx.showToast({
        title: '生成失败',
        icon: 'none'
      });
    }
  },

  /**
   * 批量生成二维码
   */
  async generateBatchQRCodes() {
    const { userInfo } = this.data;
    if (!userInfo) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    try {
      // 生成邀请码
      const inviteCode = await this.generateInviteCode(userInfo.id);

      // 批量生成二维码
      const batchResult = await this.getOrGenerateQRCode('batch', userInfo.id, {
        inviteCode,
        shareTypes: ['qrcode', 'post', 'institution']
      });

      if (batchResult.success !== false) {
        this.setData({
          showBatchQRCodes: true,
          batchQRCodes: batchResult,
          inviteCode
        });

        wx.showToast({
          title: '批量生成成功',
          icon: 'success'
        });
      } else {
        throw new Error(batchResult.message || '批量生成二维码失败');
      }
    } catch (error) {
      console.error('批量生成二维码失败:', error);
      wx.showToast({
        title: '生成失败',
        icon: 'none'
      });
    }
  },

  /**
   * 隐藏二维码弹窗
   */
  hideQRCodeModal() {
    this.setData({
      showQRCodeModal: false
    });
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  },

  /**
   * 查看全部邀请记录
   */
  viewAllInviteRecords() {
    wx.navigateTo({
      url: '/pages/invite-records/invite-records'
    });
  },

  // ==================== 分享积分相关方法 ====================



  // ==================== 二维码生成相关方法 ====================


});
