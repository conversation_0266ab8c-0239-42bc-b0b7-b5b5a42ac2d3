<layout title="发布内容"
  navBackgroundColor="#FF7B7B"
  background="linear-gradient(to bottom, #ff6b6b, #ff8585)"
  navTextColor="#FFF"
  showSearch="{{false}}"
  showLocation="{{false}}"
  showBack="{{true}}"
>
<view style="padding: 0 24rpx">
  <disclaimer />
  <site-selector  />
</view>
<view class="category-select">
<category-card
  wx:for="{{categoryList}}"
  wx:key="name"
  icon="{{item.icon}}"
  name="{{item.name}}"
  badge="{{item.badge}}"
  selected="{{selectedCategory && selectedCategory.name === item.name}}"
  bindtap="onCategorySelect"
  data-category="{{item}}"
/>
</view>
</layout> 