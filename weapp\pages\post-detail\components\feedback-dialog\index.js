Component({
  properties: {
    visible: Boolean,
    tags: Array,
    loading: Boolean
  },
  data: {
    selectedTag: '',
    content: ''
  },
  methods: {
    onSelectTag(e) {
      this.setData({ selectedTag: e.currentTarget.dataset.tag });
    },
    onInput(e) {
      this.setData({ content: e.detail.value });
    },
    onSubmitTap() {
      if (!this.data.selectedTag) {
        wx.showToast({ title: '请选择标签', icon: 'none' });
        return;
      }
      if (!this.data.content) {
        wx.showToast({ title: '请输入内容', icon: 'none' });
        return;
      }
      this.triggerEvent('submit', {
        tag: this.data.selectedTag,
        content: this.data.content
      });
      // 可选：重置内容
      // this.setData({ selectedTag: '', content: '' });
    },
    onCancelTap() {
      this.triggerEvent('cancel');
      this.setData({ selectedTag: '', content: '' });
    }
  }
}); 