// 余额明细页面
const app = getApp();
const userStore = require('../../../stores/userStore');
const { request } = require('../../../utils/request.js');

Page({
  data: {
    // 导航栏高度
    navBarHeight: app.globalData.navBarHeight,
    
    // 当前余额
    currentBalance: '0.00',
    
    // 筛选条件
    currentFilter: '',
    
    // 明细记录
    records: [],
    
    // 分页
    current: 1,
    size: 20,
    hasMore: true,
    loading: false
  },

  onLoad() {
    console.log('余额明细页面加载');
    this.initPage();
    
    // 测试请求 - 查看数据结构
    this.testRequest();
  },

  onShow() {
    // 页面显示时刷新余额
    this.loadCurrentBalance();
  },

  /**
   * 初始化页面
   */
  async initPage() {
    try {
      // 加载当前余额
      await this.loadCurrentBalance();
      
      // 加载明细记录
      await this.loadBalanceRecords();
      
    } catch (error) {
      console.error('初始化余额明细页面失败:', error);
      wx.showToast({
        title: '页面加载失败',
        icon: 'none'
      });
    }
  },

  /**
   * 加载当前余额
   */
  async loadCurrentBalance() {
    try {
      const balance = await userStore.getUserWalletBalance();
      this.setData({
        currentBalance: balance.toFixed(2)
      });
    } catch (error) {
      console.error('加载用户余额失败:', error);
      this.setData({
        currentBalance: '0.00'
      });
    }
  },

  /**
   * 加载余额明细记录
   */
  async loadBalanceRecords(isLoadMore = false) {
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      const { request } = require('../../../utils/request');
      
      const params = {
        current: isLoadMore ? this.data.current + 1 : 1,
        size: this.data.size,
        type: this.data.currentFilter || undefined
      };

      const res = await request({
        url: '/blade-chat/user/wallet/records',
        method: 'GET',
        data: params
      });

      if (res.code === 200) {
        const newRecords = res.data.records || [];
        const processedRecords = this.processRecords(newRecords);
        
        if (isLoadMore) {
          this.setData({
            records: [...this.data.records, ...processedRecords],
            current: this.data.current + 1,
            hasMore: newRecords.length === this.data.size
          });
        } else {
          this.setData({
            records: processedRecords,
            current: 1,
            hasMore: newRecords.length === this.data.size
          });
        }
      } else {
        throw new Error(res.message || '加载失败');
      }
    } catch (error) {
      console.error('加载余额明细失败:', error);
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 处理记录数据
   */
  processRecords(records) {
    return records.map(record => {
      // 格式化时间
      const timeText = this.formatTime(record.createTime);
      
      // 格式化状态
      const statusText = this.getStatusText(record.status);
      
      // 格式化标题
      const title = this.getRecordTitle(record);
      
      return {
        ...record,
        timeText,
        statusText,
        title
      };
    });
  },

  /**
   * 格式化时间
   */
  formatTime(timeStr) {
    if (!timeStr) return '';
    
    const date = new Date(timeStr);
    const now = new Date();
    const diff = now - date;
    
    // 今天
    if (diff < 24 * 60 * 60 * 1000) {
      return date.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    }
    
    // 昨天
    if (diff < 48 * 60 * 60 * 1000) {
      return '昨天 ' + date.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    }
    
    // 其他日期
    return date.toLocaleDateString('zh-CN', { 
      month: '2-digit', 
      day: '2-digit',
      hour: '2-digit', 
      minute: '2-digit' 
    });
  },

  /**
   * 获取状态文本
   */
  getStatusText(status) {
    const statusMap = {
      'SUCCESS': '成功',
      'PENDING': '处理中',
      'FAILED': '失败',
      'CANCELLED': '已取消'
    };
    return statusMap[status] || '未知';
  },

  /**
   * 获取记录标题
   */
  getRecordTitle(record) {
    const typeMap = {
      'recharge': '充值',
      'consume': '消费',
      'refund': '退款',
      'reward': '奖励',
      'withdraw': '提现'
    };
    return typeMap[record.type] || '余额变动';
  },

  /**
   * 筛选条件改变
   */
  onFilterChange(e) {
    const filter = e.currentTarget.dataset.filter;
    this.setData({
      currentFilter: filter,
      records: [],
      current: 1,
      hasMore: true
    });
    this.loadBalanceRecords();
  },

  /**
   * 加载更多
   */
  onLoadMore() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadBalanceRecords(true);
    }
  },

  /**
   * 下拉刷新
   */
  async onPullDownRefresh() {
    try {
      await this.loadCurrentBalance();
      await this.loadBalanceRecords();
      wx.stopPullDownRefresh();
    } catch (error) {
      console.error('下拉刷新失败:', error);
      wx.stopPullDownRefresh();
    }
  },

  /**
   * 测试请求 - 查看数据结构
   */
  async testRequest() {
    try {
      const res = await request({
        // url: `/blade-chat/user/recharge/records/1952264056136445953`,
        url: `/blade-chat/user/recharge/records`,
        method: 'GET'
      });
      console.log('充值记录数据结构:', res);
    } catch (error) {
      console.error('测试请求失败:', error);
    }
  }
});
