/* pkg_user/pages/card-detail/card-detail.wxss */

.card-detail-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 0;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 131, 130, 0.3);
  border-top: 4rpx solid #ff8382;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #ff8382;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 名片内容 */
.card-content {
  background: #fff;
  min-height: 100vh;
}

/* 头部信息区域 */
.header-section {
  padding: 40rpx 32rpx 32rpx;
  background: #fff;
  position: relative;
}

.avatar-container {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 24rpx;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 4rpx solid #fff;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.basic-info {
  margin-bottom: 24rpx;
}

.name-row {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.name {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-right: 12rpx;
}

.gender {
  font-size: 32rpx;
  font-weight: bold;
}

.gender.male {
  color: #4A90E2;
}

.gender.female {
  color: #FF6B9D;
}

.position {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.company {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 8rpx;
}

.user-id {
  font-size: 24rpx;
  color: #999;
  display: block;
}

.status-tags {
  display: flex;
  gap: 16rpx;
  position: absolute;
  top: 40rpx;
  right: 32rpx;
}

.tag {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.tag.public {
  background: #E8F5E8;
  color: #4CAF50;
}

.tag.open {
  background: #FFF3E0;
  color: #FF9800;
}

/* 业务简介区域 */
.intro-section {
  padding: 32rpx;
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.intro-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  display: block;
  margin-bottom: 16rpx;
}

.update-time {
  font-size: 24rpx;
  color: #999;
  display: flex;
  align-items: center;
}

.update-time::before {
  content: '🕐';
  margin-right: 8rpx;
}

/* 联系方式区域 */
.contact-section {
  padding: 32rpx;
  background: #fff;
}

.contact-row {
  display: flex;
  margin-bottom: 32rpx;
}

.contact-row:last-child {
  margin-bottom: 0;
}

.contact-item {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 24rpx 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  margin-right: 16rpx;
  transition: all 0.2s ease;
}

.contact-item:last-child {
  margin-right: 0;
}

.contact-item.full-width {
  flex: none;
  width: 100%;
  margin-right: 0;
  margin-bottom: 16rpx;
}

.contact-item:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.contact-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  width: 40rpx;
  text-align: center;
}

.contact-info {
  flex: 1;
}

.contact-label {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 4rpx;
}

.contact-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  display: block;
}

/* 相关媒体区域 */
.media-section {
  padding: 32rpx;
  background: #fff;
  border-top: 1rpx solid #f0f0f0;
}

.section-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  display: block;
  margin-bottom: 24rpx;
}

.media-grid {
  display: flex;
  gap: 16rpx;
}

.media-item {
  flex: 1;
  position: relative;
  border-radius: 12rpx;
  overflow: hidden;
  background: #f8f9fa;
}

.media-image {
  width: 100%;
  height: 200rpx;
}

.media-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}

.play-icon {
  font-size: 48rpx;
  opacity: 0.9;
}

/* 底部区域 */
.footer-section {
  padding: 32rpx;
  background: #fff;
  border-top: 1rpx solid #f0f0f0;
}

.footer-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.footer-value {
  font-size: 28rpx;
  color: #666;
}

.footer-value.owner {
  color: #ff8382;
  font-weight: 600;
}


