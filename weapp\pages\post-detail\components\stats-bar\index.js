Component({
  properties: {
    views: { type: Number, value: 0 },
    likes: { type: Number, value: 0 },
    comments: { type: Number, value: 0 },
    isLiked: { type: Boolean, value: false },
    isFavorited: { type: Boolean, value: true },
    likeLoading: { type: Boolean, value: false }
  },
  data: {},
  methods: {
    // 点赞操作
    onLikeTap() {
      if (this.data.likeLoading) return;
      this.triggerEvent('like');
    },

    onFavoriteTap() {
      this.triggerEvent('favorite');
    },

    onMoreTap() {
      this.triggerEvent('more');
    }
  }
});