/**
 * 邀请码管理页面
 * 集成邀请码生成、管理、统计和用户查看功能
 */

const { request } = require('../../utils/request');
const InviteStore = require('../../stores/inviteStore');

Page({
  data: {
    // 用户信息
    userId: '',
    userInfo: {},
    
    // 邀请码列表
    inviteCodes: [],
    
    // 邀请统计
    inviteStats: {
      totalInvites: 0,
      successfulRegistrations: 0,
      totalRewardPoints: 0,
      conversionRate: 0
    },
    
    // 我邀请的用户列表
    myInvitees: [],
    
    // 最近邀请记录
    recentRecords: [],
    
    // 邀请排行榜
    ranking: [],
    
    // 页面状态
    loading: true,
    refreshing: false,
    
    // 分页信息
    pagination: {
      current: 1,
      size: 20,
      total: 0,
      hasMore: true
    },
    
    // 当前选中的邀请码
    selectedInviteCode: null,
    selectedCodeDetails: null,
    
    // 弹窗状态
    showCodeDetails: false,
    showCreateCode: false,
    showQRCode: false,
    
    // 二维码信息
    qrCodeInfo: null,
    
    // 统计天数
    statisticsDays: 30
  },

  /**
   * 页面加载
   */
  onLoad(options) {
    console.log('邀请码管理页面加载');
    this.initPage();
  },

  /**
   * 页面显示
   */
  onShow() {
    this.refreshData();
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.refreshData().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 上拉加载更多
   */
  onReachBottom() {
    this.loadMoreInvitees();
  },

  /**
   * 初始化页面
   */
  async initPage() {
    try {
      // 获取用户信息
      const userInfo = wx.getStorageSync('userInfo');
      if (!userInfo || !userInfo.id) {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
        return;
      }

      this.setData({
        userId: userInfo.id,
        userInfo: userInfo
      });

      // 加载数据
      await this.loadAllData();

    } catch (error) {
      console.error('初始化页面失败:', error);
      this.showError('页面初始化失败');
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    this.setData({ refreshing: true });
    try {
      await this.loadAllData();
    } catch (error) {
      console.error('刷新数据失败:', error);
      this.showError('刷新失败');
    } finally {
      this.setData({ refreshing: false });
    }
  },

  /**
   * 加载所有数据
   */
  async loadAllData() {
    const { userId } = this.data;
    
    try {
      // 并行加载数据
      const [
        inviteCodesResult,
        statsResult,
        inviteesResult,
        rankingResult
      ] = await Promise.allSettled([
        this.loadInviteCodes(),
        this.loadInviteStats(),
        this.loadMyInvitees(1),
        this.loadInviteRanking()
      ]);

      // 处理结果
      if (inviteCodesResult.status === 'fulfilled') {
        this.setData({ inviteCodes: inviteCodesResult.value });
      }

      if (statsResult.status === 'fulfilled') {
        this.setData({ inviteStats: statsResult.value });
      }

      if (inviteesResult.status === 'fulfilled') {
        const { records, pagination } = inviteesResult.value;
        this.setData({ 
          myInvitees: records,
          pagination: pagination
        });
      }

      if (rankingResult.status === 'fulfilled') {
        this.setData({ ranking: rankingResult.value });
      }

    } catch (error) {
      console.error('加载数据失败:', error);
      throw error;
    }
  },

  /**
   * 加载邀请码列表
   */
  async loadInviteCodes() {
    const { userId } = this.data;
    
    try {
      const response = await request({
        url: `/blade-chat-open/qrcode/invite-codes/${userId}`,
        method: 'GET'
      });

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.msg || '获取邀请码列表失败');
      }
    } catch (error) {
      console.error('加载邀请码列表失败:', error);
      throw error;
    }
  },

  /**
   * 加载邀请统计
   */
  async loadInviteStats() {
    const { userId, statisticsDays } = this.data;
    
    try {
      const response = await request({
        url: `/blade-chat-open/qrcode/invite-stats/${userId}`,
        method: 'GET',
        data: { days: statisticsDays }
      });

      if (response.success && response.data) {
        const { basicStats, effectStats, recentRecords } = response.data;
        
        // 更新最近记录
        this.setData({ recentRecords: recentRecords || [] });
        
        // 合并统计数据
        return {
          ...basicStats,
          ...effectStats
        };
      } else {
        throw new Error(response.msg || '获取邀请统计失败');
      }
    } catch (error) {
      console.error('加载邀请统计失败:', error);
      throw error;
    }
  },

  /**
   * 加载我邀请的用户
   */
  async loadMyInvitees(page = 1) {
    const { userId } = this.data;
    const { size } = this.data.pagination;
    
    try {
      const response = await request({
        url: `/blade-chat-open/qrcode/my-invitees/${userId}`,
        method: 'GET',
        data: { current: page, size: size }
      });

      if (response.success && response.data) {
        return {
          records: response.data.records || [],
          pagination: {
            current: response.data.current || page,
            size: response.data.size || size,
            total: response.data.total || 0,
            hasMore: response.data.hasNext || false
          }
        };
      } else {
        throw new Error(response.msg || '获取邀请用户列表失败');
      }
    } catch (error) {
      console.error('加载我邀请的用户失败:', error);
      throw error;
    }
  },

  /**
   * 加载更多邀请用户
   */
  async loadMoreInvitees() {
    const { pagination } = this.data;
    
    if (!pagination.hasMore) {
      return;
    }

    try {
      const nextPage = pagination.current + 1;
      const result = await this.loadMyInvitees(nextPage);
      
      const newInvitees = [...this.data.myInvitees, ...result.records];
      
      this.setData({
        myInvitees: newInvitees,
        pagination: result.pagination
      });
      
    } catch (error) {
      console.error('加载更多邀请用户失败:', error);
      this.showError('加载更多失败');
    }
  },

  /**
   * 加载邀请排行榜
   */
  async loadInviteRanking() {
    try {
      const response = await request({
        url: '/blade-chat-open/qrcode/invite-ranking',
        method: 'GET',
        data: { type: 'total', limit: 10 }
      });

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.msg || '获取邀请排行榜失败');
      }
    } catch (error) {
      console.error('加载邀请排行榜失败:', error);
      throw error;
    }
  },

  /**
   * 显示错误信息
   */
  showError(message) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 3000
    });
  },

  // ==================== 事件处理方法 ====================

  /**
   * 创建新邀请码
   */
  async onCreateInviteCode() {
    const { userId } = this.data;

    try {
      wx.showLoading({ title: '创建中...' });

      // 使用 InviteStore 生成邀请码
      const inviteCode = await InviteStore.generateInviteCode(userId);

      wx.showToast({
        title: '创建成功',
        icon: 'success'
      });

      // 刷新邀请码列表
      await this.loadInviteCodes().then(codes => {
        this.setData({ inviteCodes: codes });
      });

    } catch (error) {
      console.error('创建邀请码失败:', error);
      this.showError(error.message || '创建邀请码失败');
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 查看邀请码详情
   */
  async onViewCodeDetails(e) {
    const { code } = e.currentTarget.dataset;

    try {
      wx.showLoading({ title: '加载中...' });

      const response = await request({
        url: `/blade-chat-open/qrcode/invite-code/${code}`,
        method: 'GET'
      });

      if (response.success && response.data) {
        this.setData({
          selectedInviteCode: code,
          selectedCodeDetails: response.data,
          showCodeDetails: true
        });
      } else {
        throw new Error(response.msg || '获取邀请码详情失败');
      }

    } catch (error) {
      console.error('获取邀请码详情失败:', error);
      this.showError(error.message || '获取详情失败');
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 生成二维码
   */
  async onGenerateQRCode(e) {
    const { code } = e.currentTarget.dataset;
    const { userId } = this.data;

    try {
      wx.showLoading({ title: '生成中...' });

      const response = await request({
        url: '/blade-chat-open/qrcode/invite',
        method: 'POST',
        data: {
          userId: userId,
          inviteCode: code
        }
      });

      if (response.success && response.data) {
        this.setData({
          qrCodeInfo: response.data,
          showQRCode: true
        });
      } else {
        throw new Error(response.msg || '生成二维码失败');
      }

    } catch (error) {
      console.error('生成二维码失败:', error);
      this.showError(error.message || '生成二维码失败');
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 分享邀请码
   */
  onShareInviteCode(e) {
    const { code } = e.currentTarget.dataset;

    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });

    // 记录分享行为
    this.recordShareAction(code, 'manual');
  },

  /**
   * 复制邀请码
   */
  onCopyInviteCode(e) {
    const { code } = e.currentTarget.dataset;

    wx.setClipboardData({
      data: code,
      success: () => {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success'
        });

        // 记录复制行为
        this.recordShareAction(code, 'copy');
      }
    });
  },

  /**
   * 保存二维码图片
   */
  onSaveQRCode() {
    const { qrCodeInfo } = this.data;

    console.log('qrCodeInfo:', qrCodeInfo);

    // 检查可能的字段名
    const qrImageUrl = qrCodeInfo?.qrCodeUrl || qrCodeInfo?.qrImageUrl || qrCodeInfo?.imageUrl || qrCodeInfo?.url;

    if (!qrCodeInfo || !qrImageUrl) {
      console.error('二维码信息不完整:', qrCodeInfo);
      this.showError('二维码图片不存在');
      return;
    }

    console.log('准备保存二维码图片:', qrImageUrl);

    wx.showLoading({ title: '保存中...' });

    // 使用 getImageInfo 获取图片信息
    wx.getImageInfo({
      src: qrImageUrl,
      success: (res) => {
        console.log('图片信息:', res);
        // 保存到相册
        wx.saveImageToPhotosAlbum({
          filePath: res.path,
          success: () => {
            wx.hideLoading();
            wx.showToast({
              title: '保存成功',
              icon: 'success'
            });
          },
          fail: (error) => {
            wx.hideLoading();
            console.error('保存二维码失败:', error);
            this.showError('保存失败');
          }
        });
      },
      fail: (error) => {
        wx.hideLoading();
        console.error('获取图片信息失败:', error);
        this.showError('图片获取失败');
      }
    });
  },

  /**
   * 记录分享行为
   */
  async recordShareAction(inviteCode, channel) {
    const { userId } = this.data;

    try {
      // 这里可以调用记录分享行为的接口
      console.log('记录分享行为:', { userId, inviteCode, channel });

    } catch (error) {
      console.error('记录分享行为失败:', error);
    }
  },

  /**
   * 关闭弹窗
   */
  onCloseModal() {
    this.setData({
      showCodeDetails: false,
      showCreateCode: false,
      showQRCode: false,
      selectedInviteCode: null,
      selectedCodeDetails: null,
      qrCodeInfo: null
    });
  },

  /**
   * 切换统计天数
   */
  onChangeStatisticsDays(e) {
    const days = parseInt(e.detail.value);
    this.setData({ statisticsDays: days });

    // 重新加载统计数据
    this.loadInviteStats().then(stats => {
      this.setData({ inviteStats: stats });
    }).catch(error => {
      console.error('重新加载统计数据失败:', error);
    });
  },

  /**
   * 查看用户详情
   */
  onViewUserDetails(e) {
    const { userId } = e.currentTarget.dataset;

    // 跳转到用户详情页面
    wx.navigateTo({
      url: `/pages/user-detail/user-detail?userId=${userId}`
    });
  }
});
