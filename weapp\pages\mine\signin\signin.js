const { request } = require('../../../utils/request.js');

Page({
  data: {
    // 用户信息
    userInfo: null,
    // 积分信息
    points: 0,
    // 签到信息
    signinInfo: {
      todaySigned: false,
      continuousDays: 0,
      totalDays: 0,
      lastSigninDate: null
    },
    // 签到奖励
    signinReward: 10,
    // 连续签到奖励配置（从后端获取）
    continuousRewards: [],
    // 本月签到记录
    monthSigninRecord: [], // 用接口数据替换假数据
    // 加载状态
    loading: false,
    signing: false,
    hasLoaded: false,
    // 页面状态标识
    isSharing: false,
    lastHideTime: 0,
    lastRefreshTime: 0
  },

  onLoad() {
    this.initPageData();
  },

  onShow() {
    // 页面显示时刷新数据（避免重复加载）
    if (this.data.hasLoaded && !this.data.isSharing) {
      // 检查是否是从分享界面返回
      const now = Date.now();
      const timeDiff = now - this.data.lastHideTime;

      // 只有在以下情况才刷新数据：
      // 1. 隐藏时间超过30秒（用户可能去了其他地方）
      // 2. 或者是跨天了（签到状态可能发生变化）
      const isLongTimeHidden = timeDiff > 30000;
      const lastDate = new Date(this.data.lastHideTime).toDateString();
      const currentDate = new Date().toDateString();
      const isCrossDay = lastDate !== currentDate;

      if (isLongTimeHidden || isCrossDay) {
        console.log('页面重新显示，需要刷新数据 - 长时间隐藏:', isLongTimeHidden, '跨天:', isCrossDay);
        this.refreshPageData();
      } else {
        console.log('短时间内重新显示，跳过数据刷新 - 隐藏时间:', timeDiff + 'ms');
      }
    } else if (this.data.isSharing) {
      console.log('分享状态中，跳过数据刷新');
    }
  },

  onHide() {
    // 记录页面隐藏时间
    this.setData({
      lastHideTime: Date.now()
    });
  },

  // 初始化页面数据
  async initPageData() {
    try {
      console.log('开始初始化页面数据...');
      this.setData({ loading: true });

      // 并行加载所有数据
      await Promise.all([
        this.loadUserInfo(),
        this.loadSigninInfo(),
        this.loadMonthSigninRecord()
      ]);

      console.log('页面数据初始化完成');
      this.setData({
        loading: false,
        hasLoaded: true
      });
    } catch (error) {
      console.error('初始化页面数据失败:', error);
      this.setData({ loading: false });
    }
  },

  // 刷新页面数据（仅刷新必要的数据）
  async refreshPageData() {
    try {
      const now = Date.now();
      // 防抖：如果距离上次刷新时间小于10秒，跳过刷新
      if (now - this.data.lastRefreshTime < 10000) {
        console.log('刷新过于频繁，跳过本次刷新');
        return;
      }

      console.log('开始刷新页面数据...');
      this.setData({ lastRefreshTime: now });

      // 只刷新签到相关数据
      await Promise.all([
        this.loadSigninInfo(),
        this.loadMonthSigninRecord()
      ]);
      console.log('页面数据刷新完成');
    } catch (error) {
      console.error('刷新页面数据失败:', error);
    }
  },

  // 获取默认连续签到奖励配置
  getDefaultContinuousRewards() {
    return [
      { days: 1, reward: 10 },
      { days: 3, reward: 15 },
      { days: 7, reward: 25 },
      { days: 15, reward: 50 },
      { days: 30, reward: 100 }
    ];
  },

  // 加载用户信息
  async loadUserInfo() {
    try {
      const userInfo = wx.getStorageSync('userInfo');
      if (userInfo) {
        this.setData({ userInfo });
        // 加载用户积分
        await this.loadUserPoints();
      }
    } catch (error) {
      console.error('加载用户信息失败:', error);
    }
  },

  // 加载用户积分
  async loadUserPoints() {
    try {
      console.log('正在加载用户积分...');
      const result = await request({
        url: '/miniapp/points/info',
        method: 'GET'
      });

      if (result.code === 200) {
        this.setData({ points: result.data.points || 0 });
        console.log('用户积分加载完成:', result.data);
      }
    } catch (error) {
      console.error('加载积分失败:', error);
    }
  },

  // 加载签到信息
  async loadSigninInfo() {
    try {
      console.log('正在加载签到信息...');
      const result = await request({
        url: '/blade-chat/signin/info',
        method: 'GET'
      });
      if (result.code === 200 && result.data) {
        // 构建签到信息对象
        const signinInfo = {
          todaySigned: result.data.todaySigned || false,
          continuousDays: result.data.continuousDays || 0,
          totalDays: result.data.totalDays || 0,
          lastSigninDate: result.data.lastSigninDate || null
        };

        // 处理连续签到奖励数据
        let continuousRewards = [];
        if (result.data.continuousRewards && Array.isArray(result.data.continuousRewards) && result.data.continuousRewards.length > 0) {
          // 验证数据格式
          const isValidFormat = result.data.continuousRewards.every(item =>
            item && typeof item.days === 'number' && typeof item.reward === 'number'
          );

          if (isValidFormat) {
            // 按天数排序
            continuousRewards = result.data.continuousRewards.sort((a, b) => a.days - b.days);
            console.log('使用后端返回的连续签到奖励配置:', continuousRewards);
          } else {
            console.warn('后端返回的连续签到奖励数据格式不正确:', result.data.continuousRewards);
            continuousRewards = this.getDefaultContinuousRewards();
          }
        } else {
          // 如果后端没有返回或数据格式不正确，使用默认配置
          console.log('后端未返回连续签到奖励配置，使用默认配置');
          continuousRewards = this.getDefaultContinuousRewards();
        }

        this.setData({
          signinInfo: signinInfo,
          signinReward: result.data.signinReward || 10,
          continuousRewards: continuousRewards
        });
        console.log('签到信息加载完成:', signinInfo);
        console.log('后端返回的原始数据:', result.data);
        console.log('todaySigned 字段值:', result.data.todaySigned, '类型:', typeof result.data.todaySigned);
        console.log('最终使用的连续签到奖励配置:', continuousRewards);
        console.log('连续签到奖励配置数量:', continuousRewards.length);
      } else {
        console.error('获取签到信息失败:', result.msg);
        wx.showToast({
          title: result.msg || '获取签到信息失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('加载签到信息失败:', error);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    }
  },

  // 加载本月签到记录
  async loadMonthSigninRecord() {
    try {
      console.log('正在加载本月签到记录...');
      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth() + 1;
      const result = await request({
        url: '/blade-chat/signin/record',
        method: 'GET',
        data: { year, month }
      });
      if (result.data) {
        console.log('签到记录数据:', result.data);
        // 处理日历数据，确保包含完整的月份日历
        const calendarData = this.generateCalendarData(year, month, result.data);
        console.log('生成的日历数据:', calendarData);

        // 计算本月签到总数
        const monthSigninCount = result.data.filter(record =>
          record.isSigned === true && record.isCurrentMonth === true
        ).length;

        this.setData({
          monthSigninRecord: calendarData,
          'signinInfo.totalDays': monthSigninCount
        });
      } else {
        console.error('获取签到记录失败:', result.msg);
        // 生成空的日历数据
        const calendarData = this.generateCalendarData(year, month, []);
        this.setData({
          monthSigninRecord: calendarData,
          'signinInfo.totalDays': 0
        });
      }
    } catch (error) {
      console.error('加载签到记录失败:', error);
      // 生成空的日历数据
      const now = new Date();
      const calendarData = this.generateCalendarData(now.getFullYear(), now.getMonth() + 1, []);
      this.setData({ monthSigninRecord: calendarData });
    }
  },

  // 生成日历数据
  generateCalendarData(year, month, signinRecords) {
    const calendarData = [];
    const today = new Date();
    const currentYear = today.getFullYear();
    const currentMonth = today.getMonth() + 1;
    const currentDate = today.getDate();

    // 获取当月第一天和最后一天
    const firstDay = new Date(year, month - 1, 1);
    const lastDay = new Date(year, month, 0);
    const daysInMonth = lastDay.getDate();
    const startWeekDay = firstDay.getDay(); // 0-6，0是周日

    // 添加上月末尾的日期（填充）
    const prevMonth = month === 1 ? 12 : month - 1;
    const prevYear = month === 1 ? year - 1 : year;
    const prevMonthLastDay = new Date(prevYear, prevMonth, 0).getDate();

    for (let i = startWeekDay - 1; i >= 0; i--) {
      calendarData.push({
        date: `${prevYear}-${String(prevMonth).padStart(2, '0')}-${String(prevMonthLastDay - i).padStart(2, '0')}`,
        day: prevMonthLastDay - i,
        isCurrentMonth: false,
        isToday: false,
        isSigned: false
      });
    }

    // 添加当月日期
    for (let day = 1; day <= daysInMonth; day++) {
      const dateStr = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      const isToday = year === currentYear && month === currentMonth && day === currentDate;

      // 修复签到状态判断逻辑 - 严格匹配日期字符串
      const signedRecord = signinRecords.find(record => {
        return record.date === dateStr;
      });

      const isSigned = signedRecord ? signedRecord.isSigned === true : false;

      calendarData.push({
        date: dateStr,
        day: day,
        isCurrentMonth: true,
        isToday: isToday,
        isSigned: isSigned
      });
    }

    // 添加下月开头的日期（填充到42个格子）
    const nextMonth = month === 12 ? 1 : month + 1;
    const nextYear = month === 12 ? year + 1 : year;
    const remainingDays = 42 - calendarData.length;

    for (let day = 1; day <= remainingDays; day++) {
      calendarData.push({
        date: `${nextYear}-${String(nextMonth).padStart(2, '0')}-${String(day).padStart(2, '0')}`,
        day: day,
        isCurrentMonth: false,
        isToday: false,
        isSigned: false
      });
    }

    return calendarData;
  },

  // 执行签到
  async doSignin() {
    if (this.data.signing || this.data.signinInfo.todaySigned) {
      return;
    }

    this.setData({ signing: true });

    try {
      const result = await request({
        url: '/blade-chat/signin/do',
        method: 'POST'
      });

      if (result.code === 200 && result.data) {
        const reward = result.data.reward || 0;
        const continuousReward = result.data.continuousReward || 0;
        const continuousDays = result.data.continuousDays || 0;

        // 显示签到成功提示
        wx.showToast({
          title: `签到成功，获得${reward}积分！`,
          icon: 'success',
          duration: 2000
        });

        // 更新页面数据
        this.setData({
          'signinInfo.todaySigned': true,
          'signinInfo.continuousDays': continuousDays,
          'signinInfo.totalDays': result.data.totalDays || this.data.signinInfo.totalDays + 1,
          points: result.data.currentPoints || this.data.points + reward + continuousReward,
          signing: false
        });

        // 刷新签到相关数据
        await this.refreshPageData();
        await this.loadUserPoints();

        // 连续签到奖励弹窗
        if (continuousReward > 0) {
          setTimeout(() => {
            wx.showModal({
              title: '连续签到奖励',
              content: `恭喜您连续签到${continuousDays}天，额外获得${continuousReward}积分！`,
              showCancel: false,
              confirmText: '太棒了'
            });
          }, 2000);
        }
      } else {
        throw new Error(result.msg || '签到失败');
      }
    } catch (error) {
      console.error('签到失败:', error);
      this.setData({ signing: false });

      wx.showToast({
        title: error.message || '签到失败，请重试',
        icon: 'none'
      });
    }
  },

  // 查看签到明细
  viewSigninDetail() {
    // 获取已签到的记录
    const signedRecords = this.data.monthSigninRecord.filter(record =>
      record.isSigned && record.isCurrentMonth
    );

    if (signedRecords.length === 0) {
      wx.showToast({
        title: '本月暂无签到记录',
        icon: 'none'
      });
      return;
    }

    // 显示签到明细
    const detailText = signedRecords.map(record =>
      `${record.date} 已签到`
    ).join('\n');

    wx.showModal({
      title: '本月签到明细',
      content: detailText,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 查看积分商城
  viewPointsMall() {
    wx.navigateTo({
      url: '/pages/mine/points-mall/points-mall'
    });
  },

  // 分享获得积分
  onShareAppMessage() {
    console.log('触发分享到好友');
    // 标记正在分享，避免不必要的数据刷新
    this.setData({
      isSharing: true
    });

    // 延迟重置分享状态
    setTimeout(() => {
      this.setData({
        isSharing: false
      });
    }, 1000);

    return {
      title: '每日签到领积分，快来和我一起打卡吧！',
      path: '/pages/mine/signin/signin',
      imageUrl: '/assets/images/share/signin-share.png'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    console.log('触发分享到朋友圈');
    // 标记正在分享，避免不必要的数据刷新
    this.setData({
      isSharing: true
    });

    // 延迟重置分享状态
    setTimeout(() => {
      this.setData({
        isSharing: false
      });
    }, 1000);

    return {
      title: '每日签到领积分，快来和我一起打卡吧！',
      imageUrl: '/assets/images/share/signin-share.png'
    };
  },

  /**
   * 查看签到明细
   */
  viewSigninDetail() {
    wx.navigateTo({
      url: '/pages/mine/signin-records/signin-records',
      success: () => {
        console.log('跳转到签到记录页面成功');
      },
      fail: (error) => {
        console.error('跳转到签到记录页面失败:', error);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  }
});