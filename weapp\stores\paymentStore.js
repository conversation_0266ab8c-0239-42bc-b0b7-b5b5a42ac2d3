/**
 * 支付数据存储管理
 * 处理支付相关的数据请求和本地存储
 */

const { request } = require('../utils/request');
const PaymentService = require('../services/paymentService');
// 使用生产配置
const { payConfig } = require('../config/payConfigProduction');

class PaymentStore {
  constructor() {
    this.paymentService = new PaymentService();
  }

  /**
   * 创建支付订单
   * @param {Object} orderData 订单数据
   * @returns {Promise} 支付结果
   */
  async createPayment(orderData) {
    try {
      const {
        businessType,
        businessId,
        amount,
        description,
        paymentMethod = 'wechat'
      } = orderData;

      // 验证必要参数
      if (!businessType || !businessId || !amount || !description) {
        throw new Error('缺少必要的订单参数');
      }

      // 获取用户openid
      const openid = wx.getStorageSync('openId');
      if (!openid && paymentMethod === 'wechat') {
        throw new Error('未获取到用户openid，请重新登录');
      }

      // 构建订单数据
      const completeOrderData = {
        ...orderData,
        openid: openid,
        paymentMethod: paymentMethod
      };

      // 根据支付方式处理
      let result;
      switch (paymentMethod) {
        case 'wechat':
          result = await this.paymentService.createPaymentOrder(completeOrderData);
          break;
        case 'alipay':
          result = await this.createAlipayOrder(completeOrderData);
          break;
        case 'balance':
          result = await this.createBalancePayment(completeOrderData);
          break;
        default:
          throw new Error(`不支持的支付方式: ${paymentMethod}`);
      }

      if (result.success) {
        // 缓存订单信息
        this.cacheOrderInfo(result.data.orderNo, completeOrderData);
      }

      return result;
    } catch (error) {
      console.error('创建支付订单失败:', error);
      return {
        success: false,
        message: error.message || '创建订单失败'
      };
    }
  }

  /**
   * 发起微信支付
   * @param {Object} payParams 支付参数
   * @returns {Promise} 支付结果
   */
  async requestWechatPayment(payParams) {
    return new Promise((resolve) => {
      console.log('发起微信支付，参数:', payParams);

      // 验证必要的支付参数
      const requiredParams = ['timeStamp', 'nonceStr', 'package', 'signType'];
      const missingParams = requiredParams.filter(param => !payParams[param]);

      if (missingParams.length > 0) {
        console.error('缺少必要的支付参数:', missingParams);
        resolve({
          success: false,
          message: `缺少支付参数: ${missingParams.join(', ')}`,
          error: { missingParams }
        });
        return;
      }

      // 构建微信支付参数
      const wechatPayParams = {
        timeStamp: payParams.timeStamp,
        nonceStr: payParams.nonceStr,
        package: payParams.package,
        signType: payParams.signType,
        paySign: payParams.paySign || payParams.sign // 支持两种签名字段名
      };

      // 检查是否有签名
      if (!wechatPayParams.paySign) {
        console.error('缺少支付签名 (paySign)');
        resolve({
          success: false,
          message: '支付参数不完整，缺少签名',
          error: { message: '缺少paySign参数' }
        });
        return;
      }

      console.log('调用wx.requestPayment，参数:', wechatPayParams);

      wx.requestPayment({
        ...wechatPayParams,
        success: (res) => {
          console.log('微信支付成功:', res);
          resolve({
            success: true,
            message: '支付成功'
          });
        },
        fail: (err) => {
          console.error('微信支付失败:', err);
          let message = '支付失败';

          if (err.errMsg) {
            if (err.errMsg.includes('cancel')) {
              message = '用户取消支付';
            } else if (err.errMsg.includes('fail')) {
              message = '支付失败，请重试';
            } else if (err.errMsg.includes('param')) {
              message = '支付参数错误';
            }
          }

          resolve({
            success: false,
            message: message,
            error: err
          });
        }
      });
    });
  }

  /**
   * 创建支付宝订单
   * @param {Object} orderData 订单数据
   * @returns {Promise} 支付结果
   */
  async createAlipayOrder(orderData) {
    try {
      const response = await request({
        url: '/api/payment/alipay/create',
        method: 'POST',
        data: orderData
      });

      if (response.code === 200) {
        return {
          success: true,
          data: response.data,
          message: '支付宝订单创建成功'
        };
      } else {
        throw new Error(response.message || '创建支付宝订单失败');
      }
    } catch (error) {
      return {
        success: false,
        message: error.message || '创建支付宝订单失败'
      };
    }
  }

  /**
   * 创建余额支付
   * @param {Object} orderData 订单数据
   * @returns {Promise} 支付结果
   */
  async createBalancePayment(orderData) {
    try {
      const response = await request({
        url: '/api/payment/balance/pay',
        method: 'POST',
        data: orderData
      });

      if (response.code === 200) {
        return {
          success: true,
          data: response.data,
          message: '余额支付成功'
        };
      } else {
        throw new Error(response.message || '余额支付失败');
      }
    } catch (error) {
      return {
        success: false,
        message: error.message || '余额支付失败'
      };
    }
  }

  /**
   * 查询订单状态
   * @param {string} orderNo 订单号
   * @returns {Promise} 查询结果
   */
  async queryOrderStatus(orderNo) {
    try {
      const response = await request({
        url: `/api/payment/orders/${orderNo}`,
        method: 'GET'
      });

      if (response.code === 200) {
        return {
          success: true,
          data: response.data
        };
      } else {
        throw new Error(response.message || '查询订单失败');
      }
    } catch (error) {
      return {
        success: false,
        message: error.message || '查询订单失败'
      };
    }
  }

  /**
   * 获取用户订单列表
   * @param {Object} params 查询参数
   * @returns {Promise} 订单列表
   */
  async getUserOrders(params = {}) {
    try {
      const queryParams = {
        page: params.page || 1,
        pageSize: params.pageSize || 10,
        status: params.status || '',
        businessType: params.businessType || ''
      };

      const response = await request({
        url: '/api/payment/orders',
        method: 'GET',
        data: queryParams
      });

      if (response.code === 200) {
        return {
          success: true,
          data: response.data
        };
      } else {
        throw new Error(response.message || '获取订单列表失败');
      }
    } catch (error) {
      return {
        success: false,
        message: error.message || '获取订单列表失败'
      };
    }
  }

  /**
   * 申请退款
   * @param {Object} refundData 退款数据
   * @returns {Promise} 退款结果
   */
  async requestRefund(refundData) {
    try {
      const response = await request({
        url: '/api/payment/refund',
        method: 'POST',
        data: refundData
      });

      if (response.code === 200) {
        return {
          success: true,
          data: response.data,
          message: '退款申请提交成功'
        };
      } else {
        throw new Error(response.message || '退款申请失败');
      }
    } catch (error) {
      return {
        success: false,
        message: error.message || '退款申请失败'
      };
    }
  }

  /**
   * 获取支付方式列表
   * @returns {Array} 支付方式列表
   */
  getPaymentMethods() {
    return [
      {
        id: 'wechat',
        name: '微信支付',
        icon: '/images/payment/wechat.png',
        enabled: true,
        description: '使用微信支付'
      },
      {
        id: 'alipay',
        name: '支付宝',
        icon: '/images/payment/alipay.png',
        enabled: false, // 小程序中通常不支持支付宝
        description: '使用支付宝支付'
      },
      {
        id: 'balance',
        name: '余额支付',
        icon: '/images/payment/balance.png',
        enabled: true,
        description: '使用账户余额支付'
      }
    ];
  }

  /**
   * 获取业务类型配置
   * @returns {Object} 业务类型配置
   */
  getBusinessTypeConfig() {
    return {
      post_promotion: {
        name: '帖子推广',
        description: '提升帖子曝光度',
        icon: '/images/business/promotion.png'
      },
      vip_upgrade: {
        name: 'VIP升级',
        description: '升级为VIP用户',
        icon: '/images/business/vip.png'
      },
      reward: {
        name: '打赏',
        description: '给作者打赏',
        icon: '/images/business/reward.png'
      },
      service_fee: {
        name: '服务费',
        description: '平台服务费用',
        icon: '/images/business/service.png'
      }
    };
  }

  /**
   * 缓存订单信息
   * @param {string} orderNo 订单号
   * @param {Object} orderData 订单数据
   */
  cacheOrderInfo(orderNo, orderData) {
    try {
      const cacheKey = `order_${orderNo}`;
      const cacheData = {
        ...orderData,
        cachedAt: Date.now()
      };
      wx.setStorageSync(cacheKey, cacheData);
    } catch (error) {
      console.error('缓存订单信息失败:', error);
    }
  }

  /**
   * 获取缓存的订单信息
   * @param {string} orderNo 订单号
   * @returns {Object|null} 订单信息
   */
  getCachedOrderInfo(orderNo) {
    try {
      const cacheKey = `order_${orderNo}`;
      return wx.getStorageSync(cacheKey) || null;
    } catch (error) {
      console.error('获取缓存订单信息失败:', error);
      return null;
    }
  }

  /**
   * 清除订单缓存
   * @param {string} orderNo 订单号
   */
  clearOrderCache(orderNo) {
    try {
      const cacheKey = `order_${orderNo}`;
      wx.removeStorageSync(cacheKey);
    } catch (error) {
      console.error('清除订单缓存失败:', error);
    }
  }

  /**
   * 格式化订单金额
   * @param {number} amount 金额（分）
   * @returns {string} 格式化后的金额
   */
  formatAmount(amount) {
    return (amount / 100).toFixed(2);
  }

  /**
   * 格式化订单状态
   * @param {string} status 订单状态
   * @returns {Object} 格式化后的状态信息
   */
  formatOrderStatus(status) {
    const statusMap = {
      PENDING: { text: '待支付', color: '#ff9500' },
      PAID: { text: '已支付', color: '#34c759' },
      CANCELLED: { text: '已取消', color: '#8e8e93' },
      REFUNDED: { text: '已退款', color: '#007aff' },
      EXPIRED: { text: '已过期', color: '#ff3b30' }
    };

    return statusMap[status] || { text: '未知状态', color: '#8e8e93' };
  }

  /**
   * 验证支付环境
   * @returns {boolean} 是否支持支付
   */
  validatePaymentEnvironment() {
    // 检查是否在微信小程序环境
    if (typeof wx === 'undefined') {
      return false;
    }

    // 检查是否支持支付API
    if (!wx.requestPayment) {
      return false;
    }

    return true;
  }
}

// 创建单例
const paymentStore = new PaymentStore();

module.exports = paymentStore;
