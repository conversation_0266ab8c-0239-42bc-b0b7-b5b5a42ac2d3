<view class="chat-container">
  <!-- 顶部导航 -->
  <!-- <view class="chat-header">
    <view class="chat-header-back"></view>
    <text class="chat-header-title">徐海震</text>
    <view class="chat-header-menu"></view>
  </view> -->

  <!-- 聊天内容区 -->
  <scroll-view class="chat-body" scroll-y="true">
    <block wx:for="{{messages}}" wx:key="id">
      <view class="chat-msg-row {{item.isSelf ? 'self' : ''}}">
        <image class="chat-avatar" src="{{item.avatar}}" />
        <view class="chat-bubble {{item.isSelf ? 'self' : ''}}">
          <text>{{item.text}}</text>
        </view>
      </view>
    </block>
  </scroll-view>

  <!-- 底部输入区 -->
  <view class="chat-footer">
    <view class="chat-footer-left">
      <image class="chat-footer-icon" src="/assets/images/message/emoji.png" />
    </view>
    <input class="chat-input" placeholder="请输入消息..." />
    <view class="chat-footer-right">
      <view class="chat-plus-btn" bindtap="onPlusTap">+</view>
    </view>
  </view>

  <!-- 弹窗 -->
  <view class="chat-popup-mask" wx:if="{{showPopup}}" bindtap="onPopupMask">
    <view class="chat-footer">
      <view class="chat-footer-left">
        <image class="chat-footer-icon" src="/assets/images/message/emoji.png" />
      </view>
      <input class="chat-input" placeholder="请输入消息..." />
      <view class="chat-footer-right">
        <view class="chat-plus-btn" bindtap="onPlusTap">+</view>
      </view>
    </view>
    <view class="chat-popup" bindtap="stopBubble">
      <view class="chat-popup-row">
        <view class="chat-popup-item">
          <image class="chat-popup-icon" src="/assets/images/message/image.png" />
          <text class="chat-popup-label">图片</text>
        </view>
        <view class="chat-popup-item">
          <image class="chat-popup-icon" src="/assets/images/message/video.png" />
          <text class="chat-popup-label">视频</text>
        </view>
        <view class="chat-popup-item">
          <image class="chat-popup-icon" src="/assets/images/message/file.png" />
          <text class="chat-popup-label">文件</text>
        </view>
      </view>
      <view class="chat-popup-row">
        <view class="chat-popup-item">
          <image class="chat-popup-icon" src="/assets/images/message/location.png" />
          <text class="chat-popup-label">位置</text>
        </view>
        <view class="chat-popup-item">
          <image class="chat-popup-icon" src="/assets/images/message/contact.png" />
          <text class="chat-popup-label">联系人</text>
        </view>
        <view class="chat-popup-item">
          <image class="chat-popup-icon" src="/assets/images/message/favorite.png" />
          <text class="chat-popup-label">收藏</text>
        </view>
      </view>
    </view>
  </view>
</view>