// mine.js
const { getUserInfo, logout, request } = require('../../utils/auth');
const { getQuickAccessIndex } = require('../../stores/indexStore');
const favoriteService = require('../../services/favoriteService');
const DEFAULT_IMAGES = {
  '寻物启事': '/assets/images/categoryDefaults/xunwu.png',
  '房屋出租': '/assets/images/categoryDefaults/fangwu.png',
  '本地招聘': '/assets/images/categoryDefaults/zhaopin.png',
  '教育培训': '/assets/images/categoryDefaults/jiaoyu.png',
  '二手交易': '/assets/images/categoryDefaults/ershou.png',
  '生活服务': '/assets/images/categoryDefaults/shenghuo.png',
  '顺风拼车': '/assets/images/categoryDefaults/pinche.png',
  '便民信息': '/assets/images/categoryDefaults/bianmin.png',
  '房产出售': '/assets/images/categoryDefaults/fangchan.png',
  '求职信息': '/assets/images/categoryDefaults/qiuzhi.png'
};

Page({
  data: {
    userInfo: {},
    hasUserInfo: false,
    canIUseGetUserProfile: false,
    stats: {
      points: 0,  // 积分
      weekPostCount: 0, // 本周发布数
      posts: 0,    // 发布数
      favorites: 0, // 收藏数
      walletBalance: 0, // 钱包余额
      exposureGrowth: 0, // 曝光增长率
      taskProgress: 0, // 任务进度(百分比)
      remainingTasks: 0  // 剩余任务数
    },
    posts: [],
    userBadges: ['活跃发布者', '社区达人'],
    // 新增layout相关数据
    navBarHeight: 88, // 默认导航栏高度
    menuButtonHeight: 0,
    showTabbar: true,
    lastScrollTop: 0,
    isRefreshing: false,
    menus: [], // 新增菜单数据
    favoriteTags: [], // 收藏标签数据
    favoritePosts: [], // 收藏帖子数据（前两个）
    // 开发模式标志
    isDevelopment: true
  },

  // 测试用
  goToUserDetail() {
    wx.navigateTo({
      url: '/pages/mine/signin/signin'
    })
  },

  onLoad: function() {
    console.log('=== 我的页面加载 ===');
    // 判断是否可以使用 getUserProfile
    if (wx.getUserProfile) {
      this.setData({
        canIUseGetUserProfile: true
      });
    }
    this.checkLogin();
    this.loadMenus(); // 新增：加载菜单
    this.loadFavoriteTags(); // 新增：加载收藏标签
    this.loadFavoritePosts(); // 新增：加载收藏帖子

    // 获取custom-nav组件实例
    this.customNav = this.selectComponent('#custom-nav');

    console.log('开发模式:', this.data.isDevelopment);
  },

  // 新增：加载菜单方法
  loadMenus: async function() {
    try {
      const menus = await getQuickAccessIndex();
      this.setData({ menus });
      console.log(this.data.menus)
    } catch (e) {
      this.setData({ menus: [] });
    }
  },

  // 新增：加载收藏标签方法
  loadFavoriteTags: async function() {
    try {
      const tags = await favoriteService.getFavoriteTags();
      const formattedTags = favoriteService.formatFavoriteTags(tags);
      this.setData({ favoriteTags: formattedTags });
      console.log('收藏标签:', formattedTags);
    } catch (error) {
      console.error('加载收藏标签失败:', error);
      this.setData({ favoriteTags: [] });
    }
  },

  // 新增：加载收藏帖子方法（只加载前两个）
  loadFavoritePosts: async function() {
    try {
      const result = await favoriteService.getFavoritePosts({
        current: 1,
        size: 2 // 只获取前两个
      });

      if (result.success && result.data && result.data.length > 0) {
        let processedPosts = this.processFavoritePostsData(result.data);
        processedPosts = this.processPostImages(processedPosts);
        this.setData({ favoritePosts: processedPosts });
        console.log('收藏帖子:', processedPosts);
      } else {
        console.log('没有收藏帖子数据');
        // 在开发模式下显示测试数据
        if (this.data.isDevelopment) {
          let testPosts = this.getTestFavoritePosts();
          testPosts = this.processPostImages(testPosts);
          this.setData({ favoritePosts: testPosts });
        } else {
          this.setData({ favoritePosts: [] });
        }
      }
    } catch (error) {
      console.error('加载收藏帖子失败:', error);
      // 在开发模式下显示测试数据
      if (this.data.isDevelopment) {
        let testPosts = this.getTestFavoritePosts();
        testPosts = this.processPostImages(testPosts);
        this.setData({ favoritePosts: testPosts });
      } else {
        this.setData({ favoritePosts: [] });
      }
    }
  },

  // 获取测试收藏帖子数据
  getTestFavoritePosts: function() {
    return [];
  },

  // 处理收藏帖子数据
  processFavoritePostsData: function(rawPosts) {
    return rawPosts.map(post => ({
      id: post.id,
      title: post.category?.name || post.categoryName || '无标题',
      content: post.content || '',
      images: post.images ? (Array.isArray(post.images) ? post.images : post.images.split(',').filter(img => img.trim())) : [],
      category: post.category || { name: post.categoryName || '其他' },
      stats: {
        viewCount: post.stats?.viewCount || post.viewCount || 0,
        likeCount: post.stats?.likeCount || post.likeCount || 0,
        favoriteCount: post.stats?.favoriteCount || post.favoriteCount || 0
      },
      createTime: post.createTime,
      publishTime: post.publishTime
    }));
  },

  // 处理帖子图片，若无图片则按分类设置默认图片
  processPostImages: function(posts) {
    return posts.map(post => {
      let images = [];
      if (post.images) {
        if (Array.isArray(post.images)) {
          images = post.images.filter(img => !!img);
        } else if (typeof post.images === 'string') {
          images = post.images.split(',').map(img => img.trim()).filter(Boolean);
        }
      }
      if (!images || images.length === 0) {
        const categoryName = (post.category && post.category.name) || post.categoryName;
        images = [DEFAULT_IMAGES[categoryName] || '/assets/images/defaults/default.png'];
      }
      return {
        ...post,
        images
      };
    });
  },

  // 导航栏准备完成事件
  onNavReady(e) {
    const navHeight = e.detail.height;
    const menuButtonHeight = e.detail.menuButtonHeight;
    console.log('导航栏信息:', e.detail);
    this.setData({
      navBarHeight: navHeight,
      menuButtonHeight: menuButtonHeight
    });
    console.log('导航栏高度:', navHeight);
  },

  // 滚动事件处理
  onScroll(e) {
    const scrollTop = e.detail.scrollTop;
    // 控制tabbar显隐
    if (scrollTop > this.data.lastScrollTop + 10) {
      this.setData({ showTabbar: false });
    } else if (scrollTop < this.data.lastScrollTop - 10) {
      this.setData({ showTabbar: true });
    }
    this.data.lastScrollTop = scrollTop;

    // 关键：同步传递给custom-nav组件
    if (this.customNav && this.customNav.handleScroll) {
      this.customNav.handleScroll(scrollTop);
    }
  },

  onShow: function() {
    this.checkLogin();
    this.getUserStats();
    this.getMyPosts();
    // 刷新收藏数据
    this.loadFavoritePosts();
  },

  // 检查登录状态
  checkLogin() {
    const userInfo = getUserInfo();
    console.log('检查 用户登录状态 userInfo', userInfo);
    if (userInfo) {
      this.setData({
        userInfo,
        hasUserInfo: true
      });
      // 登录后重新加载收藏标签
      this.loadFavoriteTags();
    } else {
      this.setData({
        userInfo: {},
        hasUserInfo: false,
        favoriteTags: [] // 清空收藏标签
      });
    }
  },

  // 去登录
  goLogin: function() {
    if (this.data.canIUseGetUserProfile) {
      // 跳转登录页面
      wx.navigateTo({
        url: '/pages/login/login'
      });
    } else {
      // 兼容处理
      wx.showToast({
        title: '请更新微信版本',
        icon: 'none'
      });
    }
  },

  // 检查登录并跳转
  checkLoginAndNavigate: function(url) {
    if (!this.data.hasUserInfo) {
      console.log('未登录，跳转到登录页面');
      wx.navigateTo({
        url: '/pages/login/login'
      });
      return false;
    }
    console.log('登录，跳转到', url);
    wx.navigateTo({ url });
    return true;
  },

  // 编辑资料
  editProfile: function() {
    // 跳转到我的资料页面
    console.log('编辑资料');
    this.checkLoginAndNavigate('/pages/mine/profile/index');
  },

  // 设置
  goSettings: function() {
    this.checkLoginAndNavigate('/pages/mine/profile/profile');
  },

  // 我的帖子
  goMyPosts: function() {
    this.checkLoginAndNavigate('/pages/mine/posts/posts');
  },

  // 我的点赞
  goMyLikes: function() {
    this.checkLoginAndNavigate('/pages/mine/likes/likes');
  },

  // 我的收藏
  goMyCollections: function() {
    this.checkLoginAndNavigate('/pkg_user/pages/collections/collections');
  },

  // 收藏标签点击事件
  onFavoriteTagTap: function(e) {
    const { index } = e.detail;
    const tag = this.data.favoriteTags[index];

    if (tag) {
      console.log('点击收藏标签:', tag.text);
      // 跳转到收藏页面，并传递标签参数
      wx.navigateTo({
        url: `/pkg_user/pages/collections/collections?tag=${encodeURIComponent(tag.text)}`
      });
    }
  },

  // 帖子点击事件（统一处理）
  goToPostDetail: function(e) {
    let id;

    // 处理不同的事件来源
    if (e.detail && e.detail.id) {
      // 来自组件事件
      id = e.detail.id;
    } else if (e.currentTarget && e.currentTarget.dataset && e.currentTarget.dataset.id) {
      // 来自直接点击事件
      id = e.currentTarget.dataset.id;
    }

    if (id) {
      console.log('跳转到帖子详情:', id);
      wx.navigateTo({
        url: `/pages/post-detail/post-detail?id=${id}`
      });
    } else {
      console.warn('无法获取帖子ID');
    }
  },

  // 浏览记录
  goViewHistory: function() {
    this.checkLoginAndNavigate('/pages/mine/history/history');
  },

  // 拨号记录
  goCallHistory: function() {
    this.checkLoginAndNavigate('/pages/mine/history/call');
  },

  // 会员服务
  goMembership: function() {
    this.checkLoginAndNavigate('/pages/mine/vip/vip');
  },

  // 联系客服
  contactService: function() {
    wx.makePhoneCall({
      phoneNumber: '400-xxx-xxxx',
      success: () => {
        console.log('拨打客服电话成功');
      },
      fail: (error) => {
        console.error('拨打客服电话失败:', error);
        wx.showToast({
          title: '拨打电话失败',
          icon: 'none'
        });
      }
    });
  },

  // 意见反馈
  goFeedback: function() {
    wx.navigateTo({
      url: '/pages/mine/feedback/feedback'
    });
  },

  // 页面跳转
  navigateTo(e) {
    console.log('navigateTo', e);
    const url = e.detail.url;
    console.log('navigateTo url', url);
    if (!this.data.hasUserInfo && url !== '/pages/login/login') {
      wx.navigateTo({
        url: '/pages/login/login'
      });
      return;
    }
    console.log('navigateTo url', url);
    wx.navigateTo({ url });
  },

  // 处理退出登录
  handleLogout() {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            userInfo: {},
            hasUserInfo: false,
            stats: {
              points: 0,
              favorites: 0,
              posts: 0,
              weekPostCount: 0,
              exposureGrowth: 0,
              taskProgress: 0,
              remainingTasks: 0
            },
            posts: []
          });
          logout();
        }
      }
    });
  },

  // 获取用户统计数据
  getUserStats: async function() {
    if (!this.data.hasUserInfo) return;

    try {
      const res = await request({
        url: '/blade-chat/user/stats',
        method: 'GET'
      });

      if (res.code === 200) {
        this.setData({
          'stats.points': res.data.points || 0,
          'stats.favorites': res.data.favoriteCount || 0,
          'stats.posts': res.data.postCount || 0,
          'stats.weeklyPosts': res.data.weekPostCount || 0,
          'stats.walletBalance': res.data.walletBalance || 0,
          'stats.exposureGrowth': res.data.exposureGrowth || 0,
          'stats.taskProgress': res.data.taskProgress || 0,
          'stats.remainingTasks': res.data.remainingTasks || 0
        });
      }
    } catch (err) {
      console.error('获取用户统计数据失败：', err);
    }
  },

  // 获取我的发布列表
  getMyPosts: async function() {
    if (!this.data.hasUserInfo) return;

    try {
      const res = await request({
        url: '/blade-chat/post/my',
        method: 'GET',
        data: {
          current: 1,
          size: 2
        }
      });
      if (res.code === 200) {
        let posts = res.data.records || [];
        posts = this.processPostImages(posts);
        console.log('获取我的发布列表 posts', posts);
        this.setData({
          posts: posts || this.data.posts
        });
      }
    } catch (err) {
      console.error('获取我的发布列表失败：', err);
    }
  },

  // 编辑帖子
  editPost(e) {
    const postId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/publish/publish?id=${postId}&type=edit`
    });
  },

  // 删除帖子
  deletePost(e) {
    // 数据源是元素的 data-xxx 比如 data-id="123"
    // const postId = e.currentTarget.dataset.id;
    // 数据源是子组件通过 triggerEvent 传递的数据对象
    const postId = e.detail.id;
    console.log(postId)
    wx.showModal({
      title: '提示',
      content: '确定要删除这条发布吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            const result = await request({
              url: `/blade-chat/post/${postId}`,
              method: 'DELETE'
            });
            
            if (result.code === 200) {
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
              this.getMyPosts(); // 重新获取列表
            }
          } catch (err) {
            console.error('删除帖子失败：', err);
            wx.showToast({
              title: '删除失败',
              icon: 'error'
            });
          }
        }
      }
    });
  },

  goMyHistory() {
    wx.navigateTo({
      url: '/pages/mine/history/history'
    })
  },

  // 跳转到我的发布列表
  goToMyPosts() {
    wx.navigateTo({
      url: '/pages/mine/my-posts/index'
    });
  },

  /**
   * 钱包余额点击事件
   */
  onWalletTap() {
    console.log('点击钱包余额，跳转到充值页面');
    wx.navigateTo({
      url: '/pages/recharge/recharge'
    });
  },

  /**
   * 积分点击事件
   */
  onPointsTap() {
    console.log('点击积分');
    wx.navigateTo({
      url: '/pages/share-points/share-points'
    });
  }
})