Component({
  properties: {
    list: {
      type: Array,
      value: []
    },
    height: {
      type: Number,
      value: 280 // 默认高度 280rpx，更符合横向比例
    }
  },
  data: {},
  methods: {
    // 轮播图点击事件
    onBannerTap(e) {
      const banner = e.currentTarget.dataset.banner;
      console.log('Banner clicked:', banner);

      // 向父组件传递点击事件
      this.triggerEvent('bannertap', { banner });
    }
  }
});
