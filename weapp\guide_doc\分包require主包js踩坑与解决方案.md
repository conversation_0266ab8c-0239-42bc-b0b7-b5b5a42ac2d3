# 微信小程序分包 require 主包 js 踩坑与解决方案

## 问题描述
在分包页面或组件中 require 主包的 js 文件（如 utils/auth.js）时，出现如下错误：

```
Error: module 'pkg_common/utils/auth.js' is not defined, require args is '../../../utils/auth'
```

## 官方限制
- 微信小程序分包机制**不允许分包直接 require 主包或其他分包的 js 文件**。
- 只能 require 分包自身的 js 文件，或 node_modules 下的 npm 包。

> 参考：[微信小程序分包官方文档](https://developers.weixin.qq.com/miniprogram/dev/framework/subpackages/basic.html)

## 解决方法
1. **将主包的工具类 js 文件（如 utils/auth.js、utils/request.js、utils/util.js）复制到分包自己的 utils 目录下。**
2. **分包页面/组件 require 路径写为相对于分包根目录的相对路径**，如：
   ```js
   const auth = require('../../utils/auth.js');
   ```
3. **如有多个分包需要用到同一工具类，每个分包都复制一份。**

## 最佳实践
- 主包和分包都需要用到的工具类，建议分别维护一份，或用 npm 包方式管理。
- require 路径必须是相对分包根目录的相对路径。
- 如遇 "module not defined" 错误，优先检查 require 路径和分包结构。

## 示例代码

**目录结构：**
```
weapp/
├── utils/
│   └── auth.js
├── pkg_common/
│   ├── pages/
│   │   └── login/
│   │       └── login.js
│   └── utils/
│       └── auth.js  // ← 复制自主包
```

**分包页面 require：**
```js
// pkg_common/pages/login/login.js
const auth = require('../../utils/auth.js');
```

---

**踩坑总结：**
- 分包不能 require 主包 js 文件。
- 工具类要复制到分包 utils 下。
- require 路径要相对分包根目录。
- 多分包共用工具类建议用 npm 包或多份维护。 