# 帖子详情页面功能说明

## 功能概述

帖子详情页面是小程序的核心功能之一，用户可以通过点击首页的帖子卡片进入详情页面，查看完整的帖子信息并进行互动操作。

## 页面结构

### 1. 导航栏
- 自定义导航栏，支持返回功能
- 渐变背景，与首页保持一致的设计风格
- 固定在页面顶部

### 2. 内容区域
- 用户信息：头像、昵称、发布时间
- 帖子标题：突出显示
- 帖子内容：详细描述
- 地址信息：位置图标 + 地址文本
- 图片展示：支持多图展示，点击可预览
- 互动统计：浏览量、评论数、点赞数
- 联系信息：联系人、联系电话（如果有）

### 3. 底部操作栏
- 点赞功能
- 收藏功能
- 分享功能
- 联系商家按钮

## 主要功能

### 1. 数据加载
- 通过帖子ID从后端API获取详情数据
- 支持加载状态显示
- 错误处理和重试机制

### 2. 图片预览
- 点击图片可全屏预览
- 支持多图浏览
- 使用微信原生图片预览功能

### 3. 互动功能
- **点赞**：支持给帖子点赞
- **收藏**：支持收藏帖子
- **分享**：支持分享到微信或复制链接
- **联系**：支持联系商家

### 4. 分享功能
- 支持分享给微信好友
- 支持分享到朋友圈
- 自动生成分享标题和图片

## 技术实现

### 1. 页面文件结构
```
weapp/pages/post-detail/
├── post-detail.js      # 页面逻辑
├── post-detail.wxml    # 页面结构
├── post-detail.wxss    # 页面样式
└── post-detail.json    # 页面配置
```

### 2. 数据接口
```javascript
// 获取帖子详情
GET /blade-chat-open/post/detail/{id}
```

### 3. 数据格式
```javascript
{
  id: 1,
  avatar: "头像URL",
  niackname: "用户昵称",
  title: "帖子标题",
  time: "发布时间",
  description: "帖子内容",
  address: "地址信息",
  images: ["图片1", "图片2"],
  views: 浏览量,
  comments: 评论数,
  likes: 点赞数,
  tag: "分类标签",
  contactName: "联系人",
  contactPhone: "联系电话"
}
```

## 页面跳转

### 1. 从首页跳转
```javascript
// 在content-card组件中
onCardTap() {
  wx.navigateTo({
    url: `/pages/post-detail/post-detail?id=${this.data.item.id}`
  });
}
```

### 2. 参数传递
- 通过URL参数传递帖子ID
- 页面加载时自动获取详情数据

## 样式设计

### 1. 设计原则
- 保持与首页一致的设计风格
- 清晰的信息层级
- 良好的可读性
- 响应式设计

### 2. 主要样式特点
- 卡片式布局
- 圆角设计
- 阴影效果
- 渐变按钮
- 图标 + 文字的组合

### 3. 响应式适配
- 支持不同屏幕尺寸
- 自适应布局
- 安全区域适配

## 交互体验

### 1. 加载体验
- 显示加载动画
- 骨架屏效果（可扩展）
- 错误状态处理

### 2. 操作反馈
- 点击反馈
- 操作成功提示
- 错误提示

### 3. 手势支持
- 图片预览手势
- 滚动优化
- 返回手势

## 性能优化

### 1. 图片优化
- 懒加载（可扩展）
- 图片压缩
- 缓存机制

### 2. 数据缓存
- 页面数据缓存
- 图片缓存
- 减少重复请求

### 3. 内存管理
- 及时释放资源
- 避免内存泄漏
- 优化渲染性能

## 扩展功能

### 1. 评论功能
- 支持查看评论列表
- 支持发表评论
- 评论分页加载

### 2. 相关推荐
- 显示相关帖子
- 智能推荐算法
- 用户兴趣匹配

### 3. 举报功能
- 支持举报不当内容
- 举报原因选择
- 举报处理反馈

### 4. 编辑功能
- 支持编辑自己的帖子
- 草稿保存
- 版本历史

## 注意事项

### 1. 数据安全
- 敏感信息脱敏
- 权限控制
- 数据验证

### 2. 用户体验
- 加载速度优化
- 错误处理完善
- 操作流程简化

### 3. 兼容性
- 不同设备适配
- 微信版本兼容
- 网络环境适配

## 后续优化

### 1. 功能增强
- 添加评论系统
- 实现点赞动画
- 支持视频播放

### 2. 性能提升
- 实现虚拟滚动
- 优化图片加载
- 添加预加载机制

### 3. 用户体验
- 添加骨架屏
- 优化加载动画
- 增强交互反馈 