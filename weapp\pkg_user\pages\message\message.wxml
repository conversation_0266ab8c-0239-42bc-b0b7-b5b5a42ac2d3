<!-- 消息通知页面设计稿实现 -->
<view class="msg-container">
  <!-- 顶部Tab -->
  <view class="msg-tabs">
    <view class="msg-tab-item" wx:for="{{tabs}}" wx:key="type" bindtap="onTabDetail" data-type="{{item.type}}">
      <view class="msg-tab-icon-wrap">
        <image class="msg-tab-icon" src="{{item.icon}}" mode="aspectFit" />
        <view wx:if="{{item.hasDot}}" class="msg-tab-dot"></view>
      </view>
      <text class="msg-tab-label">{{item.label}}</text>
    </view>
  </view>

  <!-- 消息列表 -->
  <view class="msg-list">
    <block wx:for="{{msgList}}" wx:key="id">
      <view class="msg-chat-item" bindtap="toDetail">
        <image class="msg-chat-avatar" src="{{item.avatar}}" mode="aspectFill" />
        <view class="msg-chat-content-wrap">
          <view class="msg-chat-header">
            <text class="msg-chat-name">{{item.name}}</text>
            <text class="msg-chat-time">{{item.time}}</text>
          </view>
          <text class="msg-chat-content">{{item.content}}</text>
        </view>
      </view>
    </block>
  </view>
</view> 