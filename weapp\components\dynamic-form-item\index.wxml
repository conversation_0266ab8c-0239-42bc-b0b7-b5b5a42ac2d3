<block wx:for="{{items}}" wx:key="id">
  <!-- 卡片类型 -->
  <view wx:if="{{item.type === 'card'}}" class="form-card">
    <view class="form-card-title section-title">{{item.label}}</view>
    <view class="form-card-body">
      <dynamic-form-item items="{{item.children}}" dynamicFormData="{{dynamicFormData}}" bind:dynamicinput="onDynamicInput"/>
    </view>
  </view>
  <!-- 数字输入 -->
  <view wx:elif="{{item.type === 'number'}}" class="form-item" style="margin-bottom: 24rpx;">
    <view class="section-title">{{item.label}}</view>
    <input class="contact-input"
      type="number"
      placeholder="{{item.componentProps.placeholder || '请输入'}}"
      value="{{dynamicFormData[item.field]}}"
      bindinput="onDynamicInput"
      data-field="{{item.field}}"/>
  </view>
  <!-- 普通输入框 -->
  <view wx:elif="{{item.type === 'input'}}" class="form-item" style="margin-bottom: 24rpx;">
    <view class="section-title">{{item.label}}</view>
    <input class="contact-input"
      type="text"
      placeholder="{{item.componentProps.placeholder || '请输入'}}"
      value="{{dynamicFormData[item.field]}}"
      bindinput="onDynamicInput"
      data-field="{{item.field}}"/>
  </view>
  <!-- 多行文本 -->
  <view wx:elif="{{item.type === 'textarea'}}" class="form-item" style="margin-bottom: 24rpx;">
    <view class="section-title">{{item.label}}</view>
    <textarea class="desc-input"
      placeholder="{{item.componentProps.placeholder || '请输入'}}"
      value="{{dynamicFormData[item.field]}}"
      bindinput="onDynamicInput"
      data-field="{{item.field}}"/>
  </view>
  <!-- 单选框 -->
  <view wx:elif="{{item.type === 'radio'}}" class="form-item" style="margin-bottom: 24rpx;">
    <view class="section-title">{{item.label}}</view>
    <view class="contact-type-group">
      <radio-group class="radio-group" bindchange="onDynamicInput" data-field="{{item.field}}" data-type="radio">
        <label class="radio-label" wx:for="{{item.componentProps.options}}" wx:for-item="opt" wx:key="value">
          <radio value="{{opt.value}}" checked="{{dynamicFormData[item.field] === opt.value}}"/>
          <text>{{opt.label}}</text>
        </label>
      </radio-group>
    </view>
  </view>
  <!-- 选择框 -->
  <view wx:elif="{{item.type === 'select'}}" class="form-item" style="margin-bottom: 24rpx;">
    <view class="section-title">{{item.label}}</view>
    <picker class="contact-input" mode="selector" range="{{item.componentProps.options}}" range-key="label" value="{{getSelectIndex(item)}}" bindchange="onDynamicInput" data-field="{{item.field}}" data-options="{{item.componentProps.options}}">
      <view style="line-height: 96rpx; min-height: 96rpx; color: #333;">{{dynamicFormData[item.field] || item.componentProps.placeholder || '请选择'}}</view>
    </picker>
  </view>
  <!-- 日期选择器 -->
  <view wx:elif="{{item.type === 'date'}}" class="form-item" style="margin-bottom: 24rpx;">
    <view class="section-title">{{item.label}}</view>
    <picker class="contact-input" mode="date" value="{{dynamicFormData[item.field]}}" bindchange="onDynamicInput" data-field="{{item.field}}">
      <view style="line-height: 96rpx; min-height: 96rpx; color: #333;">{{dynamicFormData[item.field] || item.componentProps.placeholder || '请选择'}}</view>
    </picker>
  </view>
  <!-- 开关 -->
  <view wx:elif="{{item.type === 'switch'}}" class="form-item" style="margin-bottom: 24rpx;">
    <view class="section-title">{{item.label}}</view>
    <switch checked="{{!!dynamicFormData[item.field]}}" bindchange="onDynamicInput" data-field="{{item.field}}" color="#FF7D7D"/>
  </view>
  <!-- 复选框 -->
  <view wx:elif="{{item.type === 'checkbox'}}" class="form-item" style="margin-bottom: 24rpx;">
    <view class="section-title">{{item.label}}</view>
    <checkbox-group bindchange="onDynamicInput" data-field="{{item.field}}" data-type="checkbox">
      <label class="radio-label" wx:for="{{item.componentProps.options}}" wx:for-item="opt" wx:key="value">
        <checkbox value="{{opt.value}}" checked="{{isChecked(item.field, opt.value)}}" color="#FF7D7D"/>
        <text>{{opt.label}}</text>
      </label>
    </checkbox-group>
  </view>
</block> 