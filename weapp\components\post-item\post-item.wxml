<view class="post-item" bindtap="onTapPost">
  <!-- 帖子图片 -->
  <view class="post-image-container">
    <image class="post-image"
           wx:if="{{imageList.length > 0}}"
           src="{{imageList[0]}}"
           mode="aspectFill"
           catchtap="previewImage"
           data-current="{{imageList[0]}}" />
    <view class="post-category">{{categoryName}}</view>
  </view>

  <!-- 帖子信息 -->
  <view class="post-info">
    <view class="post-content">{{post.content}}</view>
    <view class="post-meta">
      <view class="meta-item">
        <text class="meta-text">{{timeAgo || post.createTime}}</text>
      </view>
      <view class="meta-item">
        <image class="meta-icon" src="/assets/images/common/view.png" mode="aspectFit"/>
        <text class="meta-text">{{viewCount}}</text>
      </view>
      <view class="meta-item">
        <image class="meta-icon" src="/assets/images/common/like.png" mode="aspectFit"/>
        <text class="meta-text">{{favoriteCount}}</text>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="post-actions" catchtap="stopPropagation">
    <view class="action-btn more-btn" bindtap="onTapMore">
      ···
    </view>
  </view>
</view>