<view wx:if="{{visible}}" class="report-dialog-mask">
  <view class="report-dialog">
    <view class="dialog-header">
      <text class="dialog-title">举报帖子</text>
      <text class="dialog-subtitle">举报原因：{{reason}}</text>
    </view>
    
    <view class="dialog-content">
      <view class="input-section">
        <text class="input-label">举报内容 *</text>
        <textarea 
          class="report-textarea" 
          placeholder="请详细描述举报原因..." 
          value="{{content}}"
          bindinput="onContentInput"
          maxlength="500"
        />
        <text class="char-count">{{content.length}}/500</text>
      </view>
      
      <view class="image-section">
        <text class="input-label">上传图片（可选，最多3张）</text>
        <view class="image-list">
          <view 
            wx:for="{{images}}" 
            wx:key="index" 
            class="image-item"
          >
            <image src="{{item.path || item}}" mode="aspectFill" />
            <view class="remove-btn" data-index="{{index}}" bindtap="onImageRemove">×</view>
          </view>
          <view 
            wx:if="{{images.length < 3}}" 
            class="add-image-btn" 
            bindtap="onImageAdd"
          >
            <text class="add-icon">+</text>
            <text class="add-text">添加图片</text>
          </view>
        </view>
      </view>
    </view>
    
    <view class="dialog-actions">
      <button 
        class="btn-cancel" 
        bindtap="onCancelTap"
        disabled="{{loading}}"
      >
        取消
      </button>
      <button 
        class="btn-submit" 
        bindtap="onSubmitTap"
        disabled="{{loading}}"
      >
        {{loading ? '提交中...' : '提交举报'}}
      </button>
    </view>
  </view>
</view> 