// const BASE_URL = 'https://assistant.duofan.top'; // 替换为您的后端API地址
const BASE_URL = 'http://192.168.31.215'; // 替换为您的后端API地址

const { handleLoginExpired } = require('../../utils/loginHandler');

// 请求拦截器
const requestInterceptor = (config) => {
  const tokenData = wx.getStorageSync('token');
  const token = (typeof tokenData === 'object' && tokenData.value) ? tokenData.value : tokenData;
  const openId = wx.getStorageSync('openId');

  config.header = {
    ...config.header,
    'Tenant-Id' :"000000",
    'Authorization': `Basic d2VhcHA6c2FkZXF3ZXh6Y2Nhc2Rxd2U=`,
  };

  if (token) {
    config.header = {
      ...config.header,
      'Blade-Auth': `Bearer ${token}`
    };
  }

  // 添加OpenID头
  if (openId) {
    config.header = {
      ...config.header,
      'X-Open-ID': openId
    };
  }

  // 添加用户地区头
  try {
    const RegionManager = require('../../../utils/regionManager');
    const userRegionCode = RegionManager.getUserRegionCode();
    if (userRegionCode) {
      config.header = {
        ...config.header,
        'user-region': userRegionCode
      };
    }
  } catch (error) {
    console.error('获取用户地区信息失败:', error);
  }

  return config;
};

// 响应拦截器
const responseInterceptor = (response) => {
  const { data } = response;
  // 处理token过期
  if (data.code === 401) {
    handleLoginExpired();
    return Promise.reject(new Error('登录已过期，请重新登录'));
  }
  
  return data;
};

// 刷新token
const refreshToken = () => {
  return new Promise((resolve, reject) => {
    const tokenData = wx.getStorageSync('token');
    const refreshTokenValue = (typeof tokenData === 'object' && tokenData.refreshToken) ?
      tokenData.refreshToken : wx.getStorageSync('refreshToken');

    if (!refreshTokenValue) {
      reject(new Error('未找到刷新token'));
      return;
    }

    wx.request({
      url: `${BASE_URL}/blade-auth/token`,
      method: 'POST',
      data: {
        grantType: 'refresh_token',
        refreshToken: refreshTokenValue
      },
      success: (res) => {
        if (res.data.success) {
          const { accessToken, refreshToken } = res.data.data;
          // 保持与登录时相同的token存储格式
          wx.setStorageSync('token', {
            value: accessToken,
            datetime: Date.now(),
            refreshToken: refreshToken
          });
          resolve(accessToken);
        } else {
          reject(new Error(res.data.msg));
        }
      },
      fail: reject
    });
  });
};

// 统一请求方法
const request = (options) => {
  const config = requestInterceptor(options);
  return new Promise((resolve, reject) => {
    wx.request({
      ...config,
      url: `${BASE_URL}${config.url}`,
        success: (res) => {
        resolve(responseInterceptor(res));
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
};

module.exports = {
  request,
  refreshToken
}; 
