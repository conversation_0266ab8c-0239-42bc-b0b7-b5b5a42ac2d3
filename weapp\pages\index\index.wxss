/* 页面整体 */
page {
  height: 100vh;
  background: #f5f5f5;
  overflow: hidden; /* 禁止页面整体滚动 */
}

/* 滚动容器 */
.scroll-container {
  position: relative;
  background: #f5f5f5;
  box-sizing: border-box;
  height: 100vh;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none; /* Firefox */
}

.scroll-container::-webkit-scrollbar {
  display: none;
}

/* 页面容器 */
.container {
  background-color: #f5f6f7;
  min-height: 100vh;
}

/* 主要内容区域 */
.main-content {
  position: relative;
  z-index: 1;
  width: 100%;
  box-sizing: border-box;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 地址选择区域 */
.location {
  display: flex;
  align-items: center;
  color: #fff;
  font-size: 32rpx;
  margin-right: 20rpx;
  padding: 0 10rpx;
}

.arrow {
  font-size: 24rpx;
  margin-left: 4rpx;
}

/* 数据统计栏 */
.stats-bar {
  display: flex;
  justify-content: space-around;
  padding: 20rpx;
  background: #FF7C7C !important;
  border: none !important;
  box-shadow: none !important;
  margin: 0 !important;
  padding-top: 0 !important;
}

.stat-item {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #fff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.stat-item .icon {
  margin-right: 8rpx;
}

/* 顶部banner */
.top-banner {
  width: 100%;
  height: 280rpx;
  margin-bottom: 30rpx;
  position: relative;
}

/* 轮播图上方渐变 */
.top-banner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 80rpx;
  background: linear-gradient(to bottom, rgba(255, 133, 133, 0.6), transparent);
  pointer-events: none;
  z-index: 1;
}

/* 轮播图下方渐变 */
.top-banner::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: linear-gradient(to top, rgba(245, 245, 245, 0.9), transparent);
  pointer-events: none;
  z-index: 1;
}

/* 服务菜单网格 */
.grid-menu {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
  padding: 30rpx 20rpx;
  background: #fff;
  margin: 20rpx;
  border: 1px solid #f0f0f0;
  border-radius: 12rpx;
}

.grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 160rpx; /* 固定高度 */
  border: 1px solid #dcdcdc;
  border-radius: 8rpx;
  padding: 20rpx 10rpx;
  background: #fff;
}

.grid-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 16rpx;
}

.grid-text {
  font-size: 24rpx;
  color: #333;
  text-align: center;
}

/* 宣传栏目 */
.promo-section {
  margin: 20rpx;
  background: #fff;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

/* 标签栏 */
.tab-bar {
  display: flex;
  overflow-x: auto;
  margin: 0 -10rpx 20rpx;
  padding: 0 10rpx;
  white-space: nowrap;
  -webkit-overflow-scrolling: touch;
}

.tab-bar::-webkit-scrollbar {
  display: none;
}

.tab {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12rpx 32rpx;
  margin-right: 16rpx;
  font-size: 28rpx;
  color: #666;
  background: #f5f5f5;
  border-radius: 32rpx;
  transition: all 0.3s;
}

.tab.active {
  background: #ff6b6b;
  color: #fff;
  box-shadow: 0 4rpx 8rpx rgba(255, 107, 107, 0.2);
}

/* 内容卡片 */
.content-card {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
  padding: 24rpx;
}

/* 卡片头部 */
.card-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

/* 左侧区域 */
.header-left {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0; /* 允许内容压缩 */
}

.avatar {
  width: 88rpx;
  height: 88rpx;
  border-radius: 50%;
  margin-right: 16rpx;
  flex-shrink: 0; /* 防止头像被压缩 */
}

.shop-info {
  flex: 1;
  min-width: 0; /* 允许内容压缩 */
  margin-right: 20rpx; /* 与按钮保持间距 */
}

.shop-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.post-time {
  font-size: 28rpx;
  color: #999;
}

/* 优惠信息 */
.promotion-text {
  font-size: 32rpx;
  color: #333;
  margin: 24rpx 0;
  line-height: 1.5;
}

/* 地址信息 */
.shop-address {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 24rpx;
}

/* 图片区域 */
.food-images {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-bottom: 24rpx;
}

.food-images image {
  width: 100%;
  height: 320rpx;
  border-radius: 12rpx;
  object-fit: cover;
}

/* 底部互动栏 */
.interaction-bar {
  display: flex;
  align-items: center;
  margin-top: 8rpx;
}

.interaction-item {
  display: flex;
  align-items: center;
  margin-right: 40rpx;
  color: #999;
}

.interaction-item .icon {
  font-size: 32rpx;
  color: #999;
  margin-right: 8rpx;
}

.interaction-item text {
  font-size: 28rpx;
  color: #999;
}

/* 分享按钮样式 */
.interaction-item:nth-last-child(2) {
  margin-right: auto;
}

.interaction-item:nth-last-child(2) .icon {
  font-size: 36rpx;
  margin-right: 0;
}

.tag {
  padding: 8rpx 20rpx;
  font-size: 24rpx;
  color: #ff6b6b;
  background: rgba(255, 107, 107, 0.1);
  border-radius: 24rpx;
}

/* 底部导航栏 */
.tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: #fff;
  border-top: 1rpx solid #f5f5f5;
  display: flex;
  padding-bottom: env(safe-area-inset-bottom);
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.tab-item image {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 4rpx;
}

.tab-item text {
  font-size: 20rpx;
  color: #999;
}

.tab-item.active text {
  color: #ff6b6b;
}

/* banner和统计区域容器 */
.banner-stats-container {
  width: 100%;
  position: relative;
  z-index: 1;
}

swiper {
  width: 100%;
  height: 280rpx;
}

swiper-item image {
  width: 100%;
  height: 100%;
}

.card-section {
background-color: var(--card-background);
border-radius: 24rpx;
}


/* 分类tab */
.category-tabs-scroll {
  width: 100%;
  white-space: nowrap;
  margin-bottom: 24rpx;
}

.category-tabs {
  display: flex;
  padding: 0 8rpx;
}

.category-tabs.single-tab {
  justify-content: center;
}

.tab-item {
  padding: 10rpx 25rpx;
  font-size: 28rpx;
  color: #666;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  margin-right: 25rpx;
  white-space: nowrap;
  transition: all 0.3s ease;
  /* min-width: 150rpx; */
  text-align: center;
  flex-shrink: 0;
}

.tab-item.active {
  color: #fff;
  background-color: #ff6b6b;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
}




.posts-container {
  min-height: 200rpx;
}

.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 28rpx;
}

.no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx 0;
  margin: 0 24rpx;
  color: #999;
  font-size: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border: 1rpx solid #e9ecef;
}

/* 预加载状态样式 */
.loading-more.preloading {
  background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
  border: 1rpx solid #c3e6cb;
  color: #155724;
}

.loading-more.preloading::before {
  background: linear-gradient(90deg, transparent, rgba(21, 87, 36, 0.1), transparent);
}

/* 到达底部状态样式 */
.reached-bottom {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 24rpx 0;
  margin: 0 24rpx;
  color: #ff6b6b;
  font-size: 26rpx;
  font-weight: 500;
  background: linear-gradient(135deg, #fff0f0 0%, #ffe6e6 100%);
  border: 1rpx solid #ffebeb;
  border-radius: 20rpx;
  position: relative;
  overflow: hidden;
}

.reached-bottom::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 107, 107, 0.1), transparent);
  animation: celebration-shimmer 2s infinite;
}

@keyframes celebration-shimmer {
  0% { left: -100%; }
  50% { left: 100%; }
  100% { left: 100%; }
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
  color: #999;
  font-size: 28rpx;
}

.refresh-wave {
  width: 100%;
  height: 60rpx;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  background: transparent;
  margin-bottom: 10rpx;
}

.wave {
  position: absolute;
  left: 0;
  width: 200%;
  height: 60rpx;
  background: url('data:image/svg+xml;utf8,<svg width="100%25" height="100%25" viewBox="0 0 200 60" xmlns="http://www.w3.org/2000/svg"><path d="M0 30 Q50 60 100 30 T200 30 V60 H0 Z" fill="%23ff6b6b" /></svg>') repeat-x;
  background-size: 100% 100%;
  opacity: 0.7;
  animation: waveMove 2s linear infinite;
}

.wave1 {
  top: 0;
  opacity: 0.7;
  animation-delay: 0s;
}

.wave2 {
  top: 10rpx;
  opacity: 0.5;
  animation-delay: 0.5s;
}

.wave3 {
  top: 20rpx;
  opacity: 0.3;
  animation-delay: 1s;
}

@keyframes waveMove {
  0% { transform: translateX(0);}
  100% { transform: translateX(-50%);}
}

.refresh-text {
  position: relative;
  z-index: 2;
  color: #fff;
  font-size: 24rpx;
  margin-top: 40rpx;
  opacity: 0.85;
}

/* 下拉刷新圆环 loading 动画样式 */
.refresh-loading {
  width: 100%;
  height: 80rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  background: #ff6b6b; /* 主题色背景 */
  margin-bottom: 10rpx;
}

.circle-loader {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #fff;
  border-top: 4rpx solid #fff;
  border-right: 4rpx solid #fff;
  border-bottom: 4rpx solid #ff6b6b;
  border-left: 4rpx solid #ff6b6b;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 8rpx;
  box-shadow: 0 0 8rpx #ff6b6b33;
  background: transparent;
}

@keyframes spin {
  0% { transform: rotate(0deg);}
  100% { transform: rotate(360deg);}
}

.refresh-text {
  color: #fff;
  font-size: 24rpx;
  opacity: 0.85;
  text-shadow: 0 2rpx 8rpx #ff6b6b33;
}

.refresh-coins {
  width: 100%;
  height: 80rpx;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
  background: #ff6b6b;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  pointer-events: none; /* 不影响下方内容交互 */
}

.coin {
  position: absolute;
  top: -40rpx;
  animation: coinDrop 0.9s cubic-bezier(0.4,1.4,0.6,1) forwards;
  transform: rotate(var(--coin-rotate, 0deg));
}

@keyframes coinDrop {
  0% {
    opacity: 0;
    transform: translateY(-30rpx) scale(0.7) rotate(var(--coin-rotate, 0deg));
  }
  40% {
    opacity: 1;
    transform: translateY(40rpx) translateX(calc(var(--coin-swing, 0vw) * 0.5)) scale(1.1) rotate(calc(var(--coin-rotate, 0deg) * 0.7));
  }
  70% {
    transform: translateY(70rpx) translateX(var(--coin-swing, 0vw)) scale(0.95) rotate(calc(var(--coin-rotate, 0deg) * 0.3));
  }
  100% {
    opacity: 1;
    transform: translateY(80rpx) translateX(var(--coin-swing, 0vw)) scale(1) rotate(0deg);
  }
}

.refresh-text {
  color: #fff;
  font-size: 24rpx;
  opacity: 0.85;
  text-shadow: 0 2rpx 8rpx #ff6b6b33;
  position: relative;
  z-index: 2;
  margin-top: 40rpx;
}

.refresh-coins-fixed {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 160rpx;
  z-index: 9999;
  background: #FF7B7B;
  pointer-events: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
}

.refresh-bottom-area {
  width: 100vw;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  background: #FF7B7B;
  padding-top: 12rpx;
  padding-bottom: 18rpx;
  margin-bottom: 0;
}

.coin-tray {
  width: 80rpx;
  height: 18rpx;
  background: linear-gradient(90deg, #ffb86b 0%, #ffe066 100%);
  border-radius: 0 0 40rpx 40rpx / 0 0 18rpx 18rpx;
  box-shadow: 0 4rpx 12rpx #ffb86b55;
  margin-bottom: 0;
}

.refresh-text {
  color: #fff;
  font-size: 24rpx;
  opacity: 0.95;
  text-shadow: 0 2rpx 8rpx #ff6b6b33;
  margin-top: 2rpx;
  text-align: center;
}

.coin {
  position: absolute;
  top: 20rpx;
  animation: coinDropFixed 1.1s cubic-bezier(0.4,1.4,0.6,1) forwards;
  transform: rotate(var(--coin-rotate, 0deg));
}

@keyframes coinDropFixed {
  0% {
    opacity: 0;
    transform: translateY(-40rpx) scale(0.7) rotate(var(--coin-rotate, 0deg));
  }
  40% {
    opacity: 1;
    transform: translateY(60rpx) translateX(calc(var(--coin-swing, 0vw) * 0.5)) scale(1.1) rotate(calc(var(--coin-rotate, 0deg) * 0.7));
  }
  70% {
    transform: translateY(110rpx) translateX(var(--coin-swing, 0vw)) scale(0.95) rotate(calc(var(--coin-rotate, 0deg) * 0.3));
  }
  100% {
    opacity: 1;
    transform: translateY(120rpx) translateX(var(--coin-swing, 0vw)) scale(1) rotate(0deg);
  }
} 

.card-section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding: 0 12rpx;
  background: #fff;
}

.main-tabs {
  display: flex;
  align-items: center;
}

.tab-title-item {
  font-size: 32rpx;
  color: #999;
  margin-right: 40rpx;
  padding-bottom: 8rpx;
  position: relative;
  cursor: pointer;
  transition: color 0.2s;
}

.tab-title-item.active {
  color: #ff6b6b;
  font-weight: bold;
}

.tab-title-item.active::after {
  content: '';
  display: block;
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 6rpx;
  background: #ff6b6b;
  border-radius: 3rpx;
} 

/* 地图按钮样式 */
.map-btn {
  padding: 12rpx 24rpx;
  background: linear-gradient(135deg, #ff6b6b, #ff8585);
  color: #fff;
  font-size: 26rpx;
  font-weight: 500;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
  transition: all 0.3s ease;
}

.map-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.4);
}

.card-section-title#category-sticky {
  position: sticky;
  top: 0;
  z-index: 99;
  background: #fff;
}

.sticky-category-bar {
  position: fixed;
  left: 0;
  width: 100vw;
  z-index: 100; /* 保证低于custom-nav的z-index */
  background: #fff;
  /* box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.03); */
} 

.encourage-post {
  margin: 24rpx 0 0 0;
  text-align: center;
  color: #ff6b6b;
  font-size: 28rpx;
  font-weight: 500;
} 