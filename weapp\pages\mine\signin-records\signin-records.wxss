/* 签到记录页面样式 */
.signin-records-container {
  min-height: 100vh;
  background: #ffffff;
  padding-bottom: 40rpx;
}

/* 页面头部 */
.header {
  padding: 60rpx 40rpx 40rpx;
  text-align: center;
  color: #333;
  background: linear-gradient(135deg, #FF8383 0%, #FFB3B3 100%);
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  color: white;
}

.subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  color: white;
}

/* 统计汇总卡片 */
.summary-card {
  margin: 0 30rpx 30rpx;
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.summary-row {
  display: flex;
  justify-content: space-between;
}

.summary-item {
  text-align: center;
  flex: 1;
}

.summary-value {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.summary-label {
  font-size: 24rpx;
  color: #666;
}

/* 筛选栏 */
.filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 30rpx 20rpx;
  padding: 20rpx 30rpx;
  background: #f8f8f8;
  border-radius: 16rpx;
  border: 1rpx solid #eee;
}

.filter-item {
  display: flex;
  align-items: center;
  color: #FF8383;
  font-size: 28rpx;
}

.filter-text {
  margin-right: 8rpx;
}

/* 记录列表 */
.records-list {
  margin: 0 30rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 0;
  color: #999;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 131, 131, 0.3);
  border-top: 4rpx solid #FF8383;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-spinner.small {
  width: 40rpx;
  height: 40rpx;
  border-width: 3rpx;
}

.loading-text {
  font-size: 28rpx;
  opacity: 0.8;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx;
  color: #999;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  opacity: 0.8;
  margin-bottom: 40rpx;
}

.empty-button {
  background: linear-gradient(135deg, #FF8383, #FFB3B3);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 60rpx;
  font-size: 28rpx;
  box-shadow: 0 4rpx 16rpx rgba(255, 131, 131, 0.3);
}

/* 记录项 */
.record-item {
  display: flex;
  align-items: center;
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #f5f5f5;
}

.record-left {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
  min-width: 160rpx;
}

.record-date {
  text-align: left;
  margin-right: 20rpx;
  min-width: 80rpx;
}

.date-main {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 4rpx;
}

.date-time {
  font-size: 24rpx;
  color: #999;
}

.record-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #FF8383, #FFB3B3);
  box-shadow: 0 2rpx 8rpx rgba(255, 131, 131, 0.2);
}

.record-icon .iconfont {
  font-size: 32rpx;
  color: white;
}

.record-icon .iconfont.makeup {
  background: linear-gradient(135deg, #ffa726, #ff7043);
}

.record-center {
  flex: 1;
}

.record-title {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.signin-type {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-right: 16rpx;
}

.weekday {
  font-size: 24rpx;
  color: #666;
  background: #f5f5f5;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}

.record-desc {
  font-size: 24rpx;
  color: #999;
}

.record-right {
  text-align: right;
  min-width: 120rpx;
}

.points-main {
  font-size: 32rpx;
  font-weight: bold;
  color: #FF8383;
  margin-bottom: 8rpx;
}

.points-detail {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.base-points,
.reward-points {
  font-size: 20rpx;
  color: #999;
}

.reward-points {
  color: #ff9800;
}

/* 加载更多 */
.load-more {
  padding: 40rpx 0;
  text-align: center;
}

.load-more-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
}

.load-more-button {
  color: #FF8383;
  font-size: 28rpx;
  padding: 20rpx 40rpx;
  background: #f8f8f8;
  border-radius: 50rpx;
  border: 1rpx solid #FF8383;
}

.load-more-end {
  color: #ccc;
  font-size: 24rpx;
}

/* 日期选择器弹窗 */
.date-picker-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.date-picker-content {
  background: white;
  border-radius: 24rpx;
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.date-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx;
  border-bottom: 1rpx solid #eee;
}

.date-picker-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.date-picker-close {
  font-size: 48rpx;
  color: #999;
  line-height: 1;
}

.date-picker-body {
  padding: 40rpx;
}

.date-input-group {
  margin-bottom: 40rpx;
}

.date-input-item {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.date-label {
  width: 120rpx;
  font-size: 28rpx;
  color: #333;
}

.date-input {
  flex: 1;
  padding: 20rpx;
  background: #f5f5f5;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
}

.quick-select-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.quick-select-buttons {
  display: flex;
  gap: 20rpx;
}

.quick-button {
  flex: 1;
  background: #f5f5f5;
  color: #666;
  border: none;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 24rpx;
}

.date-picker-footer {
  display: flex;
  border-top: 1rpx solid #eee;
}

.date-picker-cancel,
.date-picker-confirm {
  flex: 1;
  padding: 30rpx;
  text-align: center;
  font-size: 32rpx;
  border: none;
  background: none;
}

.date-picker-cancel {
  color: #999;
  border-right: 1rpx solid #eee;
}

.date-picker-confirm {
  color: #FF8383;
  font-weight: bold;
}
