.contact-card-ui {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fafbfc;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.03);
  padding: 24rpx 28rpx;
  margin: 16rpx 0 24rpx 0;
}
.contact-left {
  display: flex;
  align-items: center;
}
.icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}
.phone {
  color: #FF8282;
  font-size: 30rpx;
  font-weight: 600;
  letter-spacing: 2rpx;
}
.call-btn-ui {
  width: 160rpx;
  background: #FF8282;
  color: #fff;
  border: none;
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: 600;
  padding: 12rpx 38rpx;
  box-shadow: 0 2rpx 8rpx rgba(37,116,255,0.08);
  margin-left: 24rpx;
}
.contact-title-wechat {
  color: #FF8282;
  font-size: 32rpx;
  font-weight: 600;
}
.wechat-icons {
  display: flex;
  gap: 16rpx;
  align-items: center;
}
.wechat-icon {
  width: 48rpx;
  height: 48rpx;
} 