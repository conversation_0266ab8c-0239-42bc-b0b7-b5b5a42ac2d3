<!-- 机构卡片组件 - 与主页风格一致 -->
<view class="content-card" bindtap="onTap">
  <!-- 卡片头部 -->
  <view class="card-header">
    <view class="header-left">
      <!-- 机构头像 -->
      <image
        class="avatar"
        src="{{institution.avatar || '/images/default-institution.png'}}"
        mode="aspectFill"
      />

      <!-- 机构信息 -->
      <view class="shop-info">
        <view class="shop-name">{{institution.name}}</view>
        <view class="shop-category">
          <text class="category-text">{{getCategoryName(institution.category)}}</text>
          <text class="rating" wx:if="{{institution.rating}}">⭐ {{institution.rating}}</text>
        </view>
      </view>
    </view>

    <!-- 距离标签 -->
    <view class="distance-tag">{{institution.distance}}</view>
  </view>

  <!-- 机构描述 -->
  <view class="promotion-text">{{institution.description}}</view>

  <!-- 地址信息 -->
  <view class="shop-address" wx:if="{{institution.address}}">
    📍 {{institution.address}}
  </view>

  <!-- 服务标签 -->
  <view class="service-tags" wx:if="{{institution.services && institution.services.length > 0}}">
    <view
      class="service-tag"
      wx:for="{{institution.services}}"
      wx:key="*this"
      wx:if="{{index < 3}}"
    >
      {{item}}
    </view>
    <view class="service-tag more" wx:if="{{institution.services.length > 3}}">
      +{{institution.services.length - 3}}
    </view>
  </view>

  <!-- 底部互动栏 -->
  <view class="interaction-bar">
    <view class="interaction-item" bindtap="onCallTap">
      <text class="icon">📞</text>
      <text>联系</text>
    </view>
    <view class="interaction-item">
      <text class="icon">👁️</text>
      <text>{{institution.viewCount || 0}}</text>
    </view>
    <view class="interaction-item" bindtap="onLikeTap">
      <text class="icon">👍</text>
      <text>{{institution.likeCount || 0}}</text>
    </view>
    <view class="interaction-item" bindtap="onNavigateTap">
      <text class="icon">📍</text>
      <text>导航</text>
    </view>
  </view>
</view>
