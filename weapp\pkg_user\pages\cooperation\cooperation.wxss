/* pkg_user/pages/cooperation/cooperation.wxss */

/* 使用layout组件，移除容器样式 */

/* 顶部介绍区域 */
.intro-section {
  background: linear-gradient(135deg, #FF8181, #ff8585);
  padding: 20rpx 30rpx 40rpx 30rpx;
  color: #fff;
  position: relative;
}

.intro-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to bottom, rgba(255, 107, 107, 0.8), rgba(255, 107, 107, 1));
}

/* 装饰元素 */
.intro-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 120rpx;
  height: 120rpx;
  top: 20rpx;
  right: 40rpx;
  animation-delay: 0s;
}

.circle-2 {
  width: 80rpx;
  height: 80rpx;
  top: 120rpx;
  left: 60rpx;
  animation-delay: 2s;
}

.circle-3 {
  width: 60rpx;
  height: 60rpx;
  bottom: 40rpx;
  right: 120rpx;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20rpx) rotate(180deg); }
}

.intro-header {
  text-align: center;
  margin-bottom: 32rpx;
  position: relative;
  z-index: 1;
}

.intro-icon {
  font-size: 60rpx;
  display: block;
  margin-bottom: 16rpx;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.intro-title {
  font-size: 40rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.intro-subtitle {
  font-size: 28rpx;
  opacity: 0.95;
  font-weight: 500;
}

.intro-content {
  text-align: center;
  position: relative;
  z-index: 1;
}

.intro-text {
  font-size: 26rpx;
  line-height: 1.6;
  opacity: 0.9;
  margin-bottom: 32rpx;
}

/* 特色亮点 */
.intro-highlights {
  display: flex;
  justify-content: center;
  gap: 40rpx;
  margin-top: 20rpx;
}

.highlight-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  opacity: 0.95;
}

.highlight-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
  display: block;
}

.highlight-text {
  font-size: 22rpx;
  font-weight: 500;
  text-align: center;
}

/* 表单滚动区域 */
.form-scroll {
  box-sizing: border-box;
  flex: 1;
  padding: 20rpx;
}

/* 表单区域 */
.form-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
  position: relative;
}

.section-title::before {
  content: '';
  position: absolute;
  left: -10rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 32rpx;
  background: #FF8181;
  border-radius: 3rpx;
}

/* 合作类型选择 */
.cooperation-types {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  margin-bottom: 30rpx;
}

.type-item {
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  padding: 24rpx;
  transition: all 0.3s ease;
}

.type-item.active {
  border-color: #FF8181;
  background: #fff5f5;
}

.type-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.type-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.type-check {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.type-check.checked {
  background: #FF8181;
  border-color: #FF8181;
}

.check-icon {
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
}

.type-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

/* 选中类型信息 */
.selected-type-info {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
  border-left: 6rpx solid #FF8181;
}

.info-header {
  margin-bottom: 12rpx;
}

.info-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #FF8181;
}

.info-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
}

.info-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.action-btn.secondary {
  background: #fff;
  border: 1rpx solid #FF8181;
  color: #FF8181;
}

.action-btn.secondary:active {
  background: #fff5f5;
}

/* 表单项 */
.form-item {
  margin-bottom: 30rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.form-input:focus {
  background: #fff;
  border: 2rpx solid #FF8181;
}

.form-textarea {
  width: 100%;
  min-height: 160rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  line-height: 1.5;
}

.form-textarea:focus {
  background: #fff;
  border: 2rpx solid #FF8181;
}

/* 输入框带按钮 */
.input-with-btn {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.flex-input {
  flex: 1;
}

.input-btn {
  padding: 0 24rpx;
  height: 80rpx;
  background: #FF8181;
  color: #fff;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  transition: all 0.3s ease;
}

.input-btn:active {
  background: #FF8181;
  transform: scale(0.95);
}

/* 文本计数器 */
.textarea-counter {
  display: flex;
  justify-content: flex-end;
  margin-top: 8rpx;
}

.counter-text {
  font-size: 22rpx;
  color: #999;
}

/* 温馨提示 */
.tips-section {
  background: #fff9e6;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border: 1rpx solid #ffe58f;
}

.tips-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.tips-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.tips-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #d48806;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.tips-text {
  font-size: 26rpx;
  color: #d48806;
  line-height: 1.4;
}

/* 底部提交区域 */
.submit-section {
  background: #fff;
  padding: 30rpx;
  border-top: 1rpx solid #eee;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #FF8181, #ff8585);
  color: #fff;
  font-size: 32rpx;
  font-weight: 600;
  border-radius: 44rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 107, 0.3);
  transition: all 0.3s ease;
}

.submit-btn:active {
  transform: scale(0.98);
}

.submit-btn.submitting {
  background: #ccc;
  box-shadow: none;
}

.submit-btn::after {
  display: none;
}

.submit-tips {
  text-align: center;
  margin-top: 16rpx;
}

.submit-tip-text {
  font-size: 22rpx;
  color: #999;
}
