<view class="feedback-container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-subtitle">您的反馈是我们改进的动力</text>
  </view>

  <!-- 反馈内容 -->
  <view class="feedback-section">
    <view class="section-title">
      <text class="title-text">反馈内容</text>
      <text class="required">*</text>
    </view>
    <view class="content-input-wrap">
      <textarea
        class="content-input"
        placeholder="{{replyMode ? '回复评论...' : '请详细描述您遇到的问题或建议，我们会认真处理您的反馈...'}}"
        placeholder-style="color: #999;"
        maxlength="1000"
        value="{{content}}"
        bindinput="onContentInput"
      ></textarea>
      <view class="input-counter">
        <text class="counter-text">{{content.length}}/1000</text>
      </view>

      <!-- 回复模式下显示被回复的评论信息 -->
      <view wx:if="{{replyMode && replyToComment}}" class="reply-to-info">
        <view class="reply-to-header">
          <text class="reply-to-label">回复</text>
          <text class="reply-to-user">@{{replyToComment.nickname}}</text>
          <text class="cancel-reply" bindtap="cancelReply">取消</text>
        </view>
        <view class="reply-to-content">{{replyToComment.content}}</view>
      </view>

      <!-- @用户功能按钮 -->
      <view class="mention-actions">
        <button class="mention-btn" bindtap="showMentionPanel">@用户</button>
      </view>

      <!-- @用户选择面板 -->
      <view wx:if="{{showMentionPanel}}" class="mention-panel">
        <view class="mention-header">选择要@的用户</view>
        <scroll-view class="mention-list" scroll-y>
          <view
            wx:for="{{mentionUsers}}"
            wx:key="id"
            class="mention-item"
            bindtap="selectMentionUser"
            data-user="{{item}}"
          >
            <image class="mention-avatar" src="{{item.avatar}}" />
            <text class="mention-nickname">{{item.nickname}}</text>
          </view>
        </scroll-view>
        <view class="mention-panel-actions">
          <button class="mention-cancel" bindtap="closeMentionPanel">取消</button>
        </view>
      </view>

      <!-- 已选择的@用户显示 -->
      <view wx:if="{{mentionedUsers.length > 0}}" class="mentioned-users">
        <text class="mentioned-label">已@用户：</text>
        <view class="mentioned-list">
          <view
            wx:for="{{mentionedUsers}}"
            wx:key="userId"
            class="mentioned-user"
          >
            <text class="mentioned-name">@{{item.nickname}}</text>
            <text class="remove-mention" bindtap="removeMentionUser" data-index="{{index}}">×</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 联系方式 -->
  <view class="feedback-section">
    <view class="section-title">
      <text class="title-text">联系方式</text>
      <text class="optional">（可选）</text>
    </view>
    <input 
      class="contact-input" 
      placeholder="请输入您的联系方式（手机号或微信号）" 
      placeholder-style="color: #999;"
      value="{{contactInfo}}"
      bindinput="onContactInfoInput"
    />
  </view>

  <!-- 图片上传 -->
  <view class="feedback-section">
    <view class="section-title">
      <text class="title-text">上传图片</text>
      <text class="optional">（可选，最多{{maxImageCount}}张）</text>
    </view>
    <view class="upload-area">
      <!-- 图片预览 - 一行显示3张 -->
      <view class="image-grid">
        <view class="image-item"
          wx:for="{{images}}"
          wx:key="path"
          bindtap="onPreviewImage"
          data-src="{{item.path}}"
        >
          <image src="{{item.path}}" mode="aspectFill"/>
          <view class="delete-btn" bindtap="onDeleteImage" data-index="{{index}}">×</view>
        </view>
        
        <!-- 上传按钮 -->
        <view
          class="upload-item"
          bindtap="onChooseImage"
          wx:if="{{images.length < maxImageCount}}"
        >
          <text class="plus">+</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-section">
    <button
      class="submit-btn {{submitting ? 'submitting' : ''}}"
      bindtap="onSubmit"
      disabled="{{submitting}}"
    >
      <text wx:if="{{!submitting}}">{{replyMode ? '发布回复' : '提交反馈'}}</text>
      <text wx:else>{{replyMode ? '发布中...' : '提交中...'}}</text>
    </button>
  </view>

  <!-- 提示信息 -->
  <view class="tips-section">
    <view class="tip-item">
      <text class="tip-icon">💡</text>
      <text class="tip-text">请详细描述问题，这样我们能更好地帮助您</text>
    </view>
    <view class="tip-item">
      <text class="tip-icon">📷</text>
      <text class="tip-text">上传截图能帮助我们更快定位问题</text>
    </view>
    <view class="tip-item">
      <text class="tip-icon">⏰</text>
      <text class="tip-text">我们会在3个工作日内回复您的反馈</text>
    </view>
  </view>
</view> 