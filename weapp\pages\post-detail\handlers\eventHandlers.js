/**
 * 事件处理器模块
 * 将事件处理逻辑从主页面文件中分离出来
 */

const { MESSAGES, LIMITS } = require('../config/constants');
const { contentUtils, debounce, throttle } = require('../utils/helpers');

/**
 * 用户交互事件处理器
 */
class UserInteractionHandlers {
  constructor(page) {
    this.page = page;
  }

  /**
   * 处理点赞帖子
   */
  async handleLikePost() {
    const { postDetail } = this.page.data;
    const newLikeState = !postDetail.userActions.isLiked;
    
    try {
      const success = await this.page.postManager.toggleLike(newLikeState);
      
      if (success) {
        this.page.setData({ 
          'postDetail.userActions.isLiked': newLikeState,
          'postDetail.stats.likeCount': this.page.postManager.postData.stats.likeCount
        });
        
        wx.showToast({
          title: newLikeState ? MESSAGES.SUCCESS.LIKE_POST : MESSAGES.SUCCESS.UNLIKE_POST,
          icon: 'success'
        });
      }
    } catch (error) {
      wx.showToast({
        title: MESSAGES.ERROR.LIKE_FAILED,
        icon: 'none'
      });
    }
  }

  /**
   * 处理收藏帖子
   */
  async handleFavoritePost() {
    const { postDetail } = this.page.data;
    const newFavoriteState = !postDetail.userActions.isFavorited;
    
    try {
      const success = await this.page.postManager.toggleFavorite(newFavoriteState);
      
      if (success) {
        this.page.setData({ 
          'postDetail.userActions.isFavorited': newFavoriteState,
          'postDetail.stats.favoriteCount': this.page.postManager.postData.stats.favoriteCount
        });
        
        wx.showToast({
          title: newFavoriteState ? MESSAGES.SUCCESS.FAVORITE_POST : MESSAGES.SUCCESS.UNFAVORITE_POST,
          icon: 'success'
        });
      }
    } catch (error) {
      wx.showToast({
        title: MESSAGES.ERROR.FAVORITE_FAILED,
        icon: 'none'
      });
    }
  }

  /**
   * 处理分享帖子
   */
  async handleSharePost() {
    try {
      await this.page.postManager.sharePost();
      wx.showToast({
        title: MESSAGES.SUCCESS.SHARE_POST,
        icon: 'success'
      });
    } catch (error) {
      wx.showToast({
        title: MESSAGES.ERROR.SHARE_FAILED,
        icon: 'none'
      });
    }
  }

  /**
   * 处理复制链接
   */
  handleCopyLink() {
    const { postId } = this.page.data;
    const link = `pages/post-detail/post-detail?id=${postId}`;
    
    wx.setClipboardData({
      data: link,
      success: () => {
        wx.showToast({
          title: MESSAGES.SUCCESS.COPY_LINK,
          icon: 'success'
        });
      },
      fail: () => {
        wx.showToast({
          title: MESSAGES.ERROR.COPY_FAILED,
          icon: 'none'
        });
      }
    });
  }
}

/**
 * 反馈相关事件处理器
 */
class FeedbackHandlers {
  constructor(page) {
    this.page = page;
  }

  /**
   * 处理反馈内容输入
   */
  handleFeedbackInput = debounce((e) => {
    const content = e.detail.value.trim();
    const canSubmit = contentUtils.validateContentLength(content, LIMITS.FEEDBACK_CONTENT_MAX);
    
    this.page.setData({
      feedbackContent: content,
      canSubmitFeedback: canSubmit && this.page.data.selectedTags.length > 0
    });
  }, 300);

  /**
   * 处理标签选择
   */
  handleTagSelect(e) {
    const { tag } = e.detail;
    const { selectedTags } = this.page.data;
    
    let newSelectedTags;
    if (selectedTags.includes(tag)) {
      newSelectedTags = selectedTags.filter(t => t !== tag);
    } else {
      newSelectedTags = [...selectedTags, tag];
    }
    
    this.page.setData({
      selectedTags: newSelectedTags,
      canSubmitFeedback: this.page.data.feedbackContent.trim().length > 0 && newSelectedTags.length > 0
    });
  }

  /**
   * 处理提交反馈
   */
  async handleSubmitFeedback() {
    const { feedbackContent, selectedTags, postId } = this.page.data;
    
    if (!contentUtils.validateContentLength(feedbackContent, LIMITS.FEEDBACK_CONTENT_MAX)) {
      wx.showToast({
        title: MESSAGES.VALIDATION.FEEDBACK_CONTENT_REQUIRED,
        icon: 'none'
      });
      return;
    }

    if (selectedTags.length === 0) {
      wx.showToast({
        title: MESSAGES.VALIDATION.FEEDBACK_TAG_REQUIRED,
        icon: 'none'
      });
      return;
    }

    this.page.setData({ isSubmittingFeedback: true });

    try {
      const feedbackData = {
        postId,
        content: feedbackContent,
        tags: selectedTags
      };

      const success = await this.page.feedbackManager.submitFeedback(feedbackData);
      
      if (success) {
        wx.showToast({
          title: MESSAGES.SUCCESS.SUBMIT_FEEDBACK,
          icon: 'success'
        });

        // 重置表单并关闭弹窗
        this.page.setData({
          feedbackContent: '',
          selectedTags: [],
          showFeedbackDialog: false,
          canSubmitFeedback: false
        });

        // 刷新列表
        this.page.setData({ feedbackList: this.page.feedbackManager.feedbackList });
      }
    } catch (error) {
      wx.showToast({
        title: MESSAGES.ERROR.SUBMIT_FEEDBACK_FAILED,
        icon: 'none'
      });
    } finally {
      this.page.setData({ isSubmittingFeedback: false });
    }
  }

  /**
   * 处理点赞反馈
   */
  async handleLikeFeedback(e) {
    const { feedbackId } = e.detail;
    
    try {
      const success = await this.page.feedbackManager.toggleFeedbackLike(feedbackId);
      
      if (success) {
        // 更新UI
        this.page.setData({ feedbackList: this.page.feedbackManager.feedbackList });
        
        wx.showToast({
          title: MESSAGES.SUCCESS.LIKE_POST,
          icon: 'success'
        });
      }
    } catch (error) {
      wx.showToast({
        title: MESSAGES.ERROR.LIKE_FAILED,
        icon: 'none'
      });
    }
  }
}

/**
 * 回复相关事件处理器
 */
class ReplyHandlers {
  constructor(page) {
    this.page = page;
  }

  /**
   * 处理回复内容输入
   */
  handleReplyInput = debounce((e) => {
    const content = e.detail.value.trim();
    const canSubmit = contentUtils.validateContentLength(content, LIMITS.REPLY_CONTENT_MAX);
    
    this.page.setData({
      replyContent: content,
      canSubmitReply: canSubmit
    });
  }, 300);

  /**
   * 处理显示回复输入框
   */
  handleShowReplyInput(e) {
    const { comment } = e.detail;
    
    this.page.setData({
      showReplyInput: true,
      replyTarget: comment,
      replyParentId: comment.id,
      replyPlaceholder: `回复 @${comment.author.nickname}`,
      replyContent: '',
      canSubmitReply: false
    });
  }

  /**
   * 处理隐藏回复输入框
   */
  handleHideReplyInput() {
    this.page.setData({
      showReplyInput: false,
      replyContent: '',
      replyTarget: null,
      replyParentId: null,
      canSubmitReply: false
    });
  }

  /**
   * 处理提交回复
   */
  async handleSubmitReply() {
    const { replyContent, replyTarget, replyParentId, postId } = this.page.data;
    
    if (!contentUtils.validateContentLength(replyContent, LIMITS.REPLY_CONTENT_MAX)) {
      wx.showToast({
        title: MESSAGES.VALIDATION.REPLY_CONTENT_REQUIRED,
        icon: 'none'
      });
      return;
    }

    this.page.setData({ isSubmittingReply: true });

    try {
      const replyData = {
        postId,
        parentId: replyParentId,
        content: replyContent,
        mentionedUserIds: replyTarget ? [replyTarget.userId] : []
      };

      const newReply = await this.page.commentManager.submitReply(replyData);
      
      wx.showToast({
        title: MESSAGES.SUCCESS.SUBMIT_REPLY,
        icon: 'success'
      });

      // 重置回复表单
      this.page.setData({
        showReplyInput: false,
        replyContent: '',
        replyTarget: null,
        replyParentId: null,
        canSubmitReply: false
      });

      // 刷新反馈列表
      await this.page.loadFeedbackList(postId, true);
      
    } catch (error) {
      wx.showToast({
        title: MESSAGES.ERROR.SUBMIT_REPLY_FAILED,
        icon: 'none'
      });
    } finally {
      this.page.setData({ isSubmittingReply: false });
    }
  }
}

module.exports = {
  UserInteractionHandlers,
  FeedbackHandlers,
  ReplyHandlers
};
