/**
 * 签到记录Store
 * 管理签到记录相关的数据和API调用
 */
const api = require('../config/api.js');
const { request } = require('../utils/request.js');


class SigninRecordsStore {
  constructor() {
    this.records = [];
    this.summary = {};
    this.loading = false;
    this.pagination = {
      page: 1,
      size: 20,
      total: 0,
      pages: 0,
      hasNext: false,
      hasPrev: false
    };
  }

  /**
   * 获取签到记录列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.size - 每页大小
   * @param {string} params.startDate - 开始日期
   * @param {string} params.endDate - 结束日期
   * @param {boolean} params.refresh - 是否刷新数据
   */
  async getSigninRecords(params = {}) {
    const {
      page = 1,
      size = 20,
      startDate,
      endDate,
      refresh = false
    } = params;

    // 如果是刷新或第一页，清空现有数据
    if (refresh || page === 1) {
      this.records = [];
      this.pagination.page = 1;
    }

    this.loading = true;

    try {
      const requestParams = {
        page,
        size
      };

      if (startDate) {
        requestParams.startDate = startDate;
      }
      if (endDate) {
        requestParams.endDate = endDate;
      }

      const response = await request({
        url: api.signin.records,
        method: 'GET',
        data: requestParams
      });

      if (response.success) {
        const { records, total, pages, hasNext, hasPrev } = response.data;
        
        // 如果是加载更多，追加数据；否则替换数据
        if (page === 1 || refresh) {
          this.records = records || [];
        } else {
          this.records = [...this.records, ...(records || [])];
        }

        // 更新分页信息
        this.pagination = {
          page,
          size,
          total: total || 0,
          pages: pages || 0,
          hasNext: hasNext || false,
          hasPrev: hasPrev || false
        };

        return {
          success: true,
          data: this.records,
          pagination: this.pagination
        };
      } else {
        throw new Error(response.msg || '获取签到记录失败');
      }
    } catch (error) {
      console.error('获取签到记录失败:', error);
      wx.showToast({
        title: error.message || '获取签到记录失败',
        icon: 'none'
      });
      return {
        success: false,
        error: error.message
      };
    } finally {
      this.loading = false;
    }
  }

  /**
   * 获取签到统计汇总
   */
  async getSigninSummary() {
    try {
      const response = await request({
        url: api.signin.summary,
        method: 'GET'
      });

      if (response.success) {
        this.summary = response.data || {};
        return {
          success: true,
          data: this.summary
        };
      } else {
        throw new Error(response.msg || '获取签到统计失败');
      }
    } catch (error) {
      console.error('获取签到统计失败:', error);
      wx.showToast({
        title: error.message || '获取签到统计失败',
        icon: 'none'
      });
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 加载更多记录
   */
  async loadMore(params = {}) {
    if (!this.pagination.hasNext || this.loading) {
      return {
        success: false,
        message: '没有更多数据'
      };
    }

    return await this.getSigninRecords({
      ...params,
      page: this.pagination.page + 1,
      size: this.pagination.size
    });
  }

  /**
   * 刷新记录
   */
  async refresh(params = {}) {
    return await this.getSigninRecords({
      ...params,
      page: 1,
      refresh: true
    });
  }

  /**
   * 格式化签到记录数据
   * @param {Object} record - 原始记录数据
   */
  formatRecord(record) {
    if (!record) return null;

    return {
      ...record,
      // 格式化日期显示
      displayDate: this._formatDate(record.signinDate),
      // 格式化时间显示
      displayTime: this._formatTime(record.signinTime),
      // 格式化积分显示
      displayPoints: `+${record.totalPoints}积分`,
      // 连续签到天数显示
      displayContinuous: record.continuousDays > 1 ? `连续${record.continuousDays}天` : '',
      // 奖励积分显示
      displayReward: record.continuousReward > 0 ? `+${record.continuousReward}奖励` : ''
    };
  }

  /**
   * 获取当前数据状态
   */
  getState() {
    return {
      records: this.records.map(record => this.formatRecord(record)),
      summary: this.summary,
      loading: this.loading,
      pagination: this.pagination,
      hasData: this.records.length > 0,
      canLoadMore: this.pagination.hasNext && !this.loading
    };
  }

  /**
   * 清空数据
   */
  clear() {
    this.records = [];
    this.summary = {};
    this.pagination = {
      page: 1,
      size: 20,
      total: 0,
      pages: 0,
      hasNext: false,
      hasPrev: false
    };
  }

  /**
   * 格式化日期
   */
  _formatDate(dateStr) {
    if (!dateStr) return '';
    const date = new Date(dateStr);
    const today = new Date();
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);

    if (date.toDateString() === today.toDateString()) {
      return '今天';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return '昨天';
    } else {
      return `${date.getMonth() + 1}月${date.getDate()}日`;
    }
  }

  /**
   * 格式化时间
   */
  _formatTime(timeStr) {
    if (!timeStr) return '';
    const date = new Date(timeStr);
    return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
  }

}

// 创建单例实例
const signinRecordsStore = new SigninRecordsStore();

module.exports = signinRecordsStore;
