<view class="post-list-card">
  <view class="post-list-header">
    <text class="post-list-title">发布记录</text>
    <text class="post-list-more" bindtap="onMore">查看全部</text>
  </view>
  
  <!-- 有发布内容时显示列表 -->
  <block wx:if="{{posts && posts.length > 0}}">
    <view class="post-item" wx:for="{{posts}}" wx:key="id" bindtap="onPostTap" data-id="{{item.id}}">
      <!-- 左侧图片区域 -->
      <view class="post-image-container">
        <image 
          wx:if="{{item.images && item.images.length > 0}}" 
          class="post-image" 
          src="{{item.images[0]}}"
          mode="aspectFill"
        ></image>
        <view wx:else class="post-image-placeholder">
          <text class="placeholder-text">{{item.category && item.category.name ? item.category.name.substring(0, 2) : 'PF'}}</text>
        </view>
      </view>
      
      <!-- 右侧内容区域 -->
      <view class="post-content">
        <view class="post-title">{{item.category && item.category.name ? item.category.name : ''}}</view>
        <view class="post-desc">{{item.content}}</view>
        <view class="post-meta">
          <view class="meta-item">
            <image class="meta-icon" src="/assets/images/common/view.png" mode="aspectFit"></image>
            <text>{{item.stats && item.stats.viewCount ? item.stats.viewCount : 0}}</text>
          </view>
          <view class="meta-item">
            <image class="meta-icon" src="/assets/images/common/like.png" mode="aspectFit"></image>
            <text>{{item.stats && item.stats.likeCount ? item.stats.likeCount : 0}}</text>
          </view>
          <!-- <image class="meta-action" src="/assets/images/common/delete.png" mode="aspectFit" catchtap="onDelete" data-id="{{item.id}}"></image> -->
        </view>
      </view>
    </view>
  </block>
  
  <!-- 没有发布内容时显示空状态 -->
  <view wx:else class="post-list-empty">
    <image class="empty-icon" src="/assets/images/common/add-image.png" mode="aspectFit"></image>
    <text class="empty-text">暂无发布内容</text>
    <text class="empty-desc">发布第一条内容，让更多人看到你的分享</text>
  </view>
</view> 