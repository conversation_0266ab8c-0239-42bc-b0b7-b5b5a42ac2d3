/**
 * 发布相关的工具函数
 */
class PublishHelper {
  /**
   * 构建发布数据
   * @param {Object} formData 表单数据
   * @returns {Object} 格式化后的发布数据
   */
  static buildPostData(formData) {
    // 处理图片数据 - 转换为字符串格式
    const processedImages = this.processImages(formData.images || []);
    const imagesString = processedImages.map(img => img.url).join(',');

    // 处理标签数据 - 转换为字符串格式
    const tagsString = (formData.selectedTags || []).join(',');

    // 基础数据对象
    const baseData = {
      // 接口要求的字段
      images: imagesString,
      tags: tagsString,
      contactName: formData.contactName || '',
      contactType: formData.contactType || 'phone',
      contactNumber: formData.contactNumber || '',
      content: formData.description || '', // 内容描述
      categoryId: parseInt(formData.categoryId) || 0,
      location: formData.location || '',
      address: formData.address || '',
      longitude: parseFloat(formData.longitude) || 0,
      latitude: parseFloat(formData.latitude) || 0,
      geolocation: formData.publishLocation || '',

      // 保留原有字段用于内部处理
      title: formData.title || '',
      description: formData.description || '',
      category: formData.category || '',
      selectedTags: formData.selectedTags || [],
      imageCount: formData.images ? formData.images.length : 0,
      tagCount: formData.selectedTags ? formData.selectedTags.length : 0,
      publishTime: new Date().toISOString(),
      publishType: formData.publishTypeActive || '普通',
      status: 'pending',
      userId: formData.userId || '',
      userName: formData.userName || '',
      platform: 'weapp',
      version: '1.0.0',
      deviceInfo: formData.deviceInfo || '',
      extra: {
        draftId: formData.draftId || null,
        source: 'publish_page',
        timestamp: Date.now(),
        dynamicFormData: formData.dynamicFormData || {} // 保留原始动态表单数据用于调试
      }
    };

    // 直接处理原始动态表单数据
    console.log('=== buildPostData 开始处理动态表单数据 ===');
    console.log('formData.dynamicFormData:', formData.dynamicFormData);
    console.log('formData.dynamicFormData 类型:', typeof formData.dynamicFormData);
    console.log('formData.dynamicFormData 是否为空:', !formData.dynamicFormData || Object.keys(formData.dynamicFormData || {}).length === 0);

    const flattenedDynamicFields = this.flattenDynamicFormData(formData.dynamicFormData || {});

    console.log('flattenedDynamicFields 结果:', flattenedDynamicFields);
    console.log('flattenedDynamicFields 字段数量:', Object.keys(flattenedDynamicFields).length);

    // 合并基础数据和动态表单字段
    const finalData = {
      ...baseData,
      ...flattenedDynamicFields // 动态表单字段直接平铺到最外层
    };

    console.log('构建发布数据:', {
      original: formData.dynamicFormData,
      flattened: flattenedDynamicFields,
      final: finalData
    });

    console.log('=== buildPostData 完成 ===');

    return finalData;
  }

  /**
   * 处理动态表单数据 - 按下划线自动分组为嵌套对象
   * @param {Object} dynamicFormData 动态表单数据
   * @param {Object} options 处理选项
   * @param {boolean} options.simplify 是否简化输出（只保留值，不保留元数据）
   * @returns {Object} 处理后的动态表单字段对象
   */
  static processDynamicFormData(dynamicFormData, options = {}) {
    if (!dynamicFormData || typeof dynamicFormData !== 'object') {
      return {};
    }

    const processedFields = {};
    const groupedFields = {}; // 用于存储分组后的字段

    // 遍历动态表单数据，提取有效的字段值
    Object.keys(dynamicFormData).forEach(key => {
      const fieldData = dynamicFormData[key];
      let fieldValue = null;
      let fieldMeta = {};

      // 提取字段值和元数据
      if (fieldData && typeof fieldData === 'object') {
        // 如果是对象，提取其中的值
        if (fieldData.value !== undefined && fieldData.value !== null && fieldData.value !== '') {
          fieldValue = fieldData.value;
          fieldMeta = {
            type: fieldData.type || 'text',
            label: fieldData.label || fieldData.name || key,
            required: fieldData.required || false
          };
        }
      } else if (fieldData !== undefined && fieldData !== null && fieldData !== '') {
        // 如果是简单值，直接使用
        fieldValue = fieldData;
        fieldMeta = {
          type: 'text',
          label: key,
          required: false
        };
      }

      // 如果有有效值，进行分组处理
      if (fieldValue !== null) {
        this.processFieldGrouping(key, fieldValue, fieldMeta, groupedFields);
      }
    });

    // 将分组后的字段合并到最终结果
    Object.keys(groupedFields).forEach(groupKey => {
      if (options.simplify) {
        // 简化模式：只保留值，移除元数据
        processedFields[groupKey] = this.simplifyGroupedField(groupedFields[groupKey]);
      } else {
        processedFields[groupKey] = groupedFields[groupKey];
      }
    });

    console.log('处理动态表单数据:', {
      original: dynamicFormData,
      processed: processedFields,
      options: options
    });

    return processedFields;
  }

  /**
   * 处理字段分组逻辑
   * @param {string} fieldKey 字段键名
   * @param {*} fieldValue 字段值
   * @param {Object} fieldMeta 字段元数据
   * @param {Object} groupedFields 分组后的字段对象
   */
  static processFieldGrouping(fieldKey, fieldValue, fieldMeta, groupedFields) {
    // 检查是否包含下划线
    if (fieldKey.includes('_')) {
      const parts = fieldKey.split('_');
      const groupName = parts[0]; // 第一部分作为组名
      const fieldName = parts.slice(1).join('_'); // 剩余部分作为字段名

      // 初始化组对象
      if (!groupedFields[groupName]) {
        groupedFields[groupName] = {
          _meta: {
            type: 'group',
            label: groupName,
            fields: {}
          }
        };
      }

      // 添加字段到组中
      groupedFields[groupName][fieldName] = fieldValue;
      groupedFields[groupName]._meta.fields[fieldName] = fieldMeta;

    } else {
      // 没有下划线的字段直接添加
      groupedFields[fieldKey] = {
        value: fieldValue,
        ...fieldMeta
      };
    }
  }

  /**
   * 简化分组字段，移除元数据只保留值
   * @param {Object} groupedField 分组后的字段对象
   * @returns {Object} 简化后的对象
   */
  static simplifyGroupedField(groupedField) {
    if (!groupedField || typeof groupedField !== 'object') {
      return groupedField;
    }

    const simplified = {};

    Object.keys(groupedField).forEach(key => {
      if (key !== '_meta') {
        simplified[key] = groupedField[key];
      }
    });

    return simplified;
  }

  /**
   * 直接处理原始动态表单数据并扁平化
   * @param {Object} dynamicFormData 原始动态表单数据
   * @returns {Object} 扁平化后的字段对象
   */
  static flattenDynamicFormData(dynamicFormData) {
    const flattened = {};

    console.log('flattenDynamicFormData 输入参数:', dynamicFormData);
    console.log('输入参数类型:', typeof dynamicFormData);
    console.log('输入参数是否为对象:', dynamicFormData && typeof dynamicFormData === 'object');
    console.log('输入参数键数量:', dynamicFormData ? Object.keys(dynamicFormData).length : 0);

    if (!dynamicFormData || typeof dynamicFormData !== 'object') {
      console.log('动态表单数据为空或无效:', dynamicFormData);
      return flattened;
    }

    console.log('开始处理原始动态表单数据:', dynamicFormData);
    console.log('动态表单数据的所有键:', Object.keys(dynamicFormData));

    // 遍历动态表单数据，支持两种结构：
    // 1. 扁平结构：{ "fieldKey": "value" }
    // 2. 分组结构：{ "groupKey": { "fieldKey": "value" } }
    Object.keys(dynamicFormData).forEach(key => {
      const data = dynamicFormData[key];
      console.log(`处理键: ${key}, 数据:`, data);

      if (data && typeof data === 'object' && !Array.isArray(data)) {
        // 如果是对象，可能是分组数据或字段对象
        if (data.value !== undefined) {
          // 字段对象格式：{ value: "值", type: "text" }
          const fieldValue = data.value;
          if (fieldValue !== undefined && fieldValue !== null && fieldValue !== '') {
            console.log(`处理字段对象: ${key} = ${fieldValue}`);
            if (key.includes('_') || key.includes('.')) {
              this.setNestedValue(flattened, key, fieldValue);
            } else {
              flattened[key] = fieldValue;
            }
          }
        } else {
          // 分组数据格式：{ "fieldKey1": "value1", "fieldKey2": "value2" }
          console.log(`处理分组数据: ${key}`, data);
          Object.keys(data).forEach(fieldKey => {
            const fieldValue = data[fieldKey];
            if (fieldValue !== undefined && fieldValue !== null && fieldValue !== '') {
              console.log(`处理分组字段: ${fieldKey} = ${fieldValue}`);
              if (fieldKey.includes('_') || fieldKey.includes('.')) {
                this.setNestedValue(flattened, fieldKey, fieldValue);
              } else {
                flattened[fieldKey] = fieldValue;
              }
            }
          });
        }
      } else if (data !== undefined && data !== null && data !== '') {
        // 简单值格式：直接是值
        console.log(`处理简单值: ${key} = ${data}`);
        if (key.includes('_') || key.includes('.')) {
          this.setNestedValue(flattened, key, data);
        } else {
          flattened[key] = data;
        }
      }
    });

    console.log('扁平化动态字段结果:', {
      input: dynamicFormData,
      output: flattened
    });

    return flattened;
  }

  /**
   * 将动态表单字段扁平化为顶级字段（处理已分组的数据）
   * 如果字段名包含 _ 或 . 则创建嵌套对象结构
   * @param {Object} dynamicFields 动态表单字段对象
   * @returns {Object} 扁平化后的字段对象
   */
  static flattenDynamicFields(dynamicFields) {
    const flattened = {};

    if (!dynamicFields || typeof dynamicFields !== 'object') {
      return flattened;
    }

    // 遍历动态表单字段
    Object.keys(dynamicFields).forEach(groupKey => {
      const groupData = dynamicFields[groupKey];

      if (groupData && typeof groupData === 'object') {
        // 如果是分组数据，将每个字段提取到顶级
        Object.keys(groupData).forEach(fieldKey => {
          const fieldValue = groupData[fieldKey];

          // 检查字段名是否包含 _ 或 .
          if (fieldKey.includes('_') || fieldKey.includes('.')) {
            this.setNestedValue(flattened, fieldKey, fieldValue);
          } else {
            // 直接设置为顶级字段
            flattened[fieldKey] = fieldValue;
          }
        });
      }
    });

    console.log('扁平化动态字段:', {
      input: dynamicFields,
      output: flattened
    });

    return flattened;
  }

  /**
   * 根据字段名设置嵌套值
   * 支持 _ 和 . 作为分隔符
   * @param {Object} target 目标对象
   * @param {string} fieldKey 字段名
   * @param {*} fieldValue 字段值
   */
  static setNestedValue(target, fieldKey, fieldValue) {
    // 优先使用 _ 作为分隔符，如果没有则使用 .
    const separator = fieldKey.includes('_') ? '_' : '.';
    const keys = fieldKey.split(separator);

    let current = target;

    // 遍历路径，创建嵌套对象
    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i];
      if (!current[key] || typeof current[key] !== 'object') {
        current[key] = {};
      }
      current = current[key];
    }

    // 设置最终值
    const lastKey = keys[keys.length - 1];
    current[lastKey] = fieldValue;

    console.log(`设置嵌套值: ${fieldKey} = ${fieldValue}`, {
      keys: keys,
      separator: separator,
      result: target
    });
  }

  /**
   * 构建草稿数据
   * @param {Object} formData 表单数据
   * @returns {Object} 格式化后的草稿数据
   */
  static buildDraftData(formData) {
    return {
      title: formData.title || '',
      description: formData.description || '',
      contactName: formData.contactName || '',
      contactType: formData.contactType || 'phone',
      contactNumber: formData.contactNumber || '',
      location: formData.location || '',
      latitude: formData.latitude || '',
      longitude: formData.longitude || '',
      tags: JSON.stringify(formData.selectedTags || []),
      images: JSON.stringify(formData.images || []),
      category: formData.category || '',
      saveTime: new Date().toISOString()
    };
  }

  /**
   * 验证表单数据
   * @param {Object} formData 表单数据
   * @returns {Object} 验证结果
   */
  static validateForm(formData) {
    const errors = [];

    // 验证必填字段
    if (!formData.description || !formData.description.trim()) {
      errors.push('请输入内容描述');
    }

    if (!formData.contactName || !formData.contactName.trim()) {
      errors.push('请输入联系人姓名');
    }

    if (!formData.contactNumber || !formData.contactNumber.trim()) {
      errors.push('请输入联系方式');
    }

    if (!formData.category && !formData.categoryId) {
      errors.push('请选择分类');
    }

    // 验证联系方式格式
    if (formData.contactType === 'phone' && formData.contactNumber) {
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(formData.contactNumber)) {
        errors.push('请输入正确的手机号码');
      }
    }

    if (formData.contactType === 'wechat' && formData.contactNumber) {
      if (formData.contactNumber.length < 6) {
        errors.push('微信号至少6位字符');
      }
    }

    // 验证内容长度
    if (formData.description && formData.description.length > 800) {
      errors.push('内容描述不能超过800字');
    }

    if (formData.title && formData.title.length > 50) {
      errors.push('标题不能超过50字');
    }

    // 验证图片数量
    if (formData.images && formData.images.length > 6) {
      errors.push('最多上传6张图片');
    }

    // 验证标签数量
    if (formData.selectedTags && formData.selectedTags.length > 5) {
      errors.push('最多选择5个标签');
    }

    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }

  /**
   * 处理图片数据
   * @param {Array} images 原始图片数组
   * @returns {Array} 处理后的图片数组
   */
  static processImages(images) {
    if (!images || images.length === 0) {
      return [];
    }

    return images.map((img, index) => {
      // 如果是已上传的图片（有fileId和url）
      if (img.fileId && img.url) {
        return {
          url: img.url,
          fileId: img.fileId,
          size: img.size || 0,
          type: img.type || 'image/jpeg',
          name: img.name || `image_${index + 1}.jpg`,
          isLocal: false
        };
      }
      
      // 如果是本地临时图片
      return {
        url: img.path || img.url,
        size: img.size || 0,
        type: img.type || 'image/jpeg',
        name: img.name || `image_${index + 1}.jpg`,
        isLocal: true
      };
    });
  }

  /**
   * 格式化标签数据
   * @param {Array} tags 标签数组
   * @returns {Array} 格式化后的标签数组
   */
  static formatTags(tags) {
    if (!tags || !Array.isArray(tags)) {
      return [];
    }

    return tags.filter(tag => tag && tag.trim()).map(tag => tag.trim());
  }

  /**
   * 获取设备信息
   * @returns {Object} 设备信息
   */
  static getDeviceInfo() {
    try {
      const deviceInfo = wx.getDeviceInfo();
      const windowInfo = wx.getWindowInfo();
      const appBaseInfo = wx.getAppBaseInfo();
      return {
        platform: deviceInfo.platform,
        system: deviceInfo.system,
        version: appBaseInfo.version,
        model: deviceInfo.model,
        screenWidth: windowInfo.screenWidth,
        screenHeight: windowInfo.screenHeight
      };
    } catch (error) {
      console.error('获取设备信息失败:', error);
      return {};
    }
  }

  /**
   * 生成唯一ID
   * @returns {string} 唯一ID
   */
  static generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  /**
   * 格式化文件大小
   * @param {number} bytes 字节数
   * @returns {string} 格式化后的大小
   */
  static formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 检查网络状态
   * @returns {Promise} 网络状态
   */
  static async checkNetworkStatus() {
    return new Promise((resolve) => {
      wx.getNetworkType({
        success: (res) => {
          resolve({
            isConnected: res.networkType !== 'none',
            networkType: res.networkType
          });
        },
        fail: () => {
          resolve({
            isConnected: false,
            networkType: 'unknown'
          });
        }
      });
    });
  }
}

module.exports = PublishHelper; 