Component({
  properties: {
    feedbackId: { type: String, value: '' },
    avatar: { type: String, value: '' },
    nickname: { type: String, value: '' },
    content: { type: String, value: '' },
    time: { type: String, value: '' },
    likes: { type: Number, value: 0 },
    isHelpful: { type: Boolean, value: false },
    // 新增回复相关属性
    replyToUserName: { type: String, value: '' },
    image: { type: String, value: '' },
    replyCount: { type: Number, value: 0 },
    expanded: { type: Boolean, value: false },
    replies: { type: Array, value: [] },
    hasMoreReplies: { type: Boolean, value: false },
    allRepliesLoaded: { type: Boolean, value: false },
    totalReplyCount: { type: Number, value: 0 },
    commentId: { type: String, value: '' },
    postId: { type: Number, value: 0 },
    userId: { type: String, value: '' },
    formattedContent: { type: String, value: '' }
  },
  data: {},
  methods: {
    onLikeTap() {
      // 只能点赞，不能取消点赞
      if (this.properties.isHelpful) {
        return;
      }
      this.triggerEvent('like', {
        feedbackId: this.properties.feedbackId,
        isHelpful: this.properties.isHelpful
      });
    },

    // 回复评论
    onReplyTap() {
      this.triggerEvent('reply', {
        commentId: (this.properties.commentId || this.properties.feedbackId).toString(),
        parentId: (this.properties.commentId || this.properties.feedbackId).toString(),
        postId: this.properties.postId.toString(),
        replyToComment: {
          userId: this.properties.userId.toString(),
          nickname: this.properties.nickname,
          content: this.properties.content
        }
      });
    },

    // 展开/收起回复
    onToggleReplies() {
      const expanded = !this.properties.expanded;
      const commentId = this.properties.commentId || this.properties.feedbackId;

      // 触发展开/收起事件，让父组件更新状态
      this.triggerEvent('toggleReplies', {
        commentId: commentId,
        expanded: expanded
      });

      // 如果是展开且没有回复数据，则加载回复列表
      const replies = this.properties.replies || [];
      if (expanded && replies.length === 0) {
        this.triggerEvent('loadReplies', {
          commentId: commentId
        });
      }
    },

    // 回复某个回复
    onReplyToReply(e) {
      const reply = e.currentTarget.dataset.reply;
      this.triggerEvent('reply', {
        commentId: (this.properties.commentId || this.properties.feedbackId).toString(),
        postId: this.properties.postId.toString(),
        parentId: (this.properties.commentId || this.properties.feedbackId).toString(), // 保持同一个父级
        replyToComment: {
          userId: reply.userId.toString(),
          nickname: reply.nickname,
          content: reply.content
        }
      });
    },

    // 点赞回复
    onLikeReply(e) {
      const reply = e.currentTarget.dataset.reply;
      this.triggerEvent('likeReply', {
        replyId: reply.id,
        isLiked: reply.isLiked
      });
    },

    // 预览评论图片
    onPreviewImage(e) {
      const src = e.currentTarget.dataset.src;
      wx.previewImage({
        current: src,
        urls: [src]
      });
    },

    // 预览回复图片
    onPreviewReplyImage(e) {
      const src = e.currentTarget.dataset.src;
      wx.previewImage({
        current: src,
        urls: [src]
      });
    },

    // 加载更多回复
    onLoadMoreReplies() {
      this.triggerEvent('loadMoreReplies', {
        commentId: this.properties.commentId || this.properties.feedbackId
      });
    },

    // 格式化内容方法
    formatContent() {
      const content = this.properties.content;
      if (!content) {
        this.setData({ formattedContent: '' });
        return;
      }

      // 处理@用户高亮
      const formattedContent = content.replace(
        /@([^\s@]+)/g,
        '<span style="color: #007aff; font-weight: 500;">@$1</span>'
      );

      this.setData({
        formattedContent: formattedContent
      });
    }
  },

  // 组件生命周期
  lifetimes: {
    attached() {
      // 处理@用户高亮显示
      if (this.properties.content) {
        this.formatContent();
      }

      // 调试信息：检查传递的属性
      console.log('Comment card properties:', {
        feedbackId: this.properties.feedbackId,
        replyCount: this.properties.replyCount,
        totalReplyCount: this.properties.totalReplyCount,
        hasMoreReplies: this.properties.hasMoreReplies,
        repliesLength: this.properties.replies.length
      });
    }
  },

  // 监听属性变化
  observers: {
    'content': function(newContent) {
      if (newContent) {
        this.formatContent();
      }
    }
  }
}); 