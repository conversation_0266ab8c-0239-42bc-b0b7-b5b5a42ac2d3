// pages/follow/detail/message/message.js
Page({
  data: {
    showPopup: false,
    messages: [
      {
        id: 1,
        avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
        text: '想租大约三个月，月租您看可以吗',
        isSelf: false
      },
      {
        id: 2,
        avatar: 'https://randomuser.me/api/portraits/women/2.jpg',
        text: '有的',
        isSelf: true
      },
      {
        id: 3,
        avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
        text: '可以加您个微信详细聊聊吗',
        isSelf: false
      },
      {
        id: 4,
        avatar: 'https://randomuser.me/api/portraits/women/2.jpg',
        text: '好的',
        isSelf: true
      }
    ]
  },
  onPlusTap() {
    this.setData({ showPopup: true });
  },
  onPopupMask() {
    this.setData({ showPopup: false });
  },
  stopBubble(e) {
    // 阻止冒泡
  }
})