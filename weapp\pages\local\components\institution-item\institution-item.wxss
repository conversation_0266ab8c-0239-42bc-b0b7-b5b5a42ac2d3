/* 机构卡片组件样式 - 与主页风格一致 */

/* 内容卡片 */
.content-card {
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  border: 1rpx solid #f0f0f0;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06);
  background: #fff;
  overflow: hidden;
}

.content-card:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

/* 卡片头部 */
.card-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

/* 左侧区域 */
.header-left {
  display: flex;
  align-items: flex-start;
  flex: 1;
}

.avatar {
  width: 88rpx;
  height: 88rpx;
  border-radius: 50%;
  margin-right: 16rpx;
  flex-shrink: 0;
  border: 2rpx solid #f0f0f0;
}

.shop-info {
  flex: 1;
  min-width: 0;
  margin-right: 20rpx;
}

.shop-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.shop-category {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #666;
}

.category-text {
  background: #f0f0f0;
  color: #666;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  margin-right: 12rpx;
  font-size: 24rpx;
}

.rating {
  color: #ff6b6b;
  font-weight: 500;
}

/* 距离标签 */
.distance-tag {
  background: linear-gradient(135deg, #ff6b6b, #ff8585);
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  white-space: nowrap;
}

/* 优惠信息 */
.promotion-text {
  font-size: 32rpx;
  color: #333;
  margin: 24rpx 0;
  line-height: 1.5;
}

/* 地址信息 */
.shop-address {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 24rpx;
  line-height: 1.4;
}

/* 服务标签 */
.service-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.service-tag {
  background: rgba(255, 107, 107, 0.1);
  color: #ff6b6b;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.service-tag.more {
  background: #f0f0f0;
  color: #999;
}

/* 底部互动栏 */
.interaction-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 8rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #f0f0f0;
}

.interaction-item {
  display: flex;
  align-items: center;
  color: #999;
  font-size: 26rpx;
  transition: all 0.3s ease;
  padding: 8rpx 12rpx;
  border-radius: 20rpx;
  min-width: 80rpx;
  justify-content: center;
}

.interaction-item:active {
  color: #ff6b6b;
  background: rgba(255, 107, 107, 0.1);
  transform: scale(0.95);
}

.interaction-item .icon {
  font-size: 28rpx;
  margin-right: 6rpx;
}

/* 浏览量和点赞量特殊样式 */
.interaction-item:nth-child(2) {
  color: #666;
  font-weight: 500;
}

.interaction-item:nth-child(3) {
  color: #ff6b6b;
  font-weight: 500;
}

.interaction-item:nth-child(3):active {
  background: rgba(255, 107, 107, 0.2);
}
