Component({
  properties: {
    placeholder: {
      type: String,
      value: '写下你的评价...'
    },
    institutionId: {
      type: Number,
      value: 0
    },
    replyToComment: {
      type: Object,
      value: null
    },
    parentId: {
      type: String,
      value: ''
    },
    show: {
      type: Boolean,
      value: false
    }
  },

  data: {
    content: '',
    image: '',
    uploading: false,
    submitting: false,
    maxLength: 500,
    showEmojiPanel: false,
    mentionedUsers: [],
    mentionedUserIds: []
  },

  methods: {
    // 输入内容变化
    onInput(e) {
      this.setData({
        content: e.detail.value
      });
    },

    // 选择图片
    onChooseImage() {
      wx.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.uploadImage(res.tempFilePaths[0]);
        }
      });
    },

    // 上传图片
    uploadImage(tempFilePath) {
      this.setData({ uploading: true });

      wx.uploadFile({
        url: getApp().globalData.baseUrl + '/blade-resource/oss/endpoint/put-file',
        filePath: tempFilePath,
        name: 'file',
        header: {
          'Authorization': 'Bearer ' + wx.getStorageSync('token')
        },
        success: (res) => {
          try {
            const data = JSON.parse(res.data);
            if (data.code === 200) {
              this.setData({
                image: data.data.link,
                uploading: false
              });
            } else {
              throw new Error(data.msg || '上传失败');
            }
          } catch (error) {
            console.error('图片上传失败:', error);
            wx.showToast({
              title: '图片上传失败',
              icon: 'none'
            });
            this.setData({ uploading: false });
          }
        },
        fail: (error) => {
          console.error('图片上传失败:', error);
          wx.showToast({
            title: '图片上传失败',
            icon: 'none'
          });
          this.setData({ uploading: false });
        }
      });
    },

    // 删除图片
    onRemoveImage() {
      this.setData({ image: '' });
    },

    // 提交评论
    onSubmit() {
      const { content, image, institutionId, parentId, replyToComment } = this.data;
      
      if (!content.trim()) {
        wx.showToast({
          title: '请输入评价内容',
          icon: 'none'
        });
        return;
      }

      if (this.data.submitting) {
        return;
      }

      this.setData({ submitting: true });

      const commentData = {
        content: content.trim(),
        image: image,
        institutionId: institutionId,
        relevancyType: 1, // 机构评价
        mentionedUserIds: this.data.mentionedUserIds,
        mentionedUsers: this.data.mentionedUsers
      };

      // 如果是回复
      if (parentId && replyToComment) {
        commentData.parentId = parentId;
        commentData.replyToComment = replyToComment;
      }

      // 触发提交事件
      this.triggerEvent('submit', commentData);
    },

    // 取消输入
    onCancel() {
      this.setData({
        content: '',
        image: '',
        mentionedUsers: [],
        mentionedUserIds: []
      });
      this.triggerEvent('cancel');
    },

    // 重置输入状态
    resetInput() {
      this.setData({
        content: '',
        image: '',
        submitting: false,
        mentionedUsers: [],
        mentionedUserIds: []
      });
    },

    // 设置提交状态
    setSubmitting(submitting) {
      this.setData({ submitting });
    },

    // 聚焦输入框
    focusInput() {
      this.selectComponent('#comment-input').focus();
    },

    // 预览图片
    onPreviewImage() {
      if (this.data.image) {
        wx.previewImage({
          current: this.data.image,
          urls: [this.data.image]
        });
      }
    },

    // 处理@用户功能
    onAtUser() {
      // 这里可以实现@用户的功能
      // 暂时留空，后续可以扩展
    },

    // 切换表情面板
    onToggleEmoji() {
      this.setData({
        showEmojiPanel: !this.data.showEmojiPanel
      });
    },

    // 插入表情
    onInsertEmoji(e) {
      const emoji = e.currentTarget.dataset.emoji;
      const content = this.data.content + emoji;
      this.setData({ content });
    }
  },

  // 监听属性变化
  observers: {
    'replyToComment': function(replyToComment) {
      if (replyToComment) {
        this.setData({
          placeholder: `回复 @${replyToComment.nickname}:`
        });
      } else {
        this.setData({
          placeholder: '写下你的评价...'
        });
      }
    }
  }
});
