// const BASE_URL = 'https://assistant.duofan.top'; // 替换为您的后端API地址
const BASE_URL = 'http://192.168.31.215'; // 替换为您的后端API地址

const { handleLoginExpired } = require('./loginHandler');

/**
 * 处理数字精度问题
 * @param {any} data 需要处理的数据
 * @returns {any} 处理后的数据
 */
const processNumberPrecision = (data) => {
  if (data === null || data === undefined) {
    return data;
  }

  // 处理数组
  if (Array.isArray(data)) {
    return data.map(item => processNumberPrecision(item));
  }

  // 处理对象
  if (typeof data === 'object') {
    const result = {};
    for (const key in data) {
      if (data.hasOwnProperty(key)) {
        result[key] = processNumberPrecision(data[key]);
      }
    }
    return result;
  }

  // 处理数字类型
  if (typeof data === 'number') {
    // 检查是否超出安全整数范围
    if (!Number.isSafeInteger(data) && Number.isInteger(data)) {
      console.warn(`数字精度可能丢失: ${data}，已转为字符串`);
      return data.toString(); // 转为字符串保持精度
    }

    // 处理浮点数精度问题（保留合理的小数位数）
    if (!Number.isInteger(data)) {
      // 对于小数，检查是否有精度问题
      const str = data.toString();
      if (str.includes('e') || str.length > 15) {
        // 科学计数法或过长的小数，可能有精度问题
        return parseFloat(data.toFixed(10)); // 保留10位小数
      }
    }
  }

  return data;
};

/**
 * 处理请求数据中的大数字
 * @param {any} data 请求数据
 * @returns {any} 处理后的数据
 */
const processRequestData = (data) => {
  if (!data || typeof data !== 'object') {
    return data;
  }

  const result = Array.isArray(data) ? [] : {};

  for (const [key, value] of Object.entries(data)) {
    if (typeof value === 'bigint') {
      // BigInt转为字符串
      result[key] = value.toString();
    } else if (typeof value === 'object' && value !== null) {
      // 递归处理嵌套对象
      result[key] = processRequestData(value);
    } else if (typeof value === 'number' && !Number.isSafeInteger(value) && Number.isInteger(value)) {
      // 超出安全范围的整数转为字符串
      console.warn(`请求参数 ${key} 的值 ${value} 可能丢失精度，已转为字符串`);
      result[key] = value.toString();
    } else {
      result[key] = value;
    }
  }

  return result;
};

// 过滤空值参数的工具函数
const filterEmptyParams = (data) => {
  if (!data || typeof data !== 'object') {
    return data;
  }

  const filtered = {};
  for (const key in data) {
    const value = data[key];

    // 过滤条件：过滤 null、undefined、空字符串
    // 保留：数字0、布尔值false、其他有效值
    if (value !== null && value !== undefined && value !== '') {
      filtered[key] = value;
    }
  }

  return filtered;
};

// 请求拦截器
const requestInterceptor = (config) => {
  const tokenData = wx.getStorageSync('token');
  const token = (typeof tokenData === 'object' && tokenData.value) ? tokenData.value : tokenData;
  const openId = wx.getStorageSync('openId');

  config.header = {
    ...config.header,
    'Tenant-Id' :"000000",
    'Authorization': `Basic d2VhcHA6c2FkZXF3ZXh6Y2Nhc2Rxd2U=`,
  };

  if (token) {
    config.header = {
      ...config.header,
      'Blade-Auth': `Bearer ${token}`
    };
  }

  // 添加OpenID头
  if (openId) {
    config.header = {
      ...config.header,
      'X-Open-ID': openId
    };
  }

  // 添加用户地区头
  try {
    const RegionManager = require('./regionManager');
    const userRegionCode = RegionManager.getUserRegionCode();
    if (userRegionCode) {
      config.header = {
        ...config.header,
        'user-region': userRegionCode
      };
      console.log('添加用户地区头:', userRegionCode);
    }
  } catch (error) {
    console.error('获取用户地区信息失败:', error);
  }

  // 过滤请求参数中的空值
  if (config.data) {
    config.data = filterEmptyParams(config.data);
    // 处理请求参数中的数字精度问题
    config.data = processRequestData(config.data);
  }

  return config;
};

// 响应拦截器
const responseInterceptor = (response) => {
  const { data } = response;

  // 处理token过期
  if (data.code === 401) {
    handleLoginExpired();
    return Promise.reject(new Error('登录已过期，请重新登录'));
  }

  // 处理响应数据中的数字精度问题
  let processedData = data;
  try {
    if (data && (data.data || data.records)) {
      // 处理成功响应的数据部分
      processedData = {
        ...data,
        data: data.data ? processNumberPrecision(data.data) : data.data
      };

      // 如果data.data中有records字段（分页数据）
      if (processedData.data && processedData.data.records) {
        processedData.data.records = processNumberPrecision(processedData.data.records);
      }
    } else if (data && typeof data === 'object') {
      // 处理其他类型的响应数据
      processedData = processNumberPrecision(data);
    }
  } catch (error) {
    console.warn('数字精度处理失败，使用原始数据:', error);
    processedData = data;
  }

  return processedData;
};

// 刷新token
const refreshToken = () => {
  return new Promise((resolve, reject) => {
    const tokenData = wx.getStorageSync('token');
    const refreshTokenValue = (typeof tokenData === 'object' && tokenData.refreshToken) ?
      tokenData.refreshToken : wx.getStorageSync('refreshToken');

    if (!refreshTokenValue) {
      reject(new Error('未找到刷新token'));
      return;
    }

    wx.request({
      url: `${BASE_URL}/blade-auth/token`,
      method: 'POST',
      data: {
        grantType: 'refresh_token',
        refreshToken: refreshTokenValue
      },
      success: (res) => {
        if (res.data.success) {
          const { accessToken, refreshToken } = res.data.data;
          // 保持与登录时相同的token存储格式
          wx.setStorageSync('token', {
            value: accessToken,
            datetime: Date.now(),
            refreshToken: refreshToken
          });
          resolve(accessToken);
        } else {
          reject(new Error(res.data.msg));
        }
      },
      fail: reject
    });
  });
};

// 统一请求方法
const request = (options) => {
  const config = requestInterceptor(options);
  const fullUrl = `${BASE_URL}${config.url}`;
  return new Promise((resolve, reject) => {
    wx.request({
      ...config,
      url: fullUrl,
      success: (res) => {
        try {
          const result = responseInterceptor(res);
          resolve(result);
        } catch (error) {
          console.error('响应拦截器处理失败:', error);
          reject(error);
        }
      },
      fail: (err) => {
        // 增强错误信息
        const enhancedError = new Error(`网络请求失败: ${err.errMsg || '未知错误'}`);
        enhancedError.originalError = err;
        enhancedError.requestUrl = fullUrl;
        enhancedError.requestConfig = config;

        reject(enhancedError);
      }
    });
  });
};

module.exports = {
  request,
  refreshToken
}; 
