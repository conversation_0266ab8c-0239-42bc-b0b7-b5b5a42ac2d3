/**
 * 帖子详情页工具函数
 */

const { TIME_FORMAT, DEFAULTS } = require('../config/constants');

/**
 * 时间格式化工具
 */
const timeUtils = {
  /**
   * 格式化时间显示
   * @param {string} timeStr 时间字符串
   * @returns {string} 格式化后的时间
   */
  formatTime(timeStr) {
    if (!timeStr) return '';
    
    const time = new Date(timeStr);
    const now = new Date();
    const diff = now - time;
    
    // 小于1分钟
    if (diff < 60000) {
      return TIME_FORMAT.JUST_NOW;
    }
    
    // 小于1小时
    if (diff < 3600000) {
      return Math.floor(diff / 60000) + TIME_FORMAT.MINUTES_AGO;
    }
    
    // 小于24小时
    if (diff < 86400000) {
      return Math.floor(diff / 3600000) + TIME_FORMAT.HOURS_AGO;
    }
    
    // 小于7天
    if (diff < 604800000) {
      return Math.floor(diff / 86400000) + TIME_FORMAT.DAYS_AGO;
    }
    
    // 超过7天显示具体日期
    return time.toLocaleDateString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
};

/**
 * 内容格式化工具
 */
const contentUtils = {
  /**
   * 格式化@用户内容
   * @param {string} content 原始内容
   * @returns {string} 格式化后的内容
   */
  formatContentWithMentions(content) {
    if (!content) return '';

    // 处理@用户高亮
    return content.replace(
      /@([^\s@]+)/g,
      '<span style="color: #007aff; font-weight: 500;">@$1</span>'
    );
  },

  /**
   * 验证内容长度
   * @param {string} content 内容
   * @param {number} maxLength 最大长度
   * @returns {boolean} 是否有效
   */
  validateContentLength(content, maxLength) {
    return content && content.trim().length > 0 && content.length <= maxLength;
  },

  /**
   * 截取内容预览
   * @param {string} content 内容
   * @param {number} maxLength 最大长度
   * @returns {string} 截取后的内容
   */
  truncateContent(content, maxLength = 100) {
    if (!content) return '';
    return content.length > maxLength ? content.substring(0, maxLength) + '...' : content;
  }
};

/**
 * 数据处理工具
 */
const dataUtils = {
  /**
   * 安全的ID转换
   * @param {*} id ID值
   * @returns {string} 字符串ID
   */
  safeIdToString(id) {
    return id ? id.toString() : '';
  },

  /**
   * 安全的数字转换
   * @param {*} value 值
   * @param {number} defaultValue 默认值
   * @returns {number} 数字
   */
  safeToNumber(value, defaultValue = 0) {
    const num = Number(value);
    return isNaN(num) ? defaultValue : num;
  },

  /**
   * 深拷贝对象
   * @param {object} obj 对象
   * @returns {object} 拷贝后的对象
   */
  deepClone(obj) {
    return JSON.parse(JSON.stringify(obj));
  },

  /**
   * 合并对象
   * @param {object} target 目标对象
   * @param {object} source 源对象
   * @returns {object} 合并后的对象
   */
  mergeObjects(target, source) {
    return Object.assign({}, target, source);
  }
};

/**
 * 图片处理工具
 */
const imageUtils = {
  /**
   * 获取默认头像
   * @param {string} avatar 头像URL
   * @returns {string} 头像URL
   */
  getDefaultAvatar(avatar) {
    return avatar || DEFAULTS.AVATAR;
  },

  /**
   * 预览图片
   * @param {string} url 图片URL
   * @param {Array} urls 图片URL数组
   */
  previewImage(url, urls = []) {
    wx.previewImage({
      current: url,
      urls: urls.length > 0 ? urls : [url]
    });
  }
};

/**
 * 存储工具
 */
const storageUtils = {
  /**
   * 设置存储
   * @param {string} key 键
   * @param {*} value 值
   */
  setStorage(key, value) {
    try {
      wx.setStorageSync(key, value);
    } catch (error) {
      console.error('设置存储失败:', error);
    }
  },

  /**
   * 获取存储
   * @param {string} key 键
   * @param {*} defaultValue 默认值
   * @returns {*} 值
   */
  getStorage(key, defaultValue = null) {
    try {
      return wx.getStorageSync(key) || defaultValue;
    } catch (error) {
      console.error('获取存储失败:', error);
      return defaultValue;
    }
  },

  /**
   * 移除存储
   * @param {string} key 键
   */
  removeStorage(key) {
    try {
      wx.removeStorageSync(key);
    } catch (error) {
      console.error('移除存储失败:', error);
    }
  }
};

/**
 * 防抖函数
 * @param {Function} func 函数
 * @param {number} delay 延迟时间
 * @returns {Function} 防抖后的函数
 */
function debounce(func, delay = 300) {
  let timer;
  return function (...args) {
    clearTimeout(timer);
    timer = setTimeout(() => func.apply(this, args), delay);
  };
}

/**
 * 节流函数
 * @param {Function} func 函数
 * @param {number} delay 延迟时间
 * @returns {Function} 节流后的函数
 */
function throttle(func, delay = 300) {
  let timer;
  return function (...args) {
    if (timer) return;
    timer = setTimeout(() => {
      func.apply(this, args);
      timer = null;
    }, delay);
  };
}

module.exports = {
  timeUtils,
  contentUtils,
  dataUtils,
  imageUtils,
  storageUtils,
  debounce,
  throttle
};
