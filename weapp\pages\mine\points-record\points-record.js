const { request } = require('../../../utils/request.js');
const pointsStore = require('../../../stores/pointsStore.js');

Page({
  data: {
    // 积分记录列表
    records: [],
    // 分页信息
    pagination: {
      current: 1,
      size: 20,
      total: 0
    },
    // 加载状态
    loading: false,
    // 是否还有更多数据
    hasMore: true,
    // 当前选中的类型
    selectedType: 'ALL',
    // 积分类型选项
    typeOptions: [
      { value: 'ALL', label: '全部' },
      { value: 'SIGNIN', label: '签到' },
      { value: 'EXCHANGE', label: '兑换' },
      { value: 'SHARE', label: '分享' },
      { value: 'INVITE', label: '邀请' },
      { value: 'ADMIN', label: '管理员操作' }
    ]
  },

  onLoad() {
    this.loadPointsRecords();
  },

  // 加载积分记录
  async loadPointsRecords(refresh = false) {
    if (this.data.loading) return;

    try {
      this.setData({ loading: true });

      const { current, size } = this.data.pagination;
      const { selectedType } = this.data;

      let result;
      if (selectedType === 'ALL') {
        result = await pointsStore.getPointsRecords(current, size);
      } else {
        result = await pointsStore.getPointsRecordsByType(selectedType, current, size);
      }

      if (result) {
        const records = result.records || result;
        const total = result.total || records.length;
        const newRecords = refresh ? records : [...this.data.records, ...records];
        const hasMore = newRecords.length < total;

        this.setData({
          records: newRecords,
          'pagination.total': total,
          hasMore,
          loading: false
        });
      } else {
        this.setData({ loading: false });
      }
    } catch (error) {
      console.error('加载积分记录失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },

  // 切换积分类型
  onTypeChange(e) {
    const index = e.detail.value;
    const selectedType = this.data.typeOptions[index].value;
    this.setData({
      selectedType,
      records: [],
      'pagination.current': 1,
      hasMore: true
    });
    this.loadPointsRecords(true);
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.setData({
      records: [],
      'pagination.current': 1,
      hasMore: true
    });
    this.loadPointsRecords(true).then(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 上拉加载更多
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.setData({
        'pagination.current': this.data.pagination.current + 1
      });
      this.loadPointsRecords();
    }
  },


}); 