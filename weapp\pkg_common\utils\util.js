const formatTime = date => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()

  return `${[year, month, day].map(formatNumber).join('/')} ${[hour, minute, second].map(formatNumber).join(':')}`
}
function buildQueryString(params) {
  return Object.keys(params)
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
    .join('&');
}
const formatNumber = n => {
  n = n.toString()
  return n[1] ? n : `0${n}`
}
function formatTimeAgo(dateString) {
  if (!dateString) return '';
  
  let date;
  if (typeof dateString === 'string') {
    if (dateString.includes('-')) {
      dateString = dateString.replace(/-/g, '/');
    }
    date = new Date(dateString);
    if (isNaN(date.getTime())) {
      console.warn('无效的日期格式:', dateString);
      return '未知时间';
    }
  } else {
    date = new Date(dateString);
  }
  
  const now = new Date();
  const diff = now - date;
  const minute = 60 * 1000;
  const hour = minute * 60;
  const day = hour * 24;
  const month = day * 30;
  const year = day * 365;

  if (diff < minute) {
    return '刚刚';
  } else if (diff < hour) {
    return Math.floor(diff / minute) + '分钟前';
  } else if (diff < day) {
    return Math.floor(diff / hour) + '小时前';
  } else if (diff < month) {
    return Math.floor(diff / day) + '天前';
  } else if (diff < year) {
    return Math.floor(diff / month) + '个月前';
  } else {
    return Math.floor(diff / year) + '年前';
  }
}
export function getStore(key) {
  try {
    return wx.getStorageSync(key);
  } catch (e) {
    return null;
  }
}
export function setStore(key, value) {
  try {
    wx.setStorageSync(key, value);
  } catch (e) {}
}
module.exports = {
  formatTime,
  buildQueryString,
  formatTimeAgo,
  getStore,
  setStore
}
