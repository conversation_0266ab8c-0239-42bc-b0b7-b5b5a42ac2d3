<swiper
  class="top-banner"
  indicator-dots
  autoplay
  circular
  style="height: {{height}}rpx;"
  indicator-color="rgba(255,255,255,0.5)"
  indicator-active-color="#fff"
>
  <swiper-item wx:for="{{list}}" wx:key="pageIndex" wx:for-item="item" wx:for-index="pageIndex">
    <view class="banner-item" bindtap="onBannerTap" data-banner="{{item}}">
      <image
        src="{{item.image || item}}"
        mode="aspectFill"
        class="banner-image"
      />
      <!-- 如果有标题和副标题，显示文字覆盖层 -->
      <view wx:if="{{item.title || item.subtitle}}" class="banner-overlay">
        <view class="banner-content">
          <text wx:if="{{item.title}}" class="banner-title">{{item.title}}</text>
          <text wx:if="{{item.subtitle}}" class="banner-subtitle">{{item.subtitle}}</text>
        </view>
      </view>
    </view>
  </swiper-item>
</swiper>
