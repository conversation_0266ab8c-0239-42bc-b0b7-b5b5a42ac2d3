/* 广播组件样式 */
.broadcast-container {
  margin: 20rpx;
  background: linear-gradient(135deg, #ff6b6b, #ff8585);
  border-radius: 24rpx;
  box-shadow: 0 8rpx 16rpx rgba(255, 107, 107, 0.2);
  overflow: hidden;
  position: relative;
  height: 100rpx; /* 固定高度，紧凑显示 */
}

.broadcast-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 25%, transparent 25%),
              linear-gradient(-45deg, rgba(255, 255, 255, 0.1) 25%, transparent 25%),
              linear-gradient(45deg, transparent 75%, rgba(255, 255, 255, 0.1) 75%),
              linear-gradient(-45deg, transparent 75%, rgba(255, 255, 255, 0.1) 75%);
  background-size: 20rpx 20rpx;
  background-position: 0 0, 0 10rpx, 10rpx -10rpx, -10rpx 0rpx;
  opacity: 0.3;
  pointer-events: none;
}

.broadcast-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 30rpx;
  position: relative;
  z-index: 1;
}

.broadcast-left {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  margin-right: 20rpx;
}

.broadcast-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.broadcast-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #fff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  white-space: nowrap;
}

.broadcast-swiper-container {
  flex: 1;
  height: 100%;
  min-width: 0;
}

.broadcast-more {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  transition: all 0.3s ease;
  flex-shrink: 0;
  margin-left: 20rpx;
}

.broadcast-more:active {
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

.more-text {
  font-size: 22rpx;
  color: #fff;
  margin-right: 6rpx;
  white-space: nowrap;
}

.more-arrow {
  font-size: 20rpx;
  color: #fff;
  transform: rotate(0deg);
  transition: transform 0.3s ease;
}

.broadcast-swiper {
  height: 100%;
  width: 100%;
}

.broadcast-item {
  display: flex;
  align-items: center;
  height: 100%;
  width: 100%;
}

.notice-content {
  flex: 1;
  min-width: 0;
  margin-right: 16rpx;
  overflow: hidden;
}

.notice-text {
  font-size: 26rpx;
  color: #fff;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
}

.notice-time {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
  white-space: nowrap;
  flex-shrink: 0;
}

/* 滚动动画效果 */
.broadcast-swiper {
  transition: all 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .broadcast-container {
    margin: 16rpx;
    height: 90rpx;
  }

  .broadcast-content {
    padding: 0 24rpx;
  }

  .broadcast-title {
    font-size: 24rpx;
  }

  .notice-text {
    font-size: 24rpx;
  }

  .notice-time {
    font-size: 20rpx;
  }

  .more-text {
    font-size: 20rpx;
  }
}
