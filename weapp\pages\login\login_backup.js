const { login, getUserInfo } = require('../../utils/auth');
const { request } = require('../../utils/request');

Page({
  data: {
    loading: false,
    checked: false,
    // 邀请码相关
    inviteCode: '',
    showInviteInput: false,
    inviteCodeValid: null,
    inviterInfo: null,
    // 静默邀请信息
    hasPendingInvite: false,
    pendingInviteInfo: null
  },

  onLoad(options) {
    // 检查是否已登录
    const userInfo = getUserInfo();
    if (userInfo) {
      this.navigateBack();
      return;
    }

    // 检查是否有邀请码参数
    if (options.inviteCode) {
      this.setData({
        inviteCode: options.inviteCode,
        showInviteInput: true,
        hasPendingInvite: true
      });
    }
  },

  // 处理勾选协议
  onCheckChange(e) {
    this.setData({ checked: e.detail.value.length > 0 });
  },

  // 跳转协议页面
  onProtocolTap(e) {
    const type = e.currentTarget.dataset.type;
    wx.navigateTo({
      url: `/pages/login/protocol/protocol?type=${type}`
    });
  },

  // 处理登录
  async handleLogin() {
    if (this.data.loading) return;
    if (!this.data.checked) {
      wx.showToast({
        title: '请先同意协议',
        icon: 'none'
      });
      return;
    }

    this.setData({ loading: true });

    try {
      // 执行登录
      const loginResult = await login();
      console.log('登录成功:', loginResult);

      wx.showToast({
        title: '登录成功',
        icon: 'success'
      });

      // 延迟返回
      setTimeout(() => {
        this.navigateAfterLogin();
      }, 1500);
      
    } catch (error) {
      console.error('登录失败：', error);
      wx.showToast({
        title: error.message || '登录失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 登录后导航
   */
  navigateAfterLogin() {
    const pages = getCurrentPages();
    const prevPage = pages[pages.length - 2];
    
    // 如果有上一页，则返回并刷新
    if (prevPage) {
      // 调用上一页的刷新方法
      if (typeof prevPage.onRefresh === 'function') {
        prevPage.onRefresh();
      } else if (typeof prevPage.onLoad === 'function') {
        prevPage.onLoad();
      } else if (typeof prevPage.onShow === 'function') {
        prevPage.onShow();
      }
      wx.navigateBack();
    } else {
      // 没有上一页则跳转到首页
      wx.reLaunch({
        url: '/pages/index/index'
      });
    }
  },

  // 返回上一页
  navigateBack() {
    const pages = getCurrentPages();
    if (pages.length > 1) {
      wx.navigateBack();
    } else {
      wx.reLaunch({
        url: '/pages/index/index'
      });
    }
  },

  // 切换邀请码输入显示
  toggleInviteInput() {
    this.setData({
      showInviteInput: !this.data.showInviteInput
    });
  },

  // 邀请码输入
  onInviteCodeInput(e) {
    const code = e.detail.value.toUpperCase();
    this.setData({ inviteCode: code });
    
    if (code.length >= 6) {
      this.validateInviteCode(code);
    } else {
      this.setData({
        inviteCodeValid: null,
        inviterInfo: null
      });
    }
  },

  /**
   * 验证邀请码
   */
  async validateInviteCode(code) {
    if (!code) {
      this.setData({
        inviteCodeValid: false,
        inviterInfo: null
      });
      return;
    }

    try {
      // 简单的格式验证
      if (code.length < 6 || code.length > 12) {
        this.setData({
          inviteCodeValid: false,
          inviterInfo: null
        });
        return;
      }

      this.setData({
        inviteCodeValid: true,
        inviterInfo: {
          inviterUserId: 'demo_user',
          rewardPoints: 10
        }
      });

      wx.showToast({
        title: '邀请码格式正确',
        icon: 'success'
      });
    } catch (error) {
      console.error('验证邀请码失败:', error);
      this.setData({
        inviteCodeValid: false,
        inviterInfo: null
      });
    }
  }
});
