/* 添加名片页面样式 */
.add-card-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
  max-width: 750rpx;
  margin: 0 auto;
  box-sizing: border-box;
}

/* 页面头部 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 32rpx;
  padding-top: calc(20rpx + env(safe-area-inset-top));
  background: transparent;
  position: relative;
  z-index: 100;
}

.header-back {
  position: absolute;
  left: 32rpx;
  top: calc(20rpx + env(safe-area-inset-top));
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  backdrop-filter: blur(10rpx);
}

.header-share {
  position: absolute;
  right: 32rpx;
  top: calc(20rpx + env(safe-area-inset-top));
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  backdrop-filter: blur(10rpx);
}

.back-icon, .share-icon {
  width: 40rpx;
  height: 40rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #fff;
  text-align: center;
}

/* 加载状态样式 */
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(10rpx);
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #FF7D7D;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #666;
  font-size: 28rpx;
}

/* 页面头部 */
.page-header {
  display: flex;
  align-items: center;
  padding: 40rpx 32rpx;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  margin: 24rpx 24rpx 0;
  border-radius: 24rpx;
}

.header-icon {
  font-size: 60rpx;
  margin-right: 24rpx;
}

.header-content {
  flex: 1;
}

.header-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 8rpx;
}

.header-subtitle {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

.form-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 750rpx;
  margin: 0 auto;
}

.form-scroll {
  flex: 1;
  padding: 0;
  padding-bottom: 200rpx; /* 为底部按钮留出空间 */
}

/* 表单区域 */
.form-section {
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx 32rpx;
  margin: 24rpx 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 125, 125, 0.08);
  border: 1rpx solid rgba(255, 125, 125, 0.1);
  position: relative;
  overflow: hidden;
}

.form-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #FF7D7D 0%, #FF9999 100%);
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.section-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #FF7D7D;
  margin-bottom: 40rpx;
  position: relative;
  padding-bottom: 16rpx;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 145rpx;
  height: 4rpx;
  background: linear-gradient(90deg, #FF7D7D 0%, #FF9999 100%);
  border-radius: 2rpx;
}

.form-item {
  margin-bottom: 40rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 16rpx;
  line-height: 1.4;
}

/* 输入框包装器 */
.input-wrapper {
  display: flex;
  align-items: flex-start;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  padding: 20rpx;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.input-wrapper:focus-within {
  border-color: #FF7D7D;
  background: #fff;
  box-shadow: 0 4rpx 16rpx rgba(255, 125, 125, 0.15);
  transform: translateY(-2rpx);
}

.input-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 20rpx;
  margin-top: 4rpx;
  flex-shrink: 0;
  opacity: 0.6;
}

/* 新增样式 */
.form-label-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.required-mark {
  color: #c32a2a;
  margin-left: 8rpx;
  font-size: 28rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
  line-height: 1.4;
}

.form-input {
  flex: 1;
  padding: 0;
  border: none;
  font-size: 30rpx;
  color: #333;
  background: transparent;
  box-sizing: border-box;
  line-height: 1.5;
  min-height: 44rpx;
  -webkit-appearance: none;
  appearance: none;
}

.form-input:focus {
  outline: none;
}

.form-input::placeholder {
  color: #adb5bd;
  font-size: 30rpx;
}

.form-textarea {
  flex: 1;
  padding: 0;
  border: none;
  font-size: 30rpx;
  color: #333;
  background: transparent;
  box-sizing: border-box;
  min-height: 120rpx;
  resize: none;
  line-height: 1.6;
  -webkit-appearance: none;
  appearance: none;
}

.form-textarea:focus {
  outline: none;
}

.form-textarea::placeholder {
  color: #adb5bd;
  font-size: 30rpx;
}

.form-picker {
  width: 100%;
  padding: 24rpx 20rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 16rpx;
  background: #fff;
  box-sizing: border-box;
  transition: all 0.3s ease;
  position: relative;
  min-height: 48rpx;
  -webkit-appearance: none;
  appearance: none;
  box-shadow: 0 2rpx 12rpx rgba(255, 125, 125, 0.05);
}

.form-picker:active {
  border-color: #FF7D7D;
  box-shadow: 0 0 0 4rpx rgba(255, 125, 125, 0.1);
  transform: translateY(-2rpx);
}

.picker-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.picker-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  flex: 1;
}

.picker-text:empty::before {
  content: '请选择';
  color: #999;
  font-size: 28rpx;
}

.picker-arrow {
  font-size: 32rpx;
  color: #999;
  font-weight: bold;
  transform: rotate(90deg);
  transition: transform 0.3s ease;
}

/* 隐私设置区域 */
.privacy-section {
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx 32rpx;
  margin: 0 32rpx 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 125, 125, 0.08);
  border: 1rpx solid rgba(255, 125, 125, 0.1);
  position: relative;
  overflow: hidden;
}

.privacy-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #FF7D7D 0%, #FF9999 100%);
}

.privacy-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0;
}

.privacy-left {
  display: flex;
  align-items: center;
}

.privacy-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 20rpx;
  opacity: 0.6;
}

.privacy-text {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.privacy-switch {
  transform: scale(1);
}

/* 底部操作区域 */
.footer-actions {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  max-width: 750rpx;
  padding: 32rpx;
  padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
  display: flex;
  gap: 32rpx;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20rpx);
  border-top: 1rpx solid rgba(255, 125, 125, 0.1);
  z-index: 100;
  box-shadow: 0 -8rpx 32rpx rgba(255, 125, 125, 0.08);
  box-sizing: border-box;
}

.btn-cancel, .btn-confirm {
  flex: 1;
  padding: 32rpx 24rpx;
  border-radius: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  line-height: 1.4;
  min-height: 52rpx;
  position: relative;
  overflow: hidden;
}

.btn-cancel {
  background: #fff;
  color: #FF7D7D;
  border: 3rpx solid #FF7D7D;
  box-shadow: 0 4rpx 16rpx rgba(255, 125, 125, 0.1);
}

.btn-cancel:active {
  background: rgba(255, 125, 125, 0.05);
  transform: scale(0.96);
}

.btn-confirm {
  background: linear-gradient(135deg, #FF7D7D 0%, #FF6B6B 100%);
  color: #fff;
  box-shadow: 0 8rpx 24rpx rgba(255, 125, 125, 0.4);
}

.btn-confirm:active {
  transform: scale(0.96);
  box-shadow: 0 4rpx 16rpx rgba(255, 125, 125, 0.5);
}

.btn-confirm:disabled {
  background: #d6d8db;
  color: #6c757d;
  box-shadow: none;
  transform: none;
}

/* 底部安全距离 */
.bottom-safe-area {
  height: 200rpx;
}

/* 表单滚动区域 */
.form-scroll {
  flex: 1;
  padding-top: 24rpx;
}

/* 响应式优化 */
@media (max-width: 375px) {
  .form-section {
    margin: 20rpx 24rpx;
    padding: 32rpx 24rpx;
  }

  .privacy-section {
    margin: 0 24rpx 20rpx;
    padding: 32rpx 24rpx;
  }

  .footer-actions {
    padding: 24rpx;
    gap: 24rpx;
  }

  .page-header {
    padding: 16rpx 24rpx;
    padding-top: calc(16rpx + env(safe-area-inset-top));
  }

  .header-back {
    left: 24rpx;
    top: calc(16rpx + env(safe-area-inset-top));
  }

  .header-share {
    right: 24rpx;
    top: calc(16rpx + env(safe-area-inset-top));
  }
}

/* 输入框悬停效果增强 */
.input-wrapper:hover {
  border-color: rgba(255, 125, 125, 0.4);
  box-shadow: 0 6rpx 20rpx rgba(255, 125, 125, 0.12);
}

/* 表单区域悬停效果 */
.form-section:hover {
  box-shadow: 0 12rpx 40rpx rgba(255, 125, 125, 0.12);
  transform: translateY(-4rpx);
}

.privacy-section:hover {
  box-shadow: 0 12rpx 40rpx rgba(255, 125, 125, 0.12);
  transform: translateY(-4rpx);
}

/* 主题色装饰元素 */
.form-section:hover {
  box-shadow: 0 12rpx 40rpx rgba(255, 125, 125, 0.12);
  transform: translateY(-2rpx);
}

/* 表单标签增强 */
.form-label {
  display: block;
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
  line-height: 1.4;
  position: relative;
}

/* 输入框悬停效果 */
.input-wrapper:hover {
  border-color: rgba(255, 125, 125, 0.3);
  box-shadow: 0 4rpx 16rpx rgba(255, 125, 125, 0.08);
}

/* 页面装饰元素 */
.add-card-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 0;
}

.form-container {
  position: relative;
  z-index: 1;
}