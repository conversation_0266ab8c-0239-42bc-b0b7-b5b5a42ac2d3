/* pages/payment/payment.wxss */
.payment-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 订单信息 */
.order-info {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.order-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.order-no {
  font-size: 24rpx;
  color: #666;
}

.order-detail {
  space-y: 20rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-size: 28rpx;
  color: #666;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
  text-align: right;
  flex: 1;
  margin-left: 20rpx;
}

.total-amount {
  padding-top: 20rpx;
  border-top: 1rpx solid #eee;
}

.total-amount .detail-value.amount {
  font-size: 36rpx;
  font-weight: 600;
  color: #ff4757;
}

/* 支付方式 */
.payment-methods {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
}

.methods-list {
  space-y: 20rpx;
}

.method-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-radius: 12rpx;
  border: 2rpx solid #eee;
  margin-bottom: 20rpx;
  transition: all 0.3s ease;
}

.method-item:last-child {
  margin-bottom: 0;
}

.method-item.selected {
  border-color: #007aff;
  background-color: #f0f8ff;
}

.method-item.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.method-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.method-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
}

.method-info {
  display: flex;
  flex-direction: column;
}

.method-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.method-desc {
  font-size: 24rpx;
  color: #666;
  margin-top: 4rpx;
}

.method-right {
  display: flex;
  align-items: center;
}

.radio {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.radio.checked {
  border-color: #007aff;
  background-color: #007aff;
}

.radio-inner {
  width: 20rpx;
  height: 20rpx;
  background-color: white;
  border-radius: 50%;
}

/* 优惠券部分 */
.discount-section {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.discount-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
}

.discount-left {
  flex: 1;
}

.discount-label {
  font-size: 28rpx;
  color: #333;
}

.discount-desc {
  font-size: 24rpx;
  color: #666;
  margin-top: 4rpx;
}

.discount-right {
  display: flex;
  align-items: center;
}

.discount-amount {
  font-size: 28rpx;
  color: #ff4757;
  margin-right: 10rpx;
}

.arrow {
  font-size: 24rpx;
  color: #ccc;
}

/* 协议部分 */
.agreement-section {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.agreement-item {
  display: flex;
  align-items: flex-start;
}

.checkbox {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #ddd;
  border-radius: 4rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  margin-top: 4rpx;
  transition: all 0.3s ease;
}

.checkbox.checked {
  border-color: #007aff;
  background-color: #007aff;
}

.checkbox-icon {
  font-size: 20rpx;
  color: white;
  font-weight: bold;
}

.agreement-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  flex: 1;
}

.agreement-text .link {
  color: #007aff;
  text-decoration: underline;
}

/* 支付底部 */
.payment-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 30rpx;
  border-top: 1rpx solid #eee;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.amount-summary {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 20rpx;
}

.summary-label {
  font-size: 28rpx;
  color: #666;
}

.summary-amount {
  font-size: 36rpx;
  font-weight: 600;
  color: #ff4757;
  margin-left: 10rpx;
}

.pay-button {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #007aff, #0056cc);
  color: white;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.pay-button.disabled {
  background: #ccc;
  color: #999;
}

.pay-button:not(.disabled):active {
  transform: scale(0.98);
}

/* 支付结果弹窗 */
.payment-result-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.payment-result-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background: white;
  border-radius: 20rpx;
  padding: 60rpx 40rpx 40rpx;
  margin: 40rpx;
  text-align: center;
  position: relative;
  z-index: 1;
  max-width: 600rpx;
  width: 100%;
}

.result-icon {
  margin-bottom: 30rpx;
}

.result-icon .icon {
  display: inline-block;
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  font-size: 60rpx;
  line-height: 120rpx;
  color: white;
  font-weight: bold;
}

.result-icon .icon.success {
  background: #34c759;
}

.result-icon .icon.error {
  background: #ff3b30;
}

.result-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.result-message {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.result-actions {
  display: flex;
  gap: 20rpx;
}

.action-button {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 30rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-button.primary {
  background: #007aff;
  color: white;
}

.action-button.secondary {
  background: #f5f5f5;
  color: #333;
}

/* 加载遮罩 */
.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.loading-mask.show {
  opacity: 1;
  visibility: visible;
}

.loading-content {
  background: white;
  border-radius: 16rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  min-width: 300rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}
