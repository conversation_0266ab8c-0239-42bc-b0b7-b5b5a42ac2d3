// pages/recharge-history/recharge-history.js
const { request } = require('../../../../utils/request');

Page({
  data: {
    records: [],
    loading: false,
    hasMore: true,
    page: 1,
    size: 10,
    total: 0,
    refreshing: false
  },

  onLoad() {
    this.loadRechargeRecords();
  },

  onPullDownRefresh() {
    this.refreshRecords();
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreRecords();
    }
  },

  /**
   * 加载充值记录
   */
  async loadRechargeRecords(page = 1) {
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      const result = await request({
        url: `/blade-chat/user/recharge/records?page=${page}&size=${this.data.size}`,
        method: 'GET'
      });

      const records = result.records || [];
      const total = result.total || 0;
      const hasMore = page * this.data.size < total;

      if (page === 1) {
        // 首次加载或刷新
        this.setData({
          records: records,
          total: total,
          hasMore: hasMore,
          page: page
        });
      } else {
        // 加载更多
        this.setData({
          records: [...this.data.records, ...records],
          hasMore: hasMore,
          page: page
        });
      }

    } catch (error) {
      console.error('加载充值记录失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false, refreshing: false });
      wx.stopPullDownRefresh();
    }
  },

  /**
   * 刷新记录
   */
  async refreshRecords() {
    this.setData({ refreshing: true });
    await this.loadRechargeRecords(1);
  },

  /**
   * 加载更多记录
   */
  async loadMoreRecords() {
    const nextPage = this.data.page + 1;
    await this.loadRechargeRecords(nextPage);
  },

  /**
   * 查看订单详情
   */
  viewOrderDetail(e) {
    const orderNo = e.currentTarget.dataset.orderNo;
    wx.navigateTo({
      url: `/pages/order-detail/order-detail?orderNo=${orderNo}`
    });
  },

  /**
   * 重新支付
   */
  async retryPayment(e) {
    const orderNo = e.currentTarget.dataset.orderNo;
    
    wx.showModal({
      title: '确认重新支付',
      content: '是否重新发起支付？',
      success: async (res) => {
        if (res.confirm) {
          try {
            // 这里可以调用重新支付的接口
            wx.showToast({
              title: '功能开发中',
              icon: 'none'
            });
          } catch (error) {
            wx.showToast({
              title: '操作失败',
              icon: 'error'
            });
          }
        }
      }
    });
  },

  /**
   * 格式化时间
   */
  formatTime(timeStr) {
    if (!timeStr) return '';
    const date = new Date(timeStr);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) { // 1分钟内
      return '刚刚';
    } else if (diff < 3600000) { // 1小时内
      return Math.floor(diff / 60000) + '分钟前';
    } else if (diff < 86400000) { // 1天内
      return Math.floor(diff / 3600000) + '小时前';
    } else {
      return date.toLocaleDateString() + ' ' + date.toLocaleTimeString().slice(0, 5);
    }
  },

  /**
   * 获取状态文本
   */
  getStatusText(orderStatus, paymentStatus) {
    if (paymentStatus === 'UNPAID') {
      return '待支付';
    } else if (paymentStatus === 'PAID' && orderStatus === 'SUCCESS') {
      return '充值成功';
    } else if (paymentStatus === 'PAID' && orderStatus === 'PENDING') {
      return '处理中';
    } else if (orderStatus === 'FAILED') {
      return '充值失败';
    } else if (paymentStatus === 'REFUNDED') {
      return '已退款';
    } else {
      return '未知状态';
    }
  },

  /**
   * 获取状态样式类
   */
  getStatusClass(orderStatus, paymentStatus) {
    if (paymentStatus === 'UNPAID') {
      return 'status-pending';
    } else if (paymentStatus === 'PAID' && orderStatus === 'SUCCESS') {
      return 'status-success';
    } else if (paymentStatus === 'PAID' && orderStatus === 'PENDING') {
      return 'status-processing';
    } else if (orderStatus === 'FAILED') {
      return 'status-failed';
    } else if (paymentStatus === 'REFUNDED') {
      return 'status-refunded';
    } else {
      return 'status-unknown';
    }
  }
});
