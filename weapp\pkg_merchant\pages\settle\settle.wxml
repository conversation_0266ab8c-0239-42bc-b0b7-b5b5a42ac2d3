<!-- 商家入驻页面 -->
<view class="settle-container">
  <!-- 基础信息 -->
  <view class="section-title">基础信息</view>
  <logo-uploader bind:change="onLogoChange" logo="{{form.logo}}" />
  <view class="form-group">
    <view class="label">商家名称 <text class="required">*</text></view>
    <input class="input" placeholder="请输入商家名称" value="{{form.name}}" bindinput="onInputName" />
  </view>
  <view class="form-group">
    <view class="label">商家类型</view>
    <picker class="picker" range="{{categoryList}}" value="{{form.categoryIndex}}" bindchange="onCategoryChange">
      <view class="picker-value">{{form.categoryName || '请选择商家类型'}}</view>
    </picker>
  </view>
  <view class="form-group">
    <view class="label">商家简介</view>
    <textarea class="textarea" maxlength="200" placeholder="请输入商家简介（200字以内）" value="{{form.intro}}" bindinput="onInputIntro" />
    <view class="char-count">{{form.intro.length || 0}}/200</view>
  </view>

  <!-- 联系信息 -->
  <view class="section-title">联系信息</view>
  <view class="form-group">
    <view class="label">联系人姓名 <text class="required">*</text></view>
    <input class="input" placeholder="请输入联系人姓名" value="{{form.contactName}}" bindinput="onInputContactName" />
  </view>
  <view class="form-group">
    <view class="label">联系电话 <text class="required">*</text></view>
    <view class="phone-row">
      <input class="input" placeholder="请输入联系电话" value="{{form.contactPhone}}" bindinput="onInputContactPhone" />
      <button class="code-btn" bindtap="onGetCode">获取验证码</button>
    </view>
  </view>
  <view class="form-group">
    <view class="label">验证码</view>
    <input class="input" placeholder="请输入验证码" value="{{form.code}}" bindinput="onInputCode" />
  </view>

  <!-- 经营信息 -->
  <view class="section-title">经营信息</view>
  <view class="form-group">
    <view class="label">营业执照号码</view>
    <input class="input" placeholder="请输入营业执照号码" value="{{form.licenseNo}}" bindinput="onInputLicenseNo" />
  </view>
  <license-uploader bind:change="onLicenseChange" license="{{form.licenseImg}}" />

  <!-- 地理位置 -->
  <view class="section-title">地理位置</view>
  <address-picker bind:change="onAddressChange" address="{{form.address}}" />

  <!-- 线上展示 -->
  <view class="section-title">线上展示</view>
  <album-uploader bind:change="onAlbumChange" album="{{form.album}}" />

  <!-- 主推商品/服务 -->
  <view class="section-title">主推商品/服务</view>
  <product-list bind:change="onProductListChange" products="{{form.products}}" />

  <!-- 提交按钮 -->
  <button class="submit-btn" bindtap="onSubmit">提交审核</button>
</view> 