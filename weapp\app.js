// app.js
const { checkLoginStatus, refreshToken } = require('./utils/auth');
const { handleLoginExpired } = require('./utils/loginHandler');
const { getStore, setStore } = require('./utils/util');
const { request } = require('./utils/request');
const apiManager = require('./services/apiManager');
const InviteStore = require('./stores/inviteStore');
const inviteManager = require('./utils/inviteManager');

App({
  globalData: {
    userInfo: null,
    baseUrl: 'http://192.168.31.215/api', // 添加基础API地址
    refreshLock: false,
    refreshInterval: null,
    
    tokenTime: 2592000, // token 有效期（秒），可根据实际情况调整
    request: request, // 添加全局request方法
    api: apiManager, // 添加API管理器
    isLoginExpiredHandling: false, // 防止重复处理登录过期
  },
  
  onLaunch(options) {
    console.log('小程序启动参数:', options);

    // 处理邀请参数（扫码或链接进入）
    this.handleInviteParams(options);

    // 初始化云开发环境
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力');
    } else {
      wx.cloud.init({
        env: 'cloud1-0gjj2zyo3acba1ed', // 你的云开发环境ID
        traceUser: true
      });
    }

    // 获取用户地理位置信息并保存到缓存
    wx.getLocation({
      type: 'wgs84',
      success(res) {
        const location = {
          latitude: res.latitude,
          longitude: res.longitude
        };
        setStore('userLocation', location);
        console.log('已保存用户位置:', location);
      },
      fail(err) {
        console.error('获取位置失败:', err);
        setStore('userLocation', {
          latitude: 0,
          longitude: 0
        });
      }
    });

    // 启动token自动刷新
    this.startTokenRefresh();

    // 监听网络状态变化
    this.setupNetworkListener();
  },

  // 检查是否通过邀请进入
  checkInviteEntry(options) {
    const { inviteCode, from } = options;

    if (inviteCode && from) {
      // 存储邀请信息到本地
      wx.setStorageSync('inviteInfo', {
        inviteCode,
        inviterUserId: from,
        entryTime: Date.now()
      });

      return {
        hasInvite: true,
        inviteCode,
        inviterUserId: from
      };
    }

    return {
      hasInvite: false
    };
  },

  // 处理邀请参数（静默处理）
  handleInviteParams(options) {
    try {
      console.log('处理邀请参数:', options);

      // 检查是否有邀请码参数
      if (options.inviteCode) {
        const inviteInfo = {
          inviteCode: options.inviteCode,
          inviterUserId: options.from || options.inviterUserId,
          source: 'qrcode_scan',
          scene: options.scene,
          path: options.path,
          timestamp: Date.now()
        };

        // 静默保存邀请信息
        const saved = inviteManager.saveInviteInfo(inviteInfo);

        if (saved) {
          console.log('邀请信息已静默保存，等待用户登录后自动处理:', inviteInfo);

          // 静默处理，不显示提示，避免干扰用户体验
          // 用户在登录页面会看到静默提示
        }
      }

      // 兼容其他邀请参数格式
      const legacyInvite = this.checkInviteEntry(options);
      if (legacyInvite.hasInvite && !options.inviteCode) {
        const inviteInfo = {
          inviteCode: legacyInvite.inviteCode,
          inviterUserId: legacyInvite.inviterUserId,
          source: 'legacy_format',
          scene: options.scene,
          path: options.path,
          timestamp: Date.now()
        };

        inviteManager.saveInviteInfo(inviteInfo);
        console.log('兼容格式邀请信息已保存:', inviteInfo);
      }

    } catch (error) {
      console.error('处理邀请参数失败:', error);
    }
  },

  // 兼容旧的邀请检查方法
  checkInviteEntry(options) {
    const { inviteCode, from } = options;

    if (inviteCode && from) {
      return {
        hasInvite: true,
        inviteCode,
        inviterUserId: from
      };
    }

    return {
      hasInvite: false
    };
  },

  // 处理用户登录成功后的邀请逻辑
  async handleUserLoginSuccess(userInfo) {
    try {
      console.log('用户登录成功，检查邀请信息');

      const result = await inviteManager.processInviteRegistration(userInfo.id);

      if (result.success && result.hasInvite) {
        console.log('邀请注册处理成功:', result);

        // 显示成功提示
        setTimeout(() => {
          inviteManager.showInviteSuccessMessage(result);
        }, 1500);

        return result;
      } else if (result.hasInvite && !result.success) {
        console.log('邀请注册处理失败:', result.message);

        // 显示失败提示
        setTimeout(() => {
          wx.showToast({
            title: result.message || '邀请处理失败',
            icon: 'none',
            duration: 3000
          });
        }, 1000);
      }

      return result;
    } catch (error) {
      console.error('处理用户登录后的邀请逻辑失败:', error);
      return { success: false, hasInvite: false };
    }
  },

  // 旧的处理方法（保持兼容性）
  handleInviteParamsOld(options) {
    try {
      const inviteResult = this.checkInviteEntry(options);
      if (inviteResult.hasInvite) {
        console.log('检测到邀请参数:', inviteResult);

        // 显示邀请提示
        setTimeout(() => {
          wx.showModal({
            title: '邀请提示',
            content: '您是通过好友邀请进入的，注册后邀请人将获得积分奖励！',
            confirmText: '立即注册',
            cancelText: '稍后再说',
            success: (res) => {
              if (res.confirm) {
                // 跳转到登录/注册页面
                wx.navigateTo({
                  url: '/pages/login/login?invite=true'
                });
              }
            }
          });
        }, 1000);
      }
    } catch (error) {
      console.error('处理邀请参数失败:', error);
    }
  },

  // 小程序显示时处理邀请参数
  onShow(options) {
    // 处理从分享进入的邀请参数
    if (options && (options.inviteCode || options.from)) {
      this.handleInviteParams(options);
    }
  },

  // 启动token自动刷新
  startTokenRefresh() {
    // 清除之前的定时器
    if (this.globalData.refreshInterval) {
      clearInterval(this.globalData.refreshInterval);
    }
    
    this.globalData.refreshInterval = setInterval(() => {
      const token = getStore('token') || {};
      const refresh_token = getStore('refreshToken');
      
      if (!refresh_token || !token.value) {
        return;
      }
      
      const now = Date.now();
      const tokenTime = this.globalData.tokenTime * 1000;
      
      // 检查token是否即将过期（提前5分钟刷新）
      if (now - token.datetime >= (tokenTime - 5 * 60 * 1000) && !this.globalData.refreshLock) {
        this.globalData.refreshLock = true;
        console.log('开始自动刷新token...');
        
        refreshToken(refresh_token)
          .then(res => {
            console.log('token自动刷新成功');
            setStore('token', {
              value: res.data.accessToken,
              datetime: Date.now()
            });
            setStore('refresh_token', res.data.refreshToken);
            this.globalData.refreshLock = false;
          })
          .catch(err => {
            console.error('自动刷新token失败', err);
            this.globalData.refreshLock = false;
            
            // 如果刷新失败，可能是登录过期，触发登录过期处理
            if (!this.globalData.isLoginExpiredHandling) {
              this.globalData.isLoginExpiredHandling = true;
              handleLoginExpired().finally(() => {
                this.globalData.isLoginExpiredHandling = false;
              });
            }
          });
      }
    }, 1000 * 60 * 5); // 每5分钟检查一次
  },
  
  // 设置网络状态监听
  setupNetworkListener() {
    wx.onNetworkStatusChange((res) => {
      console.log('网络状态变化:', res);
      if (res.isConnected) {
        // 网络恢复时，检查登录状态
        if (checkLoginStatus()) {
          console.log('网络恢复，检查登录状态正常');
        } else {
          console.log('网络恢复，但登录状态异常');
        }
      } else {
        console.log('网络断开');
      }
    });
  },
  
  // 全局错误处理
  onError(error) {
    console.error('小程序全局错误:', error);
  },
  
  // 小程序显示时
  onShow() {
    // 检查登录状态
    if (checkLoginStatus()) {
      console.log('小程序显示，登录状态正常');
    } else {
      console.log('小程序显示，未登录状态');
    }
  },
  
  // 小程序隐藏时
  onHide() {
    console.log('小程序隐藏');
  }
});
