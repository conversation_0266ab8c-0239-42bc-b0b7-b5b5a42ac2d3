<view class="custom-tabbar {{show ? 'show' : 'hide'}} {{isNavigating ? 'navigating' : ''}}">
  <block wx:for="{{tabList}}" wx:key="pagePath" wx:for-index="index">
    <!-- 发布按钮特殊样式 -->
    <view wx:if="{{item.isPublish}}"
          class="tabbar-publish {{currentIndex === index ? 'active' : ''}} {{isNavigating && targetIndex === index ? 'loading' : ''}}"
          bindtap="onTabClick"
          data-index="{{index}}">
      <image wx:if="{{!(isNavigating && targetIndex === index)}}" src="{{item.iconPath}}" class="tabbar-publish-icon"/>
      <view wx:else class="mini-loading">
        <view class="mini-spinner"></view>
      </view>
    </view>

    <!-- 普通tab项 -->
    <view wx:else
          class="tabbar-item {{currentIndex === index ? 'active' : ''}} {{isNavigating && targetIndex === index ? 'loading' : ''}}"
          bindtap="onTabClick"
          data-index="{{index}}">
      <image wx:if="{{!(isNavigating && targetIndex === index)}}"
             src="{{currentIndex === index ? (item.selectedIconPath || item.iconPath) : item.iconPath}}"
             class="tabbar-icon"/>
      <view wx:else class="mini-loading">
        <view class="mini-spinner"></view>
      </view>
      <text class="tabbar-text">{{item.text}}</text>
      <view class="tab-indicator"></view>
    </view>
  </block>
</view>