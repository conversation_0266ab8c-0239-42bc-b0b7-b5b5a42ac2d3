/**
 * API管理器 - 统一管理所有API服务
 */
const commentService = require('./commentService');
const postService = require('./postService');
const regionService = require('./regionService');
const config = require('../config/api');

class ApiManager {
  constructor() {
    this.comment = commentService;
    this.post = postService;
    this.region = regionService;
    this.config = config;
  }

  /**
   * 获取API配置
   * @returns {Object} API配置对象
   */
  getConfig() {
    return this.config;
  }

  /**
   * 获取评论服务
   * @returns {Object} 评论服务实例
   */
  getCommentService() {
    return this.comment;
  }

  /**
   * 获取帖子服务
   * @returns {Object} 帖子服务实例
   */
  getPostService() {
    return this.post;
  }

  /**
   * 获取地区服务
   * @returns {Object} 地区服务实例
   */
  getRegionService() {
    return this.region;
  }

  /**
   * 统一错误处理
   * @param {Error} error 错误对象
   * @param {string} operation 操作名称
   */
  handleError(error, operation = '操作') {
    console.error(`${operation}失败:`, error);
    
    let message = '网络错误，请稍后重试';
    
    if (error.response) {
      // 服务器响应错误
      const { status, data } = error.response;
      if (status === 401) {
        message = '登录已过期，请重新登录';
        // 可以在这里触发重新登录逻辑
      } else if (status === 403) {
        message = '没有权限执行此操作';
      } else if (status === 404) {
        message = '请求的资源不存在';
      } else if (data && data.msg) {
        message = data.msg;
      }
    } else if (error.message) {
      message = error.message;
    }

    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });

    return { success: false, message };
  }

  /**
   * 显示加载状态
   * @param {string} title 加载提示文字
   */
  showLoading(title = '加载中...') {
    wx.showLoading({ title, mask: true });
  }

  /**
   * 隐藏加载状态
   */
  hideLoading() {
    wx.hideLoading();
  }

  /**
   * 显示成功提示
   * @param {string} title 提示文字
   */
  showSuccess(title = '操作成功') {
    wx.showToast({
      title,
      icon: 'success',
      duration: 1500
    });
  }

  /**
   * 显示错误提示
   * @param {string} title 提示文字
   */
  showError(title = '操作失败') {
    wx.showToast({
      title,
      icon: 'none',
      duration: 2000
    });
  }

  /**
   * 确认对话框
   * @param {string} content 确认内容
   * @param {string} title 标题
   * @returns {Promise<boolean>} 用户是否确认
   */
  confirm(content, title = '提示') {
    return new Promise((resolve) => {
      wx.showModal({
        title,
        content,
        success: (res) => {
          resolve(res.confirm);
        },
        fail: () => {
          resolve(false);
        }
      });
    });
  }

  /**
   * 检查网络状态
   * @returns {Promise<boolean>} 网络是否可用
   */
  checkNetwork() {
    return new Promise((resolve) => {
      wx.getNetworkType({
        success: (res) => {
          const networkType = res.networkType;
          if (networkType === 'none') {
            this.showError('网络不可用，请检查网络设置');
            resolve(false);
          } else {
            resolve(true);
          }
        },
        fail: () => {
          this.showError('网络检查失败');
          resolve(false);
        }
      });
    });
  }

  /**
   * 防抖函数
   * @param {Function} func 要防抖的函数
   * @param {number} delay 延迟时间（毫秒）
   * @returns {Function} 防抖后的函数
   */
  debounce(func, delay = 300) {
    let timeoutId;
    return function (...args) {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
  }

  /**
   * 节流函数
   * @param {Function} func 要节流的函数
   * @param {number} delay 延迟时间（毫秒）
   * @returns {Function} 节流后的函数
   */
  throttle(func, delay = 300) {
    let lastTime = 0;
    return function (...args) {
      const now = Date.now();
      if (now - lastTime >= delay) {
        lastTime = now;
        func.apply(this, args);
      }
    };
  }
}

// 创建单例实例
const apiManager = new ApiManager();

module.exports = apiManager;
