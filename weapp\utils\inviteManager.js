/**
 * 邀请管理工具
 * 处理邀请码的存储、验证和使用
 */

const { request } = require('./request');

class InviteManager {
  constructor() {
    this.STORAGE_KEY = 'pendingInvite';
    this.INVITE_EXPIRE_TIME = 7 * 24 * 60 * 60 * 1000; // 7天过期
  }

  /**
   * 保存邀请信息（扫码或链接进入时）
   * @param {Object} inviteInfo 邀请信息
   */
  saveInviteInfo(inviteInfo) {
    try {
      const data = {
        ...inviteInfo,
        saveTime: Date.now(),
        expireTime: Date.now() + this.INVITE_EXPIRE_TIME,
        used: false
      };

      wx.setStorageSync(this.STORAGE_KEY, data);
      console.log('邀请信息已保存:', data);
      
      return true;
    } catch (error) {
      console.error('保存邀请信息失败:', error);
      return false;
    }
  }

  /**
   * 获取待处理的邀请信息
   * @returns {Object|null} 邀请信息
   */
  getPendingInvite() {
    try {
      const data = wx.getStorageSync(this.STORAGE_KEY);
      
      if (!data) {
        return null;
      }

      // 检查是否过期
      if (Date.now() > data.expireTime) {
        this.clearInviteInfo();
        console.log('邀请信息已过期，已清除');
        return null;
      }

      // 检查是否已使用
      if (data.used) {
        console.log('邀请信息已使用');
        return null;
      }

      return data;
    } catch (error) {
      console.error('获取邀请信息失败:', error);
      return null;
    }
  }

  /**
   * 标记邀请信息为已使用
   */
  markInviteAsUsed() {
    try {
      const data = wx.getStorageSync(this.STORAGE_KEY);
      if (data) {
        data.used = true;
        data.usedTime = Date.now();
        wx.setStorageSync(this.STORAGE_KEY, data);
        console.log('邀请信息已标记为已使用');
      }
    } catch (error) {
      console.error('标记邀请信息失败:', error);
    }
  }

  /**
   * 清除邀请信息
   */
  clearInviteInfo() {
    try {
      wx.removeStorageSync(this.STORAGE_KEY);
      console.log('邀请信息已清除');
    } catch (error) {
      console.error('清除邀请信息失败:', error);
    }
  }

  /**
   * 验证邀请码
   * @param {string} inviteCode 邀请码
   * @returns {Promise<Object>} 验证结果
   */
  async validateInviteCode(inviteCode) {
    try {
      const response = await request({
        url: `/blade-chat-open/qrcode/validate/${inviteCode}`,
        method: 'GET'
      });

      if (response.success && response.data) {
        return {
          valid: response.data.valid,
          message: response.data.message,
          inviterUserId: response.data.inviterUserId,
          rewardPoints: response.data.rewardPoints
        };
      } else {
        return {
          valid: false,
          message: response.msg || '验证失败'
        };
      }
    } catch (error) {
      console.error('验证邀请码失败:', error);
      return {
        valid: false,
        message: '网络错误，验证失败'
      };
    }
  }

  /**
   * 处理邀请注册（用户登录成功后调用）
   * @param {string} userId 新用户ID
   * @returns {Promise<Object>} 处理结果
   */
  async processInviteRegistration(userId) {
    try {
      const pendingInvite = this.getPendingInvite();
      
      if (!pendingInvite) {
        console.log('没有待处理的邀请信息');
        return { success: true, hasInvite: false };
      }

      console.log('开始处理邀请注册:', pendingInvite);

      // 验证邀请码是否仍然有效
      const validation = await this.validateInviteCode(pendingInvite.inviteCode);
      
      if (!validation.valid) {
        console.log('邀请码已失效:', validation.message);
        this.clearInviteInfo();
        return { 
          success: false, 
          hasInvite: true, 
          message: validation.message 
        };
      }

      // 使用邀请码
      const result = await this.useInviteCode(pendingInvite.inviteCode, userId);
      
      if (result.success) {
        // 标记为已使用
        this.markInviteAsUsed();
        
        console.log('邀请注册处理成功:', result);
        
        return {
          success: true,
          hasInvite: true,
          inviterUserId: pendingInvite.inviterUserId || validation.inviterUserId,
          rewardPoints: result.rewardPoints || validation.rewardPoints,
          message: '邀请注册成功'
        };
      } else {
        console.error('使用邀请码失败:', result.message);
        return {
          success: false,
          hasInvite: true,
          message: result.message || '邀请注册失败'
        };
      }

    } catch (error) {
      console.error('处理邀请注册失败:', error);
      return {
        success: false,
        hasInvite: true,
        message: '处理邀请注册时发生错误'
      };
    }
  }

  /**
   * 使用邀请码
   * @param {string} inviteCode 邀请码
   * @param {string} userId 新用户ID
   * @returns {Promise<Object>} 使用结果
   */
  async useInviteCode(inviteCode, userId) {
    try {
      const response = await request({
        url: '/blade-chat-open/invite/use',
        method: 'POST',
        data: {
          inviteCode: inviteCode,
          newUserId: userId,
          source: 'miniapp'
        }
      });

      if (response.success) {
        return {
          success: true,
          rewardPoints: response.data?.rewardPoints || 0,
          message: '邀请码使用成功'
        };
      } else {
        return {
          success: false,
          message: response.msg || '邀请码使用失败'
        };
      }
    } catch (error) {
      console.error('使用邀请码失败:', error);
      return {
        success: false,
        message: '网络错误，使用邀请码失败'
      };
    }
  }

  /**
   * 解析二维码或链接中的邀请信息
   * @param {string} url 二维码内容或链接
   * @returns {Object} 解析结果
   */
  parseInviteUrl(url) {
    try {
      // 处理小程序页面路径
      if (url.includes('pages/') && url.includes('inviteCode=')) {
        const match = url.match(/inviteCode=([A-Z0-9]+)/);
        if (match) {
          return {
            success: true,
            inviteCode: match[1],
            source: 'qrcode'
          };
        }
      }

      // 处理普通邀请码
      const codePattern = /^[A-Z0-9]{6,12}$/;
      if (codePattern.test(url.toUpperCase())) {
        return {
          success: true,
          inviteCode: url.toUpperCase(),
          source: 'direct'
        };
      }

      return {
        success: false,
        message: '无效的邀请信息'
      };
    } catch (error) {
      console.error('解析邀请信息失败:', error);
      return {
        success: false,
        message: '解析失败'
      };
    }
  }

  /**
   * 显示邀请成功提示
   * @param {Object} result 邀请处理结果
   */
  showInviteSuccessMessage(result) {
    if (result.success && result.hasInvite) {
      const rewardPoints = result.rewardPoints || 0;
      const message = rewardPoints > 0 
        ? `邀请注册成功！您和邀请人都获得了${rewardPoints}积分奖励` 
        : '邀请注册成功！';

      wx.showModal({
        title: '邀请成功',
        content: message,
        showCancel: false,
        confirmText: '知道了'
      });
    }
  }
}

// 创建单例实例
const inviteManager = new InviteManager();

module.exports = inviteManager;
