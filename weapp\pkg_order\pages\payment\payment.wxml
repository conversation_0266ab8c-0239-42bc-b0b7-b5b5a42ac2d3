<!--pages/payment/payment.wxml-->
<view class="payment-container">
  <!-- 订单信息 -->
  <view class="order-info">
    <view class="order-header">
      <text class="order-title">订单信息</text>
      <text class="order-no">订单号：{{orderInfo.orderNo}}</text>
    </view>
    
    <view class="order-detail">
      <view class="detail-item">
        <text class="detail-label">商品描述</text>
        <text class="detail-value">{{orderInfo.description}}</text>
      </view>
      
      <view class="detail-item">
        <text class="detail-label">业务类型</text>
        <text class="detail-value">{{businessTypeText}}</text>
      </view>
      
      <view class="detail-item total-amount">
        <text class="detail-label">支付金额</text>
        <text class="detail-value amount">¥{{orderInfo.amount}}</text>
      </view>
    </view>
  </view>

  <!-- 支付方式选择 -->
  <view class="payment-methods">
    <view class="section-title">选择支付方式</view>
    
    <view class="methods-list">
      <view 
        wx:for="{{paymentMethods}}" 
        wx:key="id"
        class="method-item {{selectedMethod === item.id ? 'selected' : ''}} {{!item.enabled ? 'disabled' : ''}}"
        bindtap="onSelectPaymentMethod"
        data-method="{{item.id}}"
      >
        <view class="method-left">
          <image class="method-icon" src="{{item.icon}}" mode="aspectFit"></image>
          <view class="method-info">
            <text class="method-name">{{item.name}}</text>
            <text class="method-desc">{{item.description}}</text>
          </view>
        </view>
        
        <view class="method-right">
          <view class="radio {{selectedMethod === item.id ? 'checked' : ''}}">
            <view class="radio-inner" wx:if="{{selectedMethod === item.id}}"></view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 优惠券/折扣信息 -->
  <view class="discount-section" wx:if="{{showDiscount}}">
    <view class="section-title">优惠信息</view>
    
    <view class="discount-item" bindtap="onSelectCoupon">
      <view class="discount-left">
        <text class="discount-label">优惠券</text>
        <text class="discount-desc" wx:if="{{selectedCoupon}}">{{selectedCoupon.name}}</text>
        <text class="discount-desc" wx:else>选择可用优惠券</text>
      </view>
      
      <view class="discount-right">
        <text class="discount-amount" wx:if="{{selectedCoupon}}">-¥{{selectedCoupon.amount}}</text>
        <text class="arrow">></text>
      </view>
    </view>
  </view>

  <!-- 支付协议 -->
  <view class="agreement-section">
    <view class="agreement-item" bindtap="onToggleAgreement">
      <view class="checkbox {{agreedToTerms ? 'checked' : ''}}">
        <text class="checkbox-icon" wx:if="{{agreedToTerms}}">✓</text>
      </view>
      <text class="agreement-text">
        我已阅读并同意
        <text class="link" bindtap="onViewAgreement" data-type="payment">《支付协议》</text>
        和
        <text class="link" bindtap="onViewAgreement" data-type="service">《服务条款》</text>
      </text>
    </view>
  </view>

  <!-- 支付按钮 -->
  <view class="payment-footer">
    <view class="amount-summary">
      <text class="summary-label">实付金额：</text>
      <text class="summary-amount">¥{{finalAmount}}</text>
    </view>
    
    <button 
      class="pay-button {{!canPay ? 'disabled' : ''}}"
      bindtap="onConfirmPayment"
      disabled="{{!canPay || isProcessing}}"
      loading="{{isProcessing}}"
    >
      {{isProcessing ? '处理中...' : '确认支付'}}
    </button>
  </view>
</view>

<!-- 支付结果弹窗 -->
<view class="payment-result-modal {{showResultModal ? 'show' : ''}}" wx:if="{{showResultModal}}">
  <view class="modal-mask" bindtap="onCloseResultModal"></view>
  <view class="modal-content">
    <view class="result-icon">
      <text class="icon {{paymentResult.success ? 'success' : 'error'}}">
        {{paymentResult.success ? '✓' : '✗'}}
      </text>
    </view>
    
    <view class="result-title">
      {{paymentResult.success ? '支付成功' : '支付失败'}}
    </view>
    
    <view class="result-message">
      {{paymentResult.message}}
    </view>
    
    <view class="result-actions">
      <button 
        class="action-button secondary" 
        bindtap="onCloseResultModal"
        wx:if="{{!paymentResult.success}}"
      >
        重新支付
      </button>
      
      <button 
        class="action-button primary" 
        bindtap="onBackToHome"
      >
        {{paymentResult.success ? '完成' : '返回'}}
      </button>
    </view>
  </view>
</view>

<!-- 加载中遮罩 -->
<view class="loading-mask {{showLoading ? 'show' : ''}}" wx:if="{{showLoading}}">
  <view class="loading-content">
    <view class="loading-spinner"></view>
    <text class="loading-text">{{loadingText}}</text>
  </view>
</view>
