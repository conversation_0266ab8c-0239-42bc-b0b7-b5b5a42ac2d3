/* 举报弹窗样式 */
.report-dialog-mask {
  position: fixed;
  left: 0; right: 0; top: 0; bottom: 0;
  background: rgba(0,0,0,0.35);
  z-index: 3000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.report-dialog {
  background: #fff;
  border-radius: 18rpx;
  padding: 40rpx 32rpx 32rpx 32rpx;
  min-width: 540rpx;
  max-width: 90vw;
  max-height: 80vh;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.12);
  display: flex;
  flex-direction: column;
  align-items: stretch;
}

.dialog-header {
  margin-bottom: 32rpx;
  text-align: center;
}

.dialog-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.dialog-subtitle {
  font-size: 26rpx;
  color: #666;
  display: block;
}

.dialog-content {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 32rpx;
}

.input-section {
  margin-bottom: 32rpx;
}

.input-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 16rpx;
  display: block;
}

.report-textarea {
  width: 100%;
  min-height: 120rpx;
  border-radius: 12rpx;
  border: 1rpx solid #eee;
  padding: 18rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  resize: none;
}

.char-count {
  font-size: 24rpx;
  color: #999;
  text-align: right;
  margin-top: 8rpx;
  display: block;
}

.image-section {
  margin-bottom: 24rpx;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 16rpx;
}

.image-item {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  overflow: hidden;
}

.image-item image {
  width: 100%;
  height: 100%;
}

.remove-btn {
  position: absolute;
  top: 4rpx;
  right: 4rpx;
  width: 32rpx;
  height: 32rpx;
  background: rgba(0,0,0,0.6);
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
}

.add-image-btn {
  width: 120rpx;
  height: 120rpx;
  border: 2rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 24rpx;
}

.add-icon {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 4rpx;
}

.add-text {
  font-size: 22rpx;
}

.dialog-actions {
  display: flex;
  gap: 24rpx;
  justify-content: center;
}

.btn-cancel {
  flex: 1;
  background: #f5f5f5;
  color: #666;
  border-radius: 12rpx;
  font-size: 30rpx;
  padding: 20rpx 0;
  border: none;
  outline: none;
}

.btn-submit {
  flex: 1;
  background: #ff6b6b;
  color: #fff;
  border-radius: 12rpx;
  font-size: 30rpx;
  font-weight: 500;
  padding: 20rpx 0;
  border: none;
  outline: none;
}

.btn-submit:disabled {
  background: #ccc;
  color: #999;
} 