.post-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
}

/* Category and Tags */
.category-tags {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 20rpx;
}

.category-name {
  padding: 6rpx 16rpx;
  background: rgba(0, 102, 255, 0.1);
  color: #0066ff;
  font-size: 24rpx;
  border-radius: 6rpx;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.tag-item {
  padding: 6rpx 16rpx;
  background: #f5f5f5;
  color: #666;
  font-size: 24rpx;
  border-radius: 6rpx;
}

.post-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 12rpx;
}

.icon-text {
  margin-left: 10rpx;
  font-size: 28rpx;
  color: #666;
}

.comment-icon {
  width: 32rpx;
  height: 32rpx;
}

.post-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.post-images {
  margin-bottom: 16rpx;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12rpx;
}

.grid-image {
  width: 100%;
  height: 200rpx;
  border-radius: 8rpx;
  object-fit: cover;
}

.location-info {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.location-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}

.location-text {
  font-size: 26rpx;
  color: #999;
}

.post-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 24rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid #eee;
}

.footer-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.footer-left text {
  flex: 1;
  font-size: 28rpx;
  color: #666;
}

.footer-left .icon {
  width: 32rpx;
  height: 32rpx;
}

.footer-right {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: flex-end;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  gap: 8rpx;
  font-size: 28rpx;
  color: #666;
}

.icon {
  width: 32rpx;
  height: 32rpx;
}

.post-item:active {
  background: #f9f9f9;
}

.post-header {
  margin-bottom: 16rpx;
  position: relative;
}

.user-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12rpx;
}

.avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 32rpx;
}

.nickname {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.post-time {
  font-size: 24rpx;
  color: #999;
}

/* 联系信息样式 */
.contact-info {
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 16rpx;
  margin-bottom: 20rpx;
}

.contact-item {
  display: flex;
  font-size: 26rpx;
  line-height: 1.8;
}

.contact-item .label {
  color: #666;
  margin-right: 8rpx;
}

.contact-item .value {
  color: #333;
  font-weight: 500;
}

/* 底部操作栏样式 */
.interaction-stats {
  display: flex;
  gap: 32rpx;
  flex: 1;
}

.stat-item.active {
  color: #4c6fff;
}

.stat-item.completed {
  color: #07c160;
}

.stat-item .count {
  color: #666;
}

.stat-item.active .count {
  color: #4c6fff;
}

/* 操作区域样式 */
.post-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16rpx;
}

.action-btn.cancel-btn,
.action-btn.cancel-like-btn {
  color: #999;
  background: #f5f5f5;
}

.delete-btn {
  color: #ff4d4f;
  background: rgba(255, 77, 79, 0.1);
}

.action-btn.completed-btn {
  color: #07c160;
  background: rgba(7, 193, 96, 0.1);
}

/* 分享按钮样式重置 */
.share-btn {
  margin: 0;
  padding: 0;
  line-height: 1;
  background: none;
  font-weight: normal;
  border: none;
  display: flex;
  align-items: center;
}

.share-btn::after {
  display: none;
}

.post-card {
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 24rpx rgba(0,0,0,0.06);
  margin: 24rpx 24rpx 0 24rpx;
  overflow: hidden;
  position: relative;
}
.post-image-wrap {
  position: relative;
  width: 100%;
  height: 320rpx;
  overflow: hidden;
}
.post-image {
  width: 100%;
  height: 320rpx;
  object-fit: cover;
  border-radius: 24rpx 24rpx 0 0;
}
.post-more-btn {
  position: absolute;
  top: 24rpx;
  right: 24rpx;
  background: rgba(0,0,0,0.5);
  color: #fff;
  border-radius: 50rpx;
  padding: 8rpx 24rpx;
  font-size: 32rpx;
  z-index: 2;
}
.post-content-wrap {
  position: relative;
  padding: 24rpx 24rpx 16rpx 24rpx;
}
/* 状态按钮 */
.meta-status {
  position: absolute;
  top: 28rpx;
  right: 25rpx;
  font-size: 24rpx;
  border-radius: 8rpx;
  padding: 2rpx 16rpx;
}
.meta-status.published {
  color: #52c41a;
  background: #eaffea;
}
.meta-status.draft {
  color: #faad14;
  background: #fffbe6;
}
.post-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #222;
  margin-bottom: 12rpx;
}
.post-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
}
.post-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 24rpx;
  color: #bbb;
  gap: 32rpx;
}
.meta-data {
  display: flex;
}
.meta-item {
  display: flex;
  align-items: center;
  margin-left: 40rpx;
}
.meta-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
}
