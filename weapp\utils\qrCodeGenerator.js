/**
 * 二维码生成工具
 * 支持生成邀请码二维码图片，包含用户信息和邀请码
 */

const inviteManager = require('./inviteManager');

class QRCodeGenerator {
  /**
   * 生成邀请码二维码
   * @param {Object} params 参数
   * @param {string} params.code 邀请码
   * @param {Object} params.userInfo 用户信息
   * @param {Object} params.options 生成选项
   * @returns {Promise} 生成结果
   */
  static async generateInvitationQRCode(params) {
    const {
      code,
      userInfo = {},
      options = {}
    } = params;

    try {
      // 构建二维码数据
      const qrData = this.generateQRCodeData(code, userInfo);
      const qrDataString = JSON.stringify(qrData);

      // 生成二维码图片
      const qrResult = await this.createQRCodeImage({
        data: qrDataString,
        size: options.size || 200,
        margin: options.margin || 10,
        colorDark: options.colorDark || '#000000',
        colorLight: options.colorLight || '#ffffff'
      });

      if (qrResult.success) {
        // 生成带装饰的邀请卡片
        const cardResult = await this.createInvitationCard({
          qrImagePath: qrResult.imagePath,
          code: code,
          userInfo: userInfo,
          options: options
        });

        return {
          success: true,
          data: {
            qrImagePath: qrResult.imagePath,
            cardImagePath: cardResult.success ? cardResult.imagePath : null,
            qrData: qrDataString,
            shareInfo: this.generateShareInfo(code, userInfo)
          }
        };
      } else {
        throw new Error(qrResult.message || '二维码生成失败');
      }
    } catch (error) {
      console.error('生成邀请码二维码失败:', error);
      return {
        success: false,
        message: error.message || '二维码生成失败'
      };
    }
  }

  /**
   * 创建二维码图片
   * @param {Object} params 参数
   * @returns {Promise} 生成结果
   */
  static async createQRCodeImage(params) {
    const {
      data,
      size = 200,
      margin = 10,
      colorDark = '#000000',
      colorLight = '#ffffff'
    } = params;

    return new Promise((resolve) => {
      // 使用小程序的二维码生成API
      wx.createCanvasContext('qrCodeCanvas', this);
      
      // 这里需要使用第三方二维码生成库，如 qrcode.js
      // 由于小程序环境限制，我们提供一个模拟实现
      const canvas = wx.createCanvasContext('qrCodeCanvas');
      
      // 设置画布大小
      canvas.width = size + margin * 2;
      canvas.height = size + margin * 2;
      
      // 绘制背景
      canvas.setFillStyle(colorLight);
      canvas.fillRect(0, 0, canvas.width, canvas.height);
      
      // 这里应该调用实际的二维码生成逻辑
      // 为了演示，我们创建一个简单的占位图
      canvas.setFillStyle(colorDark);
      canvas.fillRect(margin, margin, size, size);
      
      canvas.draw(false, () => {
        // 保存图片到临时文件
        wx.canvasToTempFilePath({
          canvasId: 'qrCodeCanvas',
          success: (res) => {
            resolve({
              success: true,
              imagePath: res.tempFilePath
            });
          },
          fail: (error) => {
            resolve({
              success: false,
              message: '保存二维码图片失败'
            });
          }
        });
      });
    });
  }

  /**
   * 创建邀请卡片
   * @param {Object} params 参数
   * @returns {Promise} 生成结果
   */
  static async createInvitationCard(params) {
    const {
      qrImagePath,
      code,
      userInfo = {},
      options = {}
    } = params;

    try {
      const canvas = wx.createCanvasContext('invitationCardCanvas');
      
      // 卡片尺寸
      const cardWidth = options.cardWidth || 375;
      const cardHeight = options.cardHeight || 500;
      
      // 设置画布大小
      canvas.width = cardWidth;
      canvas.height = cardHeight;
      
      // 绘制背景渐变
      const gradient = canvas.createLinearGradient(0, 0, 0, cardHeight);
      gradient.addColorStop(0, '#667eea');
      gradient.addColorStop(1, '#764ba2');
      canvas.setFillStyle(gradient);
      canvas.fillRect(0, 0, cardWidth, cardHeight);
      
      // 绘制标题
      canvas.setFillStyle('#ffffff');
      canvas.setFontSize(24);
      canvas.setTextAlign('center');
      canvas.fillText('邀请你加入易找易发', cardWidth / 2, 60);
      
      // 绘制用户信息
      if (userInfo.nickname) {
        canvas.setFontSize(18);
        canvas.fillText(`${userInfo.nickname} 邀请你`, cardWidth / 2, 100);
      }
      
      // 绘制邀请码
      canvas.setFontSize(20);
      canvas.setFillStyle('#ffeb3b');
      canvas.fillText(`邀请码: ${code}`, cardWidth / 2, 140);
      
      // 绘制二维码（这里需要实际加载二维码图片）
      const qrSize = 160;
      const qrX = (cardWidth - qrSize) / 2;
      const qrY = 180;
      
      // 绘制二维码背景
      canvas.setFillStyle('#ffffff');
      canvas.fillRect(qrX - 10, qrY - 10, qrSize + 20, qrSize + 20);
      
      // 这里应该绘制实际的二维码图片
      // canvas.drawImage(qrImagePath, qrX, qrY, qrSize, qrSize);
      
      // 绘制提示文字
      canvas.setFillStyle('#ffffff');
      canvas.setFontSize(14);
      canvas.fillText('扫描二维码或输入邀请码注册', cardWidth / 2, qrY + qrSize + 40);
      canvas.fillText('双方都可获得积分奖励', cardWidth / 2, qrY + qrSize + 65);
      
      // 绘制底部信息
      canvas.setFontSize(12);
      canvas.setFillStyle('#e0e0e0');
      canvas.fillText('易找易发 - 让生活更便利', cardWidth / 2, cardHeight - 30);
      
      return new Promise((resolve) => {
        canvas.draw(false, () => {
          wx.canvasToTempFilePath({
            canvasId: 'invitationCardCanvas',
            success: (res) => {
              resolve({
                success: true,
                imagePath: res.tempFilePath
              });
            },
            fail: (error) => {
              resolve({
                success: false,
                message: '保存邀请卡片失败'
              });
            }
          });
        });
      });
    } catch (error) {
      return {
        success: false,
        message: error.message || '创建邀请卡片失败'
      };
    }
  }

  /**
   * 生成二维码数据
   * @param {string} code 邀请码
   * @param {Object} userInfo 用户信息
   * @returns {Object} 二维码数据
   */
  static generateQRCodeData(code, userInfo = {}) {
    return {
      type: 'invitation',
      code: code,
      inviter: {
        id: userInfo.id || '',
        nickname: userInfo.nickname || '',
        avatar: userInfo.avatar || ''
      },
      timestamp: Date.now(),
      version: '1.0'
    };
  }

  /**
   * 生成分享内容
   * @param {Object} params 参数
   * @returns {Object} 分享内容
   */
  static generateShareContent(params) {
    const { inviterName, appName, rewardPoints } = params;

    return {
      title: `${inviterName}邀请您使用${appName}`,
      desc: `注册即可获得${rewardPoints}积分奖励，快来加入我们吧！`,
      imageUrl: '/images/share-default.png'
    };
  }

  /**
   * 生成邀请链接
   * @param {string} code 邀请码
   * @returns {string} 邀请链接
   */
  static generateInvitationLink(code) {
    // 直接跳转到首页，邀请信息会在 app.js 中处理
    return `pages/index/index?inviteCode=${code}`;
  }

  /**
   * 生成分享信息
   * @param {string} code 邀请码
   * @param {Object} userInfo 用户信息
   * @returns {Object} 分享信息
   */
  static generateShareInfo(code, userInfo = {}) {
    const shareContent = this.generateShareContent({
      inviterName: userInfo.nickname || '朋友',
      appName: '易找易发',
      rewardPoints: 10
    });

    return {
      title: shareContent.title,
      desc: shareContent.desc,
      imageUrl: shareContent.imageUrl,
      path: this.generateInvitationLink(code),
      query: `inviteCode=${code}`
    };
  }

  /**
   * 保存图片到相册
   * @param {string} imagePath 图片路径
   * @returns {Promise} 保存结果
   */
  static async saveImageToAlbum(imagePath) {
    try {
      // 先获取保存图片权限
      const authResult = await this.requestSaveImageAuth();
      if (!authResult.success) {
        return authResult;
      }

      return new Promise((resolve) => {
        wx.saveImageToPhotosAlbum({
          filePath: imagePath,
          success: () => {
            resolve({
              success: true,
              message: '图片已保存到相册'
            });
          },
          fail: (error) => {
            console.error('保存图片失败:', error);
            resolve({
              success: false,
              message: '保存图片失败'
            });
          }
        });
      });
    } catch (error) {
      return {
        success: false,
        message: error.message || '保存图片失败'
      };
    }
  }

  /**
   * 请求保存图片权限
   * @returns {Promise} 权限结果
   */
  static async requestSaveImageAuth() {
    return new Promise((resolve) => {
      wx.getSetting({
        success: (res) => {
          if (res.authSetting['scope.writePhotosAlbum']) {
            resolve({ success: true });
          } else {
            wx.authorize({
              scope: 'scope.writePhotosAlbum',
              success: () => {
                resolve({ success: true });
              },
              fail: () => {
                // 权限被拒绝，引导用户手动开启
                wx.showModal({
                  title: '提示',
                  content: '需要您授权保存图片到相册',
                  showCancel: false,
                  confirmText: '去设置',
                  success: (modalRes) => {
                    if (modalRes.confirm) {
                      wx.openSetting({
                        success: (settingRes) => {
                          if (settingRes.authSetting['scope.writePhotosAlbum']) {
                            resolve({ success: true });
                          } else {
                            resolve({
                              success: false,
                              message: '需要授权才能保存图片'
                            });
                          }
                        }
                      });
                    } else {
                      resolve({
                        success: false,
                        message: '需要授权才能保存图片'
                      });
                    }
                  }
                });
              }
            });
          }
        },
        fail: () => {
          resolve({
            success: false,
            message: '获取权限信息失败'
          });
        }
      });
    });
  }

  /**
   * 预览图片
   * @param {string} imagePath 图片路径
   * @param {Array} imageList 图片列表
   */
  static previewImage(imagePath, imageList = []) {
    wx.previewImage({
      current: imagePath,
      urls: imageList.length > 0 ? imageList : [imagePath]
    });
  }

  /**
   * 分享图片
   * @param {string} imagePath 图片路径
   * @param {Object} shareInfo 分享信息
   */
  static shareImage(imagePath, shareInfo = {}) {
    // 这里可以调用分享API或者显示分享选项
    wx.showActionSheet({
      itemList: ['保存到相册', '发送给朋友', '分享到朋友圈'],
      success: async (res) => {
        switch (res.tapIndex) {
          case 0:
            // 保存到相册
            const saveResult = await this.saveImageToAlbum(imagePath);
            wx.showToast({
              title: saveResult.message,
              icon: saveResult.success ? 'success' : 'none'
            });
            break;
          case 1:
            // 发送给朋友（这里需要实现具体的分享逻辑）
            console.log('分享给朋友:', shareInfo);
            break;
          case 2:
            // 分享到朋友圈（这里需要实现具体的分享逻辑）
            console.log('分享到朋友圈:', shareInfo);
            break;
        }
      }
    });
  }
}

module.exports = QRCodeGenerator;
