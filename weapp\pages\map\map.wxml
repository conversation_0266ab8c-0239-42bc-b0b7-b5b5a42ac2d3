<!-- pages/map/map.wxml -->
<view class="map-container">
  <!-- 地图组件 -->
  <map
    id="map"
    class="map"
    latitude="{{map.latitude}}"
    longitude="{{map.longitude}}"
    scale="{{map.scale}}"
    markers="{{map.markers}}"
    show-location="{{true}}"
    show-compass="{{false}}"
    enable-zoom="{{true}}"
    enable-scroll="{{true}}"
    enable-rotate="{{false}}"
    enable-overlooking="{{false}}"
    bindmarkertap="onMarkerTap"
    bindtap="onMapTap"
    bindregionchange="onRegionChange"
  >
    <!-- 地图控制按钮 -->
    <view class="map-controls">
      <view class="control-btn category-btn" bindtap="onShowCategoryFilter">
        <image class="control-icon" src="/assets/images/common/filter-color-main.png"></image>
        <view wx:if="{{filter.selectedCategoryId}}" class="category-badge"></view>
      </view>
      <view class="control-btn refresh-btn" bindtap="refreshData">
        <image class="control-icon" src="/assets/images/common/refresh-color-main.png"></image>
      </view>
      <view class="control-btn location-btn" bindtap="onBackToUserLocation">
        <image class="control-icon" src="/assets/images/common/local-two-color-main.png"></image>
      </view>
    </view>



    <!-- 搜索范围指示器 -->
    <view wx:if="{{ui.showSearchInfo}}" class="search-info">
      <text class="search-radius">搜索范围: {{data.searchRadiusText}}</text>
      <text class="post-count">找到 {{map.markers.length}} 个帖子</text>
    </view>

    <!-- 分类过滤指示器 -->
    <view wx:if="{{filter.selectedCategoryId}}" class="category-info">
      <text class="category-text">分类: {{filter.selectedCategoryName}}</text>
      <view class="clear-category-btn" bindtap="clearCategoryFilter">
        <text class="clear-text">×</text>
      </view>
    </view>

    <!-- 缩放级别信息 -->
    <view class="map-scale-info">
      <text class="scale-text">缩放: {{map.scale}}</text>
      <text class="scale-range-text">范围: {{ui.scaleRangeText}}</text>
    </view>

    <!-- 移动提示 -->
    <view wx:if="{{map.markers.length > 0}}" class="move-tip">
      <text class="tip-text">拖拽地图查看其他区域</text>
    </view>

    <!-- 地图状态信息 -->
    <view wx:if="{{ui.formattedSearchLatitude && ui.formattedSearchLongitude}}" class="map-status-info">
      <text class="status-text">搜索中心: {{ui.formattedSearchLatitude}}, {{ui.formattedSearchLongitude}}</text>
      <text class="status-text">搜索范围: {{data.searchRadiusText}}</text>
    </view>
  </map>

  <!-- 帖子详情弹窗 -->
  <view wx:if="{{modal.showPostDetail}}" class="post-detail-modal">
    <view class="modal-mask" bindtap="onClosePostDetail"></view>
    <view class="modal-content">
      <!-- 帖子头部信息 -->
      <view class="post-header">
        <view class="post-author">
          <image class="author-avatar" src="{{modal.selectedPost.authorAvatar || '/assets/images/common/default-avatar.png'}}" mode="aspectFill"></image>
          <view class="author-info">
            <text class="author-name">{{modal.selectedPost.authorName}}</text>
            <text class="author-gender" wx:if="{{modal.selectedPost.authorGender}}">{{modal.selectedPost.authorGender}}</text>
            <text class="post-time">{{modal.selectedPost.formattedPublishTime || modal.selectedPost.formattedCreateTime}}</text>
          </view>
        </view>
        <view class="close-btn" bindtap="onClosePostDetail">
          <text class="close-icon">×</text>
        </view>
      </view>

      <!-- 帖子内容 -->
      <view class="post-content">
        <text class="post-text">{{modal.selectedPost.content}}</text>
        
        <!-- 帖子图片 -->
        <view wx:if="{{modal.selectedPost.images && modal.selectedPost.images.length > 0}}" class="post-images">
          <image
            wx:for="{{modal.selectedPost.images}}"
            wx:key="*this"
            class="post-image"
            src="{{item}}"
            mode="aspectFill">
          </image>
        </view>
      </view>

      <!-- 帖子统计信息 -->
      <view class="post-stats">
        <view class="stat-item">
          <image class="stat-icon like-icon {{modal.selectedPost.isLiked ? 'liked' : ''}}" src="/assets/images/common/like.png" mode="aspectFit"></image>
          <text class="stat-text {{modal.selectedPost.isLiked ? 'liked' : ''}}">{{modal.selectedPost.likeCount}}</text>
        </view>
        <view class="stat-item">
          <image class="stat-icon" src="/assets/images/common/comment.png" mode="aspectFit"></image>
          <text class="stat-text">{{modal.selectedPost.commentCount}}</text>
        </view>
        <view class="stat-item" wx:if="{{modal.selectedPost.favoriteCount > 0}}">
          <image class="stat-icon" src="/assets/images/common/favorite.png" mode="aspectFit"></image>
          <text class="stat-text">{{modal.selectedPost.favoriteCount}}</text>
        </view>
        <view class="stat-item" wx:if="{{modal.selectedPost.viewCount > 0}}">
          <image class="stat-icon" src="/assets/images/common/view.png" mode="aspectFit"></image>
          <text class="stat-text">{{modal.selectedPost.viewCount}}</text>
        </view>
        <view class="stat-item" wx:if="{{modal.selectedPost.distanceText}}">
          <image class="stat-icon" src="/assets/images/common/location.png" mode="aspectFit"></image>
          <text class="stat-text">{{modal.selectedPost.distanceText}}</text>
        </view>
        <view class="stat-item" wx:if="{{modal.selectedPost.category}}">
          <text class="category-tag">{{modal.selectedPost.category.name}}</text>
        </view>
      </view>

      <!-- 帖子标签 -->
      <view wx:if="{{(modal.selectedPost.tags && modal.selectedPost.tags.length > 0) || (modal.selectedPost.tagList && modal.selectedPost.tagList.length > 0)}}" class="post-tags">
        <text class="tags-label">标签:</text>
        <view class="tags-list">
          <!-- 优先显示 tags 数组，如果没有则显示 tagList -->
          <block wx:if="{{modal.selectedPost.tags && modal.selectedPost.tags.length > 0}}">
            <text wx:for="{{modal.selectedPost.tags}}" wx:key="*this" class="tag-item">{{item}}</text>
          </block>
          <block wx:elif="{{modal.selectedPost.tagList && modal.selectedPost.tagList.length > 0}}">
            <text wx:for="{{modal.selectedPost.tagList}}" wx:key="*this" class="tag-item">{{item.tagName || item}}</text>
          </block>
        </view>
      </view>

      <!-- 联系信息 -->
      <view wx:if="{{modal.selectedPost.contactPhone || modal.selectedPost.contactName}}" class="contact-info">
        <text class="contact-label">联系方式:</text>
        <view class="contact-details">
          <text wx:if="{{modal.selectedPost.contactName}}" class="contact-name">{{modal.selectedPost.contactName}}</text>
          <text wx:if="{{modal.selectedPost.contactPhone}}" class="contact-phone">{{modal.selectedPost.contactPhone}}</text>
        </view>
      </view>

      <!-- 地址信息 -->
      <view wx:if="{{modal.selectedPost.address}}" class="address-info">
        <text class="address-label">地址:</text>
        <text class="address-text">{{modal.selectedPost.address}}</text>
      </view>

      <!-- 操作按钮 -->
      <view class="post-actions">
        <view class="action-btn secondary {{modal.selectedPost.isLiked ? 'active' : ''}}" bindtap="onLikePost">
          <image class="action-icon" src="/assets/images/common/like.png" mode="aspectFit"></image>
          <text class="action-text">{{modal.selectedPost.isLiked ? '已点赞' : '点赞'}}</text>
        </view>
        <view class="action-btn secondary {{modal.selectedPost.isFavorite ? 'active' : ''}}" bindtap="onFavoritePost">
          <image class="action-icon" src="/assets/images/common/favorite.png" mode="aspectFit"></image>
          <text class="action-text">{{modal.selectedPost.isFavorite ? '已收藏' : '收藏'}}</text>
        </view>
        <view class="action-btn primary" bindtap="onViewPostDetail">
          <text class="action-text">查看详情</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部信息栏 -->
  <view class="map-info" bindtap="showPostList">
    <view class="info-item">
      <image class="info-icon" src="/assets/images/common/marker.png" mode="aspectFit"></image>
      <text class="info-text">共找到 {{map.markers.length}} 个附近帖子</text>
    </view>
    <view class="info-tip">
      <text class="tip-text">点击查看帖子列表</text>
    </view>
  </view>

  <!-- 帖子列表弹窗 -->
  <view wx:if="{{modal.showPostList}}" class="post-list-modal {{modal.postListClosing ? 'closing' : ''}}">
    <view class="modal-mask" bindtap="hidePostList" catchtouchmove="preventClose"></view>
    <view class="modal-content" catchtap="preventClose">
      <!-- 弹窗头部 -->
      <view class="post-list-header">
        <text class="post-list-title">附近帖子 ({{modal.postList.length}})</text>
        <view class="close-btn" bindtap="hidePostList">
          <text class="close-icon">×</text>
        </view>
      </view>

      <!-- 帖子列表 -->
      <scroll-view class="post-list-scroll" scroll-y catchtouchmove="preventClose">
        <block wx:if="{{modal.postList.length > 0}}">
          <view
            wx:for="{{modal.postList}}"
            wx:key="id"
            class="post-list-item"
            bindtap="onPostListItemTap"
            data-post-id="{{item.id}}"
          >
            <!-- 帖子图片 -->
            <view class="post-item-image">
              <image 
                wx:if="{{item.images && item.images.length > 0}}" 
                class="post-image" 
                src="{{item.images[0]}}" 
                mode="aspectFill"
              ></image>
              <view wx:else class="post-image-placeholder">
                <text class="placeholder-text">{{item.category.name ? item.category.name.substring(0, 2) : 'PF'}}</text>
              </view>
            </view>

            <!-- 帖子内容 -->
            <view class="post-item-content">
              <view class="post-item-title">{{item.category.name || '附近帖子'}}</view>
              <view class="post-item-desc">{{item.content}}</view>

              <!-- 帖子标签 -->
              <view wx:if="{{(item.tags && item.tags.length > 0) || (item.tagList && item.tagList.length > 0)}}" class="post-item-tags">
                <!-- 优先显示 tags 数组，如果没有则显示 tagList，最多显示3个标签 -->
                <block wx:if="{{item.tags && item.tags.length > 0}}">
                  <text wx:for="{{item.tags}}" wx:key="*this" wx:if="{{index < 3}}" class="post-tag-item">{{item}}</text>
                  <text wx:if="{{item.tags.length > 3}}" class="post-tag-more">+{{item.tags.length - 3}}</text>
                </block>
                <block wx:elif="{{item.tagList && item.tagList.length > 0}}">
                  <text wx:for="{{item.tagList}}" wx:key="*this" wx:if="{{index < 3}}" class="post-tag-item">{{item.tagName || item}}</text>
                  <text wx:if="{{item.tagList.length > 3}}" class="post-tag-more">+{{item.tagList.length - 3}}</text>
                </block>
              </view>

              <view class="post-item-meta">
                <view class="meta-item">
                  <image class="meta-icon" src="/assets/images/common/like.png" mode="aspectFit"></image>
                  <text class="meta-text">{{item.likeCount || 0}}</text>
                </view>
                <view class="meta-item">
                  <image class="meta-icon" src="/assets/images/common/comment.png" mode="aspectFit"></image>
                  <text class="meta-text">{{item.commentCount || 0}}</text>
                </view>
                <view class="meta-item" wx:if="{{item.distance}}">
                  <image class="meta-icon" src="/assets/images/common/location.png" mode="aspectFit"></image>
                  <text class="meta-text">{{item.distance}}</text>
                </view>
              </view>
            </view>

            <!-- 箭头指示 -->
            <view class="post-item-arrow">
              <image class="arrow-icon" src="/assets/images/common/arrow-right.png" mode="aspectFit"></image>
            </view>
          </view>
        </block>

        <!-- 空状态 -->
        <view wx:else class="post-list-empty">
          <text class="empty-text">暂无附近帖子</text>
        </view>
      </scroll-view>
    </view>
  </view>
</view>

<!-- 分类过滤器弹窗 - 移到地图容器外面 -->
<view wx:if="{{category.showFilter}}" class="category-filter-modal {{category.filterClosing ? 'closing' : ''}}">
    <view class="modal-mask" bindtap="onCloseCategoryFilter" catchtouchmove="preventClose"></view>
    <view class="category-filter-content" catchtap="preventClose">
      <!-- 弹窗头部 -->
      <view class="category-filter-header">
        <text class="filter-title">选择分类</text>
        <view class="close-btn" bindtap="onCloseCategoryFilter">
          <text class="close-icon">×</text>
        </view>
      </view>

      <!-- 分类列表 -->
      <scroll-view class="category-filter-scroll" scroll-y catchtouchmove="preventClose">
        <view
          wx:for="{{category.list}}"
          wx:key="id"
          class="category-filter-item {{filter.selectedCategoryId === item.id ? 'selected' : ''}}"
          bindtap="onSelectCategory"
          data-category-id="{{item.id}}"
          data-category-name="{{item.name}}"
        >
          <!-- 分类图标 -->
          <view class="category-item-icon">
            <image
              wx:if="{{item.icon}}"
              class="category-icon"
              src="{{item.icon}}"
              mode="aspectFit"
            ></image>
            <view wx:else class="category-icon-placeholder">
              <text class="placeholder-text">{{item.name.substring(0, 2)}}</text>
            </view>
          </view>

          <!-- 分类名称 -->
          <view class="category-item-content">
            <text class="category-name">{{item.name}}</text>
            <text wx:if="{{item.description}}" class="category-desc">{{item.description}}</text>
          </view>

          <!-- 选中标识 -->
          <view wx:if="{{filter.selectedCategoryId === item.id}}" class="category-selected-icon">
            <text class="selected-text">✓</text>
          </view>
        </view>
      </scroll-view>

      <!-- 底部操作 -->
      <view class="category-filter-footer">
        <view class="filter-btn secondary" bindtap="clearCategoryFilter">
          <text class="btn-text">清除筛选</text>
        </view>
        <view class="filter-btn primary" bindtap="onCloseCategoryFilter">
          <text class="btn-text">确定</text>
        </view>
      </view>
    </view>
  </view>
