/* pkg_user/pages/apply-settle/apply-settle.wxss */

/* 使用layout组件，移除容器样式 */

.apply-header {
  background: linear-gradient(135deg, #FF8181, #ff8585);
  padding: 20rpx 30rpx 40rpx 30rpx;
  color: #fff;
  position: relative;
  overflow: hidden;
}

.apply-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 20rpx;
  background: linear-gradient(to bottom, rgba(255, 129, 129, 0.8), rgba(255, 129, 129, 1));
}

/* 装饰元素 */
.header-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 4s ease-in-out infinite;
}

.circle-1 {
  width: 100rpx;
  height: 100rpx;
  top: 30rpx;
  right: 50rpx;
  animation-delay: 0s;
}

.circle-2 {
  width: 60rpx;
  height: 60rpx;
  bottom: 30rpx;
  left: 80rpx;
  animation-delay: 2s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-15rpx) rotate(180deg); }
}

.header-content {
  text-align: center;
  position: relative;
  z-index: 1;
}

.header-icon {
  font-size: 50rpx;
  display: block;
  margin-bottom: 16rpx;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #fff;
  display: block;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.header-desc {
  font-size: 28rpx;
  color: #fff;
  opacity: 0.95;
  margin-bottom: 24rpx;
}

/* 优势展示 */
.header-benefits {
  display: flex;
  justify-content: center;
  gap: 32rpx;
  margin-top: 20rpx;
}

.benefit-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  opacity: 0.95;
}

.benefit-icon {
  font-size: 28rpx;
  margin-bottom: 6rpx;
  display: block;
}

.benefit-text {
  font-size: 20rpx;
  font-weight: 500;
  text-align: center;
}

.form-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
}

.form-item {
  margin-bottom: 40rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.form-input:focus {
  background: #fff;
  border: 2rpx solid #ff6b6b;
}

.form-textarea {
  width: 100%;
  min-height: 160rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  line-height: 1.5;
}

.form-textarea:focus {
  background: #fff;
  border: 2rpx solid #ff6b6b;
}

.submit-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #ff6b6b, #ff8585);
  color: #fff;
  font-size: 32rpx;
  font-weight: 600;
  border-radius: 44rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 107, 0.3);
  transition: all 0.3s ease;
}

.submit-btn:active {
  transform: scale(0.98);
}

.submit-btn.submitting {
  background: #ccc;
  box-shadow: none;
}

.submit-btn::after {
  display: none;
}

.tips {
  margin-top: 20rpx;
  text-align: center;
}

.tips-text {
  font-size: 24rpx;
  color: #999;
  line-height: 1.5;
}

/* 其他合作方式 */
.other-cooperation {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-top: 20rpx;
}

.other-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.cooperation-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
  transition: all 0.3s ease;
}

.cooperation-btn:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.cooperation-text {
  font-size: 26rpx;
  color: #495057;
  font-weight: 500;
}

.cooperation-arrow {
  font-size: 28rpx;
  color: #adb5bd;
}

/* 新增样式 */
.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin: 40rpx 0 20rpx 0;
  padding-left: 20rpx;
  border-left: 4rpx solid #FF8181;
}

.form-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  padding: 0 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
}

.picker-text {
  font-size: 28rpx;
  color: #333;
}

.picker-text.placeholder {
  color: #999;
}

.picker-arrow {
  font-size: 24rpx;
  color: #999;
  transform: rotate(90deg);
}

.upload-section {
  margin-top: 20rpx;
}

.upload-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 160rpx;
  background: #f8f9fa;
  border: 2rpx dashed #dee2e6;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.upload-btn:active {
  background: #e9ecef;
}

.upload-icon {
  font-size: 48rpx;
  margin-bottom: 10rpx;
}

.upload-text {
  font-size: 26rpx;
  color: #666;
}

.upload-tip {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  margin-top: 10rpx;
}

.uploaded-image {
  width: 200rpx;
  height: 150rpx;
  border-radius: 8rpx;
  overflow: hidden;
  margin-top: 20rpx;
}

.uploaded-image image {
  width: 100%;
  height: 100%;
}

/* 位置选择器样式 */
.location-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.location-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.location-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
}

.location-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.location-text.placeholder {
  color: #999;
}

.picker-arrow {
  font-size: 28rpx;
  color: #999;
  transform: rotate(0deg);
}

/* 已上传图片展示 */
.uploaded-images {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 20rpx;
}

.image-item {
  position: relative;
  width: 160rpx;
  height: 160rpx;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  object-fit: cover;
}

.delete-image-btn {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 32rpx;
  height: 32rpx;
  background: #ff4757;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.3);
}

/* 优化上传按钮样式 */
.upload-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 160rpx;
  height: 160rpx;
  border: 2rpx dashed #ddd;
  border-radius: 12rpx;
  background: #fafafa;
  transition: all 0.3s ease;
}

.upload-btn:active {
  background: #f0f0f0;
  border-color: #ff6b6b;
}

.upload-icon {
  font-size: 40rpx;
  margin-bottom: 8rpx;
}

.upload-text {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}

.upload-tip {
  font-size: 22rpx;
  color: #999;
  margin-top: 12rpx;
  text-align: center;
}

/* 图片上传区样式 */
.media-section {
  margin: 32rpx 0 0 0;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx #f0f0f0;
  padding: 32rpx 24rpx 24rpx 24rpx;
}
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}
.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #FF7D7D;
}
.section-stats {
  font-size: 26rpx;
  color: #999;
}
.upload-area {
  width: 100%;
}
.image-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
}
.image-item {
  width: 200rpx;
  height: 200rpx;
  position: relative;
  border-radius: 12rpx;
  overflow: hidden;
  background: #f8f8f8;
  box-shadow: 0 2rpx 8rpx #f0f0f0;
}
.image-item image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 12rpx;
}
.delete-btn {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 40rpx;
  height: 40rpx;
  background: rgba(255, 125, 125, 0.9);
  color: #fff;
  font-size: 32rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}
.upload-item {
  width: 200rpx;
  height: 200rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border: 2rpx dashed #FF7D7D;
  border-radius: 12rpx;
  color: #FF7D7D;
  font-size: 80rpx;
  cursor: pointer;
}
.plus {
  font-size: 80rpx;
  color: #FF7D7D;
  line-height: 1;
}

/* 营业执照上传区样式 */
.license-upload-section {
  display: flex;
  align-items: center;
  gap: 24rpx;
}
.license-upload-btn {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #FF7D7D;
  border-radius: 12rpx;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #FF7D7D;
  cursor: pointer;
}
.license-upload-icon {
  width: 64rpx;
  height: 64rpx;
  margin-bottom: 12rpx;
}
.license-upload-text {
  font-size: 28rpx;
  color: #FF7D7D;
}
.license-image-preview {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background: #f8f8f8;
  box-shadow: 0 2rpx 8rpx #f0f0f0;
}
.license-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 12rpx;
}
.license-image-preview .delete-btn {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 40rpx;
  height: 40rpx;
  background: rgba(255, 125, 125, 0.9);
  color: #fff;
  font-size: 32rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}
/* 商家图片上传区每行最多3张 */
.image-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
}
.image-item, .upload-item {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 24rpx;
}
