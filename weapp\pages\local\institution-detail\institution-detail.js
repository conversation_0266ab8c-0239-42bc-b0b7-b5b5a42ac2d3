/**
 * 机构详情页面
 * 按照小程序开发规范重构，使用Store模式管理数据
 */

const {
  getInstitutionDetail,
  getInstitutionServices,
  getInstitutionReviews,
  toggleInstitutionFavorite,
  bookInstitutionService,
  recordInstitutionView,
  getDefaultInstitutionDetail
} = require('../../../stores/institutionDetailStore.js');

// 引入帖子相关功能
const { request } = require('../../../utils/request.js');

// 引入机构评论服务
const institutionCommentService = require('./services/institutionCommentService.js');

Page({
  data: {
    // 机构详情
    institution: null,
    // 加载状态
    loading: true,
    // 机构ID
    institutionId: '',
    // 机构发布的帖子列表
    postList: [],
    // 评价列表
    reviewList: [],
    // 评论列表（新的评论系统）
    feedbackList: [],
    // 当前Tab
    currentTab: 'info', // info, posts, comments
    // 操作状态
    favoriteLoading: false,
    // 错误状态
    error: null,
    // 帖子加载状态
    postsLoading: false,
    // 评论相关状态
    commentsLoading: false,
    showCommentInput: false,
    replyToComment: null,
    parentCommentId: '',
    commentPage: {
      current: 1,
      size: 10,
      hasMore: true
    }
  },

  /**
   * 页面加载
   */
  onLoad(options) {
    const { id } = options;
    if (id) {
      this.setData({
        institutionId: id,
        institution: getDefaultInstitutionDetail()
      });
      this.initPageData(id);
    } else {
      this.handleError('参数错误', true);
    }
  },

  /**
   * 初始化页面数据
   */
  async initPageData(id) {
    try {
      // 验证机构ID
      if (!id || id === 'undefined' || id === 'null') {
        throw new Error('机构ID无效');
      }

      console.log('开始初始化机构详情页面，ID:', id);
      // 加载机构详情
      await this.loadInstitutionDetail(id);
    } catch (error) {
      console.error('页面初始化失败:', error);
      this.handleError(error.message || '页面初始化失败');
    }
  },

  /**
   * 加载机构详情
   */
  async loadInstitutionDetail(id) {
    try {
      this.setData({ loading: true, error: null });

      console.log('开始加载机构详情，ID:', id);

      const institution = await getInstitutionDetail(id);

      // 验证返回的机构数据
      if (!institution || !institution.id) {
        throw new Error('机构数据无效或不存在');
      }

      console.log('机构详情加载成功:', institution.name);

      // 处理机构图片数据
      const processedImages = this.processInstitutionImages(institution.images);

      // 验证必要字段并设置默认值
      const processedInstitution = {
        ...institution,
        name: institution.name || '未知机构',
        description: institution.description || '暂无描述',
        address: institution.address || '地址未知',
        phone: institution.phone || '',
        images: processedImages
      };

      this.setData({
        institution: processedInstitution,
        loading: false
      });

      // 设置页面标题
      wx.setNavigationBarTitle({
        title: processedInstitution.name
      });

      // 加载机构发布的帖子列表（异步执行，不阻塞主流程）
      this.loadInstitutionPosts(id).catch(error => {
        console.warn('加载机构帖子失败:', error);
      });

    } catch (error) {
      console.error('加载机构详情失败:', error);

      // 设置错误状态
      this.setData({
        loading: false,
        error: error.message || '加载失败'
      });

      // 显示错误提示
      this.handleError(error.message || '加载失败，请重试');
    }
  },

  /**
   * 处理机构图片数据
   */
  processInstitutionImages(images) {
    if (!images) return [];

    try {
      if (typeof images === 'string') {
        // 如果是JSON字符串，尝试解析
        if (images.startsWith('[') || images.startsWith('{')) {
          const parsed = JSON.parse(images);
          if (Array.isArray(parsed)) {
            return parsed.map(item => typeof item === 'string' ? item : item.url).filter(url => url);
          } else if (parsed.url) {
            return [parsed.url];
          }
        } else {
          // 如果是逗号分隔的字符串
          return images.split(',').filter(img => img.trim());
        }
      } else if (Array.isArray(images)) {
        return images.map(item => typeof item === 'string' ? item : item.url).filter(url => url);
      }
    } catch (error) {
      console.warn('处理机构图片数据失败:', error);
    }

    return [];
  },

  /**
   * 加载机构发布的帖子列表
   */
  async loadInstitutionPosts(institutionId) {
    try {
      this.setData({ postsLoading: true });

      const response = await request({
        url: '/blade-chat/post/list',
        method: 'GET',
        data: {
          institutionId: institutionId,
          current: 1,
          size: 20
        }
      });

      if (response.code === 200 && response.data && response.data.records) {
        const postList = this.processPostList(response.data.records);
        this.setData({
          postList: postList,
          postsLoading: false
        });
      } else {
        console.warn('获取机构帖子失败:', response.msg);
        this.setData({
          postList: [],
          postsLoading: false
        });
      }
    } catch (error) {
      console.error('加载机构帖子失败:', error);
      this.setData({
        postList: [],
        postsLoading: false
      });
    }
  },

  /**
   * 切换Tab
   */
  onTabChange(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({ currentTab: tab });

    if (tab === 'posts' && this.data.postList.length === 0) {
      this.loadInstitutionPosts(this.data.institutionId);
    } else if (tab === 'comments' && this.data.feedbackList.length === 0) {
      this.loadCommentList();
    }
  },



  /**
   * 处理帖子列表数据
   */
  processPostList(rawPosts) {
    return rawPosts.map(post => ({
      id: post.id,
      title: post.title || '无标题',
      content: post.content || '',
      images: post.images ? post.images.split(',').filter(img => img.trim()) : [],
      author: post.user ? post.user.nickname || '匿名用户' : '匿名用户',
      avatar: post.user ? post.user.avatar || '/assets/images/avatar/default.png' : '/assets/images/avatar/default.png',
      likeCount: post.likeCount || 0,
      commentCount: post.commentCount || 0,
      createTime: this.formatTime(post.createTime),
      category: post.category ? post.category.name || '' : '',
      isLiked: post.isLiked || false,
      isFavorited: post.isFavorited || false
    }));
  },

  /**
   * 格式化时间
   */
  formatTime(timeStr) {
    if (!timeStr) return '';

    const time = new Date(timeStr);
    const now = new Date();
    const diff = now - time;

    // 小于1分钟
    if (diff < 60000) {
      return '刚刚';
    }
    // 小于1小时
    if (diff < 3600000) {
      return Math.floor(diff / 60000) + '分钟前';
    }
    // 小于1天
    if (diff < 86400000) {
      return Math.floor(diff / 3600000) + '小时前';
    }
    // 小于7天
    if (diff < 604800000) {
      return Math.floor(diff / 86400000) + '天前';
    }

    // 超过7天显示具体日期
    return time.toLocaleDateString();
  },

  /**
   * 拨打电话
   */
  onCallPhone() {
    const phone = this.data.institution?.phone;
    if (phone) {
      wx.makePhoneCall({
        phoneNumber: phone,
        fail: (error) => {
          console.error('拨打电话失败:', error);
          wx.showToast({
            title: '拨打失败',
            icon: 'none'
          });
        }
      });
    } else {
      wx.showToast({
        title: '暂无联系电话',
        icon: 'none'
      });
    }
  },

  /**
   * 查看位置
   */
  onViewLocation() {
    const institution = this.data.institution;
    if (institution?.latitude && institution?.longitude) {
      wx.openLocation({
        latitude: parseFloat(institution.latitude),
        longitude: parseFloat(institution.longitude),
        name: institution.name,
        address: institution.address,
        fail: (error) => {
          console.error('打开地图失败:', error);
          wx.showToast({
            title: '打开地图失败',
            icon: 'none'
          });
        }
      });
    } else {
      wx.showToast({
        title: '暂无位置信息',
        icon: 'none'
      });
    }
  },

  /**
   * 分享机构
   */
  onShareAppMessage() {
    const institution = this.data.institution;
    return {
      title: `推荐机构：${institution?.name || '本地机构'}`,
      path: `/pages/local/institution-detail/institution-detail?id=${this.data.institutionId}`,
      imageUrl: institution?.logo || ''
    };
  },

  /**
   * 收藏/取消收藏机构
   */
  async onToggleFavorite() {
    if (this.data.favoriteLoading) return;

    try {
      this.setData({ favoriteLoading: true });

      const currentFavorite = this.data.institution.isFavorite;
      const newFavoriteStatus = await toggleInstitutionFavorite(
        this.data.institutionId,
        currentFavorite
      );

      this.setData({
        'institution.isFavorite': newFavoriteStatus,
        favoriteLoading: false
      });

      wx.showToast({
        title: newFavoriteStatus ? '已收藏' : '已取消收藏',
        icon: 'success'
      });

    } catch (error) {
      console.error('收藏操作失败:', error);
      this.setData({ favoriteLoading: false });

      wx.showToast({
        title: error.message || '操作失败',
        icon: 'none'
      });
    }
  },

  /**
   * 查看帖子详情
   */
  onViewPost(e) {
    const postId = e.currentTarget.dataset.postId;
    if (postId) {
      wx.navigateTo({
        url: `/pages/post-detail/post-detail?id=${postId}`
      });
    }
  },

  /**
   * 点赞帖子
   */
  async onLikePost(e) {
    const postId = e.currentTarget.dataset.postId;
    const index = e.currentTarget.dataset.index;

    if (!postId) return;

    try {
      // 这里可以调用点赞接口
      const postList = [...this.data.postList];
      const post = postList[index];

      // 切换点赞状态
      post.isLiked = !post.isLiked;
      post.likeCount = post.isLiked ? post.likeCount + 1 : Math.max(0, post.likeCount - 1);

      this.setData({ postList });

      wx.showToast({
        title: post.isLiked ? '点赞成功' : '取消点赞',
        icon: 'success'
      });

    } catch (error) {
      console.error('点赞操作失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      });
    }
  },

  /**
   * 处理错误
   */
  handleError(message, shouldGoBack = false) {
    console.error('页面错误:', message);

    // 根据错误类型显示不同的处理方式
    if (message.includes('机构ID无效') || message.includes('参数错误')) {
      wx.showModal({
        title: '参数错误',
        content: '机构信息无效，请重新选择机构',
        showCancel: false,
        confirmText: '返回',
        success: () => {
          wx.navigateBack();
        }
      });
      return;
    }

    if (message.includes('机构数据无效') || message.includes('不存在')) {
      wx.showModal({
        title: '机构不存在',
        content: '该机构可能已被删除或暂时不可用',
        showCancel: true,
        cancelText: '返回',
        confirmText: '重试',
        success: (res) => {
          if (res.confirm) {
            this.onRetry();
          } else {
            wx.navigateBack();
          }
        }
      });
      return;
    }

    // 网络错误或其他错误
    wx.showModal({
      title: '加载失败',
      content: message || '网络连接异常，请检查网络后重试',
      showCancel: true,
      cancelText: shouldGoBack ? '返回' : '取消',
      confirmText: '重试',
      success: (res) => {
        if (res.confirm) {
          this.onRetry();
        } else if (shouldGoBack) {
          wx.navigateBack();
        }
      }
    });
  },

  /**
   * 重新加载数据
   */
  onRetry() {
    if (this.data.institutionId) {
      this.initPageData(this.data.institutionId);
    }
  },

  /**
   * 下拉刷新
   */
  async onPullDownRefresh() {
    try {
      if (this.data.institutionId) {
        await this.loadInstitutionDetail(this.data.institutionId);
      }
    } finally {
      wx.stopPullDownRefresh();
    }
  },

  /**
   * 预览图片
   */
  onPreviewImage(e) {
    const { url, urls } = e.currentTarget.dataset;
    wx.previewImage({
      current: url,
      urls: urls || [url]
    });
  },

  // ==================== 评论相关方法 ====================

  /**
   * 加载评论列表
   */
  async loadCommentList(refresh = false) {
    if (this.data.commentsLoading) return;

    try {
      this.setData({ commentsLoading: true });

      const page = refresh ? { current: 1, size: 10 } : this.data.commentPage;
      const response = await institutionCommentService.getInstitutionCommentList(
        this.data.institutionId,
        page
      );

      if (response.code === 200 && response.data) {
        const newComments = institutionCommentService.processCommentList(response.data.records || []);

        this.setData({
          feedbackList: refresh ? newComments : [...this.data.feedbackList, ...newComments],
          'commentPage.current': page.current + 1,
          'commentPage.hasMore': newComments.length >= page.size,
          commentsLoading: false
        });
      } else {
        this.setData({
          feedbackList: refresh ? [] : this.data.feedbackList,
          commentsLoading: false
        });
      }
    } catch (error) {
      console.error('加载评论列表失败:', error);
      this.setData({ commentsLoading: false });
    }
  },

  /**
   * 显示评论输入框
   */
  onShowCommentInput() {
    this.setData({
      showCommentInput: true,
      replyToComment: null,
      parentCommentId: ''
    });
  },

  /**
   * 隐藏评论输入框
   */
  onHideCommentInput() {
    this.setData({
      showCommentInput: false,
      replyToComment: null,
      parentCommentId: ''
    });
  },

  /**
   * 提交评论
   */
  async onSubmitComment(e) {
    const commentData = e.detail;

    try {
      let response;
      if (commentData.parentId) {
        // 回复评论
        response = await institutionCommentService.replyInstitutionComment(commentData);
      } else {
        // 添加评论
        response = await institutionCommentService.addInstitutionComment(commentData);
      }

      if (response.code === 200) {
        wx.showToast({
          title: commentData.parentId ? '回复成功' : '评价成功',
          icon: 'success'
        });

        // 重置输入框
        this.selectComponent('#comment-input').resetInput();
        this.onHideCommentInput();

        // 刷新评论列表
        this.loadCommentList(true);
      } else {
        throw new Error(response.msg || '提交失败');
      }
    } catch (error) {
      console.error('提交评论失败:', error);
      wx.showToast({
        title: error.message || '提交失败',
        icon: 'none'
      });
    } finally {
      // 重置提交状态
      this.selectComponent('#comment-input').setSubmitting(false);
    }
  },

  /**
   * 回复评论
   */
  onCommentReply(e) {
    const { commentId, parentId, institutionId, replyToComment } = e.detail;

    this.setData({
      showCommentInput: true,
      replyToComment: replyToComment,
      parentCommentId: parentId || commentId
    });

    // 聚焦输入框
    setTimeout(() => {
      this.selectComponent('#comment-input').focusInput();
    }, 100);
  },

  /**
   * 点赞评论
   */
  async onFeedbackLikeTap(e) {
    const { feedbackId } = e.detail;

    try {
      const response = await institutionCommentService.toggleHelpful(feedbackId);

      if (response.code === 200) {
        // 更新评论列表中的点赞状态
        const feedbackList = this.data.feedbackList.map(item => {
          if (item.id == feedbackId) {
            return {
              ...item,
              isHelpful: !item.isHelpful,
              likes: item.isHelpful ? Math.max(0, item.likes - 1) : item.likes + 1
            };
          }
          return item;
        });

        this.setData({ feedbackList });

        wx.showToast({
          title: '操作成功',
          icon: 'success'
        });
      }
    } catch (error) {
      console.error('点赞操作失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      });
    }
  },

  /**
   * 切换回复展开状态
   */
  async onToggleReplies(e) {
    const { commentId, expanded } = e.detail;

    // 更新展开状态
    const feedbackList = this.data.feedbackList.map(item => {
      if (item.id == commentId) {
        return { ...item, expanded: !expanded };
      }
      return item;
    });

    this.setData({ feedbackList });

    // 如果是展开且还没有加载回复，则加载回复
    if (!expanded) {
      this.loadCommentReplies(commentId);
    }
  },

  /**
   * 加载评论回复
   */
  async loadCommentReplies(commentId) {
    try {
      const response = await institutionCommentService.getCommentReplies(commentId);

      if (response.code === 200 && response.data) {
        const replies = response.data.records || [];

        // 更新评论列表中的回复数据
        const feedbackList = this.data.feedbackList.map(item => {
          if (item.id == commentId) {
            return {
              ...item,
              replies: replies,
              allRepliesLoaded: true
            };
          }
          return item;
        });

        this.setData({ feedbackList });
      }
    } catch (error) {
      console.error('加载回复失败:', error);
    }
  },

  /**
   * 轮播图切换
   */
  onImageChange(e) {
    this.setData({
      currentImageIndex: e.detail.current
    });
  },

  /**
   * 点击图片预览
   */
  onImageTap(e) {
    const current = e.currentTarget.dataset.src;
    const urls = this.data.institution.images || [];

    if (urls.length > 0) {
      wx.previewImage({
        current: current,
        urls: urls
      });
    }
  },

  /**
   * 复制联系方式
   */
  copyContact(e) {
    const type = e.currentTarget.dataset.type;
    const institution = this.data.institution;
    let data = '';

    if (type === 'phone') {
      data = institution.phone;
    } else if (type === 'wechat') {
      data = institution.wechat;
    }

    if (!data) {
      wx.showToast({
        title: '暂无此联系方式',
        icon: 'none'
      });
      return;
    }

    wx.setClipboardData({
      data: data,
      success: () => {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success'
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        });
      }
    });
  }
});
