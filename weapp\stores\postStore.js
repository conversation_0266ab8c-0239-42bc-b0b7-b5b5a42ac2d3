const { request } = require('../utils/request.js');

// 获取帖子列表（从接口获取）
const getPostListFromAPI = async (params = {}) => {
  try {
    const response = await request({
      url: '/blade-chat-open/post/home-list',
      method: 'GET',
      data: {
        current: params.current || 1,
        size: params.size || 10,
        ...(params.categoryId && { categoryId: params.categoryId }),
        ...(params.keyword && { keyword: params.keyword }),
        ...(params.latitude && { latitude: params.latitude }),
        ...(params.longitude && { longitude: params.longitude }),
        ...(params.searchLatitude && { searchLatitude: params.searchLatitude }),
        ...(params.searchLongitude && { searchLongitude: params.searchLongitude })
      }
    });
    
    if (response.code === 200 && response.success) {
      const posts = response.data.records || [];
      
      // 处理帖子数据，转换为组件需要的格式
      return {
        records: processPosts(posts),
        total: response.data.total || 0,
        size: response.data.size || 10,
        current: response.data.current || 1
      };
    } else {
      console.error('获取帖子列表失败:', response.msg);
      return [];
    }
  } catch (error) {
    console.error('请求帖子接口失败:', error);
    return [];
  }
};

// 兼容苹果和安卓的时间格式化
const formatTime = (timeStr) => {
  if (!timeStr) return '刚刚';
  // 兼容苹果和安卓的时间格式
  let postTime;
  if (typeof timeStr === 'string' && timeStr.indexOf('-') > -1) {
    // 兼容 '2025-07-25 18:38:18' 格式
    postTime = new Date(timeStr.replace(/-/g, '/'));
  } else {
    postTime = new Date(timeStr);
  }
  if (isNaN(postTime.getTime())) return '刚刚';
  const now = new Date();
  const diff = now - postTime;
  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  if (minutes < 1) return '刚刚';
  if (minutes < 60) return `${minutes}分钟前`;
  if (hours < 24) return `${hours}小时前`;
  if (days < 7) return `${days}天前`;
  return `${postTime.getFullYear()}-${postTime.getMonth() + 1}-${postTime.getDate()}`;
};

const processPosts = (posts) => {
  if (!posts || !Array.isArray(posts)) {
    return [];
  }
  return posts.map(post => {
    let user = post.user || {};
    let category = post.category || {};
    let images = [];
    if (Array.isArray(post.images)) {
      images = post.images;
    } else if (typeof post.images === 'string' && post.images) {
      images = post.images.split(',').map(img => img.trim()).filter(Boolean);
    }
    let tags = Array.isArray(post.tags) ? post.tags : (typeof post.tags === 'string' ? post.tags.split(',').map(t => t.trim()).filter(Boolean) : []);
    // 使用formatTime格式化发布时间
    let publishTime = formatTime(post.publishTime || post.createTime || '');
    let stats = post.stats || {};
    return {
      ...post,
      avatar: user.avatar || '/assets/images/avatar.png',
      nickname: user.nickname || '匿名',
      images,
      tags,
      tag: category.name || '',
      description: post.content || '',
      time: publishTime, // 保持time字段名，但使用publishTime数据
      publishTime, // 同时提供publishTime字段
      views: stats.viewCount || 0,
      comments: stats.feedbackCount || 0,
      likes: stats.likeCount || 0,
      isHighlight: post.top === '1' || post.top === 1,
    };
  });
};

/**
 * 获取帖子列表（优先从接口获取，失败时使用模拟数据）
 * @param {Object} params 查询参数
 * @returns {Promise<Object>} 帖子列表数据
 */
const getPostList = async (params = {}) => {
  try {
    return await getPostListFromAPI(params);
  } catch (error) {
    console.error('获取帖子列表失败，使用模拟数据:', error);
    return [];
  }
};

/**
 * 获取帖子详情
 * @param {string|number} id 帖子ID
 * @returns {Promise<Object|null>} 帖子详情数据
 */
const getPostDetail = async (id) => {
  try {
    const response = await request({
      url: `/blade-chat-open/post/detail/${id}`,
      method: 'GET'
    });
    
    if (response.code === 200 && response.success) {
      const post = response.data;
      return processPostDetail(post);
    } else {
      console.error('获取帖子详情失败:', response.msg);
      return null;
    }
  } catch (error) {
    console.error('请求帖子详情接口失败:', error);
    return null;
  }
};

/**
 * 处理帖子详情数据
 * @param {Object} post 原始帖子数据
 * @returns {Object} 处理后的帖子数据
 */
const processPostDetail = (post) => {
  if (!post) return null;

  return post;
};

/**
 * 获取我的帖子列表
 * @param {Object} params 查询参数
 * @returns {Promise<Object>} 我的帖子列表数据
 */
const getMyPostList = async (params = {}) => {
  try {
    const response = await request({
      url: '/blade-miniapp/post/my-posts',
      method: 'GET',
      data: {
        openId: params.openId,
        current: params.current || 1,
        size: params.size || 10
      }
    });
    
    if (response.code === 200 && response.success) {
      const posts = response.data.records || [];
      return {
        records: processPosts(posts),
        total: response.data.total || 0,
        size: response.data.size || 10,
        current: response.data.current || 1
      };
    } else {
      console.error('获取我的帖子列表失败:', response.msg);
      return { records: [], total: 0, size: 10, current: 1 };
    }
  } catch (error) {
    console.error('请求我的帖子接口失败:', error);
    return { records: [], total: 0, size: 10, current: 1 };
  }
};

module.exports = {
  getPostList,
  getPostListFromAPI,
  getPostDetail,
  getMyPostList,
  processPosts,
  processPostDetail
}; 