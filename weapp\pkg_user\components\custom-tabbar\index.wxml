<!-- 页面切换加载动画 - 自定义样式 -->
<view wx:if="{{isNavigating && showLoadingOverlay}}" class="page-loading-overlay">
  <view class="loading-container">
    <!-- 主加载动画 -->
    <view class="main-loading-spinner">
      <view class="spinner-ring"></view>
      <view class="spinner-ring"></view>
      <view class="spinner-ring"></view>
    </view>

    <!-- 加载文字动画 -->
    <view class="loading-text-container">
      <text class="loading-text main-text">事事有着落</text>
      <text class="loading-text sub-text">件件有回音</text>
    </view>

    <!-- 点状进度指示器 -->
    <view class="loading-dots">
      <view class="loading-dot"></view>
      <view class="loading-dot"></view>
      <view class="loading-dot"></view>
      <view class="loading-dot"></view>
      <view class="loading-dot"></view>
    </view>

    <!-- 底部提示 -->
    <text class="loading-tip">{{loadingTip}}</text>
  </view>
</view>

<!-- 自定义错误提示 -->
<view wx:if="{{showErrorMessage}}" class="custom-error-overlay">
  <view class="error-container">
    <view class="error-icon">⚠️</view>
    <text class="error-message">{{errorMessage}}</text>
    <text class="error-tip">事事有着落，件件有回音</text>
  </view>
</view>

<view wx:if="{{show}}" class="custom-tabbar {{isNavigating ? 'navigating' : ''}}">
  <block wx:for="{{tabList}}" wx:key="pagePath" wx:for-index="index">
    <!-- 发布按钮特殊样式 -->
    <view wx:if="{{item.isPublish}}"
          class="tabbar-publish {{currentIndex === index ? 'active' : ''}} {{isNavigating && targetIndex === index ? 'loading' : ''}}"
          bindtap="onTabClick"
          data-index="{{index}}">
      <image wx:if="{{!(isNavigating && targetIndex === index)}}" src="{{item.iconPath}}" class="tabbar-publish-icon"/>
      <view wx:else class="mini-loading">
        <view class="mini-spinner"></view>
      </view>
    </view>

    <!-- 普通tab项 -->
    <view wx:else
          class="tabbar-item {{currentIndex === index ? 'active' : ''}} {{isNavigating && targetIndex === index ? 'loading' : ''}}"
          bindtap="onTabClick"
          data-index="{{index}}">
      <image wx:if="{{!(isNavigating && targetIndex === index)}}"
             src="{{currentIndex === index ? (item.selectedIconPath || item.iconPath) : item.iconPath}}"
             class="tabbar-icon"/>
      <view wx:else class="mini-loading">
        <view class="mini-spinner"></view>
      </view>
      <text class="tabbar-text">{{item.text}}</text>
      <view class="tab-indicator"></view>
    </view>
  </block>
</view>