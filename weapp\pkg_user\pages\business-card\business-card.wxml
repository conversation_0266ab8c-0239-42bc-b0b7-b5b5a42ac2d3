<!-- pkg_user/pages/business-card/business-card.wxml -->
<view class="card-container">
  <!-- 顶部搜索栏 -->
  <view class="search-header">
    <view class="search-box">
      <input
        class="search-input"
        placeholder="搜索收藏的名片..."
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        bindconfirm="onSearch"
      />
      <view class="search-btn" bindtap="onSearch">
        <image class="search-icon" src="/assets/images/home/<USER>"></image>
      </view>
    </view>

    <!-- 管理按钮 -->
    <view class="manage-btn" bindtap="onToggleManage">
      <text class="manage-text">{{isManageMode ? '完成' : '管理'}}</text>
    </view>
  </view>

  <!-- 分类筛选 -->
  <view class="category-filter">
    <scroll-view scroll-x class="category-scroll">
      <view class="category-list">
        <view 
          class="category-item {{selectedCategory === item.id ? 'active' : ''}}"
          wx:for="{{categories}}" 
          wx:key="id"
          data-id="{{item.id}}"
          bindtap="onCategoryChange">
          {{item.name}}
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 名片列表 -->
  <view class="card-list">
    <!-- 加载状态 -->
    <view wx:if="{{loading && cardList.length === 0}}" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在加载名片...</text>
    </view>

    <!-- 空状态 -->
    <view wx:elif="{{!loading && cardList.length === 0}}" class="empty-container">
      <text class="empty-icon">⭐</text>
      <text class="empty-text">暂无收藏名片</text>
      <text class="empty-tip">去名片库收藏感兴趣的名片吧</text>
      <view class="empty-btn" bindtap="onGoToCardLibrary">
        <text>去名片库</text>
      </view>
    </view>

    <!-- 名片列表项 -->
    <view wx:else>
      <view
        class="card-item {{item.isExpanded ? 'expanded' : ''}}"
        wx:for="{{cardList}}"
        wx:key="id"
        data-item="{{item}}"
        data-card-id="{{item.id || item.cardId}}"
        bindtap="onCardTap"
        bindlongpress="onCardLongPress">

        <!-- 名片头像 -->
        <view class="card-avatar">
          <image
            wx:if="{{item.avatar}}"
            src="{{item.avatar}}"
            class="avatar-image"
            mode="aspectFill"
          />
          <text wx:else class="avatar-placeholder">{{item.avatarText}}</text>
        </view>

        <!-- 名片信息 -->
        <view class="card-info">
          <view class="card-header">
            <view class="card-left">
              <text class="card-name">{{item.fullName}}</text>
              <text wx:if="{{item.remark}}" class="card-remark">{{item.remark}}</text>
            </view>
            <view class="card-right">
              <text class="card-category">{{item.category}}</text>
              <view class="expand-indicator {{item.isExpanded ? 'expanded' : ''}}">
                <text class="expand-arrow">▼</text>
              </view>
            </view>
          </view>

          <!-- 基础信息 -->
          <view class="card-details">
            <text class="card-position">{{item.jobTitle}}</text>
            <text class="card-company">{{item.company}}</text>
          </view>

          <view class="card-contact">
            <text class="contact-phone">📱 {{item.phone}}</text>
          </view>

          <!-- 展开的详细信息 -->
          <view class="card-expanded-content {{item.isExpanded ? 'show' : ''}}">
            <!-- 更多联系方式 -->
            <view wx:if="{{item.email || item.weixin}}" class="expanded-section">
              <text class="section-title">联系方式</text>
              <view class="contact-list">
                <view wx:if="{{item.email}}" class="contact-item">
                  <text class="contact-icon">📧</text>
                  <text class="contact-text">{{item.email}}</text>
                </view>
                <view wx:if="{{item.weixin}}" class="contact-item">
                  <text class="contact-icon">💬</text>
                  <text class="contact-text">{{item.weixin}}</text>
                </view>
                <view wx:if="{{item.address}}" class="contact-item">
                  <text class="contact-icon">📍</text>
                  <text class="contact-text">{{item.address}}</text>
                </view>
              </view>
            </view>

            <!-- 商业简介 -->
            <view wx:if="{{item.businessProfile}}" class="expanded-section">
              <text class="section-title">商业简介</text>
              <text class="business-profile">{{item.businessProfile}}</text>
            </view>

            <!-- 备注信息 -->
            <view wx:if="{{item.remark}}" class="expanded-section">
              <text class="section-title">备注</text>
              <text class="remark-content">{{item.remark}}</text>
            </view>

            <!-- 操作按钮 -->
            <view class="expanded-actions" catchtap="stopPropagation">
              <button class="action-button primary" bindtap="onCardLongPress" data-item="{{item}}">
                <text class="button-icon">👁️</text>
                <text class="button-text">查看详情</text>
              </button>
              <button class="action-button secondary" bindtap="onEditCard" data-item="{{item}}" data-index="{{index}}">
                <text class="button-icon">✏️</text>
                <text class="button-text">编辑收藏</text>
              </button>
            </view>
          </view>

          <view class="card-meta">
            <text class="favorite-time">收藏于 {{item.favoriteTime}}</text>
          </view>
        </view>

        <!-- 管理模式操作 -->
        <view wx:if="{{isManageMode}}" class="card-actions">
          <view class="manage-actions">
            <view class="action-btn edit-btn" bindtap="onEditCard" data-item="{{item}}" data-index="{{index}}">
              <text class="action-icon">✏️</text>
            </view>
            <view class="action-btn delete-btn" bindtap="onDeleteCard" data-item="{{item}}" data-index="{{index}}">
              <text class="action-icon">🗑️</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view wx:if="{{hasMore && cardList.length > 0}}" class="load-more">
      <view wx:if="{{loading}}" class="loading-more">
        <view class="loading-spinner small"></view>
        <text>加载中...</text>
      </view>
      <text wx:else class="load-more-text">上拉加载更多</text>
    </view>

    <!-- 没有更多数据 -->
    <view wx:if="{{!hasMore && cardList.length > 0}}" class="no-more">
      <text>⭐ 已显示全部收藏名片</text>
    </view>
  </view>

  <!-- 编辑弹窗 -->
  <view wx:if="{{showEditModal}}" class="modal-overlay" bindtap="onHideEditModal">
    <view class="edit-modal" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">编辑收藏信息</text>
        <view class="modal-close" bindtap="onHideEditModal">×</view>
      </view>

      <view class="modal-content">
        <!-- 名片信息预览 -->
        <view class="card-preview">
          <view class="preview-avatar">
            <image wx:if="{{editingCard.avatar}}" src="{{editingCard.avatar}}" class="preview-image" mode="aspectFill"/>
            <text wx:else class="preview-placeholder">{{editingCard.avatarText}}</text>
          </view>
          <view class="preview-info">
            <text class="preview-name">{{editingCard.fullName}}</text>
            <text class="preview-position">{{editingCard.jobTitle}} · {{editingCard.company}}</text>
          </view>
        </view>

        <!-- 分类选择 -->
        <view class="form-section">
          <text class="section-title">收藏分类</text>
          <view class="category-options">
            <view wx:for="{{favoriteCategories}}" wx:key="*this"
                  class="category-option {{editCategory === item ? 'active' : ''}}"
                  bindtap="onEditCategorySelect"
                  data-category="{{item}}">
              {{item}}
            </view>
            <view class="category-option add-category" bindtap="onShowAddCategory">
              + 新建分类
            </view>
          </view>

          <!-- 新建分类输入框 -->
          <view wx:if="{{showAddCategoryInput}}" class="add-category-input">
            <input class="category-input"
                   placeholder="请输入分类名称"
                   value="{{newCategoryName}}"
                   bindinput="onCategoryInput"
                   maxlength="10"/>
            <view class="input-actions">
              <button class="input-btn cancel" bindtap="onCancelAddCategory">取消</button>
              <button class="input-btn confirm" bindtap="onConfirmAddCategory">确定</button>
            </view>
          </view>
        </view>

        <!-- 备注编辑 -->
        <view class="form-section">
          <text class="section-title">备注信息</text>
          <textarea class="remark-textarea"
                    placeholder="为这张名片添加备注信息（可选）"
                    value="{{editRemark}}"
                    bindinput="onRemarkInput"
                    maxlength="100"
                    show-confirm-bar="false"/>
          <text class="input-count">{{editRemark.length}}/100</text>
        </view>
      </view>

      <view class="modal-footer">
        <button class="modal-btn cancel-btn" bindtap="onHideEditModal">取消</button>
        <button class="modal-btn confirm-btn" bindtap="onConfirmEdit">保存</button>
      </view>
    </view>
  </view>
</view>
