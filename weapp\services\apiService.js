/**
 * API服务封装 - 统一管理blade-wechat下的接口
 */
const { request } = require('../utils/request');

class ApiService {
  
  /**
   * 用户相关接口
   */
  static user = {
    // 获取用户信息
    async info() {
      return await request({
        url: '/blade-chat/user/info',
        method: 'GET'
      });
    },

    // 更新用户信息
    async update(data) {
      return await request({
        url: '/blade-chat/user/update',
        method: 'POST',
        data
      });
    },

    // 获取用户统计
    async stats() {
      return await request({
        url: '/blade-chat/user/stats',
        method: 'GET'
      });
    },

    // 获取用户积分
    async points() {
      return await request({
        url: '/blade-chat/user/points',
        method: 'GET'
      });
    }
  };

  /**
   * 帖子相关接口
   */
  static post = {
    // 获取帖子详情
    async detail(id) {
      return await request({
        url: '/blade-chat/post/detail',
        method: 'GET',
        data: { id }
      });
    },

    // 获取帖子列表
    async list(params) {
      return await request({
        url: '/blade-chat/post/list',
        method: 'GET',
        data: params
      });
    },

    // 发布帖子
    async publish(data) {
      return await request({
        url: '/blade-chat/post/publish',
        method: 'POST',
        data
      });
    },

    // 点赞帖子
    async like(id) {
      return await request({
        url: '/blade-chat/post/like',
        method: 'POST',
        data: { id }
      });
    },

    // 收藏帖子
    async favorite(id) {
      return await request({
        url: '/blade-chat/post/favorite',
        method: 'POST',
        data: { id }
      });
    },

    // 获取拨号记录
    async callHistory(params) {
      return await request({
        url: '/blade-chat/post/call-history',
        method: 'GET',
        data: params
      });
    },

    // 清空拨号记录
    async clearCallHistory() {
      return await request({
        url: '/blade-chat/post/clear-call-history',
        method: 'POST'
      });
    }
  };

  /**
   * 机构相关接口
   */
  static institution = {
    // 获取机构列表（分页）
    async page(params) {
      return await request({
        url: '/blade-chat-open/institution/page',
        method: 'GET',
        data: params
      });
    },

    // 获取机构详情
    async detail(id) {
      return await request({
        url: '/blade-chat-open/institution/detail',
        method: 'GET',
        data: { id }
      });
    },

    // 申请加入机构
    async apply(data) {
      return await request({
        url: '/blade-chat/institution/apply',
        method: 'POST',
        data
      });
    },

    // 创建机构
    async create(data) {
      return await request({
        url: '/blade-chat/institution/create',
        method: 'POST',
        data
      });
    },

    // 更新机构信息
    async update(data) {
      return await request({
        url: '/blade-chat/institution/update',
        method: 'POST',
        data
      });
    },

    // 获取我加入的机构列表
    async myInstitutions(params) {
      return await request({
        url: '/blade-chat/institution/my-institutions',
        method: 'GET',
        data: params
      });
    },

    // 点赞机构
    async like(id) {
      return await request({
        url: '/blade-chat/data-operate/toggle-like/institution/' + id,
        method: 'POST'
      });
    },

    // 收藏机构
    async favorite(id) {
      return await request({
        url: '/blade-chat/data-operate/toggle-favorite/institution/' + id,
        method: 'POST'
      });
    }
  };

  /**
   * 签到相关接口
   */
  static signin = {
    // 获取签到信息
    async info() {
      return await request({
        url: '/blade-chat/signin/info',
        method: 'GET'
      });
    },

    // 执行签到
    async doSignin() {
      return await request({
        url: '/blade-chat/signin/do',
        method: 'POST'
      });
    },

    // 获取月度签到记录
    async monthRecord(params) {
      return await request({
        url: '/blade-chat/signin/record',
        method: 'GET',
        data: params
      });
    },

    // 获取签到统计
    async stats() {
      return await request({
        url: '/blade-chat/signin/stats',
        method: 'GET'
      });
    },

    // 获取签到记录列表（分页）
    async records(params) {
      return await request({
        url: '/blade-chat/signin/records',
        method: 'GET',
        data: params
      });
    },

    // 获取签到统计汇总
    async summary() {
      return await request({
        url: '/blade-chat/signin/summary',
        method: 'GET'
      });
    },

    // 查询用户是否中奖
    async queryUserWin() {
      return await request({
        url: '/blade-chat/signin/queryUserWin',
        method: 'GET'
      });
    }
  };

  /**
   * 名片相关接口
   */
  static businessCard = {
    // 获取当前用户名片详情
    async myCard() {
      return await request({
        url: '/blade-chat/user/card/my-card',
        method: 'GET'
      });
    },

    // 获取名片详情
    async detail(id) {
      return await request({
        url: '/blade-chat/user/card/detail',
        method: 'GET',
        data: { id }
      });
    },

    // 获取名片列表
    async page(params) {
      return await request({
        url: '/blade-chat/user/card/page',
        method: 'GET',
        data: params
      });
    },

    // 保存名片
    async save(data) {
      return await request({
        url: '/blade-chat/user/card/save',
        method: 'POST',
        data
      });
    },

    // 更新名片
    async update(data) {
      return await request({
        url: '/blade-chat/user/card/update',
        method: 'POST',
        data
      });
    },

    // 提交名片（新增或修改）
    async submit(data) {
      return await request({
        url: '/blade-chat/user/card/submit',
        method: 'POST',
        data
      });
    },

    // 删除名片
    async remove(id) {
      return await request({
        url: '/blade-chat/user/card/remove',
        method: 'DELETE',
        data: { id }
      });
    }
  };

  /**
   * 消息相关接口
   */
  static message = {
    // 获取消息列表
    async list(params) {
      return await request({
        url: '/blade-chat/message/list',
        method: 'GET',
        data: params
      });
    },

    // 发送消息
    async send(data) {
      return await request({
        url: '/blade-chat/message/send',
        method: 'POST',
        data
      });
    },

    // 标记消息已读
    async markRead(id) {
      return await request({
        url: '/blade-chat/message/mark-read',
        method: 'POST',
        data: { id }
      });
    }
  };

  /**
   * 文件上传相关接口
   */
  static upload = {
    // 上传图片
    async image(filePath) {
      return new Promise((resolve, reject) => {
        wx.uploadFile({
          url: `${require('../utils/request').BASE_URL || 'http://192.168.31.215'}/blade-resource/oss/endpoint/put-file`,
          filePath: filePath,
          name: 'file',
          header: {
            'Tenant-Id': '000000',
            'Authorization': `Basic d2VhcHA6c2FkZXF3ZXh6Y2Nhc2Rxd2U=`,
            'Blade-Auth': `Bearer ${wx.getStorageSync('token')?.value || wx.getStorageSync('token')}`
          },
          success: (res) => {
            try {
              const data = JSON.parse(res.data);
              if (data.success) {
                resolve(data);
              } else {
                reject(new Error(data.msg || '上传失败'));
              }
            } catch (error) {
              reject(new Error('解析响应失败'));
            }
          },
          fail: reject
        });
      });
    }
  };

  /**
   * 地区相关接口
   */
  static region = {
    // 获取省份列表
    async provinces() {
      return await request({
        url: '/miniapp/region/provinces',
        method: 'GET'
      });
    },

    // 获取城市列表
    async cities(provinceCode) {
      return await request({
        url: '/miniapp/region/cities',
        method: 'GET',
        data: { provinceCode }
      });
    },

    // 获取开放的城市列表
    async openCities() {
      return await request({
        url: '/miniapp/region/cities',
        method: 'GET'
      });
    }
  };

  /**
   * 数据操作相关接口（点赞、收藏等）
   */
  static dataOperate = {
    // 切换点赞状态
    async toggleLike(type, id) {
      return await request({
        url: `/blade-chat/data-operate/toggle-like/${type}/${id}`,
        method: 'POST'
      });
    },

    // 切换收藏状态
    async toggleFavorite(type, id) {
      return await request({
        url: `/blade-chat/data-operate/toggle-favorite/${type}/${id}`,
        method: 'POST'
      });
    }
  };

  /**
   * 认证相关接口
   */
  static auth = {
    // 刷新token
    async refreshToken() {
      const refreshTokenValue = wx.getStorageSync('token')?.refreshToken;
      if (!refreshTokenValue) {
        throw new Error('没有刷新令牌');
      }

      return await request({
        url: '/blade-auth/token',
        method: 'POST',
        data: {
          grantType: 'refresh_token',
          refreshToken: refreshTokenValue
        }
      });
    },

    // 微信登录
    async wechatLogin(code) {
      return await request({
        url: '/blade-auth/token',
        method: 'POST',
        data: {
          grantType: 'wechat_miniapp',
          code: code
        }
      });
    }
  };
}

module.exports = ApiService;
