/**
 * 机构评论服务
 * 基于通用评论服务，专门处理机构评价相关功能
 */
const commentService = require('../../../../services/commentService');

class InstitutionCommentService {
  
  /**
   * 添加机构评价
   * @param {Object} commentData 评价数据
   * @param {string|number} commentData.institutionId 机构ID
   * @param {string} commentData.content 评价内容
   * @param {string} commentData.image 图片URL（可选）
   * @param {Array} commentData.mentionedUserIds 提及的用户ID列表
   * @param {Array} commentData.mentionedUsers 提及的用户信息列表
   * @returns {Promise} API响应
   */
  async addInstitutionComment(commentData) {
    try {
      const requestData = {
        ...commentData,
        relevancyType: 1, // 机构评价
        relevancyId: commentData.institutionId
      };
      
      return await commentService.addComment(requestData);
    } catch (error) {
      console.error('添加机构评价失败:', error);
      throw error;
    }
  }

  /**
   * 回复机构评价
   * @param {Object} replyData 回复数据
   * @param {string|number} replyData.institutionId 机构ID
   * @param {string} replyData.parentId 父评论ID
   * @param {string} replyData.content 回复内容
   * @param {string} replyData.image 图片URL（可选）
   * @param {Array} replyData.mentionedUserIds 提及的用户ID列表
   * @param {Array} replyData.mentionedUsers 提及的用户信息列表
   * @returns {Promise} API响应
   */
  async replyInstitutionComment(replyData) {
    try {
      const requestData = {
        ...replyData,
        relevancyType: 1, // 机构评价
        relevancyId: replyData.institutionId
      };
      
      return await commentService.replyComment(requestData);
    } catch (error) {
      console.error('回复机构评价失败:', error);
      throw error;
    }
  }

  /**
   * 获取机构评价列表
   * @param {string|number} institutionId 机构ID
   * @param {Object} query 查询参数
   * @param {number} query.current 当前页码
   * @param {number} query.size 每页大小
   * @returns {Promise} API响应
   */
  async getInstitutionCommentList(institutionId, query = {}) {
    try {
      return await commentService.getInstitutionCommentList(institutionId, query);
    } catch (error) {
      console.error('获取机构评价列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取评价回复列表
   * @param {string} parentId 父评论ID
   * @param {Object} query 查询参数（可选）
   * @returns {Promise} API响应
   */
  async getCommentReplies(parentId, query = {}) {
    try {
      return await commentService.getCommentReplies(parentId, query);
    } catch (error) {
      console.error('获取回复列表失败:', error);
      throw error;
    }
  }

  /**
   * 点赞/取消点赞评价
   * @param {string} commentId 评论ID
   * @param {string} likeType 点赞类型，默认为'LIKE'
   * @returns {Promise} API响应
   */
  async likeComment(commentId, likeType = 'LIKE') {
    try {
      return await commentService.likeComment(commentId, likeType);
    } catch (error) {
      console.error('点赞评价失败:', error);
      throw error;
    }
  }

  /**
   * 标记评价是否有帮助
   * @param {string} feedbackId 评价ID
   * @returns {Promise} API响应
   */
  async toggleHelpful(feedbackId) {
    try {
      return await commentService.toggleHelpful(feedbackId);
    } catch (error) {
      console.error('标记有帮助失败:', error);
      throw error;
    }
  }

  /**
   * 删除评价
   * @param {string} commentId 评论ID
   * @returns {Promise} API响应
   */
  async removeComment(commentId) {
    try {
      return await commentService.removeComment(commentId);
    } catch (error) {
      console.error('删除评价失败:', error);
      throw error;
    }
  }

  /**
   * 处理评价列表数据
   * @param {Array} rawComments 原始评价数据
   * @returns {Array} 处理后的评价数据
   */
  processCommentList(rawComments) {
    if (!Array.isArray(rawComments)) {
      return [];
    }

    return rawComments.map(comment => ({
      id: comment.id,
      feedbackId: comment.id,
      commentId: comment.id,
      avatar: comment.avatar || '/assets/images/bot-avatar.png',
      nickname: comment.nickname || '匿名用户',
      content: comment.content || '',
      time: this.formatTime(comment.createTime),
      likes: comment.helpfulCount || 0,
      isHelpful: comment.isHelpful || false,
      replyToUserName: comment.replyToUserName || '',
      image: comment.image || '',
      replyCount: comment.replyCount || 0,
      expanded: false,
      replies: [],
      hasMoreReplies: (comment.replyCount || 0) > 0,
      allRepliesLoaded: false,
      totalReplyCount: comment.replyCount || 0,
      userId: comment.userId || '',
      formattedContent: comment.content || ''
    }));
  }

  /**
   * 格式化时间
   * @param {string} timeStr 时间字符串
   * @returns {string} 格式化后的时间
   */
  formatTime(timeStr) {
    if (!timeStr) return '';

    const time = new Date(timeStr);
    const now = new Date();
    const diff = now - time;

    // 小于1分钟
    if (diff < 60000) {
      return '刚刚';
    }
    // 小于1小时
    if (diff < 3600000) {
      return Math.floor(diff / 60000) + '分钟前';
    }
    // 小于1天
    if (diff < 86400000) {
      return Math.floor(diff / 3600000) + '小时前';
    }
    // 小于7天
    if (diff < 604800000) {
      return Math.floor(diff / 86400000) + '天前';
    }

    // 超过7天显示具体日期
    return time.toLocaleDateString();
  }
}

// 创建单例实例
const institutionCommentService = new InstitutionCommentService();

module.exports = institutionCommentService;
