.ai-report-card {
  display: flex;
  align-items: center;
  background: linear-gradient(90deg, #ff7b7b 0%, #ffb6b6 100%);
  border-radius: 24rpx;
  box-shadow: 0 4rpx 24rpx rgba(255,107,107,0.08);
  padding: 32rpx 32rpx 32rpx 40rpx;
  margin-bottom: 32rpx;
  color: #fff;
  cursor: pointer;
}
.ai-report-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}
.ai-report-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}
.ai-report-desc {
  font-size: 24rpx;
  opacity: 0.95;
}
.ai-report-suggest {
  font-size: 22rpx;
  opacity: 0.85;
}
.ai-report-arrow {
  width: 40rpx;
  height: 40rpx;
  margin-left: 24rpx;
} 