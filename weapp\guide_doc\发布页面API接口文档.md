# 发布页面API接口文档

## 概述

本文档描述了发布页面相关的所有RESTful API接口，包括发布内容、栏目管理、标签分类、草稿管理等功能。

## 基础信息

- **基础URL**: `https://api.example.com`
- **API版本**: `v1`
- **认证方式**: Bearer <PERSON>
- **数据格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式

### 成功响应
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    // 具体数据
  },
  "timestamp": 1640995200000
}
```

### 错误响应
```json
{
  "code": 400,
  "msg": "参数错误",
  "data": null,
  "timestamp": 1640995200000
}
```

### 分页响应
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "records": [],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  },
  "timestamp": 1640995200000
}
```

## 1. 分类管理接口

### 1.1 获取分类列表
**接口描述**: 获取所有可用的分类信息

**请求信息**:
- **URL**: `GET /blade-chat/category/list`
- **认证**: 需要
- **参数**: 无

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "records": [
      {
        "id": "1",
        "name": "二手交易",
        "code": "second_hand",
        "icon": "https://example.com/icon1.png",
        "sort": 1,
        "status": 1,
        "parentId": "0",
        "level": 1,
        "children": [
          {
            "id": "11",
            "name": "数码产品",
            "code": "digital",
            "icon": "https://example.com/icon11.png",
            "sort": 1,
            "status": 1,
            "parentId": "1",
            "level": 2
          }
        ]
      }
    ],
    "total": 10,
    "size": 20,
    "current": 1,
    "pages": 1
  }
}
```

### 1.2 获取分类详情
**接口描述**: 根据分类ID获取详细信息

**请求信息**:
- **URL**: `GET /blade-chat/category/{id}`
- **认证**: 需要
- **路径参数**: 
  - `id`: 分类ID

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "id": "1",
    "name": "二手交易",
    "code": "second_hand",
    "icon": "https://example.com/icon1.png",
    "description": "二手商品交易分类",
    "sort": 1,
    "status": 1,
    "parentId": "0",
    "level": 1,
    "createTime": "2024-01-01 12:00:00",
    "updateTime": "2024-01-01 12:00:00"
  }
}
```

## 2. 标签管理接口

### 2.1 获取标签列表
**接口描述**: 获取所有标签信息，支持按分类筛选

**请求信息**:
- **URL**: `GET /blade-chat/tag/list`
- **认证**: 需要
- **查询参数**:
  - `categoryId`: 分类ID（可选）
  - `type`: 标签类型（system-系统标签，custom-自定义标签）
  - `status`: 状态（1-启用，0-禁用）
  - `current`: 当前页码
  - `size`: 每页大小

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "records": [
      {
        "id": "1",
        "name": "热门",
        "code": "hot",
        "categoryId": "1",
        "categoryName": "二手交易",
        "type": "system",
        "color": "#FF6B6B",
        "icon": "https://example.com/hot.png",
        "sort": 1,
        "status": 1,
        "useCount": 156,
        "createTime": "2024-01-01 12:00:00"
      }
    ],
    "total": 50,
    "size": 10,
    "current": 1,
    "pages": 5
  }
}
```

### 2.2 根据分类获取标签
**接口描述**: 根据分类ID获取该分类下的所有标签

**请求信息**:
- **URL**: `GET /blade-chat/tag/category/{categoryId}`
- **认证**: 需要
- **路径参数**:
  - `categoryId`: 分类ID

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": [
    {
      "id": "1",
      "name": "热门",
      "code": "hot",
      "color": "#FF6B6B",
      "icon": "https://example.com/hot.png",
      "type": "system",
      "sort": 1
    },
    {
      "id": "2",
      "name": "急售",
      "code": "urgent",
      "color": "#FF8E53",
      "icon": "https://example.com/urgent.png",
      "type": "system",
      "sort": 2
    }
  ]
}
```

### 2.3 创建自定义标签
**接口描述**: 用户创建自定义标签

**请求信息**:
- **URL**: `POST /blade-chat/tag/custom`
- **认证**: 需要
- **请求体**:
```json
{
  "name": "自定义标签",
  "categoryId": "1",
  "color": "#4ECDC4"
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "创建成功",
  "data": {
    "id": "101",
    "name": "自定义标签",
    "code": "custom_101",
    "categoryId": "1",
    "type": "custom",
    "color": "#4ECDC4",
    "createTime": "2024-01-01 12:00:00"
  }
}
```

## 3. 发布内容接口

### 3.1 创建帖子
**接口描述**: 发布新的帖子内容

**请求信息**:
- **URL**: `POST /blade-chat/post/create`
- **认证**: 需要
- **请求体**:
```json
{
  "title": "帖子标题",
  "description": "帖子详细描述",
  "categoryId": "1",
  "categoryName": "二手交易",
  "contactName": "联系人姓名",
  "contactType": "phone",
  "contactNumber": "13800138000",
  "location": "北京市朝阳区",
  "latitude": 39.9042,
  "longitude": 116.4074,
  "tags": ["热门", "急售", "面交"],
  "images": [
    {
      "url": "https://example.com/image1.jpg",
      "size": 1024000,
      "type": "image/jpeg",
      "name": "image1.jpg"
    }
  ],
  "publishType": "普通",
  "deviceInfo": {
    "platform": "ios",
    "system": "iOS 15.0",
    "model": "iPhone 13"
  }
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "发布成功",
  "data": {
    "id": "post_123456",
    "title": "帖子标题",
    "status": "pending",
    "createTime": "2024-01-01 12:00:00",
    "auditStatus": "pending"
  }
}
```

### 3.2 更新帖子
**接口描述**: 更新已发布的帖子内容

**请求信息**:
- **URL**: `PUT /blade-chat/post/{id}`
- **认证**: 需要
- **路径参数**:
  - `id`: 帖子ID
- **请求体**: 同创建帖子

**响应示例**:
```json
{
  "code": 200,
  "msg": "更新成功",
  "data": {
    "id": "post_123456",
    "updateTime": "2024-01-01 12:30:00"
  }
}
```

### 3.3 获取帖子详情
**接口描述**: 根据帖子ID获取详细信息

**请求信息**:
- **URL**: `GET /blade-chat/post/{id}`
- **认证**: 需要
- **路径参数**:
  - `id`: 帖子ID

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "id": "post_123456",
    "title": "帖子标题",
    "description": "帖子详细描述",
    "categoryId": "1",
    "categoryName": "二手交易",
    "contactName": "联系人姓名",
    "contactType": "phone",
    "contactNumber": "138****8000",
    "location": "北京市朝阳区",
    "latitude": 39.9042,
    "longitude": 116.4074,
    "tags": ["热门", "急售"],
    "images": [
      {
        "url": "https://example.com/image1.jpg",
        "size": 1024000,
        "type": "image/jpeg"
      }
    ],
    "publishType": "普通",
    "status": "published",
    "auditStatus": "approved",
    "viewCount": 156,
    "likeCount": 23,
    "commentCount": 5,
    "createTime": "2024-01-01 12:00:00",
    "updateTime": "2024-01-01 12:00:00"
  }
}
```

## 4. 草稿管理接口

### 4.1 保存草稿
**接口描述**: 保存帖子草稿

**请求信息**:
- **URL**: `POST /blade-chat/post/draft`
- **认证**: 需要
- **请求体**:
```json
{
  "title": "草稿标题",
  "description": "草稿内容",
  "categoryId": "1",
  "contactName": "联系人",
  "contactType": "phone",
  "contactNumber": "13800138000",
  "location": "北京市",
  "latitude": 39.9042,
  "longitude": 116.4074,
  "tags": ["热门"],
  "images": [
    {
      "url": "https://example.com/draft1.jpg",
      "size": 1024000
    }
  ]
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "保存成功",
  "data": {
    "id": "draft_123456",
    "createTime": "2024-01-01 12:00:00"
  }
}
```

### 4.2 更新草稿
**接口描述**: 更新已存在的草稿

**请求信息**:
- **URL**: `PUT /blade-chat/post/draft/{id}`
- **认证**: 需要
- **路径参数**:
  - `id`: 草稿ID
- **请求体**: 同保存草稿

**响应示例**:
```json
{
  "code": 200,
  "msg": "更新成功",
  "data": {
    "id": "draft_123456",
    "updateTime": "2024-01-01 12:30:00"
  }
}
```

### 4.3 获取草稿列表
**接口描述**: 获取用户的草稿列表

**请求信息**:
- **URL**: `GET /blade-chat/post/draft-list`
- **认证**: 需要
- **查询参数**:
  - `current`: 当前页码
  - `size`: 每页大小

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "records": [
      {
        "id": "draft_123456",
        "title": "草稿标题",
        "description": "草稿内容预览...",
        "categoryName": "二手交易",
        "imageCount": 2,
        "tagCount": 3,
        "createTime": "2024-01-01 12:00:00",
        "updateTime": "2024-01-01 12:30:00"
      }
    ],
    "total": 5,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 4.4 获取草稿详情
**接口描述**: 根据草稿ID获取详细信息

**请求信息**:
- **URL**: `GET /blade-chat/post/draft/{id}`
- **认证**: 需要
- **路径参数**:
  - `id`: 草稿ID

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "id": "draft_123456",
    "title": "草稿标题",
    "description": "草稿详细内容",
    "categoryId": "1",
    "categoryName": "二手交易",
    "contactName": "联系人",
    "contactType": "phone",
    "contactNumber": "13800138000",
    "location": "北京市",
    "latitude": 39.9042,
    "longitude": 116.4074,
    "tags": "[\"热门\", \"急售\"]",
    "images": "[{\"url\": \"https://example.com/draft1.jpg\", \"size\": 1024000}]",
    "createTime": "2024-01-01 12:00:00",
    "updateTime": "2024-01-01 12:30:00"
  }
}
```

### 4.5 删除草稿
**接口描述**: 删除指定的草稿

**请求信息**:
- **URL**: `DELETE /blade-chat/post/draft/{id}`
- **认证**: 需要
- **路径参数**:
  - `id`: 草稿ID

**响应示例**:
```json
{
  "code": 200,
  "msg": "删除成功",
  "data": null
}
```

## 5. 文件上传接口

### 5.1 上传图片
**接口描述**: 上传帖子图片

**请求信息**:
- **URL**: `POST /blade-chat/upload/image`
- **认证**: 需要
- **Content-Type**: `multipart/form-data`
- **请求参数**:
  - `file`: 图片文件
  - `category`: 分类（可选，用于文件分类存储）

**响应示例**:
```json
{
  "code": 200,
  "msg": "上传成功",
  "data": {
    "url": "https://example.com/uploads/image_123456.jpg",
    "size": 1024000,
    "type": "image/jpeg",
    "name": "image_123456.jpg",
    "width": 1920,
    "height": 1080
  }
}
```

### 5.2 批量上传图片
**接口描述**: 批量上传多张图片

**请求信息**:
- **URL**: `POST /blade-chat/upload/images`
- **认证**: 需要
- **Content-Type**: `multipart/form-data`
- **请求参数**:
  - `files[]`: 图片文件数组
  - `category`: 分类（可选）

**响应示例**:
```json
{
  "code": 200,
  "msg": "上传成功",
  "data": [
    {
      "url": "https://example.com/uploads/image_1.jpg",
      "size": 1024000,
      "type": "image/jpeg",
      "name": "image_1.jpg"
    },
    {
      "url": "https://example.com/uploads/image_2.jpg",
      "size": 2048000,
      "type": "image/jpeg",
      "name": "image_2.jpg"
    }
  ]
}
```

## 6. 位置服务接口

### 6.1 获取位置信息
**接口描述**: 根据经纬度获取位置信息

**请求信息**:
- **URL**: `GET /blade-chat/location/info`
- **认证**: 需要
- **查询参数**:
  - `latitude`: 纬度
  - `longitude`: 经度

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "address": "北京市朝阳区三里屯街道",
    "province": "北京市",
    "city": "北京市",
    "district": "朝阳区",
    "street": "三里屯街道",
    "latitude": 39.9042,
    "longitude": 116.4074
  }
}
```

## 7. 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

### 业务错误码

| 错误码 | 说明 |
|--------|------|
| 1001 | 分类不存在 |
| 1002 | 标签不存在 |
| 1003 | 帖子不存在 |
| 1004 | 草稿不存在 |
| 1005 | 图片上传失败 |
| 1006 | 文件格式不支持 |
| 1007 | 文件大小超限 |
| 1008 | 内容包含敏感词 |
| 1009 | 发布频率过高 |

## 8. 接口调用示例

### 8.1 完整发布流程

```javascript
// 1. 获取分类列表
const categories = await request({
  url: '/blade-chat/category/list',
  method: 'GET'
});

// 2. 根据分类获取标签
const tags = await request({
  url: `/blade-chat/tag/category/${categoryId}`,
  method: 'GET'
});

// 3. 上传图片
const uploadResult = await request({
  url: '/blade-chat/upload/images',
  method: 'POST',
  data: formData
});

// 4. 发布帖子
const postResult = await request({
  url: '/blade-chat/post/create',
  method: 'POST',
  data: {
    title: '帖子标题',
    description: '帖子内容',
    categoryId: categoryId,
    tags: selectedTags,
    images: uploadResult.data,
    // ... 其他字段
  }
});
```

### 8.2 草稿管理流程

```javascript
// 1. 保存草稿
const draftResult = await request({
  url: '/blade-chat/post/draft',
  method: 'POST',
  data: draftData
});

// 2. 获取草稿列表
const draftList = await request({
  url: '/blade-chat/post/draft-list',
  method: 'GET',
  data: { current: 1, size: 10 }
});

// 3. 加载草稿详情
const draftDetail = await request({
  url: `/blade-chat/post/draft/${draftId}`,
  method: 'GET'
});

// 4. 发布草稿
const publishResult = await request({
  url: '/blade-chat/post/create',
  method: 'POST',
  data: {
    ...draftDetail.data,
    tags: JSON.parse(draftDetail.data.tags),
    images: JSON.parse(draftDetail.data.images)
  }
});

// 5. 删除草稿
await request({
  url: `/blade-chat/post/draft/${draftId}`,
  method: 'DELETE'
});
```

## 9. 注意事项

1. **认证要求**: 除公开接口外，所有接口都需要在请求头中携带有效的认证Token
2. **参数验证**: 所有必填参数都需要进行验证，缺失或格式错误会返回400错误
3. **文件上传**: 图片文件大小限制为10MB，支持jpg、png、gif格式
4. **频率限制**: 发布接口有频率限制，建议间隔不少于30秒
5. **内容审核**: 发布的内容会进行敏感词检测，包含敏感词的内容会被拒绝
6. **数据格式**: 所有时间字段使用ISO 8601格式，坐标使用WGS84坐标系 