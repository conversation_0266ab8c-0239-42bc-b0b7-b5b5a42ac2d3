/**
 * 机构收藏功能测试
 * 用于验证修复后的收藏功能是否正常工作
 */

const { toggleInstitutionFavorite } = require('./institutionDetailStore.js');

/**
 * 测试机构收藏功能
 */
async function testInstitutionFavorite() {
  console.log('开始测试机构收藏功能...');
  
  try {
    // 测试用例1: 正常收藏操作
    console.log('\n=== 测试用例1: 正常收藏操作 ===');
    const testInstitutionId = '123';
    const currentFavoriteStatus = false; // 假设当前未收藏
    
    console.log(`测试机构ID: ${testInstitutionId}`);
    console.log(`当前收藏状态: ${currentFavoriteStatus}`);
    
    const result = await toggleInstitutionFavorite(testInstitutionId, currentFavoriteStatus);
    console.log(`操作结果: ${result}`);
    console.log(`预期结果: true (已收藏)`);
    console.log(`测试结果: ${result === true ? '✅ 通过' : '❌ 失败'}`);
    
  } catch (error) {
    console.error('测试失败:', error.message);
    
    // 检查错误类型
    if (error.message.includes('机构ID不能为空')) {
      console.log('❌ 参数验证失败');
    } else if (error.message.includes('收藏操作失败')) {
      console.log('❌ 收藏操作失败');
    } else if (error.message.includes('网络')) {
      console.log('⚠️  网络连接问题，这是正常的（在测试环境中）');
    } else {
      console.log('❌ 未知错误');
    }
  }
  
  try {
    // 测试用例2: 空参数验证
    console.log('\n=== 测试用例2: 空参数验证 ===');
    await toggleInstitutionFavorite('', false);
    console.log('❌ 应该抛出参数错误');
  } catch (error) {
    if (error.message.includes('机构ID不能为空')) {
      console.log('✅ 参数验证正常');
    } else {
      console.log('❌ 参数验证异常:', error.message);
    }
  }
  
  try {
    // 测试用例3: null参数验证
    console.log('\n=== 测试用例3: null参数验证 ===');
    await toggleInstitutionFavorite(null, false);
    console.log('❌ 应该抛出参数错误');
  } catch (error) {
    if (error.message.includes('机构ID不能为空')) {
      console.log('✅ null参数验证正常');
    } else {
      console.log('❌ null参数验证异常:', error.message);
    }
  }
  
  console.log('\n=== 测试完成 ===');
}

/**
 * 测试API配置
 */
function testAPIConfig() {
  console.log('\n=== 测试API配置 ===');
  
  try {
    const { institution: API } = require('../config/api.js');
    
    console.log('机构API配置:');
    console.log('- page:', API.page);
    console.log('- detail:', API.detail);
    console.log('- services:', API.services);
    console.log('- reviews:', API.reviews);
    console.log('- book:', API.book);
    console.log('- view:', API.view);
    
    // 检查必要的API是否存在
    const requiredAPIs = ['page', 'detail', 'services', 'reviews', 'book', 'view'];
    const missingAPIs = requiredAPIs.filter(api => !API[api]);
    
    if (missingAPIs.length === 0) {
      console.log('✅ 所有必要的API配置都存在');
    } else {
      console.log('❌ 缺少API配置:', missingAPIs.join(', '));
    }
    
  } catch (error) {
    console.error('❌ API配置测试失败:', error.message);
  }
}

/**
 * 测试数据操作Store
 */
function testDataOperateStore() {
  console.log('\n=== 测试数据操作Store ===');
  
  try {
    const { toggleFavorite, DATA_TYPES } = require('./dataOperateStore.js');
    
    console.log('数据操作Store导入成功');
    console.log('- toggleFavorite函数:', typeof toggleFavorite);
    console.log('- DATA_TYPES常量:', DATA_TYPES);
    
    // 检查数据类型常量
    if (DATA_TYPES.INSTITUTION === 'institution') {
      console.log('✅ INSTITUTION数据类型正确');
    } else {
      console.log('❌ INSTITUTION数据类型错误:', DATA_TYPES.INSTITUTION);
    }
    
    if (typeof toggleFavorite === 'function') {
      console.log('✅ toggleFavorite函数存在');
    } else {
      console.log('❌ toggleFavorite函数不存在');
    }
    
  } catch (error) {
    console.error('❌ 数据操作Store测试失败:', error.message);
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始运行机构收藏功能测试套件');
  console.log('=====================================');
  
  // 测试API配置
  testAPIConfig();
  
  // 测试数据操作Store
  testDataOperateStore();
  
  // 测试收藏功能
  await testInstitutionFavorite();
  
  console.log('\n=====================================');
  console.log('🏁 测试套件运行完成');
}

// 如果直接运行此文件，执行测试
if (typeof module !== 'undefined' && require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testInstitutionFavorite,
  testAPIConfig,
  testDataOperateStore,
  runAllTests
};
