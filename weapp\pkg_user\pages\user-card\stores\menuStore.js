const { request } = require('../utils/request.js');

// 获取菜单配置数据
const getMenuConfig = async () => {
  try {
    const response = await request({
      url: '/blade-chat-open/config/menu',
      method: 'GET'
    });
    if (response.code === 200 && response.success) {
      const data = {
        menus: response.data.menus || [],
        banners: response.data.banners || [],
        userMenus: response.data.userMenus || []
      };
      return data;
    } else {
      console.error('获取菜单配置失败:', response.msg);
      return {
        menus: [],
        banners: [],
        userMenus: []
      };
    }
  } catch (error) {
    console.error('请求菜单配置接口失败:', error);
    return {
      menus: [],
      banners: [],
      userMenus: []
    };
  }
};

// 处理菜单数据，转换为 quickAccess 格式
const processMenusToQuickAccess = (menus) => {
  if (!menus || !Array.isArray(menus)) {
    return [];
  }
  
  return menus.map(menu => ({
    id: menu.id,
    icon: menu.image || '/assets/images/menu/more.png', // 默认图标
    title: menu.name,
    color: menu.color || '#FF7800FF',
    url: menu.url || '',
    sortWeight: parseInt(menu.sortWeight) || 0
  })).sort((a, b) => a.sortWeight - b.sortWeight); // 按权重排序
};

// 处理轮播图数据
const processBanners = (banners) => {
  if (!banners || !Array.isArray(banners)) {
    return [];
  }
  
  return banners.map(banner => ({
    id: banner.id,
    image: banner.image,
    title: banner.name,
    url: banner.url || '',
    color: banner.color || '#4F3333FF'
  })).sort((a, b) => parseInt(b.sortWeight) - parseInt(a.sortWeight)); // 按权重倒序
};

module.exports = {
  getMenuConfig,
  processMenusToQuickAccess,
  processBanners
}; 
