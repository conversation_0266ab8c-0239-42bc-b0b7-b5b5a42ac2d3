<layout title="个人信息" showBack="true" showBackButton="true"  background="transparent"
  text-color="#000">
  <view class="profile-header">
    <image class="avatar" src="{{userInfo.avatar || '/assets/images/default-avatar.png'}}" mode="aspectFill" bindtap="onChangeAvatar" />
    <button class="change-avatar-btn" bindtap="onChangeAvatar">更换头像</button>
  </view>
  <view wx:if="{{showAvatarLoading}}" class="loading-mask">
    <view class="loading-spinner"></view>
  </view>
  <view class="profile-list">
    <profile-item label="昵称" value="{{userInfo.nickname}}" canEdit="true" bind:tap="onEditNickname" />
    <picker mode="selector" range="{{genderArray}}" value="{{genderIndex}}" bindchange="onGenderChange">
      <profile-item label="性别" value="{{userInfo.gender}}" canEdit="true" />
    </picker>
    <picker mode="date" value="{{userInfo.birthday}}" start="1900-01-01" end="2099-12-31" bindchange="onBirthdayChange">
      <profile-item label="生日" value="{{userInfo.birthday}}" canEdit="true" />
    </picker>
    <picker mode="region" bindchange="onRegionChange">
      <profile-item label="地区" value="{{userInfo.region}}" canEdit="true" />
    </picker>
    <profile-item label="个性签名" value="{{userInfo.signature}}" canEdit="true" bind:tap="onEditSignature" />
    <profile-item label="手机号" value="{{userInfo.phone}}" />
    <!-- <profile-item label="邮箱地址" value="{{userInfo.email}}" /> -->
    <!-- <profile-item label="实名认证" value="{{userInfo.verified ? '已认证' : '未认证'}}" /> -->
  </view>
  <button class="logout-btn" bindtap="onLogout">退出登录</button>
  <!-- 昵称编辑弹窗 -->
  <view wx:if="{{showNicknameDialog}}" class="dialog-mask">
    <view class="dialog-box">
      <view class="dialog-title">修改昵称</view>
      <input class="dialog-input" value="{{nicknameInput}}" bindinput="onNicknameInput" maxlength="12" />
      <view class="dialog-actions">
        <button class="dialog-btn" bindtap="onNicknameDialogCancel">取消</button>
        <button class="dialog-btn primary" bindtap="onNicknameDialogConfirm">确定</button>
      </view>
    </view>
  </view>
  <!-- 个性签名编辑弹窗 -->
  <view wx:if="{{showSignatureDialog}}" class="dialog-mask">
    <view class="dialog-box">
      <view class="dialog-title">修改个性签名</view>
      <textarea class="dialog-input" value="{{signatureInput}}" bindinput="onSignatureInput" maxlength="30" />
      <view class="dialog-actions">
        <button class="dialog-btn" bindtap="onSignatureDialogCancel">取消</button>
        <button class="dialog-btn primary" bindtap="onSignatureDialogConfirm">确定</button>
      </view>
    </view>
  </view>
</layout> 