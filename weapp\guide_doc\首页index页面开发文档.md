# 首页 index 页面开发文档

## 1. 页面结构概览

首页采用组件化开发，主要结构如下：

- 顶部自定义导航栏（custom-nav）
- 下拉刷新金币动画区
- 主内容区（scroll-view，含统计条、轮播、快捷入口、栏目卡片等）
- 底部自定义 TabBar（custom-tabbar）

---

## 2. 主要组件与作用

### 2.1 顶部导航栏

```
<custom-nav 
  id="custom-nav"
  background="linear-gradient(to bottom, #ff6b6b, #ff8585)"
  text-color="#ffffff"
  show-location="{{true}}"
  show-back="{{false}}"
  fixed="{{true}}"
  bind:navReady="onNavReady"
  bind:showDrawer="onShowDrawer">
</custom-nav>
```
- 自定义导航栏，支持背景渐变、文字色、位置选择等。
- 事件：
  - `navReady`：导航栏高度计算完成，便于内容区适配。
  - `showDrawer`：触发侧边栏（如有）。

### 2.2 下拉刷新动画区

```
<view wx:if="{{isRefreshing}}" class="refresh-coins-fixed" style="padding-top: {{navBarHeight}}px;">
  <block wx:for="{{coins}}" wx:key="index">
    <view ...>🪙</view>
  </block>
</view>
```
- 下拉刷新时顶部展示金币动画，提升用户体验。
- `coins` 数组控制金币数量、位置、动画。

### 2.3 主内容区（scroll-view）

```
<scroll-view ...>
  <view class="main-content">
    <view wx:if="{{isRefreshing}}" class="refresh-bottom-area">
      <view class="coin-tray"></view>
      <text class="refresh-text">下一个信息可能有宝藏...</text>
    </view>
    <stats-bar></stats-bar>
    <banner-swiper list="{{banners}}"></banner-swiper>
    <quick-access list="{{quickAccess}}"></quick-access>
    <view class="card-section">
      <card title="宣传栏目" moreText="查看更多">
        <view class="category-tabs">
          <view class="tab-item {{currentTab === index ? 'active' : ''}}" wx:for="{{categories}}" wx:key="index" data-index="{{index}}">
            {{item}}
          </view>
        </view>
        <content-card wx:for="{{posts}}" wx:key="id" post="{{item}}" />
      </card>
    </view>
  </view>
</scroll-view>
```
- 支持下拉刷新，自动适配导航栏高度。
- 事件：
  - `onScroll`：滚动监听（如吸顶、懒加载等）。
  - `onPullDownRefresh`：下拉刷新触发。
- 主要子组件：
  - `stats-bar`：热度统计（浏览、发布、分享等），可根据地理位置动态刷新。
  - `banner-swiper`：轮播图，数据由 `banners` 数组驱动。
  - `quick-access`：快捷入口，数据由 `quickAccess` 数组驱动。
  - `card-section`：栏目卡片，支持多分类切换与内容展示。
    - `categories`：栏目分类数组。
    - `posts`：当前分类下的内容列表。

### 2.4 底部自定义TabBar

```
<custom-tabbar show="{{showTabbar}}" />
```
- 自定义底部导航栏，支持首页/发布/我的等Tab切换。

---

## 3. 数据流与接口

- `banners`、`quickAccess`、`categories`、`posts` 等数据建议通过接口异步获取。
- 统计区（`stats-bar`）可根据地理位置变化动态刷新。
- 推荐在 `onLoad`、`onPullDownRefresh` 等生命周期中拉取数据。

---

## 4. 交互说明

- 下拉刷新：触发金币动画和数据刷新。
- 导航栏位置切换：可影响统计区、内容区等数据的展示。
- 栏目切换：点击不同tab切换内容。
- 轮播图、快捷入口、内容卡片：均支持点击跳转（需在js中实现事件处理）。

---

## 5. 扩展建议

- 组件复用：如 `banner-swiper`、`quick-access`、`content-card` 建议独立为可复用组件。
- 样式适配：注意导航栏高度、底部安全区适配。
- 性能优化：长列表建议分页或懒加载。
- 无数据/异常处理：各区块建议有 loading/empty/error 状态。

---

## 6. 相关文件

- `index.wxml`：页面结构
- `index.js`：数据与交互逻辑
- `index.wxss`：页面样式
- `components/`：各自定义组件

---

如需详细组件开发文档或接口对接说明，请补充需求。 