import { request } from '../utils/request.js';

/**
 * 获取分类下的动态表单配置
 * @param {string|number} categoryId
 * @returns {Promise<Object|null>} configJson对象
 */
export const getDynamicFormConfig = async (categoryId) => {
  const res = await request({
    url: `/blade-chat/post/form/category/${categoryId}`,
    method: 'GET'
  });
  console.log('getDynamicFormConfig', res);

  if (res.data && res.data.configJson) {
    console.log('getDynamicFormConfig', res.data);
    return JSON.parse(res.data.configJson).schemas[0];
  }
  return null;
}; 