Component({
  properties: {
    list: {
      type: Array,
      value: []
    }
  },
  data: {
    pagedList: []
  },
  observers: {
    'list': function(list) {
      if (!list || list.length === 0) {
        this.setData({ pagedList: [] });
        return;
      }
      const pageSize = 8; // 每页8个
      const pagedList = [];
      for (let i = 0; i < list.length; i += pageSize) {
        pagedList.push(list.slice(i, i + pageSize));
      }
      this.setData({ pagedList });
    }
  },
  methods: {
    onItemTap(e) {
      const item = e.currentTarget.dataset.item;
      console.log('点击了快速访问项:', item);
      
      // 触发父组件事件
      this.triggerEvent('itemtap', { item });
      
      // 如果有URL，进行跳转
      if (item.url) {
        if (item.url.startsWith('/')) {
          wx.navigateTo({
            url: item.url
          });
        } else {
          // 处理外部链接或其他逻辑
          console.log('处理外部链接:', item.url);
        }
      }
    }
  }
});
