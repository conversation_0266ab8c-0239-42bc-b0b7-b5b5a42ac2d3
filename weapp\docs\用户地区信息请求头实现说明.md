# 用户地区信息请求头实现说明

## 功能概述

实现了当用户选择地址时，将用户请求来自哪个地区的信息放到请求头的 `user-region` 字段中，例如：`user-region: '3207'`

## 实现方案

### 1. 创建地区管理工具

**文件：** `weapp/utils/regionManager.js`

**主要功能：**
- 保存用户选择的地区信息到本地存储
- 获取用户地区编码用于请求头
- 提供地区信息的增删改查功能
- 支持地区信息过期检查

**核心方法：**
```javascript
// 保存用户地区信息
RegionManager.saveUserRegion(regionInfo)

// 获取地区编码（用于请求头）
RegionManager.getUserRegionCode()

// 获取完整地区信息
RegionManager.getUserRegion()

// 清除地区信息
RegionManager.clearUserRegion()
```

### 2. 修改地区选择组件

**修改文件：**
- `weapp/components/region-picker/index.js`
- `weapp/pkg_user/components/region-picker/index.js`

**修改内容：**
```javascript
// 在确认选择时保存地区信息
confirmSelection() {
  // ... 原有逻辑

  // 保存用户选择的地区信息到本地存储
  const regionInfo = {
    regionCode: lastRegion.regionCode,
    regionName: lastRegion.regionName,
    level: lastRegion.level,
    selectedRegions: selectedRegions,
    fullAddress: fullAddress
  };
  
  RegionManager.saveUserRegion(regionInfo);
  console.log('用户选择地区已保存:', regionInfo);

  // ... 原有逻辑
}
```

### 3. 修改请求拦截器

**修改文件：**
- `weapp/utils/request.js`
- `weapp/pkg_common/utils/request.js`
- `weapp/pkg_merchant/utils/request.js`
- `weapp/pkg_user/utils/request.js`

**修改内容：**
```javascript
// 请求拦截器中添加用户地区头
const requestInterceptor = (config) => {
  // ... 原有逻辑

  // 添加用户地区头
  try {
    const RegionManager = require('./regionManager');
    const userRegionCode = RegionManager.getUserRegionCode();
    if (userRegionCode) {
      config.header = {
        ...config.header,
        'user-region': userRegionCode
      };
      console.log('添加用户地区头:', userRegionCode);
    }
  } catch (error) {
    console.error('获取用户地区信息失败:', error);
  }

  // ... 原有逻辑
};
```

## 数据结构

### 地区信息存储格式
```javascript
{
  regionCode: '3207',           // 地区编码
  regionName: '连云港市',        // 地区名称
  level: 2,                     // 地区级别 (2-市, 3-区县)
  selectedRegions: [            // 完整的地区选择路径
    {
      regionCode: '3207',
      regionName: '连云港市',
      level: 2
    },
    {
      regionCode: '320703',
      regionName: '连云区',
      level: 3
    }
  ],
  fullAddress: '连云港市连云区',  // 完整地址
  timestamp: 1704067200000,     // 保存时间戳
  isFromLocation: false         // 是否来自定位（可选）
}
```

### 请求头格式
```
user-region: 3207
```

## 使用流程

### 1. 用户选择地区
1. 用户打开地区选择组件
2. 选择城市和区县
3. 确认选择后，地区信息自动保存到本地存储

### 2. 发送请求
1. 每次发送HTTP请求时，请求拦截器自动获取用户地区编码
2. 将地区编码添加到请求头的 `user-region` 字段
3. 后端可以通过该字段识别用户所在地区

### 3. 地区信息管理
- 地区信息持久化存储在本地
- 支持更新和清除
- 可以检查信息是否过期

## 扩展功能

### 1. 定位地区支持
```javascript
// 从位置信息保存地区
RegionManager.saveLocationRegion(locationRegion);
```

### 2. 地区信息显示
```javascript
// 获取用于显示的地区信息
const displayInfo = RegionManager.getDisplayRegionInfo();
// 返回：{ hasRegion: true, displayText: '连云港市连云区', regionCode: '3207' }
```

### 3. 过期检查
```javascript
// 检查地区信息是否过期（默认30天）
const isExpired = RegionManager.isRegionExpired();
```

## 后端处理建议

### 1. 请求头解析
```java
@RequestHeader(value = "user-region", required = false) String userRegion
```

### 2. 地区相关业务逻辑
- 根据用户地区提供本地化内容
- 地区统计和分析
- 地区权限控制
- 本地化推荐

## 注意事项

### 1. 兼容性处理
- 请求拦截器中使用 try-catch 包装，避免影响正常请求
- 地区编码为空时不添加请求头

### 2. 性能考虑
- 地区信息缓存在本地存储，避免重复获取
- 请求拦截器中的处理逻辑简单高效

### 3. 数据一致性
- 用户重新选择地区时会覆盖之前的信息
- 支持清除地区信息的功能

### 4. 错误处理
- 获取地区信息失败不影响请求发送
- 提供详细的错误日志用于调试

## 测试验证

### 1. 功能测试
1. 选择不同地区，验证地区信息是否正确保存
2. 发送请求，检查请求头是否包含 `user-region` 字段
3. 重启小程序，验证地区信息是否持久化

### 2. 边界测试
1. 未选择地区时的请求处理
2. 地区信息损坏时的容错处理
3. 网络异常时的表现

### 3. 性能测试
1. 大量请求时的性能表现
2. 地区信息获取的响应时间

## 示例

### 请求示例
```
GET /api/institution/page HTTP/1.1
Host: localhost
user-region: 3207
Blade-Auth: Bearer xxx
X-Open-ID: xxx
Content-Type: application/json
```

### 日志示例
```
用户选择地区已保存: {regionCode: "3207", regionName: "连云港市", ...}
添加用户地区头: 3207
```

通过这个实现，每次用户发送请求时都会自动在请求头中包含用户所在地区的信息，方便后端进行地区相关的业务处理。
