const UploadHelper = require('../../../utils/upload');
const { request } = require('../../../utils/request');
const PublishStore = require('../../../stores/publishStore');

Page({
  data: {
    userInfo: {
      avatar: '',
      nickname: '张小明',
      gender: '男',
      birthday: '1990-01-01',
      region: '上海市',
      signature: '这个人很懒，什么都没写',
      phone: '138****8888',
      email: 'zh****@email.com',
      verified: true
    },
    showNicknameDialog: false,
    nicknameInput: '',
    showSignatureDialog: false,
    signatureInput: '',
    showGenderPicker: false,
    birthday: '',
    showBirthdayPicker: false,
    region: '',
    showRegionPicker: false,
    showAvatarLoading: false,
    genderArray: ['男', '女'],
    genderIndex: 0
  },
  onLoad() {
    // 页面加载时读取本地 userInfo
    const userInfo = wx.getStorageSync('userInfo') || this.data.userInfo;
    let genderIndex = 0;
    if(userInfo.gender === '女') genderIndex = 1;
    this.setData({ userInfo, genderIndex });
  },
  async onChangeAvatar() {
    this.setData({ showAvatarLoading: true });
    try {
      // 选择图片
      const res = await wx.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera']
      });
      if (res.tempFilePaths && res.tempFilePaths.length > 0) {
        const filePath = res.tempFilePaths[0];
        // 构造图片对象，isTemp:true 以便 uploadImages 识别
        const imageObj = {
          path: filePath,
          isTemp: true,
          name: filePath.split('/').pop(),
          type: 'image/jpeg',
          size: 0
        };
        // 调用帖子上传图片逻辑
        const uploadResult = await PublishStore.uploadImages([imageObj], 'avatar');
        if (uploadResult.success && uploadResult.data && uploadResult.data[0]) {
          const avatarUrl = uploadResult.data[0].url;
          await this.updateUserProfile({ avatar: avatarUrl });
          this.setData({ 'userInfo.avatar': avatarUrl });
          wx.showToast({ title: '头像已更新', icon: 'success' });
        } else {
          wx.showToast({ title: uploadResult.message || '上传失败', icon: 'none' });
        }
      }
    } catch (e) {
      wx.showToast({ title: '上传失败', icon: 'none' });
    }
    this.setData({ showAvatarLoading: false });
  },
  async updateUserProfile(updateData) {
    try {
      const res = await request({
        url: '/blade-chat/user/update',
        method: 'POST',
        data: updateData
      });
      if (res.code === 200) {
        // debugger
        this.setData({ userInfo: { ...this.data.userInfo, ...updateData } });
        wx.setStorageSync('userInfo', { ...this.data.userInfo, ...updateData });
        return true;
      } else {
        wx.showToast({ title: res.msg || '修改失败', icon: 'none' });
        return false;
      }
    } catch (e) {
      wx.showToast({ title: '网络错误', icon: 'none' });
      return false;
    }
  },
  onEditNickname() {
    this.setData({
      showNicknameDialog: true,
      nicknameInput: this.data.userInfo.nickname
    });
  },
  onNicknameInput(e) {
    this.setData({ nicknameInput: e.detail.value });
  },
  onNicknameDialogConfirm() {
    const nickname = this.data.nicknameInput;
    this.updateUserProfile({ nickname });
    this.setData({
      'userInfo.nickname': nickname,
      showNicknameDialog: false
    });
  },
  onNicknameDialogCancel() {
    this.setData({ showNicknameDialog: false });
  },
  onEditGender() {
    this.setData({ showGenderPicker: true });
  },
  onGenderChange(e) {
    const gender = this.data.genderArray[e.detail.value];
    this.updateUserProfile({ gender });
    this.setData({
      'userInfo.gender': gender,
      genderIndex: e.detail.value
    });
  },
  onEditBirthday() {
    this.setData({
      showBirthdayPicker: true,
      birthday: this.data.userInfo.birthday || ''
    });
  },
  onBirthdayChange(e) {
    const birthday = e.detail.value;
    this.updateUserProfile({ birthday });
    this.setData({
      'userInfo.birthday': birthday,
      showBirthdayPicker: false
    });
  },
  onEditRegion() {
    this.setData({
      showRegionPicker: true,
      region: this.data.userInfo.region || '请选择地区'
    });
  },
  onRegionChange(e) {
    const region = e.detail.value.join('');
    this.updateUserProfile({ region });
    this.setData({
      'userInfo.region': region,
      showRegionPicker: false
    });
  },
  onEditSignature() {
    this.setData({
      showSignatureDialog: true,
      signatureInput: this.data.userInfo.signature
    });
  },
  onSignatureInput(e) {
    this.setData({ signatureInput: e.detail.value });
  },
  onSignatureDialogConfirm() {
    const signature = this.data.signatureInput;
    this.updateUserProfile({ signature });
    this.setData({
      'userInfo.signature': signature,
      showSignatureDialog: false
    });
  },
  onSignatureDialogCancel() {
    this.setData({ showSignatureDialog: false });
  },
  onAccountSecurity() {
    // TODO: 跳转账号安全页面
  },
  onLogout() {
    wx.removeStorageSync('userInfo');
    wx.showToast({ title: '已退出登录', icon: 'success' });
    setTimeout(() => {
      wx.reLaunch({ url: '/pages/index/index' });
    }, 800);
  }
}) 