const { regionCache } = require('../../utils/regionCache');

Component({
  properties: {
    // 标题
    title: {
      type: String,
      value: ''
    },
    // 是否显示返回按钮
    showBack: {
      type: Boolean,
      value: false
    },
    // 是否显示搜索框
    showSearch: {
      type: Boolean,
      value: true
    },
    // 背景颜色
    background: {
      type: String,
      value: 'linear-gradient(to bottom, #ff6b6b, #ff8585)'
    },
    // 是否显示地址选择
    showLocation: {
      type: Boolean,
      value: false
    },
    textColor: {
      type: String,
      value: '#ffffff'
    },
    // 是否固定导航栏
    fixed: {
      type: Boolean,
      value: true
    },
    // 默认地区编码
    defaultRegionCode: {
      type: String,
      value: ''
    }
  },

  data: {
    navBarHeight: 0,    // 导航栏高度
    menuButtonHeight: 0, // 胶囊高度
    menuButtonTop: 0,    // 胶囊顶部距离
    statusBarHeight: 0,  // 状态栏高度
    menuButtonInfo: null, // 胶囊信息
    screenWidth: 0,      // 屏幕宽度
    isScrolled: false,   // 是否已滚动
    currentBackground: '', // 当前背景色
    currentTextColor: '',   // 当前文字颜色
    currentLocation: '',    // 当前选择的位置
    currentLocationName: '', // 当前位置的简短名称（只显示最后一级）
    showLocationModal: false, // 是否显示位置选择弹窗
    showRegionPicker: false, // 是否显示地区选择器
    selectedRegion: null,    // 选中的地区信息
    locationList: ['灌南县', '连云区', '海州区', '赣榆区', '东海县', '灌云县',''] // 连云港市各区县（兼容旧版本）
  },

  lifetimes: {
    attached() {
      this.initNavBar();
      this.setData({
        currentBackground: this.properties.background,
        currentTextColor: this.properties.textColor
      });

      this.initLocation();
    }
  },

  methods: {
    initNavBar() {
      // 获取窗口信息
      const windowInfo = wx.getWindowInfo();
      // 获取胶囊按钮位置信息
      const menuButtonInfo = wx.getMenuButtonBoundingClientRect();

      console.log('导航栏信息:', menuButtonInfo);
      // 计算导航栏高度
      const navBarHeight = (menuButtonInfo.top - windowInfo.statusBarHeight) * 2 + menuButtonInfo.height + windowInfo.statusBarHeight;
      const menuButtonHeight = menuButtonInfo.height;
      this.setData({
        navBarHeight,
        menuButtonHeight: menuButtonInfo.height,
        menuButtonTop: menuButtonInfo.top,
        statusBarHeight: windowInfo.statusBarHeight,
        menuButtonInfo,
        screenWidth: windowInfo.screenWidth
      });

      // 设置CSS变量，供页面使用
      wx.setStorageSync('navBarHeight', navBarHeight);

      // 通知页面导航栏高度已计算完成
      this.triggerEvent('navReady', { height: navBarHeight});
    },

    // 初始化位置信息
    async initLocation() {
      try {
        // 优先使用缓存的当前位置
        const cachedLocation = regionCache.getCurrentLocation();
        if (cachedLocation) {
          this.setData({
            currentLocation: cachedLocation.fullAddress,
            currentLocationName: cachedLocation.displayName,
            selectedRegion: cachedLocation.region
          });
          return;
        }

        // 如果有默认地区编码，获取对应的地区信息
        if (this.properties.defaultRegionCode) {
          const fullAddress = await regionCache.getFullAddress(this.properties.defaultRegionCode);
          if (fullAddress) {
            const displayName = this.getDisplayName(fullAddress);
            const locationInfo = {
              fullAddress: this.buildFullAddress(fullAddress),
              displayName: displayName,
              region: {
                regionCode: this.properties.defaultRegionCode,
                regionName: displayName,
                level: fullAddress.level
              }
            };

            this.setData({
              currentLocation: locationInfo.fullAddress,
              currentLocationName: locationInfo.displayName,
              selectedRegion: locationInfo.region
            });

            // 缓存位置信息
            regionCache.setCurrentLocation(locationInfo);
            return;
          }
        }

        // 兜底显示默认位置
        this.setData({
          currentLocation: '灌南县',
          currentLocationName: '灌南',
          selectedRegion: null
        });
      } catch (error) {
        console.error('初始化位置信息失败:', error);
        this.setData({
          currentLocation: '定位中...',
          currentLocationName: '定位中...',
          selectedRegion: null
        });
      }
    },

    // 构建完整地址
    buildFullAddress(fullAddress) {
      const parts = [];
      if (fullAddress.provinceName) parts.push(fullAddress.provinceName);
      if (fullAddress.cityName) parts.push(fullAddress.cityName);
      if (fullAddress.districtName) parts.push(fullAddress.districtName);
      if (fullAddress.townName) parts.push(fullAddress.townName);
      if (fullAddress.villageName) parts.push(fullAddress.villageName);
      return parts.join('');
    },

    // 获取显示名称（只显示最后一级）
    getDisplayName(fullAddress) {
      if (fullAddress.villageName) return fullAddress.villageName;
      if (fullAddress.townName) return fullAddress.townName;
      if (fullAddress.districtName) return fullAddress.districtName;
      if (fullAddress.cityName) return fullAddress.cityName;
      if (fullAddress.provinceName) return fullAddress.provinceName;
      return '未知地区';
    },

    // 处理滚动事件
    handleScroll(scrollTop) {
      // console.log('滚动事件:', scrollTop);
      const threshold = 50; // 滚动阈值
      const isScrolled = scrollTop > threshold;
      
      if (isScrolled !== this.data.isScrolled) {
        this.setData({
          isScrolled,
          currentBackground: isScrolled ? '#ffffff' : this.properties.background,
          currentTextColor: isScrolled ? '#666666' : this.properties.textColor
        });
      }
    },

    // 返回上一页
    onBack() {
      wx.navigateBack({
        delta: 1
      });
    },

    // 跳转到搜索页
    onSearch() {
      wx.navigateTo({
        url: '/pkg_common/pages/search/search'
      });
    },

    // 显示位置选择弹窗
    onLocationSelect() {
      console.log('显示地区选择器');
      this.setData({ showRegionPicker: true });
    },

    // 隐藏位置选择弹窗
    hideLocationModal() {
      this.setData({ showLocationModal: false });
    },

    // 选择位置
    selectLocation(e) {
      const location = e.currentTarget.dataset.location;
      this.setData({ 
        currentLocation: location,
        showLocationModal: false 
      });
      
      // 保存到本地存储
      wx.setStorageSync('selectedLocation', location);
      
      // 触发位置选择事件
      this.triggerEvent('locationChange', { location });
    },

    // 地区选择器确认事件
    onRegionConfirm(e) {
      const { selectedRegions, lastRegion, fullAddress } = e.detail;

      // 只显示最后一级的地区名称
      const displayName = lastRegion ? lastRegion.regionName : (fullAddress || '定位中...');

      this.setData({
        currentLocation: fullAddress,
        currentLocationName: displayName,
        selectedRegion: lastRegion,
        showRegionPicker: false
      });

      // 缓存位置信息
      const locationInfo = {
        fullAddress: fullAddress,
        displayName: displayName,
        region: lastRegion,
        selectedRegions: selectedRegions
      };
      regionCache.setCurrentLocation(locationInfo);

      // 触发位置选择事件
      this.triggerEvent('locationChange', {
        location: fullAddress,
        locationName: displayName,
        region: lastRegion,
        selectedRegions
      });
    },

    // 关闭地区选择器
    onRegionClose() {
      this.setData({ showRegionPicker: false });
    },

    // 地区选择器显示状态变化
    onRegionShowChange(e) {
      const { show } = e.detail;
      // 通知页面隐藏/显示底部导航栏
      this.triggerEvent('regionpickershow', { show });
    }
  }
}); 