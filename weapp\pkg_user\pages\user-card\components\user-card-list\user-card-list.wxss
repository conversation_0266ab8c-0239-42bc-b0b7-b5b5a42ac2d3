.card-list-container {
  width: 100%;
  padding: 24rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.card-list-column {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.business-card {
  width: 100%;
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
  position: relative;
}

.card-content {
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

/* 添加名片按钮 */
.add-card-btn {
  width: 100%;
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
}

.add-card-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.12);
  border-color: #ff8080;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.empty-desc {
  font-size: 28rpx;
  color: #999;
  line-height: 1.5;
}

.add-btn-content {
  padding: 40rpx 32rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  position: relative;
}

.add-icon {
  font-size: 48rpx;
  color: #ff8080;
  font-weight: bold;
  width: 60rpx;
  height: 60rpx;
  text-align: center;
  line-height: 55rpx;
}

.add-text {
  font-size: 34rpx;
  color: #333;
  font-weight: 600;
  flex: 1;
}

/* 卡片头部 */
.card-header {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
  margin-bottom: 8rpx;
}

.avatar-section {
  flex-shrink: 0;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: #f0f0f0;
}

.avatar-placeholder {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-text {
  font-size: 32rpx;
  color: #666;
  font-weight: 500;
}

.company-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.company-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
}

.person-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex-wrap: wrap;
}

.person-name {
  font-size: 28rpx;
  color: #ff8080;
  font-weight: 600;
}

.person-position {
  font-size: 24rpx;
  color: #666;
}

.status-tag {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.status-tag.approved {
  background: #e8f5e8;
  color: #52c41a;
}

.status-tag.pending {
  background: #fff7e6;
  color: #fa8c16;
}

.status-tag.rejected {
  background: #fff1f0;
  color: #f5222d;
}

/* 联系信息 */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  margin-bottom: 8rpx;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  min-height: 32rpx;
}

.info-icon {
  font-size: 24rpx;
  flex-shrink: 0;
  width: 24rpx;
  text-align: center;
}

.info-text {
  font-size: 26rpx;
  color: #555;
  line-height: 1.4;
  flex: 1;
}

/* 卡片底部 */
.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.footer-left {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.time-info {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.time-icon {
  font-size: 20rpx;
  width: 20rpx;
  text-align: center;
}

.time-text {
  font-size: 22rpx;
  color: #999;
}

.card-type-tag {
  padding: 4rpx 12rpx;
  background: #f5f5f5;
  border-radius: 12rpx;
  font-size: 20rpx;
  color: #666;
}

.footer-right {
  display: flex;
  align-items: center;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.action-btn {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.action-icon {
  font-size: 24rpx;
  width: 24rpx;
  text-align: center;
}

/* 无数据状态 */
.no-cards {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  color: #999;
  font-size: 28rpx;
}
