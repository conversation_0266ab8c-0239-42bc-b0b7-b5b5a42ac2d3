/* pages/follow/detail/message/message.wxss */
.chat-container {
  background: #f7f6f6;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.chat-header {
  height: 96rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: #fff;
  font-size: 34rpx;
  font-weight: 600;
  color: #222;
  border-bottom: 1rpx solid #ededed;
}

.chat-header-back {
  position: absolute;
  left: 32rpx;
  width: 40rpx;
  height: 40rpx;
}

.chat-header-title {
  text-align: center;
}

.chat-header-menu {
  position: absolute;
  right: 32rpx;
  width: 40rpx;
  height: 40rpx;
}

.chat-body {
  flex: 1;
  padding: 32rpx 0 32rpx 0;
  overflow: auto;
}

.chat-msg-row {
  display: flex;
  align-items: flex-end;
  margin-bottom: 32rpx;
  padding: 0 32rpx;
}

.chat-msg-row.self {
  flex-direction: row-reverse;
}

.chat-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  margin: 0 16rpx;
}

.chat-bubble {
  max-width: 60vw;
  background: #fff;
  border-radius: 16rpx;
  font-size: 30rpx;
  color: #222;
  padding: 24rpx 32rpx;
  margin: 0 8rpx;
  word-break: break-all;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
}

.chat-bubble.self {
  background: #ff8383;
  color: #fff;
}

/* 底部输入区 */
.chat-footer {
  background: #fff;
  display: flex;
  align-items: center;
  padding: 28rpx 24rpx;
}

.chat-footer-left {
  display: flex;
  align-items: center;
  margin-right: 12rpx;
}

.chat-footer-icon {
  width: 70rpx;
  height: 70rpx;
}

.chat-input {
  flex: 1;
  height: 70rpx;
  border-radius: 32rpx;
  background: #f7f6f6;
  border: none;
  padding: 0 24rpx;
  font-size: 30rpx;
  margin-right: 12rpx;
}

.chat-footer-right {
  margin-left: 12rpx;
}

.chat-plus-btn {
  width: 56rpx;
  height: 56rpx;
  background: #ff8383;
  color: #fff;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  font-weight: 600;
}

/* 弹窗遮罩 */
.chat-popup-mask {
  position: fixed;
  left: 0;
  bottom: 0;
  background: rgba(0,0,0,0.15);
  z-index: 100;
  display: flex;
  flex-direction: column;
}

.chat-popup {
  width: 100vw;
  background: #fff;
  padding: 32rpx 0 32rpx 0;
  /* box-shadow: 0 -4rpx 24rpx rgba(0,0,0,0.08); */
}

.chat-popup-row {
  display: flex;
  justify-content: space-around;
  margin-bottom: 32rpx;
}

.chat-popup-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 120rpx;
}

.chat-popup-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 12rpx;
}

.chat-popup-label {
  font-size: 26rpx;
  color: #222;
}