// publish.js
const PublishStore = require('../../stores/publishStore');
const PublishHelper = require('../../utils/publishHelper');
const FileValidator = require('../../utils/fileValidator');
const config = require('../../config/api');
const app = getApp();
import { getDynamicFormConfig } from '../../stores/formStore.js';

// 引入腾讯地图SDK
const QQMapWX = require('../../scripts/qqmap-wx-jssdk.min.js');

// 实例化API核心类
const qqmapsdk = new QQMapWX({
  key: config.map.key,
  secret: "I8qLYb8lPeCAyJ7AZzbYGaPJRUJejciI" // 修正拼写错误：serect -> secret
});

Page({
  data: {
    // 表单数据
    title: '',
    description: '',
    contactName: '',
    contactType: 'phone',
    contactNumber: '',
    location: '',
    latitude: '',
    longitude: '',
    address: '',
    enableLocation: true, // 是否启用位置信息
    
    // 图片相关
    images: [],
    imageRows: [],
    maxImageCount: 6,
    
    // 标签相关
    selectedTags: [],
    customTag: '',
    maxTagCount: 5,
    maxTagLength: 6,
    tagList: [], // 动态加载的标签列表
    categoryTags: [], // 当前分类的标签
    
    // 分类相关
    category: '',
    categoryId: '', // 分类ID
    // 发布相关
    publishTypeActive: '普通',
    loading: false,
    
    // 草稿相关
    draftId: null,
    draftCount: 0,
    
    // 字数限制
    maxContentLength: 800,
    currentContentLength: 0,
    maxTitleLength: 50,
    
    // 弹窗控制
    showTagModal: false,
    
    // 分类列表（临时数据）
    categoryList: [],

    // 新增位置处理相关
    publishLocation: '',
    isAnonymous: "0", // 是否匿名发布

    // 动态表单相关
    dynamicFormConfig: {}, // 动态表单配置
    dynamicFormData: {},    // 动态表单数据

    // 位置相关
    userLocation: null,
    locationReady: false, // 位置是否已获取
    locationLoading: true // 位置获取加载状态
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    console.log('发布页面加载参数:', options);
    this.initializePage(options);

    // 测试动态表单数据平铺功能
    setTimeout(() => {
      this.testDynamicFieldFlattening();
    }, 1000);

    // 延迟获取位置，确保页面渲染完成
    setTimeout(() => {
      this.getUserLocation();
    }, 500);
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    // this.handleDraftEdit();
    // this.updateDraftCount();
  },



  /**
   * 页面卸载时清理资源
   */
  onUnload() {
    // 清理其他资源
    if (this.locationWatcher) {
      clearInterval(this.locationWatcher);
      this.locationWatcher = null;
    }

    console.log('发布页面资源已清理');
  },

  /**
   * 通用逆地址解析方法
   * @param {number} latitude 纬度
   * @param {number} longitude 经度
   * @param {Object} options 配置选项
   * @returns {Promise} Promise对象
   */
  reverseGeocodeWithOptions(latitude, longitude, options = {}) {
    const defaultOptions = {
      get_poi: 1, // 是否返回周边POI列表：1.返回；0不返回(默认)
      poi_options: 'address_format=short;radius=1000;policy=1', // POI选项配置
      coord_type: 5, // 输入的locations的坐标类型，可选值为[1,6]之间的整数，每个数字代表的类型说明：1 GPS坐标 2 sogou经纬度 3 baidu经纬度 4 mapbar经纬度 5 [默认]腾讯、google、高德坐标 6 sogou墨卡托
      ...options
    };

    return new Promise((resolve, reject) => {
      qqmapsdk.reverseGeocoder({
        location: {
          latitude: latitude,
          longitude: longitude
        },
        success: (res) => {
          console.log('逆地址解析成功:', res);
          resolve(res);
        },
        fail: (error) => {
          console.error('逆地址解析失败:', error);
          reject(error);
        }
      });
    });
  },

  /**
   * 获取用户位置
   */
  getUserLocation() {
    this.setData({ locationLoading: true });
    wx.getLocation({
      type: 'gcj02',
      altitude: true, // 获取高度信息
      success: (res) => {
        console.log('获取位置成功:', res);

        const userLocation = {
          latitude: res.latitude,
          longitude: res.longitude,
          accuracy: res.accuracy,
          altitude: res.altitude
        };

        this.setData({
          latitude: res.latitude,
          longitude: res.longitude,
          userLocation: userLocation,
          locationReady: true,
          locationLoading: false
        });

        // 如果用户未选择位置且启用了位置信息，自动设置当前位置
        if (!this.data.location && this.data.enableLocation) {
          this.autoSetLocationFromCoordinates(res.latitude, res.longitude);
        }
      },
      fail: (err) => {
        console.error('获取位置失败:', err);
        this.setData({
          locationLoading: false,
          locationReady: false
        });

        // 根据错误类型显示不同提示
        let title = '位置获取失败';
        let content = '需要获取您的位置信息来设置发布位置';

        if (err.errMsg.includes('auth deny')) {
          title = '位置权限被拒绝';
          content = '请在设置中开启位置权限，以便为您设置发布位置';
        } else if (err.errMsg.includes('timeout')) {
          title = '定位超时';
          content = '定位超时，请检查网络连接或稍后重试';
        }

        wx.showModal({
          title: title,
          content: content,
          confirmText: '去设置',
          cancelText: '手动选择',
          success: (modalRes) => {
            if (modalRes.confirm) {
              wx.openSetting({
                success: (settingRes) => {
                  if (settingRes.authSetting['scope.userLocation']) {
                    // 用户重新授权后重新获取位置
                    this.getUserLocation();
                  }
                }
              });
            } else {
              // 用户选择手动选择位置，不自动设置
              console.log('用户选择手动选择位置');
            }
          }
        });
      }
    });
  },

  /**
   * 根据坐标自动设置位置
   */
  async autoSetLocationFromCoordinates(latitude, longitude) {
    console.log('autoSetLocationFromCoordinates', latitude, longitude);

    // 检查是否启用位置
    if (!this.data.enableLocation) {
      console.log('位置功能已关闭，跳过自动设置位置');
      return;
    }

    try {
      // 使用通用逆地址解析方法
      const res = await this.reverseGeocodeWithOptions(latitude, longitude, {
        get_poi: 1,
        poi_options: 'address_format=short;radius=1000;policy=1'
      });

      if (res.status === 0) {
        const result = res.result;
        let address = result.address;

        // 优先使用推荐地址
        if (result.formatted_addresses && result.formatted_addresses.recommend) {
          address = result.formatted_addresses.recommend;
        }

        console.log('自动设置位置:', address);

        this.setData({
          location: address,
          latitude: latitude,
          longitude: longitude,
          address: address
        });

        // 显示自动设置位置提示
        wx.showToast({
          title: '已自动设置位置',
          icon: 'success',
          duration: 2000
        });
      } else {
        console.error('逆地址解析失败:', res.message);
        this.setFallbackLocation(latitude, longitude);
      }
    } catch (error) {
      console.error('逆地址解析异常:', error);
      this.setFallbackLocation(latitude, longitude);
    }
  },

  /**
   * 获取详细地址信息
   * @param {number} latitude 纬度
   * @param {number} longitude 经度
   * @returns {Promise<Object>} 详细地址信息
   */
  async getDetailedAddressInfo(latitude, longitude) {
    try {
      const res = await this.reverseGeocodeWithOptions(latitude, longitude, {
        get_poi: 1,
        poi_options: 'address_format=short;radius=2000;policy=1;page_size=20;page_index=1'
      });

      if (res.status === 0) {
        const result = res.result;
        return {
          success: true,
          address: result.address,
          formattedAddress: result.formatted_addresses?.recommend || result.address,
          addressComponent: result.address_component,
          pois: result.pois || [],
          adInfo: result.ad_info
        };
      } else {
        return {
          success: false,
          error: res.message || '地址解析失败'
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error.message || '地址解析异常'
      };
    }
  },

  /**
   * 设置备用位置（使用坐标）
   */
  setFallbackLocation(latitude, longitude) {
    const coordinateAddress = `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
    this.setData({
      location: coordinateAddress,
      latitude: latitude,
      longitude: longitude,
      address: coordinateAddress
    });

    wx.showToast({
      title: '地址解析失败，已设置坐标位置',
      icon: 'none',
      duration: 2000
    });
  },



  /**
   * 初始化页面
   */
  async initializePage(options) {
    try {
      // 处理草稿编辑
      // if (options.draftId) {
      //   this.setData({ draftId: options.draftId });
      //   await this.loadDraft(options.draftId);
      // }
      
      // 处理分类参数
      if (options.category) {
        const categoryName = decodeURIComponent(options.category);
        const categoryId = decodeURIComponent(options.categoryId);
        this.setData({ category: categoryName });
        
        if (categoryId) {
          this.setData({ categoryId: categoryId });
          // 加载该分类下的标签
          await this.loadTagsByCategory(categoryId);
          // 加载动态表单配置
          await this.loadDynamicForm(categoryId);
        }
      }
      
      // 更新草稿数量
      this.updateDraftCount();
    } catch (error) {
      console.error('页面初始化失败:', error);
    }
  },

  /**
   * 处理草稿编辑
   */
  handleDraftEdit() {
    const editDraftId = wx.getStorageSync('temp_edit_draft_id');
    if (editDraftId) {
      console.log('检测到要编辑的草稿ID:', editDraftId);
      this.setData({ draftId: editDraftId });
      this.loadDraft(editDraftId);
      wx.removeStorageSync('temp_edit_draft_id');
    }
  },

  // ==================== 表单输入处理 ====================

  /**
   * 标题输入处理
   */
  onTitleInput: function(e) {
    const value = e.detail.value;
    if (value.length > this.data.maxTitleLength) {
      wx.showToast({
        title: `标题最多输入${this.data.maxTitleLength}字`,
        icon: 'none'
      });
      this.setData({
        title: value.slice(0, this.data.maxTitleLength)
      });
      return;
    }
    
    this.setData({ title: value });
  },

  /**
   * 描述输入处理
   */
  onDescriptionInput: function(e) {
    const value = e.detail.value;
    if (value.length > this.data.maxContentLength) {
      wx.showToast({
        title: `最多输入${this.data.maxContentLength}字`,
        icon: 'none'
      });
      this.setData({
        description: value.slice(0, this.data.maxContentLength),
        currentContentLength: this.data.maxContentLength
      });
      return;
    }
    
    this.setData({
      description: value,
      currentContentLength: value.length
    });
  },

  /**
   * 联系人姓名输入
   */
  onContactNameInput(e) {
    this.setData({ contactName: e.detail.value });
  },

  /**
   * 选择联系方式类型
   */
  selectContactType(e) {
    const type = e.detail.value;
    this.setData({
      contactType: type,
      contactNumber: ''
    });
  },

  /**
   * 联系号码输入
   */
  onContactNumberInput(e) {
    this.setData({ contactNumber: e.detail.value });
  },

  /**
   * 匿名发布切换
   */
  onAnonymousChange(e) {
    this.setData({
      isAnonymous: e.detail.value
    });
  },

  // ==================== 图片处理 ====================

  /**
   * 选择图片
   */
  chooseImage: function() {
    const currentCount = this.data.images.length;
    
    if (currentCount >= this.data.maxImageCount) {
      wx.showToast({
        title: `最多上传${this.data.maxImageCount}张图片`,
        icon: 'none'
      });
      return;
    }

    wx.chooseMedia({
      count: this.data.maxImageCount - currentCount,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 验证选择的图片
        const validImages = [];
        const invalidImages = [];
        
        res.tempFiles.forEach(file => {
          const validation = FileValidator.validateImage(file.tempFilePath, file.size);
          
          if (validation.isValid) {
            validImages.push({
              path: file.tempFilePath,
              size: file.size,
              type: file.type,
              isTemp: true,
              name: FileValidator.generateSafeFileName(file.tempFilePath)
            });
          } else {
            invalidImages.push({
              path: file.tempFilePath,
              errors: validation.errors
            });
          }
        });
        
        // 显示验证错误
        if (invalidImages.length > 0) {
          const errorMessage = invalidImages[0].errors[0];
          wx.showToast({
            title: errorMessage,
            icon: 'none',
            duration: 3000
          });
        }
        
        // 添加有效图片
        if (validImages.length > 0) {
          const updatedImages = [...this.data.images, ...validImages];
          this.setData({ images: updatedImages });
          this.updateImageLayout();
          
          // 显示成功提示
          // if (validImages.length > 0) {
          //   wx.showToast({
          //     title: `已选择${validImages.length}张图片`,
          //     icon: 'success'
          //   });
          // }
        }
      },
      fail: (error) => {
        console.error('选择图片失败:', error);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 更新图片布局
   */
  updateImageLayout: function() {
    const images = this.data.images;
    const rows = [];
    for (let i = 0; i < images.length; i += 3) {
      rows.push(images.slice(i, i + 3));
    }
    this.setData({ imageRows: rows });
  },

  /**
   * 删除图片
   */
  deleteImage: function(e) {
    const index = e.currentTarget.dataset.index || 0;
    const images = [...this.data.images];
    images.splice(index, 1);
    
    this.setData({ images });
    this.updateImageLayout();
  },

  /**
   * 预览图片
   */
  previewImage(e) {
    const { src } = e.currentTarget.dataset;
    const urls = this.data.images.map(img => img.path);
    wx.previewImage({
      current: src,
      urls
    });
  },

  // ==================== 位置处理 ====================

  /**
   * 处理导航栏位置选择变化
   */
  onLocationChange(e) {
    const { location } = e.detail;
    console.log('位置选择变化:', location);
    
    // 更新发布位置信息
    this.setData({
      publishLocation: location
    });
    
    // 显示位置选择成功提示
    wx.showToast({
      title: `已选择${location}`,
      icon: 'success',
      duration: 1500
    });
  },

  /**
   * 位置开关变化
   */
  onLocationSwitchChange(e) {
    const enableLocation = e.detail.value;
    console.log('位置开关变化:', enableLocation);

    this.setData({
      enableLocation: enableLocation
    });

    if (enableLocation) {
      // 开启位置时，如果没有位置信息且用户位置已获取，自动设置当前位置
      if (!this.data.location && this.data.userLocation) {
        this.autoSetLocationFromCoordinates(this.data.userLocation.latitude, this.data.userLocation.longitude);
      }
      wx.showToast({
        title: '已开启位置信息',
        icon: 'success',
        duration: 1500
      });
    } else {
      // 关闭位置时，清空位置信息
      this.setData({
        location: '',
        latitude: '',
        longitude: '',
        address: ''
      });
      wx.showToast({
        title: '已关闭位置信息',
        icon: 'none',
        duration: 1500
      });
    }
  },

  /**
   * 选择位置
   */
  chooseLocation() {
    // 检查是否启用位置
    if (!this.data.enableLocation) {
      wx.showToast({
        title: '请先开启位置信息',
        icon: 'none'
      });
      return;
    }

    wx.chooseLocation({
      success: (res) => {
        console.log('选择位置成功：', res);
        this.setData({
          location: res.address ,
          latitude: res.latitude,
          longitude: res.longitude,
          address: res.name || res.address
        });
      },
      fail: (err) => {
        console.error('选择位置失败：', err);
        if (err.errMsg.indexOf('auth deny') !== -1) {
          wx.showModal({
            title: '提示',
            content: '需要您授权使用位置信息',
            success: (res) => {
              if (res.confirm) {
                wx.openSetting();
              }
            }
          });
        }
      }
    });
  },

  // ==================== 标签处理 ====================

  /**
   * 标签点击处理
   */
  onTagTap(e) {
    const tag = e.currentTarget.dataset.tag;
    let selectedTags = this.data.selectedTags.slice();
    const idx = selectedTags.indexOf(tag);
    
    if (idx > -1) {
      selectedTags.splice(idx, 1);
    } else {
      if (selectedTags.length >= this.data.maxTagCount) {
        wx.showToast({
          title: `最多选择${this.data.maxTagCount}个标签`,
          icon: 'none'
        });
        return;
      }
      selectedTags.push(tag);
    }
    
    this.setData({ selectedTags });
  },

  /**
   * 自定义标签输入
   */
  onCustomTagInput(e) {
    this.setData({ customTag: e.detail.value });
  },

  /**
   * 添加自定义标签
   */
  addCustomTag() {
    const customTag = this.data.customTag.trim();
    if (!customTag) {
      wx.showToast({
        title: '请输入标签内容',
        icon: 'none'
      });
      return;
    }

    if (customTag.length > this.data.maxTagLength) {
      wx.showToast({
        title: `标签最多${this.data.maxTagLength}字`,
        icon: 'none'
      });
      return;
    }

    let selectedTags = this.data.selectedTags.slice();
    if (selectedTags.indexOf(customTag) > -1) {
      wx.showToast({
        title: '标签已存在',
        icon: 'none'
      });
      return;
    }

    if (selectedTags.length >= this.data.maxTagCount) {
      wx.showToast({
        title: `最多选择${this.data.maxTagCount}个标签`,
        icon: 'none'
      });
      return;
    }

    // 创建自定义标签
    this.createCustomTag(customTag);
  },

  /**
   * 创建自定义标签
   */
  async createCustomTag(tagName) {
    if (!this.data.categoryId) {
      wx.showToast({
        title: '请先选择分类',
        icon: 'none'
      });
      return;
    }

    try {
      const tagData = {
        name: tagName,
        categoryId: this.data.categoryId,
        color: '#4ECDC4'
      };

      const result = await PublishStore.createCustomTag(tagData);
      
      if (result.success) {
        // 添加到已选标签
        let selectedTags = this.data.selectedTags.slice();
        selectedTags.push(tagName);
        
        this.setData({
          selectedTags,
          customTag: ''
        });

        // 重新加载分类标签
        await this.loadTagsByCategory(this.data.categoryId);
        
        // wx.showToast({
        //   title: '标签创建成功',
        //   icon: 'success'
        // });
      } else {
        wx.showToast({
          title: result.message || '创建标签失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('创建自定义标签失败:', error);
      wx.showToast({
        title: '创建标签失败',
        icon: 'none'
      });
    }
  },

  /**
   * 删除标签
   */
  deleteTag(e) {
    const index = e.currentTarget.dataset.index;
    let selectedTags = this.data.selectedTags.slice();
    selectedTags.splice(index, 1);
    this.setData({ selectedTags });
  },

  // ==================== 弹窗控制 ====================

  /**
   * 隐藏标签选择弹窗
   */
  hideTagModal() {
    this.setData({ showTagModal: false });
  },

  /**
   * 确认标签选择并发布
   */
  confirmTagsAndPublish() {
    this.setData({ showTagModal: false });
    setTimeout(() => {
      this.confirmPublish();
    }, 300);
  },

  // ==================== 发布处理 ====================

  /**
   * 发布按钮点击
   */
  async publishPost() {
    if (this.data.loading) return;
    
    // 表单验证
    const validation = PublishHelper.validateForm(this.data);
    if (!validation.isValid) {
      wx.showToast({
        title: validation.errors[0],
        icon: 'none'
      });
      return;
    }

    // 动态表单验证
    const dynamicValidation = this.validateDynamicFormData();
    if (!dynamicValidation.isValid) {
      wx.showToast({
        title: dynamicValidation.errors[0] || '请完善必填信息',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 显示标签选择弹窗
    this.setData({ showTagModal: true });
  },

  /**
   * 确认发布
   */
  async confirmPublish() {
    if (this.data.loading) return;
    
    this.setData({ loading: true });
    
    try {
      wx.showLoading({
        title: '发布中...',
        mask: true
      });

      // 先上传图片
      const uploadResult = await this.uploadImages();
      if (!uploadResult.success) {
        wx.hideLoading();
        wx.showToast({
          title: uploadResult.message || '图片上传失败',
          icon: 'none'
        });
        return;
      }

      // 准备发布数据
      const postData = await this.preparePostData(uploadResult.data);
      console.log('准备发布的数据:', postData);
      
      // 调用发布接口
      const publishResult = await PublishStore.createPost(postData);
      
      wx.hideLoading();

      if (publishResult.success) {
        wx.showToast({
          title: '发布成功',
          icon: 'success',
          duration: 2000
        });

        // 删除草稿
        if (this.data.draftId) {
          await PublishStore.deleteDraft(this.data.draftId);
        }

        // 跳转到发布成功页面
        wx.redirectTo({
          url: `/pages/publish/success/success?title=${encodeURIComponent(this.data.title)}&description=${encodeURIComponent(this.data.description)}&postId=${publishResult.data?.id || ''}`
        });
        return;
      } else {
        wx.showToast({
          title: publishResult.message || '发布失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('发布失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '发布失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 上传图片
   */
  async uploadImages() {
    if (!this.data.images || this.data.images.length === 0) {
      return { success: true, data: [] };
    }

    try {
      // 使用PublishStore中的上传方法
      const result = await PublishStore.uploadImages(
        this.data.images,
        'post',
        null // 发布时还没有帖子ID，先传null
      );

      return result;
    } catch (error) {
      console.error('上传图片失败:', error);
      return {
        success: false,
        message: '图片上传失败'
      };
    }
  },

  /**
   * 准备发布数据
   */
  async preparePostData(uploadedImages = []) {
    // 构建发布数据
    const formData = {
      ...this.data,
      deviceInfo: PublishHelper.getDeviceInfo()
    };

    // 如果用户选择了发布位置，优先使用选择的位置
    if (this.data.publishLocation) {
      formData.publishLocation = this.data.publishLocation;
    }

    // 如果关闭了位置信息，清空位置相关字段
    if (!this.data.enableLocation) {
      formData.location = '';
      formData.latitude = '';
      formData.longitude = '';
      formData.address = '';
      formData.publishLocation = '';
    }
    // 确保分类ID是数字类型
    if (this.data.categoryId) {
      formData.categoryId = this.data.categoryId;
    }

    // 添加已上传的图片数据
    if (uploadedImages && uploadedImages.length > 0) {
      formData.images = uploadedImages;
    }

    // 添加动态表单数据（始终添加，即使为空）
    formData.dynamicFormData = this.data.dynamicFormData || {};
    console.log('准备发布的动态表单数据:', this.data.dynamicFormData);
    console.log('动态表单数据是否为空:', !this.data.dynamicFormData || Object.keys(this.data.dynamicFormData || {}).length === 0);
    console.log('动态表单数据键数量:', Object.keys(this.data.dynamicFormData || {}).length);
    
    const postData = PublishHelper.buildPostData(formData);

    // 输出完整的发布数据用于调试
    console.log('=== 发布数据详情 ===');
    console.log('原始表单数据:', formData);
    console.log('处理后的发布数据:', postData);
    console.log('动态表单数据摘要:', this.getDynamicFormDataSummary());

    
    // 显示平铺到最外层的动态字段
    const flattenedFields = {};
    Object.keys(postData).forEach(key => {
      // 提取动态字段（排除已知的基础字段）
      const baseFields = ['images', 'tags', 'contactName', 'contactType', 'contactNumber',
                         'content', 'categoryId', 'location', 'address', 'longitude',
                         'latitude', 'geolocation', 'title', 'description', 'category',
                         'selectedTags', 'imageCount', 'tagCount', 'publishTime',
                         'publishType', 'status', 'userId', 'userName', 'platform',
                         'version', 'deviceInfo', 'extra'];

      if (!baseFields.includes(key)) {
        flattenedFields[key] = postData[key];
      }
    });

    console.log('平铺到最外层的动态字段:', flattenedFields);
    console.log('字段处理规则说明:');
    console.log('- 普通字段(如: qyfr8o21) -> 直接作为顶级字段');
    console.log('- 包含_的字段(如: user_name) -> 创建嵌套对象 {user: {name: value}}');
    console.log('- 包含.的字段(如: config.title) -> 创建嵌套对象 {config: {title: value}}');
    console.log('- 注意: 不再包含 dynamicFields 字段，所有动态字段直接平铺到最外层');

    // 显示嵌套字段的详细结构
    console.log('=== 嵌套字段详细分析 ===');
    Object.keys(flattenedFields).forEach(key => {
      const value = flattenedFields[key];
      if (typeof value === 'object' && value !== null) {
        console.log(`嵌套字段 "${key}":`, value);
        this.logNestedStructure(value, `  ${key}`);
      } else {
        console.log(`普通字段 "${key}": ${value}`);
      }
    });
    console.log('==================');

    return postData;
  },

  // ==================== 草稿处理 ====================

  /**
   * 保存草稿
   */
  async saveDraft() {
    if (!this.data.description.trim()) {
      wx.showToast({
        title: '请输入内容描述',
        icon: 'none'
      });
      return;
    }

    try {
      const draftData = PublishHelper.buildDraftData(this.data);
      const result = await PublishStore.saveDraft(draftData, this.data.draftId);
      
      if (result.success) {
        wx.showToast({
          title: '草稿保存成功',
          icon: 'success'
        });
        
        if (!this.data.draftId) {
          this.setData({ draftId: result.data });
        }
        
        this.updateDraftCount();
      } else {
        wx.showToast({
          title: result.message || '保存失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('保存草稿失败:', error);
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    }
  },

  /**
   * 加载草稿
   */
  async loadDraft(draftId) {
    try {
      const result = await PublishStore.loadDraft(draftId);
      
      if (result.success) {
        const draft = result.data;
        this.setData({
          title: draft.title || '',
          description: draft.description || '',
          contactName: draft.contactName || '',
          contactType: draft.contactType || 'phone',
          contactNumber: draft.contactNumber || '',
          location: draft.location || '',
          latitude: draft.latitude || '',
          longitude: draft.longitude || '',
          selectedTags: draft.tags ? JSON.parse(draft.tags) : [],
          images: draft.images ? JSON.parse(draft.images) : [],
          category: draft.category || '',
          categoryId: draft.categoryId || '',
          isAnonymous: draft.isAnonymous || "0", // 加载草稿时也加载匿名状态
          dynamicFormData: draft.dynamicFormData ? JSON.parse(draft.dynamicFormData) : {} // 加载草稿时也加载动态表单数据
        });
        
        // 如果有分类ID，加载对应的标签
        if (draft.categoryId) {
          await this.loadTagsByCategory(draft.categoryId);
          // 加载动态表单配置
          await this.loadDynamicForm(draft.categoryId);
        }
        
        this.updateImageLayout();
      } else {
        wx.showToast({
          title: result.message || '加载草稿失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('加载草稿失败:', error);
      wx.showToast({
        title: '加载草稿失败',
        icon: 'none'
      });
    }
  },

  /**
   * 更新草稿数量
   */
  async updateDraftCount() {
    try {
      const result = await PublishStore.getDraftCount();
      if (result.success) {
        this.setData({ draftCount: result.count });
      }
    } catch (error) {
      console.error('获取草稿数量失败:', error);
    }
  },

  // ==================== 分类处理 ====================

  /**
   * 加载分类列表
   */
  async loadCategories() {
    try {
      const result = await PublishStore.loadCategories();
      
      if (result.success) {
        this.setData({ categories: result.data });
        console.log('分类列表:', result.data);
      } else {
        wx.showToast({
          title: result.message || '加载分类失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('加载分类失败:', error);
      wx.showToast({
        title: '加载分类失败',
        icon: 'none'
      });
    }
  },

  /**
   * 根据分类加载标签
   */
  async loadTagsByCategory(categoryId) {
    try {
      const result = await PublishStore.getTagsByCategory(categoryId);
      
      if (result.success) {
        this.setData({ 
          categoryTags: result.data,
          tagList: result.data.map(tag => tag.tagName) // 使用tagName字段
        });
        console.log('分类标签:', result.data);
      } else {
        console.error('加载分类标签失败:', result.message);
      }
    } catch (error) {
      console.error('加载分类标签失败:', error);
    }
  },

  /**
   * 加载动态表单配置
   */
  async loadDynamicForm(categoryId) {
    console.log('加载动态表单配置:', categoryId);
    const config = await getDynamicFormConfig(categoryId);
    console.log('loadDynamicForm', config);
    if (config) {
      this.setData({
        dynamicFormConfig: config
      });
      this.initDynamicFormData(config);
    }
  },
  initDynamicFormData(config) {
    // 初始化动态表单数据为空对象，用于存储用户输入
    this.setData({ dynamicFormData: {} });
    console.log('初始化动态表单数据:', this.data.dynamicFormData);
  },

  /**
   * 动态表单输入处理
   */
  onDynamicInput(e) {
    console.log('onDynamicInput 事件触发:', e);

    if (!e.detail) {
      console.error('onDynamicInput: e.detail 为空');
      return;
    }

    const { field, value } = e.detail;
    console.log('onDynamicInput 字段更新:', { field, value });

    if (!field) {
      console.error('onDynamicInput: field 为空');
      return;
    }

    this.setData({
      [`dynamicFormData.${field}`]: value
    });

    console.log('动态表单数据更新后:', this.data.dynamicFormData);
    console.log('动态表单数据字段数量:', Object.keys(this.data.dynamicFormData).length);
  },

  /**
   * 验证动态表单数据
   * @returns {Object} 验证结果
   */
  validateDynamicFormData() {
    const { dynamicFormData, dynamicFormConfig } = this.data;
    const errors = [];
    const warnings = [];

    if (!dynamicFormConfig || !dynamicFormConfig.children) {
      return { isValid: true, errors, warnings };
    }

    // 遍历表单配置，检查必填字段
    dynamicFormConfig.children.forEach(field => {
      if (field.required) {
        const fieldValue = dynamicFormData[field.name];
        if (!fieldValue || (typeof fieldValue === 'object' && !fieldValue.value) ||
            (typeof fieldValue === 'string' && fieldValue.trim() === '')) {
          errors.push(`${field.label || field.name} 是必填项`);
        }
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  },

  /**
   * 获取动态表单数据摘要
   * @returns {Object} 数据摘要
   */
  getDynamicFormDataSummary() {
    const { dynamicFormData } = this.data;
    const summary = {
      totalFields: 0,
      filledFields: 0,
      emptyFields: 0,
      fieldTypes: {}
    };

    Object.keys(dynamicFormData).forEach(key => {
      const fieldData = dynamicFormData[key];
      summary.totalFields++;

      if (fieldData && ((typeof fieldData === 'object' && fieldData.value) ||
          (typeof fieldData === 'string' && fieldData.trim()))) {
        summary.filledFields++;
      } else {
        summary.emptyFields++;
      }

      // 统计字段类型
      const fieldType = (fieldData && fieldData.type) || 'text';
      summary.fieldTypes[fieldType] = (summary.fieldTypes[fieldType] || 0) + 1;
    });

    return summary;
  },

  /**
   * 递归显示嵌套结构
   */
  logNestedStructure(obj, prefix = '') {
    if (typeof obj !== 'object' || obj === null) {
      return;
    }

    Object.keys(obj).forEach(key => {
      const value = obj[key];
      if (typeof value === 'object' && value !== null) {
        console.log(`${prefix}.${key}:`, value);
        this.logNestedStructure(value, `${prefix}.${key}`);
      } else {
        console.log(`${prefix}.${key}: ${value}`);
      }
    });
  },

  /**
   * 测试动态表单数据平铺功能
   */
  testDynamicFieldFlattening() {
    console.log('=== 测试动态表单数据平铺功能 ===');

    // 创建测试数据
    const testDynamicFormData = {
      // 普通字段
      'simple_field': 'simple_value',
      'qyfr8o21': '测试值1',

      // 包含下划线的字段
      'user_name': '张三',
      'user_age': '25',
      'contact_phone': '13800138000',
      'user_info_address': '北京市朝阳区',

      // 包含点号的字段
      'config.title': '配置标题',
      'config.description': '配置描述',
      'settings.theme': 'dark',
      'settings.lang.code': 'zh-CN'
    };

    // 模拟动态表单数据结构
    const mockDynamicFields = {
      input: testDynamicFormData,
      textarea: {
        'content_main': '主要内容',
        'content.summary': '内容摘要'
      },
      number: {
        'price_amount': '100',
        'quantity.total': '50'
      }
    };

    console.log('测试输入数据:', testDynamicFormData);

    // 测试当前实际的数据结构（从控制台看到的）
    const actualDynamicFormData = {
      "textarea": {
        "5fhzy3zl": "2",
        "67swn05h": "2",
        "bdyryzwv": "2"
      },
      "input": {
        "qyfr8o21": "2"
      }
    };

    console.log('测试实际数据结构:', actualDynamicFormData);

    // 使用 PublishHelper 处理分组数据
    const flattened = PublishHelper.flattenDynamicFormData(actualDynamicFormData);

    console.log('分组数据平铺结果:', flattened);

    // 也测试扁平数据处理
    console.log('--- 测试扁平数据处理 ---');
    const flatDynamicFormData = {
      'qyfr8o21': '2',
      '5fhzy3zl': '2',
      '67swn05h': '2',
      'bdyryzwv': '2'
    };
    const flatFlattened = PublishHelper.flattenDynamicFormData(flatDynamicFormData);
    console.log('扁平数据平铺结果:', flatFlattened);

    console.log('=== 测试完成 ===');

    return flattened;
  },

  // ==================== 其他方法 ====================

  /**
   * 向标题添加标签内容
   */
  addToTitle: function(e) {
    const tag = e.currentTarget.dataset.tag;
    const newTitle = this.data.title ? `${this.data.title} ${tag}` : tag;
    this.setData({ title: newTitle });
  },

  /**
   * 向描述添加标签内容
   */
  addToDescription: function(e) {
    const tag = e.currentTarget.dataset.tag;
    const newDescription = this.data.description ? `${this.data.description} ${tag}` : tag;
    this.setData({ description: newDescription });
  }
}); 