.form-card {
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 1px 2px rgba(255, 125, 125, 0.06);
  margin-bottom: 30rpx;
  padding: 40rpx;
}
.form-card-title {
  font-weight: 500;
  font-size: 32rpx;
  color: #FF7D7D;
  margin-bottom: 24rpx;
}
.form-card-body {
  padding: 0;
}
.form-item {
  margin-bottom: 24rpx;
}
.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 24rpx;
}
.contact-input {
  width: 100%;
  height: 96rpx;
  padding: 0 32rpx;
  font-size: 30rpx;
  color: #333;
  box-sizing: border-box;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  background: #fff;
}
.desc-input {
  width: 100%;
  height: 280rpx;
  padding: 32rpx;
  font-size: 30rpx;
  color: #333;
  box-sizing: border-box;
  line-height: 1.6;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  background: #fff;
}
.contact-type-group {
  margin-bottom: 24rpx;
}
.radio-group {
  display: flex;
  gap: 48rpx;
}
.radio-label {
  display: flex;
  align-items: center;
  font-size: 30rpx;
  color: #333;
}
.radio-label radio {
  margin-right: 12rpx;
  transform: scale(0.9);
}

/* 修改单选框的选中颜色 */
.radio-label radio .wx-radio-input.wx-radio-input-checked {
  background-color: #FF7D7D !important;
  border-color: #FF7D7D !important;
}

/* 修改单选框选中后中间的对勾颜色 */
.radio-label radio .wx-radio-input.wx-radio-input-checked::before {
  color: #fff;
}

/* 修改未选中状态的边框颜色 */
.radio-label radio .wx-radio-input {
  border-color: #ddd;
} 