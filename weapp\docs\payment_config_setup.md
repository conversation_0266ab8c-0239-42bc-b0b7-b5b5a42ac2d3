# 支付配置设置指南

## 📋 你的商户信息

根据提供的信息，已为你配置了以下参数：

### 基本信息
- **小程序ID**: `wx5f0591468a438c48`
- **商户号**: `1722243412`
- **APIv3密钥**: `asdqweqwezxc12312312313233215AA2`
- **证书序列号**: `2C625B38494EB8D0FA6BFF9DDD78B4F4381BA75E`

### 证书文件
- ✅ 商户证书已保存到: `weapp/cert/apiclient_cert.pem`
- ✅ 证书请求已保存到: `weapp/cert/apiclient_cert.csr`

## ⚠️ 需要完成的配置

### 1. 设置API密钥（重要）

你需要在微信商户平台设置32位的API密钥：

1. 登录 [微信商户平台](https://pay.weixin.qq.com)
2. 进入 **账户中心** → **API安全**
3. 设置API密钥（32位字符，包含大小写字母和数字）
4. 将密钥更新到配置文件中

```javascript
// 在 weapp/config/payConfigProduction.js 中更新
mchKey: 'YOUR_32_CHARACTER_API_KEY_HERE', // 替换为实际的API密钥
```

### 2. 配置回调地址

将回调地址替换为你的实际域名：

```javascript
// 支付回调地址
notifyUrl: 'https://yourdomain.com/api/pay/notify',

// 退款回调地址  
refundNotifyUrl: 'https://yourdomain.com/api/pay/refund-notify',
```

### 3. 下载并配置私钥文件

你需要下载商户私钥文件：

1. 在微信商户平台的 **API安全** 页面
2. 下载商户私钥文件 `apiclient_key.pem`
3. 将文件保存到 `weapp/cert/apiclient_key.pem`

### 4. 生成P12证书文件（用于退款）

如果需要退款功能，需要生成P12格式的证书：

```bash
# 使用OpenSSL生成P12证书
openssl pkcs12 -export -out apiclient_cert.p12 -inkey apiclient_key.pem -in apiclient_cert.pem
```

## 🔧 配置文件使用

### 使用生产配置

```javascript
// 引入生产配置
const { payConfig, getCurrentWechatPayConfig } = require('./config/payConfigProduction');

// 获取当前配置
const config = getCurrentWechatPayConfig();
console.log('当前支付配置:', config);
```

### 环境切换

```javascript
// 开发环境使用沙箱
payConfig.wechatPay.currentEnv = 'sandbox';

// 生产环境使用正式配置
payConfig.wechatPay.currentEnv = 'production';
```

## 🏗️ 后端配置示例

### Spring Boot 配置

```yaml
# application.yml
wechat:
  pay:
    app-id: wx5f0591468a438c48
    mch-id: 1722243412
    mch-key: ${WECHAT_PAY_MCH_KEY} # 从环境变量读取
    api-v3-key: asdqweqwezxc12312312313233215AA2
    merchant-serial-number: 2C625B38494EB8D0FA6BFF9DDD78B4F4381BA75E
    cert-path: classpath:cert/apiclient_cert.pem
    key-path: classpath:cert/apiclient_key.pem
    notify-url: https://yourdomain.com/api/pay/notify
```

### Java 配置类

```java
@Configuration
public class WxPayConfig {
    
    @Value("${wechat.pay.app-id}")
    private String appId;
    
    @Value("${wechat.pay.mch-id}")
    private String mchId;
    
    @Value("${wechat.pay.mch-key}")
    private String mchKey;
    
    @Value("${wechat.pay.api-v3-key}")
    private String apiV3Key;
    
    @Value("${wechat.pay.merchant-serial-number}")
    private String merchantSerialNumber;
    
    @Bean
    public WxPayService wxPayService() {
        WxPayService wxPayService = new WxPayServiceImpl();
        WxPayConfig payConfig = new WxPayConfig();
        
        payConfig.setAppId(appId);
        payConfig.setMchId(mchId);
        payConfig.setMchKey(mchKey);
        payConfig.setApiV3Key(apiV3Key);
        payConfig.setMerchantSerialNumber(merchantSerialNumber);
        payConfig.setCertPath("classpath:cert/apiclient_cert.pem");
        payConfig.setKeyPath("classpath:cert/apiclient_key.pem");
        
        wxPayService.setConfig(payConfig);
        return wxPayService;
    }
}
```

## 🔒 安全注意事项

### 1. 密钥安全
- ❌ 不要将API密钥硬编码在代码中
- ✅ 使用环境变量或配置文件管理
- ✅ 定期更换API密钥

### 2. 证书安全
- ✅ 证书文件权限设置为只读
- ✅ 不要将证书文件提交到版本控制
- ✅ 定期检查证书有效期

### 3. 回调安全
- ✅ 验证回调签名
- ✅ 使用HTTPS协议
- ✅ 验证回调来源IP

## 🧪 测试验证

### 1. 配置验证

```javascript
const { validatePayConfig } = require('./config/payConfigProduction');

try {
  validatePayConfig();
  console.log('✅ 支付配置验证通过');
} catch (error) {
  console.error('❌ 支付配置验证失败:', error.message);
}
```

### 2. 小额测试

建议先进行小额测试（如0.01元）验证支付流程：

```javascript
const testPayment = {
  businessType: 'test',
  businessId: 'test_001',
  amount: '0.01',
  description: '支付测试'
};
```

### 3. 沙箱测试

在正式上线前，建议在沙箱环境充分测试：

```javascript
// 切换到沙箱环境
payConfig.wechatPay.currentEnv = 'sandbox';
```

## 📱 小程序配置

### 1. 域名白名单

在小程序后台配置服务器域名：
- request合法域名：`https://yourdomain.com`
- uploadFile合法域名：`https://yourdomain.com`

### 2. 支付授权目录

在微信商户平台配置支付授权目录：
- 授权目录：`https://yourdomain.com/`

## 🚀 快速启动

### 1. 更新配置文件

```javascript
// 修改 weapp/config/payConfigProduction.js
const productionPayConfig = {
  wechatPay: {
    appId: 'wx5f0591468a438c48',
    mchId: '1722243412',
    mchKey: 'YOUR_ACTUAL_32_CHAR_API_KEY', // 替换为实际密钥
    apiV3Key: 'asdqweqwezxc12312312313233215AA2',
    merchantSerialNumber: '2C625B38494EB8D0FA6BFF9DDD78B4F4381BA75E',
    notifyUrl: 'https://yourdomain.com/api/pay/notify', // 替换为实际域名
    // ... 其他配置
  }
};
```

### 2. 使用新配置

```javascript
// 在需要使用支付的地方
const { payConfig } = require('./config/payConfigProduction');

// 替换原来的配置引用
// const { payConfig } = require('./config/payConfig');
```

### 3. 测试支付功能

访问支付演示页面进行测试：
```
/pages/payment-demo/payment-demo
```

## ✅ 配置检查清单

- [ ] API密钥已在商户平台设置（32位）
- [ ] 回调地址已更新为实际域名
- [ ] 商户私钥文件已下载并配置
- [ ] P12证书已生成（如需退款功能）
- [ ] 小程序域名白名单已配置
- [ ] 支付授权目录已配置
- [ ] 配置验证通过
- [ ] 小额测试成功

完成以上配置后，你的支付系统就可以正常使用了！
