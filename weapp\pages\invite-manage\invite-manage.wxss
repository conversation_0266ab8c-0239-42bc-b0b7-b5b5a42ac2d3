/* 邀请码管理页面样式 */

.invite-manage-container {
  min-height: 100vh;
  background-color: #fef8f8;
  padding-bottom: 20rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #ffe0e0;
  border-top: 4rpx solid #ff7d7d;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 20rpx;
  color: #666;
  font-size: 28rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 主要内容 */
.main-content {
  padding: 20rpx;
}

/* 统计卡片 */
.stats-card {
  background: linear-gradient(135deg, #ff7d7d 0%, #ff6b6b 100%);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(255, 125, 125, 0.2);
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.stats-title {
  font-size: 36rpx;
  font-weight: bold;
}

.stats-filter {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.2);
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
}

.stats-filter .icon-arrow-down {
  margin-left: 10rpx;
  font-size: 20rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
}

.stats-item {
  text-align: center;
}

.stats-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.stats-label {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 通用区块样式 */
.section {
  background: white;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f8f8f8;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.section-count {
  font-size: 24rpx;
  color: #666;
}

/* 创建按钮 */
.create-btn {
  display: flex;
  align-items: center;
  background: #ff7d7d;
  color: white;
  border: none;
  border-radius: 30rpx;
  padding: 15rpx 25rpx;
  font-size: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 125, 125, 0.3);
}

.create-btn .icon-add {
  margin-right: 10rpx;
  font-size: 20rpx;
}

/* 邀请码列表 */
.invite-codes-list {
  padding: 0 30rpx 30rpx;
}

.invite-code-item {
  border: 1rpx solid #ffe0e0;
  border-radius: 15rpx;
  padding: 25rpx;
  margin-bottom: 20rpx;
  background: #fff;
  box-shadow: 0 2rpx 8rpx rgba(255, 125, 125, 0.1);
}

.code-info {
  margin-bottom: 20rpx;
}

.code-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.code-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  font-family: 'Courier New', monospace;
}

.code-status {
  padding: 5rpx 15rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  color: white;
}

.code-status.active {
  background: #ff7d7d;
}

.code-status.inactive {
  background: #ff6b6b;
}

.code-status.expired {
  background: #ffa500;
}

.code-stats {
  display: flex;
  gap: 30rpx;
  margin-bottom: 10rpx;
}

.stat-item {
  font-size: 24rpx;
  color: #666;
}

.code-time {
  font-size: 22rpx;
  color: #999;
}

.code-actions {
  display: flex;
  gap: 15rpx;
}

.action-btn {
  flex: 1;
  padding: 15rpx;
  border: none;
  border-radius: 10rpx;
  font-size: 24rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.action-btn.detail {
  background: #fff0f0;
  color: #ff7d7d;
}

.action-btn.qr {
  background: #fff0f0;
  color: #ff7d7d;
}

.action-btn.share {
  background: #fff0f0;
  color: #ff7d7d;
}

.action-btn.copy {
  background: #fff0f0;
  color: #ff7d7d;
}

.action-btn:active {
  transform: scale(0.95);
}

/* 邀请用户列表 */
.invitees-list {
  padding: 0 30rpx 30rpx;
}

.invitee-item {
  display: flex;
  align-items: center;
  padding: 25rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.invitee-item:last-child {
  border-bottom: none;
}

.invitee-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.invitee-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.invitee-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.invitee-time,
.invitee-code {
  font-size: 22rpx;
  color: #666;
}

.invitee-reward {
  text-align: right;
}

.reward-points {
  font-size: 24rpx;
  color: #ff7d7d;
  font-weight: bold;
}

/* 最近记录 */
.recent-records {
  padding: 0 30rpx 30rpx;
}

.record-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.record-item:last-child {
  border-bottom: none;
}

.record-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.record-action {
  font-size: 26rpx;
  color: #333;
}

.record-time {
  font-size: 22rpx;
  color: #999;
}

.record-result {
  padding: 5rpx 15rpx;
  border-radius: 15rpx;
  font-size: 22rpx;
  color: white;
}

.record-result.success {
  background: #ff7d7d;
}

.record-result.pending {
  background: #ffa500;
}

.record-result.failed {
  background: #ff6b6b;
}

/* 排行榜 */
.ranking-list {
  padding: 0 30rpx 30rpx;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.ranking-item:last-child {
  border-bottom: none;
}

.ranking-number {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #ffe0e0;
  color: #ff7d7d;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 20rpx;
}

.ranking-number.top {
  background: linear-gradient(135deg, #ff7d7d, #ff6b6b);
  color: white;
}

.ranking-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.ranking-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.ranking-name {
  font-size: 26rpx;
  color: #333;
}

.ranking-count {
  font-size: 22rpx;
  color: #666;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx;
  color: #999;
}

.empty-state .iconfont {
  font-size: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  margin-bottom: 30rpx;
}

.empty-action {
  background: #ff7d7d;
  color: white;
  border: none;
  border-radius: 30rpx;
  padding: 20rpx 40rpx;
  font-size: 26rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 125, 125, 0.3);
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 40rpx;
}

.modal-content {
  background: white;
  border-radius: 20rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f8f8f8;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.icon-close {
  font-size: 40rpx;
  color: #999;
  cursor: pointer;
}

.modal-body {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

/* 详情弹窗 */
.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 26rpx;
  color: #666;
}

.detail-value {
  font-size: 26rpx;
  color: #333;
  font-weight: bold;
}

.registered-users {
  margin-top: 30rpx;
}

.users-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.user-item:last-child {
  border-bottom: none;
}

.user-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 15rpx;
}

.user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 5rpx;
}

.user-name {
  font-size: 24rpx;
  color: #333;
}

.user-time {
  font-size: 20rpx;
  color: #999;
}

/* 二维码弹窗 */
.qr-modal {
  max-width: 500rpx;
}

.qr-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.qr-image {
  width: 300rpx;
  height: 300rpx;
  border: 1rpx solid #ffe0e0;
  border-radius: 15rpx;
  margin-bottom: 20rpx;
}

.qr-code-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
  font-family: 'Courier New', monospace;
}

.qr-tip {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 30rpx;
}

.qr-actions {
  display: flex;
  gap: 20rpx;
}

.qr-btn {
  flex: 1;
  padding: 20rpx;
  border: none;
  border-radius: 15rpx;
  font-size: 26rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.qr-btn.save {
  background: #fff0f0;
  color: #ff7d7d;
}

.qr-btn.share {
  background: #fff0f0;
  color: #ff7d7d;
}

.qr-btn:active {
  transform: scale(0.95);
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .stats-grid {
    grid-template-columns: 1fr 1fr;
    gap: 20rpx;
  }

  .stats-number {
    font-size: 40rpx;
  }

  .code-actions {
    flex-wrap: wrap;
  }

  .action-btn {
    min-width: 120rpx;
  }
}
