Component({
  properties: {
    title: {
      type: String,
      value: ''
    },
    extra: {
      type: String,
      value: ''
    },
    footer: {
      type: Boolean,
      value: false
    },
    shadow: {
      type: Boolean,
      value: false
    },
    className: {
      type: String,
      value: ''
    }
  },

  methods: {
    onClick() {
      this.triggerEvent('click');
    }
  }
}); 