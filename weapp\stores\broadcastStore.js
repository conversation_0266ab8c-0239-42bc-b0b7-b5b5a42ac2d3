const { request } = require('../utils/request');

/**
 * 广播通知相关的数据请求和业务逻辑
 */
class BroadcastStore {
  /**
   * 获取广播通知列表
   * @param {Object} params 查询参数
   * @returns {Promise} 广播通知列表
   */
  static async getNoticeList(params = {}) {
    try {
      const defaultParams = {
        page: 1,
        size: 20,
        type: null
      };
      
      const queryParams = { ...defaultParams, ...params };
      
      const res = await request({
        url: '/blade-chat-open/notice/list',
        method: 'GET',
        data: queryParams
      });

      if (res.code === 200) {
        // 处理通知数据
        const processedNotices = this.processNoticeList(res.data.records || []);
        
        return {
          success: true,
          data: {
            records: processedNotices,
            total: res.data.total || 0,
            current: res.data.current || 1,
            size: res.data.size || 20,
            pages: res.data.pages || 1
          },
          message: '获取广播通知成功'
        };
      } else {
        return {
          success: false,
          message: res.msg || '获取广播通知失败'
        };
      }
    } catch (error) {
      console.error('获取广播通知失败:', error);
      return {
        success: false,
        message: '网络错误，请稍后重试'
      };
    }
  }

  /**
   * 获取广播通知详情
   * @param {string} id 通知ID
   * @returns {Promise} 通知详情
   */
  static async getNoticeDetail(id) {
    try {
      const res = await request({
        url: `/blade-chat-open/notice/detail/${id}`,
        method: 'GET'
      });

      if (res.code === 200) {
        const processedNotice = this.processNoticeDetail(res.data);
        
        return {
          success: true,
          data: processedNotice,
          message: '获取通知详情成功'
        };
      } else {
        return {
          success: false,
          message: res.msg || '获取通知详情失败'
        };
      }
    } catch (error) {
      console.error('获取通知详情失败:', error);
      return {
        success: false,
        message: '网络错误，请稍后重试'
      };
    }
  }

  /**
   * 处理通知列表数据
   * @param {Array} notices 原始通知列表
   * @returns {Array} 处理后的通知列表
   */
  static processNoticeList(notices) {
    return notices.map(notice => this.processNoticeItem(notice));
  }

  /**
   * 处理单个通知数据
   * @param {Object} notice 原始通知数据
   * @returns {Object} 处理后的通知数据
   */
  static processNoticeItem(notice) {
    return {
      id: notice.id,
      type: notice.type,
      title: notice.title,
      content: notice.content,
      author: notice.author,
      authorAvatar: notice.authorAvatar,
      createTime: notice.createTime,
      timeText: this.formatTime(notice.createTime),
      relatedId: notice.relatedId,
      relatedType: notice.relatedType,
      categoryName: notice.categoryName,
      institutionName: notice.institutionName,
      days: notice.days,
      points: notice.points,
      priority: notice.priority,
      icon: notice.icon || this.getDefaultIcon(notice.type)
    };
  }

  /**
   * 处理通知详情数据
   * @param {Object} notice 原始通知详情
   * @returns {Object} 处理后的通知详情
   */
  static processNoticeDetail(notice) {
    return {
      ...this.processNoticeItem(notice),
      fullContent: notice.content,
      images: notice.images || [],
      tags: notice.tags || [],
      location: notice.location,
      viewCount: notice.viewCount || 0,
      likeCount: notice.likeCount || 0
    };
  }

  /**
   * 格式化时间显示
   * @param {string} timeString 时间字符串
   * @returns {string} 格式化后的时间
   */
  static formatTime(timeString) {
    if (!timeString) return '';
    
    try {
      const time = new Date(timeString);
      const now = new Date();
      const diff = now - time;
      
      // 小于1分钟
      if (diff < 60000) {
        return '刚刚';
      }
      
      // 小于1小时
      if (diff < 3600000) {
        const minutes = Math.floor(diff / 60000);
        return `${minutes}分钟前`;
      }
      
      // 小于1天
      if (diff < 86400000) {
        const hours = Math.floor(diff / 3600000);
        return `${hours}小时前`;
      }
      
      // 小于7天
      if (diff < 604800000) {
        const days = Math.floor(diff / 86400000);
        return `${days}天前`;
      }
      
      // 超过7天，显示具体日期
      const month = String(time.getMonth() + 1).padStart(2, '0');
      const day = String(time.getDate()).padStart(2, '0');
      return `${month}-${day}`;
    } catch (error) {
      console.warn('时间格式化失败:', error);
      return timeString;
    }
  }

  /**
   * 获取默认图标
   * @param {string} type 通知类型
   * @returns {string} 图标
   */
  static getDefaultIcon(type) {
    const iconMap = {
      post: '📝',
      institution: '🏪',
      checkin: '✅',
      announcement: '📢',
      system: '⚙️',
      warning: '⚠️'
    };
    
    return iconMap[type] || '📢';
  }

  /**
   * 获取通知类型显示名称
   * @param {string} type 通知类型
   * @returns {string} 显示名称
   */
  static getTypeDisplayName(type) {
    const typeMap = {
      post: '帖子发布',
      institution: '机构入驻',
      checkin: '签到',
      announcement: '公告',
      system: '系统通知',
      warning: '警告'
    };
    
    return typeMap[type] || '通知';
  }
}

module.exports = BroadcastStore;
