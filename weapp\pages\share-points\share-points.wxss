/* 分享积分记录页面样式 */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.page-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.page-subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
}

/* 积分统计卡片 */
.stats-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  color: white;
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.stats-title {
  font-size: 32rpx;
  font-weight: bold;
}

.refresh-btn {
  font-size: 28rpx;
  padding: 10rpx 20rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
}

.stats-content {
  display: flex;
  justify-content: space-between;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-value {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  opacity: 0.8;
}

/* 邀请好友卡片 */
.invite-card {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  color: white;
}

.invite-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30rpx;
}

.invite-title-section {
  flex: 1;
}

.invite-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.invite-reward {
  display: block;
  font-size: 24rpx;
  opacity: 0.9;
}

.invite-icon {
  font-size: 40rpx;
}

.invite-content {
  /* 内容样式 */
}

.invite-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.invite-stat-item {
  text-align: center;
  flex: 1;
}

.invite-stat-value {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.invite-stat-label {
  display: block;
  font-size: 24rpx;
  opacity: 0.8;
}

.invite-actions {
  display: flex;
  gap: 20rpx;
}

.invite-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  border-radius: 15rpx;
  font-size: 28rpx;
  border: none;
  color: white;
}

.invite-btn.primary {
  background: rgba(255, 255, 255, 0.3);
}

.invite-btn.secondary {
  background: rgba(255, 255, 255, 0.2);
}

.invite-btn .btn-icon {
  margin-right: 10rpx;
  font-size: 24rpx;
}

.invite-btn .btn-text {
  font-size: 26rpx;
}

/* 邀请记录卡片 */
.invite-records-card {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.invite-records-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.invite-records-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.view-all-btn {
  font-size: 26rpx;
  color: #667eea;
}

.invite-records-list {
  /* 列表样式 */
}

.invite-record-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.invite-record-item:last-child {
  border-bottom: none;
}

.invite-record-avatar {
  margin-right: 20rpx;
}

.avatar-img {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
}

.invite-record-info {
  flex: 1;
}

.invitee-name {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 5rpx;
}

.invite-time {
  display: block;
  font-size: 24rpx;
  color: #999;
}

.invite-record-reward {
  text-align: right;
}

.reward-points {
  display: block;
  font-size: 28rpx;
  color: #ff6b6b;
  font-weight: bold;
  margin-bottom: 5rpx;
}

.reward-label {
  display: block;
  font-size: 22rpx;
  color: #999;
}

.empty-invite-records {
  text-align: center;
  padding: 60rpx 0;
}

.empty-invite-records .empty-text {
  display: block;
  font-size: 28rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.empty-invite-records .empty-tip {
  display: block;
  font-size: 24rpx;
  color: #ccc;
}

/* 今日分享情况 */
.today-card {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.today-header {
  margin-bottom: 30rpx;
}

.today-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.today-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.today-item:last-child {
  margin-bottom: 0;
}

.today-type {
  flex: 1;
}

.type-name {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 5rpx;
}

.type-points {
  display: block;
  font-size: 24rpx;
  color: #ff6b6b;
}

.today-progress {
  display: flex;
  align-items: center;
  width: 200rpx;
}

.progress-text {
  font-size: 24rpx;
  color: #666;
  margin-right: 20rpx;
  min-width: 80rpx;
}

.progress-bar {
  flex: 1;
  height: 8rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

/* 筛选器 */
.filter-bar {
  margin-bottom: 30rpx;
}

.filter-tabs {
  display: flex;
  background: white;
  border-radius: 20rpx;
  padding: 10rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.filter-tab {
  flex: 1;
  text-align: center;
  padding: 20rpx 10rpx;
  font-size: 28rpx;
  color: #666;
  border-radius: 15rpx;
  transition: all 0.3s ease;
}

.filter-tab.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

/* 积分记录列表 */
.records-list {
  margin-bottom: 30rpx;
}

.record-item {
  display: flex;
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.record-icon {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 30rpx;
  flex-shrink: 0;
}

.icon-share {
  font-size: 36rpx;
}

.record-content {
  flex: 1;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.record-title {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  flex: 1;
}

.record-points {
  font-size: 32rpx;
  color: #ff6b6b;
  font-weight: bold;
}

.record-details {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.record-time {
  font-size: 24rpx;
  color: #999;
}

.record-type {
  font-size: 24rpx;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  padding: 5rpx 15rpx;
  border-radius: 10rpx;
}

.record-remark {
  margin-top: 10rpx;
}

.remark-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  display: block;
  font-size: 32rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.empty-tip {
  display: block;
  font-size: 28rpx;
  color: #999;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 50rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 30rpx 0;
}

.load-more-text {
  font-size: 28rpx;
  color: #667eea;
}

.no-more {
  text-align: center;
  padding: 30rpx 0;
}

.no-more-text {
  font-size: 24rpx;
  color: #999;
}

/* 分享积分规则 */
.rules-card {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.rules-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.rules-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.rules-toggle {
  font-size: 28rpx;
  color: #667eea;
}

.rules-content {
  margin-top: 30rpx;
}

.rule-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.rule-item:last-child {
  border-bottom: none;
}

.rule-type {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.rule-points {
  font-size: 28rpx;
  color: #ff6b6b;
  margin: 0 30rpx;
}

.rule-limit {
  font-size: 24rpx;
  color: #999;
}

.rule-note {
  margin-top: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
}

.note-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

/* 快捷分享按钮 */
.quick-share-bar {
  position: fixed;
  bottom: 30rpx;
  left: 30rpx;
  right: 30rpx;
  z-index: 100;
}

.quick-share-btn {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 30rpx rgba(102, 126, 234, 0.3);
}

.btn-icon {
  margin-right: 15rpx;
  font-size: 36rpx;
}

.btn-text {
  font-size: 32rpx;
}

/* 快捷操作栏 */
.quick-actions-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 15rpx;
  z-index: 100;
}

.quick-action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20rpx 10rpx;
  border-radius: 15rpx;
  font-size: 24rpx;
  border: none;
  min-height: 100rpx;
}

.quick-action-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.quick-action-btn.secondary {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  color: white;
}

.quick-action-btn.tertiary {
  background: #f8f9fa;
  color: #333;
  border: 1rpx solid #e9ecef;
}

.quick-action-btn .btn-icon {
  margin-right: 0;
  margin-bottom: 8rpx;
  font-size: 32rpx;
}

.quick-action-btn .btn-text {
  font-size: 24rpx;
}

/* 邀请二维码弹窗 */
.qrcode-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.qrcode-content {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin: 40rpx;
  max-width: 600rpx;
  width: 100%;
}

.qrcode-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
}

.qrcode-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.qrcode-close {
  font-size: 40rpx;
  color: #999;
  padding: 10rpx;
}

.qrcode-body {
  text-align: center;
}

.qrcode-container {
  display: flex;
  justify-content: center;
  margin-bottom: 30rpx;
}

.qrcode-canvas {
  border: 1rpx solid #e9ecef;
  border-radius: 10rpx;
}

.qrcode-info {
  margin-bottom: 40rpx;
}

.qrcode-tip {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.invite-code {
  display: block;
  font-size: 24rpx;
  color: #999;
  background: #f8f9fa;
  padding: 15rpx;
  border-radius: 10rpx;
  margin: 0 auto;
  max-width: 300rpx;
}

.qrcode-actions {
  display: flex;
  gap: 20rpx;
}

.qrcode-btn {
  flex: 1;
  padding: 25rpx;
  border-radius: 15rpx;
  font-size: 28rpx;
  border: none;
}

.qrcode-btn.save {
  background: #667eea;
  color: white;
}

.qrcode-btn.share {
  background: #ff6b6b;
  color: white;
}

/* 积分记录图标样式 */
.record-icon .icon-invite {
  color: #ff6b6b;
}

.record-icon .icon-share {
  color: #667eea;
}

.record-icon .icon-default {
  color: #28a745;
}

/* 邀请信息样式 */
.invite-info {
  margin-top: 15rpx;
}

.invitee-detail {
  display: flex;
  align-items: center;
}

.invitee-avatar {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  margin-right: 15rpx;
}

.invitee-name {
  font-size: 24rpx;
  color: #666;
}

/* 规则部分样式调整 */
.rule-section {
  margin-bottom: 30rpx;
}

.rule-section:last-child {
  margin-bottom: 0;
}

.rule-section-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 1rpx solid #e9ecef;
}

.rule-note .note-text {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.rule-note .note-text:last-child {
  margin-bottom: 0;
}

/* 底部安全区域 */
.container {
  padding-bottom: 200rpx; /* 为固定底栏留出空间 */
}
