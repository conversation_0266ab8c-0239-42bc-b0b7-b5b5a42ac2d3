const BroadcastStore = require('../../stores/broadcastStore');

Page({
  data: {
    notices: [],
    currentFilter: '', // 当前筛选类型
    loading: false,
    hasMore: true,
    currentPage: 1,
    pageSize: 20
  },

  onLoad(options) {
    console.log('广播列表页面加载');
    this.loadNotices();
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.refreshNotices();
  },

  // 筛选类型切换
  onFilterChange(e) {
    const filter = e.currentTarget.dataset.filter;
    if (filter === this.data.currentFilter) return;
    
    this.setData({
      currentFilter: filter,
      notices: [],
      currentPage: 1,
      hasMore: true
    });
    
    this.loadNotices();
  },

  // 加载通知列表
  async loadNotices() {
    if (this.data.loading || !this.data.hasMore) return;
    
    this.setData({ loading: true });
    
    try {
      const result = await BroadcastStore.getNoticeList({
        page: this.data.currentPage,
        size: this.data.pageSize,
        type: this.data.currentFilter || null
      });
      
      if (result.success) {
        const newNotices = result.data.records.map(notice => ({
          ...notice,
          typeText: BroadcastStore.getTypeDisplayName(notice.type)
        }));
        
        const notices = this.data.currentPage === 1 
          ? newNotices 
          : [...this.data.notices, ...newNotices];
        
        this.setData({
          notices,
          hasMore: result.data.current < result.data.pages,
          currentPage: this.data.currentPage + 1
        });
      } else {
        wx.showToast({
          title: result.message || '加载失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('加载通知失败:', error);
      wx.showToast({
        title: '网络错误',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
      wx.stopPullDownRefresh();
    }
  },

  // 刷新通知列表
  async refreshNotices() {
    this.setData({
      notices: [],
      currentPage: 1,
      hasMore: true
    });
    
    await this.loadNotices();
  },

  // 加载更多
  onLoadMore() {
    this.loadNotices();
  },

  // 点击通知
  onNoticeClick(e) {
    const notice = e.currentTarget.dataset.notice;
    console.log('点击通知:', notice);
    
    // 根据通知类型进行跳转
    this.handleNoticeNavigation(notice);
  },

  // 处理通知跳转
  handleNoticeNavigation(notice) {
    const { type, relatedId, relatedType } = notice;
    
    switch (type) {
      case 'post':
        // 跳转到帖子详情
        wx.navigateTo({
          url: `/pages/post-detail/post-detail?id=${relatedId}`
        });
        break;
        
      case 'institution':
        // 跳转到机构详情
        wx.navigateTo({
          url: `/pages/institution-detail/institution-detail?id=${relatedId}`
        });
        break;
        
      case 'checkin':
        // 跳转到签到页面
        wx.navigateTo({
          url: `/pages/checkin/checkin`
        });
        break;
        
      case 'announcement':
        // 跳转到公告详情
        wx.navigateTo({
          url: `/pages/announcement-detail/announcement-detail?id=${relatedId}`
        });
        break;
        
      default:
        // 显示通知详情
        wx.showModal({
          title: notice.title,
          content: notice.content,
          showCancel: false
        });
        break;
    }
  }
});
