/**
 * 帖子相关API服务
 */
const { request } = require('../utils/request');
const config = require('../config/api');

class PostService {
  
  /**
   * 获取帖子详情
   * @param {string} postId 帖子ID
   * @returns {Promise} API响应
   */
  async getPostDetail(postId) {
    try {
      const response = await request({
        url: `${config.post.detail}/${postId}`,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('获取帖子详情失败:', error);
      throw error;
    }
  }

  /**
   * 点赞/取消点赞帖子
   * @param {string} postId 帖子ID
   * @returns {Promise} API响应
   */
  async toggleLike(postId) {
    try {
      const response = await request({
        url: `${config.post.like}/${postId}`,
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('点赞帖子失败:', error);
      throw error;
    }
  }

  /**
   * 收藏/取消收藏帖子
   * @param {string} postId 帖子ID
   * @returns {Promise} API响应
   */
  async toggleFavorite(postId) {
    try {
      const response = await request({
        url: `${config.post.favorite}/${postId}`,
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('收藏帖子失败:', error);
      throw error;
    }
  }

  /**
   * 获取帖子列表
   * @param {Object} query 查询参数
   * @param {number} query.current 当前页码
   * @param {number} query.size 每页大小
   * @param {string} query.categoryId 分类ID（可选）
   * @param {string} query.keyword 关键词（可选）
   * @returns {Promise} API响应
   */
  async getPostList(query = {}) {
    try {
      const response = await request({
        url: config.post.list,
        method: 'GET',
        data: {
          current: query.current || 1,
          size: query.size || 10,
          ...query
        }
      });
      return response;
    } catch (error) {
      console.error('获取帖子列表失败:', error);
      throw error;
    }
  }

  /**
   * 发布帖子
   * @param {Object} postData 帖子数据
   * @returns {Promise} API响应
   */
  async publishPost(postData) {
    try {
      const response = await request({
        url: config.post.publish,
        method: 'POST',
        data: postData
      });
      return response;
    } catch (error) {
      console.error('发布帖子失败:', error);
      throw error;
    }
  }

  /**
   * 记录分享
   * @param {string} postId 帖子ID
   * @returns {Promise} API响应
   */
  async recordShare(postId) {
    try {
      const response = await request({
        url: `/blade-chat/post/share/${postId}`,
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('记录分享失败:', error);
      throw error;
    }
  }

  /**
   * 举报帖子
   * @param {Object} reportData 举报数据
   * @param {string} reportData.postId 帖子ID
   * @param {string} reportData.reason 举报原因
   * @param {string} reportData.description 举报描述
   * @returns {Promise} API响应
   */
  async reportPost(reportData) {
    try {
      const response = await request({
        url: '/blade-chat/post/report',
        method: 'POST',
        data: reportData
      });
      return response;
    } catch (error) {
      console.error('举报帖子失败:', error);
      throw error;
    }
  }

  /**
   * 添加浏览记录
   * @param {string} postId 帖子ID
   * @returns {Promise} API响应
   */
  async addViewHistory(postId) {
    try {
      const response = await request({
        url: `/blade-chat/post/view/${postId}`,
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('添加浏览记录失败:', error);
      throw error;
    }
  }
}

// 创建单例实例
const postService = new PostService();

module.exports = postService;
