.task-progress-card {
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 24rpx rgba(255,107,107,0.08);
  padding: 32rpx 32rpx 24rpx 32rpx;
  margin-bottom: 32rpx;
}
.task-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}
.task-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #222;
}
.task-points {
  font-size: 24rpx;
  color: #ff6b6b;
  margin-left: 16rpx;
}
.task-detail {
  font-size: 24rpx;
  color: #ff6b6b;
  margin-left: auto;
  cursor: pointer;
}
.task-progress {
  display: flex;
  align-items: center;
  gap: 24rpx;
}
.progress-bar {
  flex: 1;
  height: 16rpx;
  background: #fbeaea;
  border-radius: 8rpx;
  overflow: hidden;
  margin-right: 16rpx;
}
.progress-inner {
  height: 100%;
  background: linear-gradient(90deg, #ff7b7b 0%, #ffb6b6 100%);
  border-radius: 8rpx;
  transition: width 0.3s;
}
.task-desc {
  font-size: 22rpx;
  color: #999;
  min-width: 160rpx;
} 