.login-container {
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  background-color: #fff;
}

.logo {
  margin: 60rpx 0;
  width: 200rpx;
  height: 200rpx;
}

.logo image {
  width: 100%;
  height: 100%;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 60rpx;
}

.login-btn {
  width: 100%;
  margin: 40rpx 0;
}

.btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  text-align: center;
  color: #fff;
  background: #07c160;
  transition: all 0.3s;
}

.btn.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.btn:active {
  opacity: 0.8;
}

.tips {
  font-size: 24rpx;
  color: #999;
  margin-top: 40rpx;
}

.link {
  color: #07c160;
}

.protocol-check {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999;
  margin-top: 40rpx;
}

.protocol-check .link {
  color: #07c160;
  margin: 0 4rpx;
}

/* 邀请码部分样式 */
.invite-section {
  width: 100%;
  margin: 30rpx 0;
}

/* 静默邀请提示 */
.pending-invite-tip {
  display: flex;
  align-items: center;
  padding: 20rpx;
  margin-bottom: 15rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 15rpx;
  box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);
}

.pending-invite-tip .tip-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 15rpx;
  filter: brightness(0) invert(1);
}

.pending-invite-tip .tip-text {
  font-size: 26rpx;
  color: #fff;
  font-weight: 500;
}

.invite-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  background: #f8f9ff;
  border-radius: 15rpx;
  border: 2rpx solid #e0e6ff;
  transition: all 0.3s;
}

.invite-toggle:active {
  background: #f0f4ff;
}

.invite-text {
  font-size: 28rpx;
  color: #667eea;
  margin-right: 10rpx;
}

.invite-icon {
  width: 24rpx;
  height: 24rpx;
  transition: transform 0.3s;
}

.invite-icon.rotate {
  transform: rotate(180deg);
}

.invite-input-section {
  margin-top: 20rpx;
  padding: 30rpx;
  background: #f8f9ff;
  border-radius: 15rpx;
  border: 2rpx solid #e0e6ff;
}

.input-group {
  display: flex;
  align-items: center;
  background: white;
  border-radius: 12rpx;
  border: 2rpx solid #e0e0e0;
  overflow: hidden;
  transition: border-color 0.3s;
}

.input-group:focus-within {
  border-color: #667eea;
}

.invite-input {
  flex: 1;
  padding: 25rpx 20rpx;
  font-size: 28rpx;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
}

.invite-input.valid {
  color: #4caf50;
}

.invite-input.invalid {
  color: #f44336;
}

.scan-btn {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #667eea;
  transition: background 0.3s;
}

.scan-btn:active {
  background: #5a6fd8;
}

.scan-btn image {
  width: 40rpx;
  height: 40rpx;
}

.invite-status {
  display: flex;
  align-items: center;
  margin-top: 20rpx;
  padding: 15rpx;
  border-radius: 10rpx;
}

.invite-status.valid {
  background: #e8f5e8;
  border: 2rpx solid #c8e6c9;
}

.invite-status.invalid {
  background: #ffebee;
  border: 2rpx solid #ffcdd2;
}

.status-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 15rpx;
}

.status-text {
  font-size: 26rpx;
  flex: 1;
}

.invite-status.valid .status-text {
  color: #4caf50;
}

.invite-status.invalid .status-text {
  color: #f44336;
}

.reward-tip {
  display: flex;
  align-items: center;
  margin-top: 15rpx;
  padding: 15rpx;
  background: #fff3e0;
  border: 2rpx solid #ffcc02;
  border-radius: 10rpx;
}

.tip-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 15rpx;
}

.tip-text {
  font-size: 24rpx;
  color: #ff9800;
  flex: 1;
}
