# 程序反馈功能说明

## 1. 功能概述

程序反馈功能允许用户提交使用过程中的问题、建议和意见，帮助开发团队改进产品。用户可以通过文字描述和图片上传的方式提交反馈。

## 2. 功能特性

### 2.1 反馈提交
- **文字反馈**：支持最多1000字符的文字描述
- **联系方式**：可选填写手机号或微信号
- **图片上传**：支持最多6张图片上传，参考发布页面布局
- **实时预览**：图片上传后可预览和删除
- **字数统计**：实时显示已输入字符数

### 2.2 反馈类型
- **APP_FEEDBACK**：程序反馈（默认）
- **BUG_REPORT**：问题报告
- **FEATURE_REQUEST**：功能建议

### 2.3 设备信息收集
- 小程序版本
- 系统版本
- 设备信息

## 3. 页面结构

### 3.1 文件结构
```
weapp/pages/mine/feedback/
├── feedback.js      # 页面逻辑
├── feedback.wxml    # 页面结构
├── feedback.wxss    # 页面样式
└── feedback.json    # 页面配置
```

### 3.2 组件结构
- **页面标题**：显示"程序反馈"标题和副标题
- **反馈内容**：文本输入区域，支持字数限制
- **图片上传**：图片选择、预览、删除功能
- **提交按钮**：提交反馈的主要操作
- **提示信息**：使用说明和注意事项

## 4. 技术实现

### 4.1 数据存储
- 使用 `stores/feedbackStore.js` 管理反馈相关数据请求
- 遵循小程序开发规范，将数据请求逻辑与页面逻辑分离

### 4.2 图片处理
- 使用 `wx.chooseMedia` API 选择图片
- 支持相册和相机两种选择方式
- 图片上传前进行大小和格式验证

### 4.3 表单验证
- 内容不能为空
- 内容长度不超过1000字符
- 图片数量不超过6张

## 5. API接口

### 5.1 提交反馈
```
POST /blade-chat/feedback/painpoint/save
Content-Type: application/json
X-Open-ID: {openId}

{
    "content": "反馈内容",
    "image": "图片路径或链接",
    "contactInfo": "联系方式"
}
```

### 5.2 获取反馈历史
```
GET /miniapp/feedback/history?page=1&limit=10
X-Open-ID: {openId}
```

### 5.3 获取反馈详情
```
GET /miniapp/feedback/detail/{id}
X-Open-ID: {openId}
```

## 6. 用户体验

### 6.1 界面设计
- 采用现代化的卡片式设计
- 使用渐变色按钮提升视觉效果
- 响应式布局适配不同屏幕尺寸

### 6.2 交互体验
- 实时字数统计
- 图片预览和删除
- 提交状态反馈
- 错误提示和成功提示

### 6.3 性能优化
- 图片压缩上传
- 防重复提交
- 网络异常处理

## 7. 后台管理

### 7.1 反馈列表
- 分页显示所有反馈
- 支持按状态、类型、优先级筛选
- 支持关键词搜索

### 7.2 反馈处理
- 状态更新（待处理、处理中、已解决、已关闭）
- 添加处理备注
- 设置优先级

### 7.3 数据统计
- 反馈数量统计
- 处理效率分析
- 用户满意度评估

## 8. 安全考虑

### 8.1 数据安全
- 用户身份验证
- 数据加密传输
- 敏感信息脱敏

### 8.2 内容安全
- 内容过滤和审核
- 图片安全检查
- 防止恶意提交

## 9. 部署说明

### 9.1 数据库
- 执行 `feedback_table.sql` 创建反馈表
- 确保数据库连接配置正确

### 9.2 后端服务
- 确保反馈相关接口正常启动
- 配置文件上传服务
- 设置跨域访问权限

### 9.3 小程序
- 更新小程序版本
- 配置服务器域名
- 测试反馈功能

## 10. 测试建议

### 10.1 功能测试
- 反馈提交功能
- 图片上传功能
- 表单验证功能
- 错误处理功能

### 10.2 性能测试
- 大量图片上传测试
- 并发提交测试
- 网络异常测试

### 10.3 兼容性测试
- 不同设备测试
- 不同系统版本测试
- 不同网络环境测试

## 11. 维护说明

### 11.1 日常维护
- 监控反馈提交情况
- 及时处理用户反馈
- 定期清理无效数据

### 11.2 功能优化
- 根据用户反馈优化界面
- 增加更多反馈类型
- 优化图片上传体验

### 11.3 数据分析
- 分析反馈热点问题
- 评估功能改进效果
- 制定产品优化计划

---

**文档版本**: v1.0  
**更新时间**: 2025-03-10  
**维护人员**: 开发团队 