const UploadHelper = require('../../../../utils/upload');

const FileValidator = require('../../../../utils/fileValidator');

Component({
  properties: {
    album: {
      type: Array,
      value: []
    }
  },
  methods: {
    onChooseAlbum() {
      const remain = 4 - this.data.album.length;
      if (remain <= 0) return;
      wx.chooseMedia({
        count: remain,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          if (res.tempFiles && res.tempFiles.length > 0) {
            const valid = [];
            const invalid = [];
            res.tempFiles.forEach(file => {
              const validation = FileValidator.validateImage(file.tempFilePath, file.size);
              if (validation.isValid) {
                valid.push(file.tempFilePath);
              } else {
                invalid.push(validation.errors[0]);
              }
            });
            if (invalid.length > 0) {
              wx.showToast({ title: invalid[0], icon: 'none' });
            }
            if (valid.length > 0) {
              const newList = this.data.album.concat(valid);
              this.triggerEvent('change', { album: newList });
            }
          }
        }
      });
    },
    onDeleteAlbum(e) {
      const idx = e.currentTarget.dataset.index;
      const newList = this.data.album.slice();
      newList.splice(idx, 1);
      this.triggerEvent('change', { album: newList });
    }
  }
}); 