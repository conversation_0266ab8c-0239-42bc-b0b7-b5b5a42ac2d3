/* chat.wxss */
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f6f7;
  position: relative;
}

/* 聊天列表区域 */
.chat-list {
  flex: 1;
  padding: 24rpx;
  padding-bottom: 180rpx;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  box-sizing: border-box;
}

.chat-list-content {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

/* 消息样式 */
.message {
  display: flex;
  flex-direction: column;
  max-width: 80%;
  margin-bottom: 24rpx;
}

.message-user {
  align-self: flex-end;
  padding-right: 16rpx;
}

.message-ai {
  align-self: flex-start;
}

.message-header {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 8rpx;
}

.avatar-container {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.avatar {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background: #e0e0e0;
}

.avatar-name {
  font-size: 24rpx;
  color: #999;
}

.message-content {
  padding: 20rpx 24rpx;
  border-radius: 20rpx;
  font-size: 28rpx;
  line-height: 1.5;
  position: relative;
  word-break: break-all;
}

.message-user .message-content {
  background: #4C6FFF;
  color: #fff;
  border-top-right-radius: 4rpx;
}

.message-ai .message-content {
  background: #fff;
  color: #333;
  border-top-left-radius: 4rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

/* 输入区域样式 */
.input-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 24rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  background: #f5f6f7;
  z-index: 100;
}

.input-box {
  display: flex;
  align-items: center;
  background: #fff;
  padding: 20rpx 24rpx;
  border-radius: 40rpx;
  gap: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.voice-icon {
  width: 40rpx;
  height: 40rpx;
  opacity: 0.7;
}

.voice-icon.active {
  opacity: 1;
}

.chat-input {
  flex: 1;
  font-size: 28rpx;
  height: 40rpx;
  min-height: 40rpx;
}

.voice-button {
  flex: 1;
  height: 40rpx;
  line-height: 40rpx;
  text-align: center;
  font-size: 28rpx;
  color: #999;
  transition: all 0.2s;
}

.voice-button.recording {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 8rpx;
}

.right-tools {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.plus-icon,
.send-icon {
  width: 40rpx;
  height: 40rpx;
  opacity: 0.7;
}

.plus-icon:active,
.send-icon:active {
  opacity: 1;
}

/* 顶部导航栏 */
.nav-bar {
  padding: 24rpx;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.nav-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.nav-icon {
  width: 40rpx;
  height: 40rpx;
  opacity: 0.7;
} 