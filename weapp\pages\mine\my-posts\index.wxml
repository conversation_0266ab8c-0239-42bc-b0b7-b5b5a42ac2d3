<layout
  title="我的帖子"
  showSearch="{{false}}"
  showLocation="{{false}}"
  background="linear-gradient(to bottom, #ff6b6b, #ff8585)"
  showBack="{{true}}"
/>
<!--我的帖子页面-->
<view class="my-posts-page">
  <!-- 搜索栏 -->
  <view class="search-container">
    <view class="search-box">
      <image class="search-icon" src="/assets/images/common/search.png" mode="aspectFit"/>
      <input
        class="search-input"
        placeholder="搜索我的帖子内容..."
        placeholder-class="input-placeholder"
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        bindconfirm="onSearch"
        focus="{{searchFocus}}"
      />
      <view wx:if="{{searchKeyword}}" class="clear-btn" bindtap="onClearSearch">
        <text class="clear-icon">×</text>
      </view>
    </view>
  </view>

  <!-- 标签筛选 -->
  <view class="filter-container">
    <scroll-view class="filter-scroll" scroll-x="true">
      <view class="filter-item {{tab === 'all' ? 'active' : ''}}" bindtap="onTabChange" data-tab="all">
        全部
      </view>
      <view class="filter-item {{tab === 'published' ? 'active' : ''}}" bindtap="onTabChange" data-tab="published">
        已发布
      </view>
      <view class="filter-item {{tab === 'draft' ? 'active' : ''}}" bindtap="onTabChange" data-tab="draft">
        草稿箱
      </view>
      <view class="filter-item {{tab === 'completed' ? 'active' : ''}}" bindtap="onTabChange" data-tab="completed">
        已完成
      </view>
    </scroll-view>
  </view>
  <!-- 帖子列表 -->
  <scroll-view
    class="posts-scroll"
    scroll-y="true"
    bindscrolltolower="onReachBottom"
    refresher-enabled="true"
    bindrefresherrefresh="onRefresh"
    refresher-triggered="{{refreshing}}"
  >
    <!-- 加载状态 -->
    <view wx:if="{{loading && posts.length === 0}}" class="loading-container">
      <view class="loading-content">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>
    </view>

    <!-- 帖子列表 -->
    <view wx:elif="{{posts.length > 0}}" class="posts-list">
      <block wx:for="{{posts}}" wx:key="id">
        <post-item post="{{item}}" bind:edit="onEditPost" bind:delete="onDeletePost" bind:moveDraft="onMoveDraft" bind:completed="onCompletedPost" bind:more="onMore" />
      </block>

      <!-- 加载更多 -->
      <view wx:if="{{loadingMore}}" class="loading-more">
        <view class="loading-spinner small"></view>
        <text class="loading-text">加载更多...</text>
      </view>

      <!-- 没有更多 -->
      <view wx:if="{{noMore && posts.length > 0}}" class="no-more">
        <text class="no-more-text">没有更多了</text>
      </view>
    </view>

    <!-- 空状态 -->
    <view wx:else class="empty-container">
      <view class="empty-content">
        <image class="empty-image" src="/assets/images/common/empty-posts.png" mode="aspectFit"/>
        <text class="empty-title">暂无帖子</text>
        <text class="empty-desc">{{getEmptyDesc(tab)}}</text>
        <view class="empty-action" bindtap="onGoPublish">
          <text class="action-text">去发布</text>
        </view>
      </view>
    </view>
  </scroll-view>
  <!-- 遮罩层 -->
  <view class="action-mask" wx:if="{{showActionMenu}}" bindtap="hideActionMenu"></view>

  <!-- 底部弹出菜单 -->
  <view class="action-sheet {{showActionMenu ? 'show' : ''}}">
    <view class="action-sheet-content">
      <view class="action-item" bindtap="onEditPostMenu">
        <image class="action-icon" src="/assets/images/common/edit.png" mode="aspectFit"/>
        <text class="action-text">编辑</text>
      </view>
      <view class="action-item" bindtap="onMoveDraftMenu">
        <image class="action-icon" src="/assets/images/common/draft.png" mode="aspectFit"/>
        <text class="action-text">移动草稿箱</text>
      </view>
      <view class="action-item" bindtap="onCompletedPostMenu">
        <image class="action-icon" src="/assets/images/common/complete.png" mode="aspectFit"/>
        <text class="action-text">标记已完成</text>
      </view>
      <view class="action-item delete" bindtap="onDeletePostMenu">
        <image class="action-icon" src="/assets/images/common/delete.png" mode="aspectFit"/>
        <text class="action-text">删除</text>
      </view>
    </view>
    <view class="action-cancel" bindtap="hideActionMenu">
      <text class="cancel-text">取消</text>
    </view>
  </view>
</view>