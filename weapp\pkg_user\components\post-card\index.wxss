/* 动态内容样式 */
.post-item {
  background: #fff;
  margin: 0 24rpx 24rpx;
  padding: 26rpx;
  border-radius: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.03);
}
.post-content {
  font-size: 30rpx;
  color: #222;
  margin-bottom: 16rpx;
}
.post-img-list {
  display: flex;
  gap: 16rpx;
  margin-bottom: 16rpx;
}
.p-img {
  /* object-fit: cover; */
  width: 200rpx;
  height: 200rpx;
}

/* 底部信息栏 */
.post-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.post-time {
  font-size: 24rpx;
  color: #888;
}
.post-actions {
  display: flex;
  align-items: center;
}
.action-item {
  display: flex;
  align-items: center;
  margin-left: 28rpx;
}
.action-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}
.action-count {
  font-size: 24rpx;
  color: #888;
}