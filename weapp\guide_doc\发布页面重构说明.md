# 发布页面重构说明

## 重构概述

根据小程序开发规范，对发布页面的JS代码进行了全面重构，实现了**关注点分离**和**高内聚低耦合**的架构设计。

## 重构原则

### 1. 关注点分离
- **页面JS**：只负责视图交互和生命周期管理
- **Store层**：负责数据请求和业务逻辑
- **工具函数**：负责数据处理和格式化

### 2. 高内聚低耦合
- 每个模块职责单一明确
- 模块间依赖关系简单清晰
- 便于维护和扩展

## 重构内容

### 1. 创建 Store 层 (`stores/publishStore.js`)

**职责**：所有与后端接口的数据交互

**包含方法**：
- `createPost()` - 创建帖子
- `saveDraft()` - 保存草稿
- `loadDraft()` - 加载草稿
- `deleteDraft()` - 删除草稿
- `getDraftCount()` - 获取草稿数量
- `loadCategories()` - 加载分类列表
- `uploadImages()` - 上传图片
- `deleteCloudFile()` - 删除云存储文件

**优势**：
- 接口调用逻辑集中管理
- 便于接口变更和维护
- 支持多页面复用

### 2. 创建工具函数层 (`utils/publishHelper.js`)

**职责**：数据处理、格式化和验证

**包含方法**：
- `buildPostData()` - 构建发布数据
- `buildDraftData()` - 构建草稿数据
- `validateForm()` - 表单验证
- `processImages()` - 处理图片数据
- `formatTags()` - 格式化标签
- `getDeviceInfo()` - 获取设备信息
- `generateId()` - 生成唯一ID
- `formatFileSize()` - 格式化文件大小
- `checkNetworkStatus()` - 检查网络状态

**优势**：
- 数据处理逻辑复用
- 验证规则统一管理
- 便于测试和维护

### 3. 重构页面JS (`pages/publish/publish.js`)

**职责**：视图交互和生命周期管理

**重构内容**：

#### 3.1 数据初始化
```javascript
// 重构前：直接在onLoad中处理
onLoad: function(options) {
  this.updateDraftCount();
  this.loadCategories();
  // ... 其他初始化逻辑
}

// 重构后：抽离到独立方法
onLoad: function(options) {
  this.initializePage(options);
}

async initializePage(options) {
  try {
    await this.loadCategories();
    if (options.draftId) {
      await this.loadDraft(options.draftId);
    }
    this.updateDraftCount();
  } catch (error) {
    console.error('页面初始化失败:', error);
  }
}
```

#### 3.2 方法分组
按功能将方法分为多个模块：
- **表单输入处理**：`onTitleInput`、`onDescriptionInput`等
- **图片处理**：`chooseImage`、`deleteImage`、`previewImage`等
- **位置处理**：`chooseLocation`
- **标签处理**：`onTagTap`、`addCustomTag`、`deleteTag`等
- **发布处理**：`publishPost`、`confirmPublish`、`preparePostData`等
- **草稿处理**：`saveDraft`、`loadDraft`、`updateDraftCount`等
- **分类处理**：`loadCategories`、`onCategorySelect`等

#### 3.3 错误处理统一化
```javascript
// 重构前：每个方法都有独立的错误处理
async loadCategories() {
  try {
    const res = await request({...});
    if (res.code === 200) {
      this.setData({categories: res.data.records});
    } else {
      wx.showToast({title: res.msg, icon: 'none'});
    }
  } catch (error) {
    console.error('加载分类失败:', error);
    wx.showToast({title: '加载分类失败', icon: 'none'});
  }
}

// 重构后：错误处理统一在Store层
async loadCategories() {
  try {
    const result = await PublishStore.loadCategories();
    if (result.success) {
      this.setData({categories: result.data});
    } else {
      wx.showToast({title: result.message, icon: 'none'});
    }
  } catch (error) {
    console.error('加载分类失败:', error);
    wx.showToast({title: '加载分类失败', icon: 'none'});
  }
}
```

## 重构效果

### 1. 代码结构更清晰
- 页面JS文件从500+行减少到300+行
- 方法按功能分组，便于查找和维护
- 注释完善，代码可读性提升

### 2. 职责分离明确
- 页面只负责UI交互
- Store负责数据请求
- 工具函数负责数据处理

### 3. 可维护性提升
- 接口变更只需修改Store层
- 数据处理逻辑可复用
- 便于单元测试

### 4. 扩展性增强
- 新增功能只需在对应层添加
- 支持多页面复用Store方法
- 便于后续功能扩展

## 使用示例

### 1. 发布帖子
```javascript
// 页面层：只负责交互
async publishPost() {
  const validation = PublishHelper.validateForm(this.data);
  if (!validation.isValid) {
    wx.showToast({title: validation.errors[0], icon: 'none'});
    return;
  }
  
  const postData = await this.preparePostData();
  const result = await PublishStore.createPost(postData);
  // 处理结果...
}

// Store层：负责接口调用
static async createPost(postData) {
  const res = await request({
    url: '/blade-chat/post/create',
    method: 'POST',
    data: postData
  });
  return this.formatResponse(res);
}

// 工具层：负责数据准备
static buildPostData(formData) {
  return {
    title: formData.title,
    description: formData.description,
    // ... 其他字段
  };
}
```

### 2. 保存草稿
```javascript
// 页面层
async saveDraft() {
  const draftData = PublishHelper.buildDraftData(this.data);
  const result = await PublishStore.saveDraft(draftData, this.data.draftId);
  // 处理结果...
}
```

## 后续优化建议

### 1. 状态管理
- 考虑引入全局状态管理
- 统一管理用户信息、草稿状态等

### 2. 缓存优化
- 分类列表缓存
- 草稿自动保存
- 图片压缩优化

### 3. 错误处理
- 统一错误处理机制
- 网络异常重试机制
- 用户友好的错误提示

### 4. 性能优化
- 图片懒加载
- 表单防抖处理
- 内存泄漏防护

## 总结

通过本次重构，发布页面的代码结构更加规范，符合小程序开发规范的要求。实现了关注点分离，提高了代码的可维护性和可扩展性，为后续功能开发和维护奠定了良好的基础。 