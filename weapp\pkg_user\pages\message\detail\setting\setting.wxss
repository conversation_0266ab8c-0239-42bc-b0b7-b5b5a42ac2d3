/* pages/follow/detail/setting/setting.wxss */
.notice-list {
  padding: 16rpx 0 0 0;
  background: #f7f6f6;
  min-height: 100vh;
}

.notice-card {
  background: #fff;
  border-radius: 16rpx;
  margin: 24rpx 24rpx 0 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.04);
  padding: 32rpx 28rpx 24rpx 28rpx;
}

.notice-header {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #bdbdbd;
  margin-bottom: 12rpx;
}

.notice-type {
  font-weight: 500;
}

.notice-date {
  margin-left: 24rpx;
  font-size: 22rpx;
}

.notice-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #222;
  margin-bottom: 8rpx;
}

.notice-content {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 24rpx;
}

.notice-action {
  display: flex;
  align-items: center;
  color: #ff8383;
  font-size: 28rpx;
  font-weight: 500;
  margin-top: 8rpx;
  cursor: pointer;
}

.notice-action-text {
  color: #ff8383;
}

.notice-action-arrow {
  margin-left: 8rpx;
  color: #ff8383;
  font-size: 32rpx;
}