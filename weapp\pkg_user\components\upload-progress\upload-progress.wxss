.upload-progress {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  border-radius: 12rpx;
  padding: 40rpx;
  z-index: 9999;
  min-width: 400rpx;
}

.progress-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4ECDC4, #44A08D);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-text {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  color: #fff;
  font-size: 28rpx;
}

.status-text {
  flex: 1;
  text-align: left;
}

.progress-percent {
  text-align: right;
  font-weight: bold;
}

.status-icon {
  text-align: center;
  margin-top: 20rpx;
}

.status-icon .iconfont {
  font-size: 48rpx;
  color: #4ECDC4;
}

.status-icon.error .iconfont {
  color: #ff4757;
} 