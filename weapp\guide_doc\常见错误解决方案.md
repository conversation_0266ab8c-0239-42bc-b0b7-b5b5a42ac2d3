# 小程序开发常见错误解决方案

## 1. 图片资源在 WXSS 中无法访问错误

### 错误信息
```
[渲染层网络层错误] pages/index/components/content-card/index.wxss 中的本地资源图片无法通过 WXSS 获取，可以使用网络图片，或者 base64，或者使用<image/>标签。
```

### 错误原因
微信小程序的 WXSS 文件中无法直接通过 `background-image: url()` 引用本地图片资源。

### 错误代码示例
```css
/* ❌ 错误代码 */
.post-time::before {
  content: '';
  display: inline-block;
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
  background-image: url('/assets/images/detail/clock-icon.png'); /* 错误！无法访问本地资源 */
  background-size: contain;
}
```

### 解决方案

#### 方案一：使用 `<image>` 标签（推荐）
```wxml
<!-- ✅ 在 WXML 中使用 image 标签 -->
<view class="post-time">
  <image class="icon clock" src="/assets/images/detail/clock-icon.png" />
  <text>{{post.time}}</text>
</view>
```

```css
/* ✅ 对应的 WXSS 样式 */
.post-time {
  font-size: 26rpx;
  color: #999;
  display: flex;
  align-items: center;
}

.icon.clock {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
  vertical-align: middle;
}
```

#### 方案二：使用网络图片
```css
.post-time::before {
  content: '';
  display: inline-block;
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
  background-image: url('https://your-domain.com/images/clock-icon.png');
  background-size: contain;
}
```

#### 方案三：使用 Base64 编码
```css
.post-time::before {
  content: '';
  display: inline-block;
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...');
  background-size: contain;
}
```

### 最佳实践建议
1. **优先使用 `<image>` 标签**：这是最符合小程序规范的方式
2. **避免在 WXSS 中使用本地图片**：减少错误发生的可能性
3. **统一图标管理**：将图标统一放在 `assets/images/` 目录下
4. **使用语义化类名**：如 `.icon.clock` 而不是 `.icon-1`

## 2. 图片路径错误

### 错误信息
```
Failed to load image: /assets/images/xxx.png
```

### 解决方案
1. 检查图片路径是否正确
2. 确保图片文件存在
3. 使用相对路径而非绝对路径
4. 检查文件名大小写

## 3. 图片加载失败处理

### 添加错误处理
```wxml
<image 
  src="/assets/images/detail/clock-icon.png" 
  binderror="handleImageError"
  mode="aspectFill"
/>
```

```javascript
handleImageError(e) {
  console.log('图片加载失败:', e.detail);
  // 设置默认图片
  this.setData({
    imageUrl: '/assets/images/default.png'
  });
}
```

## 4. 性能优化建议

### 图片优化
- 压缩图片大小
- 使用合适的图片格式（PNG用于图标，JPG用于照片）
- 懒加载大图片
- 预加载关键图片

### 代码优化
- 避免在循环中使用复杂计算
- 合理使用 `setData`
- 及时清理定时器和事件监听

---

## 相关文档
- [微信小程序官方文档](https://developers.weixin.qq.com/miniprogram/dev/framework/)
- [小程序图片资源使用说明](https://developers.weixin.qq.com/miniprogram/dev/qa.html#%E6%9C%AC%E5%9C%B0%E8%B5%84%E6%BA%90%E6%97%A0%E6%B3%95%E9%80%9A%E8%BF%87-wxss-%E8%8E%B7%E5%8F%96)
- [小程序开发规范](./小程序开发规范.md) 