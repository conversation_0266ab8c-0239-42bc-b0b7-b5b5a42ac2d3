const { getCategoryList } = require('../../../stores/categoryStore.js');

Page({
  data: {
    categoryList: [],
    selectedCategory: null
  },
  onLoad() {
    this.loadCategoryList();
  },
  async loadCategoryList() {
    const list = await getCategoryList();
    this.setData({ categoryList: list });
  },
  onCategorySelect(e) {
    const category = e.currentTarget.dataset.category;
    this.setData({ selectedCategory: category });
    wx.navigateTo({
      url: `/pages/publish/publish?category=${encodeURIComponent(category.name)}&categoryId=${category.id}`
    });
  }
}); 