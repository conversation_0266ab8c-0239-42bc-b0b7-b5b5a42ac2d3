<!-- 使用layout组件 -->
<custom-nav
  id="custom-nav"
  background="linear-gradient(to bottom, #ff6b6b, #ff8585)"
  text-color="#ffffff"
  show-location="{{true}}"
  show-back="{{false}}"
  show-search="{{true}}"
  search-type="1"
  bind:navReady="onNavReady"
  bind:showDrawer="onShowDrawer"
  bind:regionpickershow="onRegionPickerShow"
  bind:searchTap="onSearchTap">
</custom-nav>

<!-- 分类标签吸顶 -->
<view wx:if="{{showStickyCategory}}" class="sticky-category-bar" style="top: {{navBarHeight}}px;">
  <scroll-view scroll-x class="category-tabs-scroll" show-scrollbar="false">
    <view class="category-tabs {{categories.length <= 1 ? 'single-tab' : ''}}">
      <view
        class="tab-item {{selectedCategory === item.id ? 'active' : ''}}"
        wx:for="{{categories}}"
        wx:key="id"
        data-index="{{index}}"
        bindtap="switchTab">
        {{item.name}}
      </view>
    </view>
  </scroll-view>
</view>

<!-- 下拉刷新金币动画 -->
<view wx:if="{{isRefreshing}}" class="refresh-coins-fixed" style="padding-top: {{navBarHeight}}px;">
  <block wx:for="{{coins}}" wx:key="index">
    <view
      class="coin"
      style="left: {{item.left}}vw; font-size: {{item.size}}rpx; animation-delay: {{item.delay}}s; --coin-rotate: {{item.rotate}}deg; --coin-swing: {{item.swing}}vw;"
    >🪙</view>
  </block>
</view>

<scroll-view
  scroll-y
  refresher-enabled="true"
  refresher-default-style="none"
  refresher-background="#FF7B7B"
  bindscroll="onScroll"
  bindrefresherrefresh="onPullDownRefresh"
  refresher-triggered="{{isRefreshing}}"
  class="scroll-container main-scroll-view"
  style="margin-top: {{navBarHeight}}px; height: calc(100vh - {{navBarHeight}}px);"
  scroll-into-view="{{scrollIntoView}}"
  enable-back-to-top="{{!reachedBottom}}"
  scroll-with-animation="{{true}}"
>
  <view class="main-content">
    <!-- 托盘和加载文字放在内容区顶部 -->
    <view wx:if="{{isRefreshing}}" class="refresh-bottom-area">
      <view class="coin-tray"></view>
      <text class="refresh-text">事事有着落，件件有回音...</text>
    </view>

    <!-- 轮播图区域 - 延伸到导航栏下方 -->
    <view class="banner-container" style="margin-top: -{{navBarHeight}}px; padding-top: {{navBarHeight}}px;">
      <banner-swiper
        list="{{banners}}"
        height="{{280}}"
        bind:bannertap="onBannerTap"
      />
    </view>

    <!-- 你的内容区域 -->
    <view class="card-section" id="category-section">
      <card>
        <!-- 主Tab区域：最新/附近 + 申请入驻 -->
        <view class="main-tab-section">
          <view class="main-tabs">
            <view
              wx:for="{{['最新', '附近']}}"
              wx:key="index"
              class="main-tab-item {{currentTab === index ? 'active' : ''}}"
              data-index="{{index}}"
              bindtap="switchMainTab"
            >
              {{item}}
            </view>
          </view>
          <view class="header-actions">
            <view class="action-btn apply-settle-btn" bindtap="onApplySettle">
              申请入驻
            </view>
          </view>
        </view>
        <!-- 分类筛选 -->
        <scroll-view scroll-x class="category-tabs-scroll" show-scrollbar="false" id="category-tabs-scroll">
          <view class="category-tabs {{categories.length <= 1 ? 'single-tab' : ''}}">
            <view
              class="tab-item {{selectedCategory === item.id ? 'active' : ''}}"
              wx:for="{{categories}}"
              wx:key="id"
              data-index="{{index}}"
              bindtap="switchTab">
              {{item.name}}
            </view>
          </view>
        </scroll-view>
        <view class="institutions-container">
          <!-- 机构列表 -->
          <block wx:for="{{institutionList}}" wx:key="id">
            <view class="institution-card" bindtap="onInstitutionTap" data-id="{{item.id}}">
              <!-- 左侧图片（优先 images[0]，否则 logo，没有则不显示） -->
              <image wx:if="{{item.images && item.images.length > 0}}" class="institution-img" src="{{item.images[0]}}" mode="aspectFill" />
              <image wx:elif="{{item.logo}}" class="institution-img" src="{{item.logo}}" mode="aspectFill" />
              <!-- 无图片不显示 image 标签 -->
              <!-- 右侧信息区 -->
              <view class="institution-info">
                <view class="top-row">
                  <text class="institution-title">{{item.name}}</text>
                  <text wx:if="{{isNew(item.createTime)}}" class="tag-new">新店</text>
                  <text wx:if="{{item.institutionStats && item.institutionStats.favoriteCount > 10}}" class="tag-hot">人气榜</text>
                </view>
                <view class="meta-row">
                  <text class="category">{{item.typeName}}</text>
                </view>
                <view class="desc-row">
                  <text class="desc">{{item.description}}</text>
                </view>
                <view class="feature-row" wx:if="{{item.specialServices}}">
                  <block wx:for="{{item.specialServices.split(',')}}" wx:key="index">
                    <text class="feature-tag">{{item}}</text>
                  </block>
                </view>
                <view class="bottom-row">
                  <text class="stat">浏览 {{item.institutionStats && item.institutionStats.viewCount || 0}}</text>
                  <text class="stat">点赞 {{item.institutionStats && item.institutionStats.likeCount || 0}}</text>
                  <text class="stat">收藏 {{item.institutionStats && item.institutionStats.favoriteCount || 0}}</text>
                  <text class="distance">{{item.distanceStr}}</text>
                </view>
                <view class="address-row">
                  <text class="address">{{item.province}}{{item.city}}{{item.district}}{{item.detailAddress}}</text>
                </view>
              </view>
            </view>
          </block>
          <!-- 底部状态区域 -->
          <view class="bottom-status">
            <!-- 空数据状态 -->
            <view wx:if="{{!loading && institutionList.length === 0}}" class="empty-state">
              <text class="empty-icon">🏢</text>
              <text class="empty-text">暂无机构信息</text>
              <text class="empty-tip">申请入驻，让更多人找到您的店铺</text>
            </view>

            <!-- 加载中状态 -->
            <view wx:elif="{{loading}}" class="loading-more">
              <text>{{institutionList.length === 0 ? '加载中...' : '加载更多...'}}</text>
            </view>

            <!-- 没有更多数据状态 -->
            <view wx:elif="{{!hasMore && institutionList.length > 0}}" class="no-more">
              <text>🏢 已显示全部机构</text>
            </view>
          </view>
        </view>
      </card>
    </view>
  </view>
</scroll-view>
<custom-tabbar id="customTabbar" show="{{showTabbar}}" />
