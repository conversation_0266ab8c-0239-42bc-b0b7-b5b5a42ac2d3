// 支付配置文件
module.exports = {
  // 充值配置
  recharge: {
    // 预设充值金额选项
    suggestedAmounts: [10, 50, 100, 200, 500],

    // 最小充值金额
    minAmount: 1,

    // 最大充值金额
    maxAmount: 10000
  },
  
  // 支付方式配置
  paymentMethods: {
    wechat: {
      name: '微信支付',
      icon: '/images/icons/wechat-pay.png',
      enabled: true
    }
  },

  // 验证充值金额
  validateAmount(amount) {
    const numAmount = parseFloat(amount);

    if (isNaN(numAmount) || numAmount <= 0) {
      return {
        valid: false,
        message: '请输入有效的充值金额'
      };
    }

    if (numAmount < this.recharge.minAmount) {
      return {
        valid: false,
        message: `最小充值金额为${this.recharge.minAmount}元`
      };
    }

    if (numAmount > this.recharge.maxAmount) {
      return {
        valid: false,
        message: `最大充值金额为${this.recharge.maxAmount}元`
      };
    }

    return {
      valid: true,
      message: '金额有效'
    };
  },
  
  // 格式化金额显示
  formatAmount(amount) {
    const numAmount = parseFloat(amount);
    if (isNaN(numAmount)) return '0.00';
    return numAmount.toFixed(2);
  },

  // 生成订单描述
  generateOrderDescription(amount) {
    const formattedAmount = this.formatAmount(amount);
    return `钱包充值 ${formattedAmount}元`;
  }
};
