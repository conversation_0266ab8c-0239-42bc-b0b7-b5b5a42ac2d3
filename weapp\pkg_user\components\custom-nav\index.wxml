<!-- 自定义导航栏 -->
<view class="custom-nav {{fixed ? 'nav-fixed' : ''}} {{isScrolled ? 'scrolled' : ''}}" style="height: {{navBarHeight}}px; background: {{currentBackground}}">
  <view style="padding-top: {{statusBarHeight}}px; height: {{navBarHeight - statusBarHeight}}px;">
    <view class="nav-content" style="height: {{navBarHeight - statusBarHeight}}px; position: relative;">
      <!-- 返回按钮 -->
      <view wx:if="{{showBack}}" 
            class="back-btn"
            bindtap="onBack">
        <image class="back-icon" src="/assets/images/common/back-black.png" mode="aspectFit"></image>
      </view>

      <!-- 地址选择 -->
      <view wx:if="{{showLocation}}"
            class="location"
            style="color: {{currentTextColor}};"
            bindtap="onLocationSelect"
            >
        <text>{{currentLocationName || currentLocation || '定位中...'}}</text>
        <image class="location-arrow" src="/assets/images/common/arrow-down-white.png" mode="aspectFit"></image>
      </view>

      <!-- 绝对居中标题 -->
      <block wx:if="{{title && !showSearch}}">
        <view class="nav-title-abs" style="color: {{currentTextColor}};">
          {{title}}
        </view>
      </block>

      <!-- 搜索框 -->
      <view wx:if="{{showSearch}}" class="search-box" style="margin-right: {{menuButtonInfo.width + (menuButtonInfo.right - screenWidth) * 2 + 20}}px;" bindtap="onSearch">
        <text class="search-placeholder" style="color: {{currentTextColor}}">搜索</text>
        <image class="search-icon" src="/assets/images/home/<USER>"></image>
      </view>

      <!-- 占位符 -->
      <view class="nav-placeholder" style="height: {{navBarHeight}}px;width: {{menuButtonInfo.width}}px;"></view> 
    </view>
  </view>
</view>

<!-- 地区选择器 -->
<region-picker
  show="{{showRegionPicker}}"
  max-level="{{3}}"
  bind:confirm="onRegionConfirm"
  bind:close="onRegionClose"
  bind:showchange="onRegionShowChange">
</region-picker>

<!-- 位置选择弹窗（兼容旧版本） -->
<view class="location-modal" wx:if="{{showLocationModal}}">
  <view class="modal-mask" bindtap="hideLocationModal"></view>
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">选择发布位置</text>
      <text class="modal-close" bindtap="hideLocationModal">×</text>
    </view>

    <view class="modal-body">
      <view class="location-list">
        <view
          class="location-item {{currentLocation === item ? 'selected' : ''}}"
          wx:for="{{locationList}}"
          wx:key="*this"
          bindtap="selectLocation"
          data-location="{{item}}"
        >
          {{item}}
          <image wx:if="{{currentLocation === item}}" class="check-icon" src="/assets/images/common/check.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>
  </view>
</view>
