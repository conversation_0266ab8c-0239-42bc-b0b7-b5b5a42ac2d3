<view class="favorite-tags-card">
  <view class="favorite-tags-header">
    <text class="favorite-tags-title">收藏夹</text>
    <text class="favorite-tags-more" bindtap="onMore">查看全部</text>
  </view>
  <!-- 有收藏标签但没有帖子时显示标签 -->
  <view wx:if="{{tags && tags.length > 0}}" class="favorite-tags-list">
    <block wx:for="{{tags}}" wx:key="text">
      <text class="favorite-tag {{item.selected ? 'selected' : ''}}" bindtap="onTagTap" data-index="{{index}}">{{item.text}}</text>
    </block>
  </view>
  <!-- 有收藏帖子时显示帖子列表 -->
  <block wx:if="{{posts && posts.length > 0}}">
    <view class="favorite-post-item" wx:for="{{posts}}" wx:key="id" bindtap="onPostTap" data-id="{{item.id}}">
      <!-- 左侧图片区域 -->
      <view class="post-image-container">
        <image
          wx:if="{{item.images && item.images.length > 0}}"
          class="post-image"
          src="{{item.images[0]}}"
          mode="aspectFill"
        ></image>
        <view wx:else class="post-image-placeholder">
          <text class="placeholder-text">{{item.category && item.category.name ? item.category.name.substring(0, 2) : 'PF'}}</text>
        </view>
      </view>

      <!-- 右侧内容区域 -->
      <view class="post-content">
        <view class="post-title">{{item.category && item.category.name ? item.category.name : ''}}</view>
        <view class="post-desc">{{item.content}}</view>
        <view class="post-meta">
          <view class="meta-item">
            <image class="meta-icon" src="/assets/images/common/view.png" mode="aspectFit"></image>
            <text>{{item.stats && item.stats.viewCount ? item.stats.viewCount : 0}}</text>
          </view>
          <view class="meta-item">
            <image class="meta-icon" src="/assets/images/common/like.png" mode="aspectFit"></image>
            <text>{{item.stats && item.stats.likeCount ? item.stats.likeCount : 0}}</text>
          </view>
        </view>
      </view>
    </view>
  </block>



  <!-- 空状态显示 -->
  <view wx:else class="favorite-tags-empty">
    <image class="empty-icon" src="/assets/images/mine/collections.png" mode="aspectFit"></image>
    <text class="empty-text">暂无收藏内容</text>
    <text class="empty-desc">浏览时点击收藏，内容会出现在这里</text>
  </view>
</view>