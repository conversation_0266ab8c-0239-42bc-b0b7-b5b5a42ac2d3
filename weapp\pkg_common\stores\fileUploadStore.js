const { request } = require('../utils/request');
const config = require('../config/api');

/**
 * 文件上传相关的数据请求和业务逻辑
 * 遵循小程序开发规范，将所有文件上传相关的接口调用封装在此store中
 */
class FileUploadStore {
  /**
   * 上传单个文件
   * @param {string} filePath 文件路径
   * @param {string} uploadSource 上传来源
   * @param {string} businessType 业务类型
   * @param {string|number} businessId 业务ID
   * @returns {Promise} 上传结果
   */
  static async uploadFile(filePath, uploadSource = 'miniapp', businessType = null, businessId = null) {
    return new Promise((resolve) => {
      wx.uploadFile({
        url: `${config.baseUrl}${config.fileUpload.upload}`,
        filePath: filePath,
        name: 'file',
        formData: {
          uploadSource: uploadSource,
          businessType: businessType || '',
          businessId: businessId || ''
        },
        header: {
          'Tenant-Id': config.auth.tenantId,
          'Authorization': config.auth.basicAuth,
          'Blade-Auth': `Bearer ${wx.getStorageSync('token').value || ''}`
        },
        success: (res) => {
          if (res.statusCode === 200) {
            try {
              const data = JSON.parse(res.data);
              if (data.code === 200) {
                resolve({
                  success: true,
                  data: data.data,
                  message: '文件上传成功'
                });
              } else {
                resolve({
                  success: false,
                  message: data.msg || '文件上传失败'
                });
              }
            } catch (error) {
              resolve({
                success: false,
                message: '响应数据解析失败'
              });
            }
          } else {
            resolve({
              success: false,
              message: '网络请求失败'
            });
          }
        },
        fail: (error) => {
          console.error('文件上传失败:', error);
          resolve({
            success: false,
            message: '文件上传失败'
          });
        }
      });
    });
  }

  /**
   * 批量上传文件
   * @param {Array} filePaths 文件路径数组
   * @param {string} uploadSource 上传来源
   * @param {string} businessType 业务类型
   * @param {string|number} businessId 业务ID
   * @returns {Promise} 上传结果
   */
  static async uploadFiles(filePaths, uploadSource = 'miniapp', businessType = null, businessId = null) {
    try {
      const uploadPromises = filePaths.map(filePath => 
        this.uploadFile(filePath, uploadSource, businessType, businessId)
      );
      
      const results = await Promise.all(uploadPromises);
      
      // 检查是否有上传失败的文件
      const failedUploads = results.filter(result => !result.success);
      if (failedUploads.length > 0) {
        return {
          success: false,
          message: `有${failedUploads.length}个文件上传失败`,
          data: results
        };
      }
      
      return {
        success: true,
        data: results.map(result => result.data),
        message: '所有文件上传成功'
      };
    } catch (error) {
      console.error('批量上传文件失败:', error);
      return {
        success: false,
        message: '批量上传失败'
      };
    }
  }

  /**
   * 获取文件访问URL
   * @param {string} fileId 文件ID
   * @returns {Promise} 文件URL
   */
  static async getFileUrl(fileId) {
    try {
      const res = await request({
        url: config.fileUpload.getFileUrl,
        method: 'GET',
        data: { fileId }
      });

      if (res.code === 200) {
        return {
          success: true,
          data: res.data,
          message: '获取文件URL成功'
        };
      } else {
        return {
          success: false,
          message: res.msg || '获取文件URL失败'
        };
      }
    } catch (error) {
      console.error('获取文件URL失败:', error);
      return {
        success: false,
        message: '获取文件URL失败'
      };
    }
  }

  /**
   * 下载文件
   * @param {string} fileId 文件ID
   * @returns {Promise} 下载结果
   */
  static async downloadFile(fileId) {
    try {
      const res = await request({
        url: config.fileUpload.download || '/blade-system/file-upload/download',
        method: 'GET',
        data: { fileId },
        responseType: 'blob'
      });

      if (res.code === 200) {
        return {
          success: true,
          data: res.data,
          message: '文件下载成功'
        };
      } else {
        return {
          success: false,
          message: res.msg || '文件下载失败'
        };
      }
    } catch (error) {
      console.error('文件下载失败:', error);
      return {
        success: false,
        message: '文件下载失败'
      };
    }
  }

  /**
   * 删除文件
   * @param {string|Array} ids 文件ID或ID数组
   * @returns {Promise} 删除结果
   */
  static async removeFile(ids) {
    try {
      const res = await request({
        url: config.fileUpload.removeFile,
        method: 'POST',
        data: { ids }
      });

      if (res.code === 200) {
        return {
          success: true,
          message: '文件删除成功'
        };
      } else {
        return {
          success: false,
          message: res.msg || '文件删除失败'
        };
      }
    } catch (error) {
      console.error('文件删除失败:', error);
      return {
        success: false,
        message: '文件删除失败'
      };
    }
  }

  /**
   * 获取文件列表
   * @param {Object} params 查询参数
   * @returns {Promise} 文件列表
   */
  static async getFileList(params = {}) {
    try {
      const res = await request({
        url: config.fileUpload.getFileList,
        method: 'GET',
        data: params
      });

      if (res.code === 200) {
        return {
          success: true,
          data: res.data.records || [],
          total: res.data.total || 0,
          message: '获取文件列表成功'
        };
      } else {
        return {
          success: false,
          message: res.msg || '获取文件列表失败'
        };
      }
    } catch (error) {
      console.error('获取文件列表失败:', error);
      return {
        success: false,
        message: '获取文件列表失败'
      };
    }
  }

  /**
   * 根据业务类型获取文件列表
   * @param {string} businessType 业务类型
   * @param {string|number} businessId 业务ID
   * @returns {Promise} 文件列表
   */
  static async getFilesByBusiness(businessType, businessId) {
    try {
      const res = await request({
        url: config.fileUpload.getFilesByBusiness,
        method: 'GET',
        data: { businessType, businessId }
      });

      if (res.code === 200) {
        return {
          success: true,
          data: res.data || [],
          message: '获取业务文件成功'
        };
      } else {
        return {
          success: false,
          message: res.msg || '获取业务文件失败'
        };
      }
    } catch (error) {
      console.error('获取业务文件失败:', error);
      return {
        success: false,
        message: '获取业务文件失败'
      };
    }
  }

  /**
   * 获取文件统计信息
   * @returns {Promise} 统计信息
   */
  static async getFileStats() {
    try {
      const res = await request({
        url: config.fileUpload.getFileStats,
        method: 'GET'
      });

      if (res.code === 200) {
        return {
          success: true,
          data: res.data,
          message: '获取文件统计成功'
        };
      } else {
        return {
          success: false,
          message: res.msg || '获取文件统计失败'
        };
      }
    } catch (error) {
      console.error('获取文件统计失败:', error);
      return {
        success: false,
        message: '获取文件统计失败'
      };
    }
  }

  /**
   * 清理过期文件
   * @returns {Promise} 清理结果
   */
  static async cleanExpiredFiles() {
    try {
      const res = await request({
        url: config.fileUpload.cleanExpiredFiles,
        method: 'POST'
      });

      if (res.code === 200) {
        return {
          success: true,
          message: '过期文件清理成功'
        };
      } else {
        return {
          success: false,
          message: res.msg || '过期文件清理失败'
        };
      }
    } catch (error) {
      console.error('过期文件清理失败:', error);
      return {
        success: false,
        message: '过期文件清理失败'
      };
    }
  }

  /**
   * 获取存储配置
   * @returns {Promise} 存储配置
   */
  static async getStorageConfig() {
    try {
      const res = await request({
        url: config.fileUpload.storageConfig,
        method: 'GET'
      });

      if (res.code === 200) {
        return {
          success: true,
          data: res.data,
          message: '获取存储配置成功'
        };
      } else {
        return {
          success: false,
          message: res.msg || '获取存储配置失败'
        };
      }
    } catch (error) {
      console.error('获取存储配置失败:', error);
      return {
        success: false,
        message: '获取存储配置失败'
      };
    }
  }

  /**
   * 更新存储配置
   * @param {Object} config 配置信息
   * @returns {Promise} 更新结果
   */
  static async updateStorageConfig(config) {
    try {
      const res = await request({
        url: config.fileUpload.storageConfig,
        method: 'POST',
        data: config
      });

      if (res.code === 200) {
        return {
          success: true,
          message: '存储配置更新成功'
        };
      } else {
        return {
          success: false,
          message: res.msg || '存储配置更新失败'
        };
      }
    } catch (error) {
      console.error('存储配置更新失败:', error);
      return {
        success: false,
        message: '存储配置更新失败'
      };
    }
  }
}

module.exports = FileUploadStore; 