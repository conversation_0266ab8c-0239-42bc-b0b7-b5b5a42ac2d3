const { getMenuConfig, processMenusToQuickAccess, processBanners } = require('./menuStore.js');
const { getCategoryList } = require('./categoryStore.js');
const { getPostList } = require('./postStore.js');

const getBanners = () => {
  return [
    '/assets/images/banner.png',
    '/assets/images/banner.png',
    '/assets/images/banner.png',
  ];
};

// 从接口获取轮播图数据
const getBannersFromAPI = async () => {
  try {
    const config = await getMenuConfig();
    const banners = processBanners(config.banners);
    
    // 如果接口没有数据，返回默认数据
    if (banners.length === 0) {
      return getBanners();
    }
    
    return banners.map(banner => banner.image);
  } catch (error) {
    console.error('获取轮播图数据失败，使用默认数据:', error);
    return getBanners();
  }
};

const getQuickAccess = () => {
  return [

  ];
}

// 从接口获取快速访问首页菜单数据
const getQuickAccessFromAPI = async () => {
  try {
    const config = await getMenuConfig();
    // 用 menus 字段
    const quickAccess = processMenusToQuickAccess(config.menus);
    
    // 如果接口没有数据，返回默认数据
    if (quickAccess.length === 0) {
      return getQuickAccess();
    }
    
    return quickAccess;
  } catch (error) {
    console.error('获取快速访问菜单数据失败，使用默认数据:', error);
    return getQuickAccess();
  }
};

// 从接口获取快速访问我的菜单数据
const getQuickAccessIndex = async () => {
  try {
    const config = await getMenuConfig();
    // 用 userMenus 字段
    const quickAccess = processMenusToQuickAccess(config.userMenus);
    
    // 如果接口没有数据，返回默认数据
    if (quickAccess.length === 0) {
      return getQuickAccess();
    }
    
    return quickAccess;
  } catch (error) {
    console.error('获取快速访问菜单数据失败，使用默认数据:', error);
    return getQuickAccess();
  }
};

// 获取分类列表（从categoryStore获取）
const getCategories = async () => {
  try {
    const categories = await getCategoryList();
    // 添加"全部"选项，返回完整的分类对象
    return [
      { id: null, name: '全部' }, 
      ...categories.map(cat => ({ id: cat.id, name: cat.name }))
    ];
  } catch (error) {
    console.error('获取分类列表失败，使用默认数据:', error);
    return [
      { id: null, name: '全部' },
      { id: 1, name: '求职招聘' },
      { id: 2, name: '二手转让' },
      { id: 3, name: '本地推荐' }
    ];
  }
}

// 获取帖子列表（从postStore获取）
const getPosts = async (params = {}) => {
  try {
    console.log('getPosts 接收到的参数:', params);
    const result = await getPostList(params);
    console.log('getPostList 返回结果:', result);
    return result.records || [];
  } catch (error) {
    console.error('获取帖子列表失败，使用默认数据:', error);
    return getDefaultPosts();
  }
}

// 默认帖子数据
const getDefaultPosts = () => {
  return [
  ];
}

module.exports = {
  getBanners,
  getBannersFromAPI,
  getQuickAccess,
  getQuickAccessFromAPI,
  getQuickAccessIndex,
  getCategories,
  getPosts,
  getDefaultPosts
}; 
