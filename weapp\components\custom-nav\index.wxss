/* 自定义导航栏 */
.custom-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  transition: all 0.3s ease;
}

.nav-fixed {
  position: fixed;
}

.nav-content {
  display: flex;
  padding: 0 20rpx;
  width: 100%;
  justify-content: space-between;
  align-items: center;
}

/* 返回按钮 */
.back-btn {
  display: flex;
  align-items: center;
  height: 32px;
  min-width: 32px;
}

.back-icon {
  width: 36rpx;
  height: 36rpx;
}

/* 地址选择 */
.location {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  line-height: 1.4;
  margin-right: 20rpx;
  padding: 8rpx 16rpx;
  white-space: nowrap;
  border-radius: 20rpx;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.location:hover {
  background: rgba(255, 255, 255, 0.3);
}

.location-arrow {
  width: 24rpx;
  height: 24rpx;
  margin-left: 8rpx;
  transition: transform 0.3s ease;
  /* transform: rotate(90deg); */
}

.location:active .location-arrow {
  transform: rotate(270deg);
}

.nav-title-abs {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  text-align: center;
  font-size: 34rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
  z-index: 1;
}

/* 搜索框 */
.search-box {
  display: flex;
  align-items: center;
  position: absolute;
  right: 220rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.search-box:hover {
  background: rgba(255, 255, 255, 0.3);
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-left: 12rpx;
  flex-shrink: 0; /* 防止图标缩小 */
}

.search-placeholder {
  color: #999;
  font-size: 28rpx;
  line-height: 1.4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

/* 占位符 */
.nav-placeholder {
  background: transparent;
}

/* 滚动时的样式 */
.custom-nav.scrolled {
  background: #ffffff !important;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
}

/* 位置选择弹窗 */
.location-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 40rpx 32rpx;
  transform: translateY(0);
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  font-size: 48rpx;
  color: #999;
  padding: 10rpx;
  line-height: 1;
}

.modal-body {
  max-height: 600rpx;
  overflow-y: auto;
}

.location-list {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.location-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 0;
  font-size: 32rpx;
  color: #333;
  border-bottom: 1rpx solid #f5f5f5;
  transition: all 0.3s ease;
}

.location-item:last-child {
  border-bottom: none;
}

.location-item.selected {
  color: #FF7D7D;
  font-weight: 500;
}

.location-item:active {
  background: #f8f8f8;
}

.check-icon {
  width: 32rpx;
  height: 32rpx;
} 