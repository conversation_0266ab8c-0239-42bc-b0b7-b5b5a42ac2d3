<!--邀请码管理页面-->
<view class="invite-manage-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 主要内容 -->
  <view wx:else class="main-content">
    <!-- 统计卡片 -->
    <view class="stats-card">
      <view class="stats-header">
        <text class="stats-title">邀请统计</text>
        <picker mode="selector" range="{{['7天', '30天', '90天']}}" range-key="label" 
                value="{{statisticsDays === 7 ? 0 : statisticsDays === 30 ? 1 : 2}}"
                bindchange="onChangeStatisticsDays">
          <view class="stats-filter">
            <text>{{statisticsDays}}天</text>
            <text class="iconfont icon-arrow-down"></text>
          </view>
        </picker>
      </view>
      
      <view class="stats-grid">
        <view class="stats-item">
          <text class="stats-number">{{inviteStats.totalInvites || 0}}</text>
          <text class="stats-label">总邀请数</text>
        </view>
        <view class="stats-item">
          <text class="stats-number">{{inviteStats.successfulRegistrations || 0}}</text>
          <text class="stats-label">成功注册</text>
        </view>
        <view class="stats-item">
          <text class="stats-number">{{inviteStats.totalRewardPoints || 0}}</text>
          <text class="stats-label">获得积分</text>
        </view>
        <view class="stats-item">
          <text class="stats-number">{{inviteStats.conversionRate || 0}}%</text>
          <text class="stats-label">转化率</text>
        </view>
      </view>
    </view>

    <!-- 邀请码列表 -->
    <view class="section">
      <view class="section-header">
        <text class="section-title">我的邀请码</text>
        <button class="create-btn" bindtap="onCreateInviteCode">
          <text class="iconfont icon-add"></text>
          <text>创建邀请码</text>
        </button>
      </view>

      <view class="invite-codes-list">
        <view wx:for="{{inviteCodes}}" wx:key="inviteCode" class="invite-code-item">
          <view class="code-info">
            <view class="code-main">
              <text class="code-text">{{item.inviteCode}}</text>
              <view class="code-status {{item.status}}">{{item.statusText}}</view>
            </view>
            <view class="code-stats">
              <text class="stat-item">使用次数: {{item.usedCount || 0}}</text>
              <text class="stat-item">注册人数: {{item.registeredCount || 0}}</text>
            </view>
            <view class="code-time">
              <text>创建时间: {{item.createTime}}</text>
            </view>
          </view>
          
          <view class="code-actions">
            <button class="action-btn detail" data-code="{{item.inviteCode}}" bindtap="onViewCodeDetails">
              详情
            </button>
            <button class="action-btn qr" data-code="{{item.inviteCode}}" bindtap="onGenerateQRCode">
              二维码
            </button>
            <button class="action-btn share" data-code="{{item.inviteCode}}" bindtap="onShareInviteCode">
              分享
            </button>
            <button class="action-btn copy" data-code="{{item.inviteCode}}" bindtap="onCopyInviteCode">
              复制
            </button>
          </view>
        </view>

        <!-- 空状态 -->
        <view wx:if="{{inviteCodes.length === 0}}" class="empty-state">
          <text class="iconfont icon-empty"></text>
          <text class="empty-text">暂无邀请码</text>
          <button class="empty-action" bindtap="onCreateInviteCode">创建第一个邀请码</button>
        </view>
      </view>
    </view>

    <!-- 我邀请的用户 -->
    <view class="section">
      <view class="section-header">
        <text class="section-title">我邀请的用户</text>
        <text class="section-count">({{myInvitees.length}}人)</text>
      </view>

      <view class="invitees-list">
        <view wx:for="{{myInvitees}}" wx:key="id" class="invitee-item" 
              data-user-id="{{item.userId}}" bindtap="onViewUserDetails">
          <image class="invitee-avatar" src="{{item.avatar || '/assets/images/default-avatar.png'}}" />
          <view class="invitee-info">
            <text class="invitee-name">{{item.nickname || '新用户'}}</text>
            <text class="invitee-time">注册时间: {{item.registerTime}}</text>
            <text class="invitee-code">邀请码: {{item.inviteCode}}</text>
          </view>
          <view class="invitee-reward">
            <text class="reward-points">+{{item.rewardPoints || 0}}积分</text>
          </view>
        </view>

        <!-- 空状态 -->
        <view wx:if="{{myInvitees.length === 0}}" class="empty-state">
          <text class="iconfont icon-user-empty"></text>
          <text class="empty-text">暂无邀请用户</text>
        </view>
      </view>
    </view>

    <!-- 最近记录 -->
    <view class="section" wx:if="{{recentRecords.length > 0}}">
      <view class="section-header">
        <text class="section-title">最近记录</text>
      </view>

      <view class="recent-records">
        <view wx:for="{{recentRecords}}" wx:key="id" class="record-item">
          <view class="record-info">
            <text class="record-action">{{item.actionText}}</text>
            <text class="record-time">{{item.createTime}}</text>
          </view>
          <view class="record-result {{item.status}}">
            {{item.statusText}}
          </view>
        </view>
      </view>
    </view>

    <!-- 邀请排行榜 -->
    <view class="section" wx:if="{{ranking.length > 0}}">
      <view class="section-header">
        <text class="section-title">邀请排行榜</text>
      </view>

      <view class="ranking-list">
        <view wx:for="{{ranking}}" wx:key="userId" class="ranking-item">
          <view class="ranking-number {{index < 3 ? 'top' : ''}}">{{index + 1}}</view>
          <image class="ranking-avatar" src="{{item.avatar || '/assets/images/default-avatar.png'}}" />
          <view class="ranking-info">
            <text class="ranking-name">{{item.nickname || '用户' + item.userId}}</text>
            <text class="ranking-count">邀请 {{item.inviteCount}} 人</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 邀请码详情弹窗 -->
  <view wx:if="{{showCodeDetails}}" class="modal-overlay" bindtap="onCloseModal">
    <view class="modal-content" catchtap="">
      <view class="modal-header">
        <text class="modal-title">邀请码详情</text>
        <text class="iconfont icon-close" bindtap="onCloseModal"></text>
      </view>
      
      <view class="modal-body">
        <view class="detail-item">
          <text class="detail-label">邀请码:</text>
          <text class="detail-value">{{selectedCodeDetails.codeInfo.inviteCode}}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">使用次数:</text>
          <text class="detail-value">{{selectedCodeDetails.codeInfo.usedCount || 0}}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">注册人数:</text>
          <text class="detail-value">{{selectedCodeDetails.totalRegistered || 0}}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">转化率:</text>
          <text class="detail-value">{{selectedCodeDetails.conversionRate || 0}}%</text>
        </view>
        
        <!-- 注册用户列表 -->
        <view class="registered-users" wx:if="{{selectedCodeDetails.registeredUsers.length > 0}}">
          <text class="users-title">注册用户列表:</text>
          <view wx:for="{{selectedCodeDetails.registeredUsers}}" wx:key="id" class="user-item">
            <image class="user-avatar" src="{{item.avatar || '/assets/images/default-avatar.png'}}" />
            <view class="user-info">
              <text class="user-name">{{item.nickname || '新用户'}}</text>
              <text class="user-time">{{item.registerTime}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 二维码弹窗 -->
  <view wx:if="{{showQRCode}}" class="modal-overlay" bindtap="onCloseModal">
    <view class="modal-content qr-modal" catchtap="">
      <view class="modal-header">
        <text class="modal-title">邀请二维码</text>
        <text class="iconfont icon-close" bindtap="onCloseModal"></text>
      </view>
      
      <view class="modal-body">
        <view class="qr-container">
          <image class="qr-image" src="{{qrCodeInfo.qrCodeUrl || qrCodeInfo.qrImageUrl || qrCodeInfo.imageUrl || qrCodeInfo.url}}" />
          <text class="qr-code-text">{{qrCodeInfo.inviteCode}}</text>
          <text class="qr-tip">扫描二维码或分享邀请码给好友</text>
        </view>
        
        <view class="qr-actions">
          <button class="qr-btn save" bindtap="onSaveQRCode">保存图片</button>
          <button class="qr-btn share" bindtap="onShareInviteCode" data-code="{{qrCodeInfo.inviteCode}}">分享</button>
        </view>
      </view>
    </view>
  </view>
</view>
