// pages/recharge/recharge.js
const app = getApp();
const paymentStore = require('../../../stores/paymentStore');
const { request } = require('../../../utils/request.js');

Page({
  data: {
    // 导航栏高度
    navBarHeight: app.globalData.navBarHeight,
    
    // 余额信息
    currentBalance: '0.00',
    
    // 充值配置
    minAmount: 1,
    maxAmount: 10000,
    suggestedAmounts: [10, 50, 100, 200, 500],
    
    // 选择的金额
    selectedAmount: null,
    customAmount: '',
    finalAmount: '0.00',
    
    // 支付方式
    selectedPayMethod: 'wechat',
    
    // 状态
    canRecharge: false,
    isProcessing: false,
    showLoading: false,
    loadingText: '处理中...',
    
    // 支付结果
    showResultModal: false,
    paymentResult: {}
  },

  onLoad() {
    console.log('充值页面加载');
    this.initPage();
  },

  onShow() {
    // 页面显示时刷新余额
    this.getUserStats();
  },



  /**
   * 初始化页面
   */
  async initPage() {
    try {
      // 加载用户余额
      await this.getUserStats();
      
      // 加载充值配置
      await this.loadRechargeConfig();
      
    } catch (error) {
      console.error('初始化充值页面失败:', error);
      wx.showToast({
        title: '页面加载失败',
        icon: 'none'
      });
    }
  },

  // 加载用户余额
  async getUserStats() {
    try {
      const res = await request({
        url: '/blade-chat/user/stats',
        method: 'GET'
      });

      if (res.code === 200) {
        const balance = res.data.walletBalance || 0;
        this.setData({
          currentBalance: balance.toFixed(2)
        });
        console.log('用户余额已更新:', balance.toFixed(2));
      } else {
        console.error('获取用户统计数据失败:', res.message);
        this.setData({
          currentBalance: '0.00'
        });
      }
    } catch (err) {
      console.error('获取用户统计数据失败：', err);
      this.setData({
        currentBalance: '0.00'
      });
    }
  },

  /**
   * 加载充值配置
   */
  async loadRechargeConfig() {
    try {
      // 这里可以从后端获取充值配置
      // 暂时使用默认配置
      const config = {
        minAmount: 0.01,
        maxAmount: 10000,
        suggestedAmounts: [10, 50, 100, 200, 500]
      };
      
      this.setData({
        minAmount: config.minAmount,
        maxAmount: config.maxAmount,
        suggestedAmounts: config.suggestedAmounts
      });
    } catch (error) {
      console.error('加载充值配置失败:', error);
    }
  },

  /**
   * 选择快捷金额
   */
  selectAmount(e) {
    const amount = e.currentTarget.dataset.amount;

    if (amount >= this.data.minAmount) {
      this.setData({
        selectedAmount: amount,
        customAmount: '',
        finalAmount: amount.toFixed(2)
      });
      this.checkCanRecharge();
    } else {
      wx.showToast({
        title: `最小充值金额为${this.data.minAmount}元`,
        icon: 'none'
      });
    }
  },

  /**
   * 自定义金额输入
   */
  onCustomAmountInput(e) {
    const value = e.detail.value;
    
    // 实时更新支付金额
    let finalAmount = '0.00';
    if (value && !isNaN(parseFloat(value))) {
      const amount = parseFloat(value);
      if (amount > 0) {
        finalAmount = amount.toFixed(2);
      }
    }
    
    this.setData({
      customAmount: value,
      selectedAmount: null,
      finalAmount: finalAmount
    });
    
    // 实时检查是否可以充值
    this.checkCanRecharge();
  },

  /**
   * 自定义金额失焦
   */
  onCustomAmountBlur(e) {
    const value = e.detail.value;
    let finalAmount = '0.00';
    
    // 验证输入值
    if (value && !isNaN(parseFloat(value))) {
      const amount = parseFloat(value);
      if (amount > 0) {
        // 验证金额范围
        if (amount < this.data.minAmount) {
          wx.showToast({
            title: `最小充值金额为${this.data.minAmount}元`,
            icon: 'none'
          });
          this.setData({
            customAmount: '',
            finalAmount: '0.00'
          });
        } else if (amount > this.data.maxAmount) {
          wx.showToast({
            title: `最大充值金额为${this.data.maxAmount}元`,
            icon: 'none'
          });
          this.setData({
            customAmount: '',
            finalAmount: '0.00'
          });
        } else {
          finalAmount = amount.toFixed(2);
          this.setData({
            finalAmount: finalAmount
          });
        }
      } else {
        this.setData({
          customAmount: '',
          finalAmount: '0.00'
        });
      }
    } else {
      // 输入无效，清空
      this.setData({
        customAmount: '',
        finalAmount: '0.00'
      });
    }
    
    this.checkCanRecharge();
  },

  /**
   * 选择支付方式
   */
  selectPayMethod(e) {
    const method = e.currentTarget.dataset.method;
    this.setData({
      selectedPayMethod: method
    });
  },

  /**
   * 检查是否可以充值
   */
  checkCanRecharge() {
    const amount = parseFloat(this.data.finalAmount);

    // 验证金额范围
    const isValidAmount = amount >= this.data.minAmount && amount <= this.data.maxAmount;
    const canRecharge = isValidAmount && this.data.selectedPayMethod;

    this.setData({ canRecharge });

    console.log(`充值验证: 金额=${amount}, 验证结果=${isValidAmount}, 可充值=${canRecharge}`);

    // 如果验证失败，显示错误信息
    if (!isValidAmount && amount > 0) {
      if (amount < this.data.minAmount) {
        console.warn(`金额验证失败: 最小充值金额为${this.data.minAmount}元`);
      } else if (amount > this.data.maxAmount) {
        console.warn(`金额验证失败: 最大充值金额为${this.data.maxAmount}元`);
      }
    }
  },

  /**
   * 开始充值
   */
  async startRecharge() {
    if (!this.data.canRecharge || this.data.isProcessing) {
      return;
    }

    const amount = parseFloat(this.data.finalAmount);
    
    // 验证金额
    if (amount < this.data.minAmount) {
      wx.showToast({
        title: `最小充值金额为${this.data.minAmount}元`,
        icon: 'none'
      });
      return;
    }
    
    if (amount > this.data.maxAmount) {
      wx.showToast({
        title: `最大充值金额为${this.data.maxAmount}元`,
        icon: 'none'
      });
      return;
    }

    this.setData({
      isProcessing: true,
      showLoading: true,
      loadingText: '创建充值订单...'
    });

    try {
      // 创建充值订单
      const orderResult = await this.createRechargeOrder(amount);
      
      if (!orderResult.success) {
        throw new Error(orderResult.message);
      }

      this.setData({
        loadingText: '启动支付...'
      });

      // 发起支付
      const paymentResult = await this.processPayment(orderResult.data);

      // 显示支付结果，传递订单数据用于轮询
      this.showPaymentResult(paymentResult, orderResult.data);

    } catch (error) {
      console.error('充值失败:', error);
      this.showPaymentResult({
        success: false,
        message: error.message || '充值失败，请重试'
      });
    } finally {
      this.setData({
        isProcessing: false,
        showLoading: false
      });
    }
  },

  /**
   * 创建充值订单
   */
  async createRechargeOrder(amount) {
    const { request } = require('../../../utils/request');
    const { getOpenIdForPayment } = require('../../../utils/openIdHelper');

    try {
      // 获取用于支付的OpenID
      const openId = await getOpenIdForPayment();

      // 调用后端API创建充值订单
      const result = await request({
        url: `/blade-chat/user/recharge/order/create?amount=${amount}&openId=${openId}`,
        method: 'POST'
      });

      return result
    } catch (error) {
      throw new Error(error.message || '创建充值订单失败');
    }
  },

  /**
   * 处理支付
   */
  async processPayment(orderData) {
    try {
      console.log('处理支付，订单数据:', orderData);
      if (this.data.selectedPayMethod === 'wechat') {
        return await paymentStore.requestWechatPayment(orderData.payParams);
      } else {
        throw new Error('不支持的支付方式');
      }
    } catch (error) {
      console.error('支付处理失败:', error);
      throw new Error(error.message || '支付失败');
    }
  },

  /**
   * 显示支付结果
   */
  showPaymentResult(result, orderData = null) {
    if (result.success && orderData) {
      // 支付成功，开始轮询订单状态
      this.startOrderStatusPolling(orderData);
    } else {
      // 支付失败，直接显示结果
      this.setData({
        showResultModal: true,
        paymentResult: result
      });
    }
  },

  /**
   * 隐藏结果弹窗
   */
  hideResultModal() {
    this.setData({
      showResultModal: false
    });
  },

  /**
   * 开始轮询订单状态
   */
  async startOrderStatusPolling(orderData) {
    const maxAttempts = 30; // 最大轮询次数（30次，约1分钟）
    const interval = 2000; // 轮询间隔（2秒）
    let attempts = 0;

    // 显示轮询状态
    this.setData({
      showResultModal: true,
      paymentResult: {
        success: true,
        message: '支付成功，正在确认订单状态...',
        isPolling: true
      }
    });

    const pollOrderStatus = async () => {
      attempts++;

      try {
        console.log(`轮询订单状态，第${attempts}次，订单号：${orderData.orderNo}`);

        // 每隔几次轮询触发一次后端同步（避免过于频繁）
        if (attempts % 3 === 1) {
          console.log('触发后端同步检查...');
          try {
            const syncResult = await this.triggerImmediateSync(orderData.orderNo);
            if (syncResult.success && syncResult.statusChanged) {
              console.log('后端同步发现状态变化，立即查询最新状态');
            }
          } catch (syncError) {
            console.warn('后端同步失败，继续轮询:', syncError);
          }
        }

        // 查询订单状态
        const statusResult = await this.queryOrderStatus(orderData.orderNo);

        if (statusResult.success) {
          const { orderStatus, paymentStatus } = statusResult.data;

          console.log(`订单状态：${orderStatus}，支付状态：${paymentStatus}`);

          // 检查是否支付完成
          if (paymentStatus === 'PAID' && orderStatus === 'SUCCESS') {
            // 支付和充值都完成
            this.handleOrderCompleted(statusResult.data);
            return; // 结束轮询
          } else if (orderStatus === 'FAILED' || paymentStatus === 'REFUNDED') {
            // 订单失败或已退款
            this.handleOrderFailed(statusResult.data);
            return; // 结束轮询
          } else if (orderStatus === 'CANCELLED') {
            // 订单取消
            this.handleOrderCancelled(statusResult.data);
            return; // 结束轮询
          } else if (paymentStatus === 'PAID') {
            // 支付完成，但充值可能还在处理中
            this.setData({
              'paymentResult.message': '支付完成，充值处理中...'
            });
            // 继续轮询，不return
          } else {
            // 其他状态（PENDING/UNPAID等），继续轮询
            console.log('订单仍在处理中，继续轮询...');
          }
        } else {
          // 查询失败，记录错误但继续轮询
          console.warn(`查询订单状态失败，第${attempts}次:`, statusResult.error);
        }

        // 继续轮询
        if (attempts < maxAttempts) {
          setTimeout(pollOrderStatus, interval);
        } else {
          // 轮询超时
          this.handlePollingTimeout(orderData.orderNo);
        }

      } catch (error) {
        console.error('轮询订单状态失败:', error);

        // 继续轮询（网络错误不应该停止轮询）
        if (attempts < maxAttempts) {
          setTimeout(pollOrderStatus, interval);
        } else {
          this.handlePollingTimeout(orderData.orderNo);
        }
      }
    };

    // 开始轮询
    pollOrderStatus();
  },

  /**
   * 查询订单状态
   */
  async queryOrderStatus(orderNo) {
    const { request } = require('../../../utils/request');

    try {
      const result = await request({
        url: `/blade-chat/user/recharge/order/status?orderNo=${orderNo}`,
        method: 'GET'
      });

      return result
    } catch (error) {
      console.error('查询订单状态失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  },

  /**
   * 触发后端立即同步订单状态
   */
  async triggerImmediateSync(orderNo) {
    try {
      const { request } = require('../../../utils/request');

      console.log('触发后端立即同步订单状态:', orderNo);

      const result = await request({
        url: `/blade-chat/payment/status/sync/immediate/${orderNo}`,
        method: 'POST'
      });

      if (result.success) {
        console.log('后端同步结果:', result.data);
        return {
          success: true,
          data: result.data,
          statusChanged: result.data.statusChanged
        };
      } else {
        console.warn('后端同步失败:', result.message);
        return {
          success: false,
          error: result.message || '同步失败'
        };
      }
    } catch (error) {
      console.error('触发后端同步失败:', error);
      return {
        success: false,
        error: error.message || '同步失败'
      };
    }
  },

  /**
   * 处理订单完成
   */
  handleOrderCompleted(orderData) {
    console.log('订单完成:', orderData);

    this.setData({
      paymentResult: {
        success: true,
        message: '充值成功！',
        isPolling: false,
        isCompleted: true
      }
    });

    // 刷新用户余额
    this.refreshUserBalance();
  },

  /**
   * 处理订单失败
   */
  handleOrderFailed(orderData) {
    console.log('订单失败:', orderData);

    this.setData({
      paymentResult: {
        success: false,
        message: '充值失败，请重试',
        isPolling: false
      }
    });
  },

  /**
   * 处理订单取消
   */
  handleOrderCancelled(orderData) {
    console.log('订单取消:', orderData);

    this.setData({
      paymentResult: {
        success: false,
        message: '订单已取消',
        isPolling: false
      }
    });
  },

  /**
   * 处理轮询超时
   */
  handlePollingTimeout(orderNo) {
    console.log('轮询超时，订单号:', orderNo);

    this.setData({
      paymentResult: {
        success: false,
        message: '订单状态确认超时，请稍后在充值记录中查看结果，或联系客服处理。',
        isPolling: false,
        isTimeout: true
      }
    });
  },

  /**
   * 刷新用户余额
   */
  async refreshUserBalance() {
    try {
      // 刷新当前页面的余额显示
      await this.getUserStats();
      console.log('用户余额已刷新');

      // 如果有全局的用户信息管理，也可以刷新全局状态
      // const app = getApp();
      // await app.refreshUserInfo();

    } catch (error) {
      console.error('刷新用户余额失败:', error);
    }
  },

  /**
   * 处理结果确认
   */
  handleResultConfirm() {
    if (this.data.paymentResult.success && this.data.paymentResult.isCompleted) {
      // 充值成功，返回上一页
      wx.navigateBack();
    } else if (this.data.paymentResult.isTimeout) {
      // 超时情况，可以选择查看充值记录或返回
      wx.showModal({
        title: '提示',
        content: '是否查看充值记录？',
        success: (res) => {
          if (res.confirm) {
            // 跳转到充值记录页面
            wx.navigateTo({
              url: '/pgk_order/pages/recharge/recharge-history/recharge-history'
            });
          } else {
            this.hideResultModal();
          }
        }
      });
    } else {
      // 其他情况，关闭弹窗重新充值
      this.hideResultModal();
    }
  },

  /**
   * 查看余额明细
   */
  viewBalanceDetail() {
    wx.navigateTo({
      url: '/pgk_order/pages/balance-detail/balance-detail'
    });
  }
});
