.quick-access-wrapper {
  margin: 20rpx;
  background-color: #fff;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.04);
  padding: 20rpx 0;
}

.swiper {
  height: 320rpx; /* 调整后的高度 */
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(2, 1fr);
  height: 100%;
  justify-content: center;
}

.access-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.icon-background {
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx; /* 微调间距 */
}

/* 默认背景色，当接口没有提供颜色时使用 */
.icon-background {
  background-color: #FF7800FF;
}

.icon {
  width: 48rpx;
  height: 48rpx;
}

.title {
  font-size: 22rpx;
  color: #333;
}
