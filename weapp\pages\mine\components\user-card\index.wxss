.user-card {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 24rpx rgba(255,107,107,0.08);
  padding: 32rpx 24rpx;
  margin-bottom: 32rpx;
}
.avatar-wrap {
  width: 112rpx;
  height: 112rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 24rpx;
  border: 4rpx solid #ff6b6b;
}
.avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}
.user-info {
  flex: 1;
}
.nickname {
  font-size: 36rpx;
  font-weight: bold;
  color: #222;
  margin-bottom: 12rpx;
}
.tags {
  display: flex;
  gap: 12rpx;
}
.tag {
  font-size: 22rpx;
  padding: 4rpx 16rpx;
  border-radius: 16rpx;
  background: #f5f5f5;
  color: #ff6b6b;
}
.tag.purple {
  background: #f3e8ff;
  color: #ff6b6b;
}
.tag.blue {
  background: #e6f0ff;
  color: #4a90e2;
}
.edit-btn {
  display: flex;
  align-items: center;
  color: #ff6b6b;
  font-size: 26rpx;
  margin-left: 24rpx;
  cursor: pointer;
}
.edit-btn image {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
} 