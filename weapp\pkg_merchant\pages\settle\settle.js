const FileUploadStore = require('../../stores/fileUploadStore');
const merchantStore = require('../../stores/merchantStore');

Page({
  data: {
    form: {
      logo: '',
      name: '',
      categoryIndex: 0,
      categoryName: '',
      intro: '',
      contactName: '',
      contactPhone: '',
      code: '',
      licenseNo: '',
      licenseImg: '',
      address: '',
      album: [],
      products: ['']
    },
    categoryList: ['餐饮', '零售', '教育', '服务', '其他'],
    codeBtnText: '获取验证码',
    codeBtnDisabled: false,
    codeTimer: null,
    codeCountdown: 60
  },
  onLoad() {},
  // 事件处理函数
  onLogoChange(e) {
    this.setData({ 'form.logo': e.detail.logo });
  },
  onLicenseChange(e) {
    this.setData({ 'form.licenseImg': e.detail.license });
  },
  onAlbumChange(e) {
    this.setData({ 'form.album': e.detail.album });
  },
  onAddressChange(e) {
    this.setData({ 'form.address': e.detail.address });
  },
  onProductListChange(e) {
    this.setData({ 'form.products': e.detail.products });
  },
  onInputName(e) {
    this.setData({ 'form.name': e.detail.value });
  },
  onCategoryChange(e) {
    const idx = e.detail.value;
    this.setData({
      'form.categoryIndex': idx,
      'form.categoryName': this.data.categoryList[idx]
    });
  },
  onInputIntro(e) {
    this.setData({ 'form.intro': e.detail.value });
  },
  onInputContactName(e) {
    this.setData({ 'form.contactName': e.detail.value });
  },
  onInputContactPhone(e) {
    this.setData({ 'form.contactPhone': e.detail.value });
  },
  onInputCode(e) {
    this.setData({ 'form.code': e.detail.value });
  },
  onInputLicenseNo(e) {
    this.setData({ 'form.licenseNo': e.detail.value });
  },
  onGetCode() {
    const phone = this.data.form.contactPhone;
    if (!/^1[3-9]\d{9}$/.test(phone)) {
      wx.showToast({ title: '请输入正确的手机号', icon: 'none' });
      return;
    }
    this.setData({ codeBtnDisabled: true, codeBtnText: '发送中...' });
    merchantStore.getVerifyCode(phone).then(() => {
      wx.showToast({ title: '验证码已发送', icon: 'none' });
      this.startCodeCountdown();
    }).catch(err => {
      wx.showToast({ title: err || '发送失败', icon: 'none' });
      this.setData({ codeBtnDisabled: false, codeBtnText: '获取验证码' });
    });
  },
  startCodeCountdown() {
    let count = 60;
    this.setData({ codeBtnText: `${count}s`, codeBtnDisabled: true });
    this.data.codeTimer && clearInterval(this.data.codeTimer);
    this.data.codeTimer = setInterval(() => {
      count--;
      if (count <= 0) {
        clearInterval(this.data.codeTimer);
        this.setData({ codeBtnText: '获取验证码', codeBtnDisabled: false });
      } else {
        this.setData({ codeBtnText: `${count}s` });
      }
    }, 1000);
  },
  async onSubmit() {
    const f = this.data.form;
    if (!f.logo) return wx.showToast({ title: '请上传商家Logo', icon: 'none' });
    if (!f.name) return wx.showToast({ title: '请输入商家名称', icon: 'none' });
    if (!f.categoryName) return wx.showToast({ title: '请选择商家类型', icon: 'none' });
    if (!f.contactName) return wx.showToast({ title: '请输入联系人姓名', icon: 'none' });
    if (!/^1[3-9]\d{9}$/.test(f.contactPhone)) return wx.showToast({ title: '请输入正确的手机号', icon: 'none' });
    if (!f.code) return wx.showToast({ title: '请输入验证码', icon: 'none' });
    if (!f.licenseImg) return wx.showToast({ title: '请上传营业执照照片', icon: 'none' });
    if (!f.address) return wx.showToast({ title: '请选择经营地址', icon: 'none' });
    if (!f.products.filter(Boolean).length) return wx.showToast({ title: '请填写至少一个主营商品/服务', icon: 'none' });
    wx.showLoading({ title: '上传图片...' });
    // 统一上传图片
    let logoUrl = f.logo;
    let licenseUrl = f.licenseImg;
    let albumUrls = f.album;
    try {
      // 上传logo
      if (f.logo && !/^http/.test(f.logo)) {
        const logoRes = await FileUploadStore.uploadFile(f.logo, 'miniapp', 'merchant_logo');
        if (logoRes.success && logoRes.data && logoRes.data.url) {
          logoUrl = logoRes.data.url;
        } else {
          throw new Error('Logo上传失败');
        }
      }
      // 上传执照
      if (f.licenseImg && !/^http/.test(f.licenseImg)) {
        const licRes = await FileUploadStore.uploadFile(f.licenseImg, 'miniapp', 'merchant_license');
        if (licRes.success && licRes.data && licRes.data.url) {
          licenseUrl = licRes.data.url;
        } else {
          throw new Error('执照上传失败');
        }
      }
      // 上传相册（所有本地图片都要上传，最终album只保留后端url）
      const localAlbum = f.album.filter(item => /^http:\/\/tmp\//.test(item) || /^wxfile:/.test(item));
      if (localAlbum.length > 0) {
        const albumRes = await Promise.all(localAlbum.map(path => FileUploadStore.uploadFile(path, 'miniapp', 'merchant_album')));
        const uploaded = albumRes.map(res => (res.success && res.data && res.data.url) ? res.data.url : null).filter(Boolean);
        // albumUrls只保留后端url
        let uploadedIdx = 0;
        albumUrls = f.album.map(item => {
          if ((/^http:\/\//.test(item) && !/^http:\/\/tmp\//.test(item))) {
            return item;
          } else {
            return uploaded[uploadedIdx++] || '';
          }
        }).filter(Boolean);
      }
      // albumUrls只保留后端url
      albumUrls = albumUrls.filter(url => /^http/.test(url) && !/^http:\/\/tmp\//.test(url));
    } catch (err) {
      wx.hideLoading();
      wx.showToast({ title: err.message || '图片上传失败', icon: 'none' });
      return;
    }
    wx.showLoading({ title: '提交中...' });
    const submitData = { ...f, logo: logoUrl, licenseImg: licenseUrl, album: albumUrls };
    merchantStore.submitMerchant(submitData).then(() => {
      wx.hideLoading();
      wx.showToast({ title: '提交成功，等待审核', icon: 'success' });
      setTimeout(() => wx.navigateBack(), 1500);
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({ title: err || '提交失败', icon: 'none' });
    });
  }
}); 