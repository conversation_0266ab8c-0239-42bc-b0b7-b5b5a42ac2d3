Page({
  data: {
    title: '',
    description: '',
    postId: ''
  },
  onLoad(options) {
    this.setData({
      title: options.title || '',
      description: options.description || '',
      postId: options.postId || ''
    });
    console.log(this.data);
  },
  onViewPost() {
    // 跳转到帖子详情页
    wx.redirectTo({
      url: `/pages/post-detail/post-detail?id=${this.data.postId}`
    });
  },
  onBackHome() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  }
}); 