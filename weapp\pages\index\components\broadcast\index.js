Component({
  properties: {
    notices: {
      type: Array,
      value: []
    }
  },

  data: {
    currentIndex: 0
  },

  methods: {
    // 轮播切换事件
    onSwiperChange(e) {
      this.setData({
        currentIndex: e.detail.current
      });
    },

    // 点击通知事件
    onNoticeClick(e) {
      const notice = e.currentTarget.dataset.notice;
      console.log('点击广播通知:', notice);
      
      // 触发父组件事件
      this.triggerEvent('noticeclick', { notice });
      
      // 根据通知类型进行跳转
      this.handleNoticeNavigation(notice);
    },

    // 处理通知跳转
    handleNoticeNavigation(notice) {
      const { type, relatedId, relatedType } = notice;

      switch (type) {
        case 'post':
          // 跳转到帖子详情
          wx.navigateTo({
            url: `/pages/post-detail/post-detail?id=${relatedId}`
          });
          break;

        case 'institution':
          // 跳转到机构详情
          wx.navigateTo({
            url: `/pages/institution-detail/institution-detail?id=${relatedId}`
          });
          break;

        case 'checkin':
          // 跳转到签到页面
          wx.navigateTo({
            url: `/pages/checkin/checkin`
          });
          break;

        case 'announcement':
          // 跳转到公告详情
          wx.navigateTo({
            url: `/pages/announcement-detail/announcement-detail?id=${relatedId}`
          });
          break;

        default:
          // 默认跳转到广播列表页面
          wx.navigateTo({
            url: `/pages/broadcast-list/broadcast-list`
          });
          break;
      }
    },

    // 点击更多按钮
    onMoreClick() {
      console.log('点击查看更多广播');
      wx.navigateTo({
        url: '/pages/broadcast-list/broadcast-list'
      });
    }
  }
});
