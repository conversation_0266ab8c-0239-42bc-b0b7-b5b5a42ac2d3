.feedback-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 32rpx;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 48rpx;
}

.page-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
}

.page-subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
}

/* 反馈区块 */
.feedback-section {
  background: #fff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06);
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.required {
  color: #ff6b6b;
  font-size: 32rpx;
  margin-left: 8rpx;
}

.optional {
  color: #999;
  font-size: 28rpx;
  margin-left: 16rpx;
  font-weight: normal;
}

/* 内容输入 */
.content-input-wrap {
  position: relative;
}

.content-input {
  width: 100%;
  min-height: 240rpx;
  padding: 24rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  font-size: 30rpx;
  color: #333;
  background: #fff;
  box-sizing: border-box;
  line-height: 1.6;
}

.content-input:focus {
  border-color: #ff6b6b;
  outline: none;
}

.input-counter {
  position: absolute;
  bottom: 16rpx;
  right: 16rpx;
}

.counter-text {
  font-size: 24rpx;
  color: #999;
}

/* 联系方式输入 */
.contact-input {
  width: 100%;
  height: 88rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  font-size: 30rpx;
  color: #333;
  background: #fff;
  box-sizing: border-box;
}

.contact-input:focus {
  border-color: #ff6b6b;
  outline: none;
}

/* 图片上传 */
.upload-area {
  margin-top: 16rpx;
}

.image-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.image-item {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background: #f5f5f5;
}

.image-item image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.delete-btn {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 40rpx;
  height: 40rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
}

.upload-item {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #ddd;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  cursor: pointer;
  transition: all 0.3s ease;
}

.upload-item:active {
  background: #f0f0f0;
  border-color: #ff6b6b;
}

.plus {
  font-size: 60rpx;
  color: #999;
  font-weight: 300;
}

/* 提交按钮 */
.submit-section {
  margin: 48rpx 0;
}

.submit-btn {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
  border: none;
  border-radius: 48rpx;
  color: #fff;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 32rpx rgba(255, 107, 107, 0.3);
  transition: all 0.3s ease;
}

.submit-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(255, 107, 107, 0.3);
}

.submit-btn.submitting {
  background: #ccc;
  box-shadow: none;
}

/* 提示信息 */
.tips-section {
  background: #fff;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06);
}

.tip-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  margin-top: 4rpx;
}

.tip-text {
  flex: 1;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

/* 评论回复相关样式 */

/* 回复信息显示 */
.reply-to-info {
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border-left: 6rpx solid #007aff;
}

.reply-to-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.reply-to-label {
  font-size: 24rpx;
  color: #666;
  margin-right: 12rpx;
}

.reply-to-user {
  font-size: 26rpx;
  color: #007aff;
  font-weight: 500;
  flex: 1;
}

.cancel-reply {
  font-size: 24rpx;
  color: #ff3b30;
  padding: 8rpx 16rpx;
  border: 1rpx solid #ff3b30;
  border-radius: 20rpx;
}

.reply-to-content {
  font-size: 26rpx;
  color: #333;
  line-height: 1.4;
  max-height: 120rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

/* @用户功能 */
.mention-actions {
  margin-top: 20rpx;
  display: flex;
  justify-content: flex-end;
}

.mention-btn {
  background-color: #007aff;
  color: white;
  font-size: 26rpx;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  border: none;
}

.mention-btn::after {
  border: none;
}

/* @用户选择面板 */
.mention-panel {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  border-radius: 24rpx 24rpx 0 0;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 60vh;
  display: flex;
  flex-direction: column;
}

.mention-header {
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  text-align: center;
  border-bottom: 1rpx solid #eee;
}

.mention-list {
  flex: 1;
  padding: 0 30rpx;
}

.mention-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.mention-item:last-child {
  border-bottom: none;
}

.mention-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 24rpx;
}

.mention-nickname {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.mention-panel-actions {
  padding: 30rpx;
  border-top: 1rpx solid #eee;
}

.mention-cancel {
  width: 100%;
  background-color: #f8f9fa;
  color: #666;
  font-size: 28rpx;
  padding: 24rpx;
  border-radius: 12rpx;
  border: none;
}

.mention-cancel::after {
  border: none;
}

/* 已选择的@用户显示 */
.mentioned-users {
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: #f0f8ff;
  border-radius: 12rpx;
}

.mentioned-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 12rpx;
  display: block;
}

.mentioned-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.mentioned-user {
  display: flex;
  align-items: center;
  background-color: #007aff;
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.mentioned-name {
  margin-right: 8rpx;
}

.remove-mention {
  font-size: 20rpx;
  font-weight: bold;
  cursor: pointer;
}