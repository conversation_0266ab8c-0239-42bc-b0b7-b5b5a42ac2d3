# 机构详情页面优化说明

## 优化概述

对机构详情页面进行了全面优化，提升了信息展示的优雅性和用户体验，包括机构卡片点击功能、详情页面信息展示优化等。

## 主要优化内容

### 1. 机构卡片点击功能修复

**修改文件：** `weapp/pages/local/local.js`

**问题修复：**
- 修正了点击事件处理逻辑
- 从 `e.detail.institution` 改为 `e.currentTarget.dataset.id`
- 确保机构ID正确传递到详情页面

**修改前：**
```javascript
onInstitutionTap(e) {
  const institution = e.detail.institution; // ❌ 错误的获取方式
  // ...
}
```

**修改后：**
```javascript
onInstitutionTap(e) {
  const institutionId = e.currentTarget.dataset.id; // ✅ 正确的获取方式
  // ...
}
```

### 2. 机构详情页面头部优化

**修改文件：** `weapp/pages/local/institution-detail/institution-detail.wxml`

**优化内容：**

#### 2.1 添加机构图片轮播
```xml
<!-- 机构主图轮播 -->
<view wx:if="{{institution.images && institution.images.length > 0}}" class="header-images">
  <swiper class="images-swiper" indicator-dots="{{institution.images.length > 1}}">
    <swiper-item wx:for="{{institution.images}}" wx:key="*this">
      <image class="header-image" src="{{item}}" mode="aspectFill" bindtap="onImageTap" />
    </swiper-item>
  </swiper>
  <view class="image-count">{{currentImageIndex + 1}}/{{institution.images.length}}</view>
</view>
```

#### 2.2 优化机构信息展示
- **机构名称和标签**：名称 + 类型标签 + 认证标签
- **统计信息**：评分、浏览量、收藏数
- **地址信息**：完整地址 + 距离显示
- **营业状态**：营业时间 + 当前状态（营业中/已打烊）

### 3. 机构信息内容优化

#### 3.1 联系信息模块
```xml
<!-- 联系信息 -->
<view class="info-section">
  <view class="section-header">
    <text class="section-icon">📞</text>
    <text class="section-title">联系方式</text>
  </view>
  <view class="section-content">
    <view class="contact-item" bindtap="onCallPhone">
      <text class="contact-label">电话：</text>
      <text class="contact-value phone">{{institution.phone}}</text>
      <text class="contact-action">拨打</text>
    </view>
    <view class="contact-item" bindtap="copyContact" data-type="wechat">
      <text class="contact-label">微信：</text>
      <text class="contact-value">{{institution.wechat}}</text>
      <text class="contact-action">复制</text>
    </view>
  </view>
</view>
```

#### 3.2 地址位置模块
```xml
<!-- 地址信息 -->
<view class="info-section">
  <view class="section-header">
    <text class="section-icon">📍</text>
    <text class="section-title">地址位置</text>
  </view>
  <view class="section-content">
    <view class="address-info">
      <text class="full-address">{{institution.fullAddress}}</text>
      <button class="navigate-btn" bindtap="onViewLocation">
        <text class="navigate-text">导航</text>
      </button>
    </view>
  </view>
</view>
```

#### 3.3 特色服务模块
```xml
<!-- 特色服务 -->
<view class="info-section">
  <view class="section-header">
    <text class="section-icon">⭐</text>
    <text class="section-title">特色服务</text>
  </view>
  <view class="section-content">
    <view class="services-container">
      <text wx:for="{{institution.specialServices}}" class="service-tag">{{item}}</text>
    </view>
  </view>
</view>
```

### 4. 样式优化

**修改文件：** `weapp/pages/local/institution-detail/institution-detail.wxss`

#### 4.1 头部样式优化
- **图片轮播**：400rpx高度，支持指示器和图片计数
- **渐变背景**：保持原有的渐变效果
- **信息布局**：优化间距和排版

#### 4.2 新增样式组件

**联系信息样式：**
```css
.contact-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.contact-value.phone {
  color: #ff6b6b;
}

.contact-action {
  color: #ff6b6b;
  background: rgba(255, 107, 107, 0.1);
  border-radius: 16rpx;
}
```

**特色服务样式：**
```css
.service-tag {
  background: linear-gradient(135deg, #ff6b6b, #ff8a8a);
  color: white;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  font-weight: 500;
}
```

### 5. 功能增强

**修改文件：** `weapp/pages/local/institution-detail/institution-detail.js`

#### 5.1 新增方法

**图片轮播控制：**
```javascript
onImageChange(e) {
  this.setData({
    currentImageIndex: e.detail.current
  });
}
```

**图片预览功能：**
```javascript
onImageTap(e) {
  const current = e.currentTarget.dataset.src;
  const urls = this.data.institution.images || [];
  wx.previewImage({ current, urls });
}
```

**联系方式复制：**
```javascript
copyContact(e) {
  const type = e.currentTarget.dataset.type;
  // 复制电话或微信号到剪贴板
  wx.setClipboardData({ data: contactData });
}
```

## 优化效果

### 1. 视觉效果提升
- ✅ **头部轮播图**：展示机构多张图片
- ✅ **信息层次**：清晰的信息分组和视觉层次
- ✅ **现代设计**：圆角、阴影、渐变等现代设计元素
- ✅ **状态指示**：营业状态、认证标识等

### 2. 交互体验优化
- ✅ **一键拨打**：点击电话号码直接拨打
- ✅ **快速复制**：一键复制微信号等联系方式
- ✅ **地图导航**：直接跳转到地图应用导航
- ✅ **图片预览**：支持图片放大查看

### 3. 信息展示完善
- ✅ **完整信息**：展示机构的所有重要信息
- ✅ **分类清晰**：按功能模块组织信息
- ✅ **状态明确**：营业状态、认证状态等
- ✅ **统计数据**：浏览量、收藏数等

### 4. 响应式设计
- ✅ **适配性强**：适配不同屏幕尺寸
- ✅ **加载优化**：图片懒加载，性能优化
- ✅ **错误处理**：完善的错误状态处理

## 技术特点

### 1. 组件化设计
- 模块化的信息展示组件
- 可复用的样式类
- 清晰的组件层次结构

### 2. 数据处理
- 完善的数据验证和默认值处理
- 图片数组的智能处理
- 特色服务字符串的分割处理

### 3. 用户体验
- 流畅的动画效果
- 直观的操作反馈
- 完善的错误提示

### 4. 性能优化
- 图片懒加载
- 条件渲染减少DOM节点
- 合理的数据更新策略

## 使用说明

### 1. 页面跳转
```javascript
// 从机构列表跳转到详情页
wx.navigateTo({
  url: `/pages/local/institution-detail/institution-detail?id=${institutionId}`
});
```

### 2. 数据格式
机构详情数据应包含以下字段：
```javascript
{
  id: "机构ID",
  name: "机构名称",
  typeName: "机构类型",
  description: "机构描述",
  images: ["图片URL数组"],
  phone: "联系电话",
  wechat: "微信号",
  businessHours: "营业时间",
  specialServices: "特色服务,逗号分隔",
  fullAddress: "完整地址",
  latitude: "纬度",
  longitude: "经度",
  isVerified: "是否认证",
  rating: "评分",
  viewCount: "浏览次数",
  favoriteCount: "收藏次数"
}
```

通过这些优化，机构详情页面现在提供了更加优雅和完整的信息展示，大大提升了用户体验。
