Component({
  properties: {
    products: {
      type: Array,
      value: ['']
    }
  },
  methods: {
    onInputProduct(e) {
      const idx = e.currentTarget.dataset.index;
      const value = e.detail.value;
      const list = this.data.products.slice();
      list[idx] = value;
      this.triggerEvent('change', { products: list });
    },
    onAddProduct() {
      const list = this.data.products.concat(['']);
      this.triggerEvent('change', { products: list });
    },
    onDeleteProduct(e) {
      const idx = e.currentTarget.dataset.index;
      const list = this.data.products.slice();
      list.splice(idx, 1);
      this.triggerEvent('change', { products: list });
    }
  }
}); 