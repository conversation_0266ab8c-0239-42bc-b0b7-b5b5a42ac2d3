/* 地区选择器样式 */
.region-picker-mask {
  position: fixed;
  left: 0; right: 0; top: 0; bottom: 0;
  background: rgba(0,0,0,0.4);
  z-index: 1000;
}

.region-picker-popup {
  position: fixed;
  left: 0; right: 0; bottom: 0;
  background: #fff;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  z-index: 1001;
  width: 100vw;
  max-width: 100vw;
  /* 动画初始状态 */
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(.4,0,.2,1);
  /* 高度撑满底部 */
  min-height: 60vh;
  display: flex;
  flex-direction: column;
  padding-bottom: env(safe-area-inset-bottom);
}
.region-picker-popup.show {
  transform: translateY(0);
}

/* 标题栏 */
.region-picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.header-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 32rpx;
}

/* 标签页 */
.region-picker-tabs {
  display: flex;
  padding: 0 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background-color: #fafafa;
}

.tab-item {
  flex: 1;
  padding: 24rpx 0;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  min-width: 0;
}

.tab-item.active {
  color: #ff6b6b;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #ff6b6b;
  border-radius: 2rpx;
}

.tab-name {
  font-size: 28rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120rpx;
}

.tab-selected {
  margin-left: 8rpx;
  color: #ff6b6b;
  font-size: 24rpx;
}

/* 内容区域 */
.region-picker-content {
  flex: 1;
  overflow: hidden;
}

.region-list {
  height: 50vh; /* 减少高度，为底部导航栏留出空间 */
  max-height: 500rpx;
  min-height: 300rpx; /* 确保最小高度 */
}

.region-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f5f5f5;
  transition: background-color 0.2s;
}

.region-item:active {
  background-color: #f8f8f8;
}

.region-item.selected {
  background-color: #fff5f5;
  color: #ff6b6b;
}

.region-name {
  font-size: 32rpx;
  color: #333;
  flex: 1;
}

.region-item.selected .region-name {
  color: #ff6b6b;
}

.region-selected {
  color: #ff6b6b;
  font-size: 28rpx;
  font-weight: 600;
}

/* 加载状态 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 空状态 */
.empty-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 底部按钮 */
.region-picker-footer {
  padding: 30rpx 40rpx;
  border-top: 1rpx solid #f0f0f0;
  background-color: #fff;
  /* 确保底部按钮区域不被遮挡 */
  position: relative;
  z-index: 1;
}

.confirm-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #ff6b6b, #ff8585);
  color: #fff;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}

.confirm-btn:active {
  opacity: 0.8;
}

.confirm-btn[disabled] {
  background: #eee;
  color: #bbb;
  border: none;
}

/* 响应式适配 */
@media screen and (max-height: 600px) {
  .region-picker-container {
    max-height: calc(90vh - 100rpx);
    margin-bottom: 100rpx;
  }

  .region-list {
    height: 40vh;
    max-height: 400rpx;
  }
}

@media screen and (min-height: 800px) {
  .region-picker-container {
    max-height: calc(75vh - 100rpx);
  }

  .region-list {
    height: 55vh;
    max-height: 600rpx;
  }
}

/* 针对有刘海屏的设备 */
@supports (padding: max(0px)) {
  .region-picker-container {
    padding-bottom: max(env(safe-area-inset-bottom), 20rpx);
  }
}
