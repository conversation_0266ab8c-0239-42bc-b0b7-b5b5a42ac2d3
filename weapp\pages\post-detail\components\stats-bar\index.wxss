.stats-bar-ui {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fafbfc;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
  padding: 24rpx 32rpx;
  margin-bottom: 20rpx;
}

.stats-left {
  display: flex;
  align-items: center;
  gap: 40rpx;
}

.stats-right {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  font-size: 30rpx;
  color: #888;
  transition: all 0.3s ease;
}

.icon {
  width: 38rpx;
  height: 38rpx;
  margin-right: 10rpx;
  transition: all 0.3s ease;
}

/* 点赞相关样式 */
.like-item {
  cursor: pointer;
  padding: 8rpx 12rpx;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.like-item:active {
  transform: scale(0.95);
  background: rgba(255, 107, 107, 0.1);
}

.like-item.liked .like-icon {
  filter: none;
  /* animation: likeAnimation 0.6s ease; */
}

.like-item.liked .like-text {
  color: #ff6b6b;
  font-weight: 600;
}

/* .like-icon.loading {
  animation: rotate 1s linear infinite;
} */

/* 点赞动画 */
@keyframes likeAnimation {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

/* 加载动画 */
@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.stats-bar-ui text {
  font-size: 30rpx;
  color: #888;
}