.profile-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 0 20rpx 0;
  background: #fff;
}
.avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  background: #f5f5f5;
}
.change-avatar-btn {
  margin-top: -40rpx;
  background: rgba(0,0,0,0.5);
  color: #fff;
  border-radius: 40rpx;
  font-size: 24rpx;
  padding: 8rpx 32rpx;
}
.profile-list {
  margin: 20rpx 0;
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}
.logout-btn {
  margin: 60rpx auto 0 auto;
  display: block;
  color: #ff4d4f;
  background: #fff;
  border: 1rpx solid #ff4d4f;
  border-radius: 32rpx;
  width: 80%;
  font-size: 28rpx;
  padding: 16rpx 0;
}
.dialog-mask {
  position: fixed;
  left: 0; right: 0; top: 0; bottom: 0;
  background: rgba(0,0,0,0.4);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.dialog-box {
  width: 80vw;
  background: #fff;
  border-radius: 16rpx;
  padding: 40rpx 32rpx 24rpx 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.08);
}
.dialog-title {
  font-size: 32rpx;
  color: #ff6b6b;
  margin-bottom: 24rpx;
  text-align: center;
}
.dialog-input {
  width: 100%;
  min-height: 60rpx;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 28rpx;
  margin-bottom: 24rpx;
  box-sizing: border-box;
}
.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 24rpx;
}
.dialog-btn {
  min-width: 120rpx;
  padding: 12rpx 0;
  border-radius: 8rpx;
  background: #f5f5f5;
  color: #333;
  font-size: 28rpx;
  border: none;
}
.dialog-btn.primary {
  background: linear-gradient(to right, #ff6b6b, #ff8585);
  color: #fff;
  border: none;
}
.loading-mask {
  position: fixed;
  left: 0; right: 0; top: 0; bottom: 0;
  background: rgba(255,255,255,0.7);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 8rpx solid #eee;
  border-top: 8rpx solid #ff8585;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
} 