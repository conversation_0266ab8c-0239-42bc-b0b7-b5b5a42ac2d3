# WXSS语法错误修复说明

## 错误描述

```
[ WXSS 文件编译错误] 
./pages/local/institution-detail/institution-detail.wxss(241:1): error at token `}`
```

## 问题原因

在第241行存在一个多余的 `}` 符号，导致CSS语法错误。

## 修复前的代码

```css
.status-text.closed {
  background: rgba(244, 67, 54, 0.8);
  color: white;
}
}  /* ❌ 多余的大括号 */
```

## 修复后的代码

```css
.status-text.closed {
  background: rgba(244, 67, 54, 0.8);
  color: white;
}  /* ✅ 正确的语法 */
```

## 修复结果

- ✅ 删除了多余的 `}` 符号
- ✅ WXSS文件编译错误已解决
- ✅ 样式文件语法正确
- ✅ 不影响任何样式效果

## 验证

使用IDE诊断工具验证，确认没有其他语法错误：
```
No diagnostics found.
```

现在机构详情页面的样式文件可以正常编译和运行了。
