<!--添加名片页面-->
<view class="add-card-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
  </view>

  <view wx:else class="form-container">
    <scroll-view class="form-scroll" scroll-y="true">
      <!-- 基本信息 -->
      <view class="form-section">
        <view class="section-title">基本信息</view>

        <view class="form-item">
          <view class="form-label">姓名</view>
          <view class="input-wrapper">
            <image class="input-icon" src="/assets/images/common/user.png" mode="aspectFit"/>
            <input class="form-input" placeholder="请输入人名" value="{{formData.fullName}}" bindinput="onInputChange" data-field="fullName"/>
          </view>
        </view>

        <view class="form-item">
          <view class="form-label">公司名称</view>
          <view class="input-wrapper">
            <image class="input-icon" src="/assets/images/common/company.png" mode="aspectFit"/>
            <input class="form-input" placeholder="请输入公司名称" value="{{formData.company}}" bindinput="onInputChange" data-field="company"/>
          </view>
        </view>

        <view class="form-item">
          <view class="form-label">职位</view>
          <view class="input-wrapper">
            <image class="input-icon" src="/assets/images/common/job.png" mode="aspectFit"/>
            <input class="form-input" placeholder="请输入职位" value="{{formData.jobTitle}}" bindinput="onInputChange" data-field="jobTitle"/>
          </view>
        </view>

        <view class="form-item">
          <view class="form-label">业务简介</view>
          <view class="input-wrapper">
            <image class="input-icon" src="/assets/images/common/desc.png" mode="aspectFit"/>
            <textarea class="form-textarea" placeholder="请输入业务简介" value="{{formData.businessProfile}}" bindinput="onInputChange" data-field="businessProfile"/>
          </view>
        </view>
      </view>

      <!-- 联系信息 -->
      <view class="form-section">
        <view class="section-title">联系信息</view>

        <view class="form-item">
          <view class="form-label">电话</view>
          <view class="input-wrapper">
            <image class="input-icon" src="/assets/images/common/phone.png" mode="aspectFit"/>
            <input class="form-input" placeholder="请输入电话号码" value="{{formData.phone}}" bindinput="onInputChange" data-field="phone"/>
          </view>
        </view>

        <view class="form-item">
          <view class="form-label">地址</view>
          <view class="input-wrapper">
            <image class="input-icon" src="/assets/images/common/location.png" mode="aspectFit"/>
            <input class="form-input" placeholder="请输入地址" value="{{formData.address}}" bindinput="onInputChange" data-field="address"/>
          </view>
        </view>

        <view class="form-item">
          <view class="form-label">微信</view>
          <view class="input-wrapper">
            <image class="input-icon" src="/assets/images/common/wechat.png" mode="aspectFit"/>
            <input class="form-input" placeholder="请输入微信号" value="{{formData.weixin}}" bindinput="onInputChange" data-field="weixin"/>
          </view>
        </view>
      </view>

      <!-- 媒体资源
      <view class="form-section">
        <view class="section-title">媒体资源</view>

        <view class="form-item">
          <view class="form-label">图片URL</view>
          <view class="input-wrapper">
            <image class="input-icon" src="/assets/images/common/image.png" mode="aspectFit"/>
            <input class="form-input" placeholder="请输入图片URL" value="{{formData.images}}" bindinput="onInputChange" data-field="images"/>
          </view>
        </view>

        <view class="form-item">
          <view class="form-label">视频URL</view>
          <view class="input-wrapper">
            <image class="input-icon" src="/assets/images/common/video.png" mode="aspectFit"/>
            <input class="form-input" placeholder="请输入视频URL" value="{{formData.video}}" bindinput="onInputChange" data-field="video"/>
          </view>
        </view>
      </view>
 -->
      <!-- 业务信息
      <view class="form-section">
        <view class="section-title">业务信息</view>

        <view class="form-item">
          <view class="form-label">备注信息</view>
          <view class="input-wrapper">
            <image class="input-icon" src="/assets/images/common/note.png" mode="aspectFit"/>
            <textarea class="form-textarea" placeholder="请输入备注信息" value="{{formData.description}}" bindinput="onInputChange" data-field="description"/>
          </view>
        </view>
      </view>
 -->
      <!-- 隐私设置 -->
      <view class="privacy-section">
        <view class="privacy-item">
          <view class="privacy-left">
            <image class="privacy-icon" src="/assets/images/common/privacy.png" mode="aspectFit"/>
            <text class="privacy-text">是否公开</text>
          </view>
          <switch class="privacy-switch" checked="{{formData.isPublic}}" bindchange="onPrivacyChange" color="#FF7D7D"/>
        </view>
      </view>

      <!-- 底部安全距离 -->
      <view class="bottom-safe-area"></view>
    </scroll-view>
  </view>

  <!-- 底部按钮 -->
  <view class="footer-actions">
    <button class="btn-cancel" bindtap="onCancel">取消</button>
    <button class="btn-confirm" bindtap="onConfirm" disabled="{{submitting}}">
      {{submitting ? '提交中...' : (mode === 'edit' ? '保存修改' : '确认添加')}}
    </button>
  </view>
</view>
