/**
 * 邀请好友工具类
 */

const { request } = require('./request');

class InviteUtil {
  
  /**
   * 生成邀请码
   */
  static async generateInviteCode(userId) {
    try {
      const response = await request({
        url: '/blade-chat-open/invite/generate-code',
        method: 'POST',
        data: { userId }
      });
      
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.msg || '生成邀请码失败');
      }
    } catch (error) {
      console.error('生成邀请码失败:', error);
      throw error;
    }
  }

  /**
   * 处理邀请注册
   */
  static async handleInviteRegister(inviteCode, newUserId) {
    try {
      const response = await request({
        url: '/blade-chat-open/invite/register',
        method: 'POST',
        data: { inviteCode, newUserId }
      });
      
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.msg || '处理邀请注册失败');
      }
    } catch (error) {
      console.error('处理邀请注册失败:', error);
      throw error;
    }
  }

  /**
   * 验证邀请码
   */
  static async validateInviteCode(inviteCode) {
    try {
      const response = await request({
        url: '/blade-chat-open/invite/validate-code',
        method: 'GET',
        data: { inviteCode }
      });
      
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.msg || '验证邀请码失败');
      }
    } catch (error) {
      console.error('验证邀请码失败:', error);
      throw error;
    }
  }

  /**
   * 获取邀请统计
   */
  static async getInviteStats(userId) {
    try {
      const response = await request({
        url: '/blade-chat-open/invite/stats',
        method: 'GET',
        data: { userId }
      });
      
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.msg || '获取邀请统计失败');
      }
    } catch (error) {
      console.error('获取邀请统计失败:', error);
      return {};
    }
  }

  /**
   * 记录邀请分享
   */
  static async recordInviteShare(userId, channel, inviteCode) {
    try {
      const response = await request({
        url: '/blade-chat-open/invite/record-share',
        method: 'POST',
        data: { userId, channel, inviteCode }
      });
      
      return response.success;
    } catch (error) {
      console.error('记录邀请分享失败:', error);
      return false;
    }
  }

  /**
   * 生成邀请链接
   */
  static generateInviteUrl(inviteCode, userId) {
    const appId = getApp().globalData.appId;
    const path = `pages/index/index?inviteCode=${inviteCode}&from=${userId}`;
    return `https://mp.weixin.qq.com/mp/waerrpage?appid=${appId}&type=text&text=${encodeURIComponent('邀请你加入，注册即可获得积分奖励！')}&path=${encodeURIComponent(path)}`;
  }

  /**
   * 生成邀请二维码数据
   */
  static generateQRCodeData(inviteCode, userId) {
    const appId = getApp().globalData.appId;
    const path = `pages/index/index?inviteCode=${inviteCode}&from=${userId}`;
    
    return {
      appId,
      path,
      width: 200,
      scene: `${inviteCode}_${userId}`,
      page: 'pages/index/index',
      check_path: false,
      env_version: 'release'
    };
  }

  /**
   * 分享邀请内容
   */
  static getInviteShareContent(userInfo, inviteCode) {
    const userName = userInfo.nickName || '朋友';
    
    return {
      title: `${userName}邀请你加入，注册即可获得积分奖励！`,
      path: `/pages/index/index?inviteCode=${inviteCode}&from=${userInfo.id}`,
      imageUrl: '/images/invite-share.png'
    };
  }

  /**
   * 检查是否通过邀请进入
   */
  static checkInviteEntry(options) {
    const { inviteCode, from } = options;
    
    if (inviteCode && from) {
      // 存储邀请信息到本地
      wx.setStorageSync('inviteInfo', {
        inviteCode,
        inviterUserId: from,
        entryTime: Date.now()
      });
      
      return {
        hasInvite: true,
        inviteCode,
        inviterUserId: from
      };
    }
    
    return {
      hasInvite: false
    };
  }

  /**
   * 处理用户注册后的邀请逻辑
   */
  static async handleUserRegisterWithInvite(newUserId) {
    try {
      const inviteInfo = wx.getStorageSync('inviteInfo');
      
      if (inviteInfo && inviteInfo.inviteCode) {
        // 处理邀请注册
        const result = await this.handleInviteRegister(inviteInfo.inviteCode, newUserId);
        
        if (result.success) {
          // 显示邀请成功提示
          wx.showModal({
            title: '邀请成功',
            content: `恭喜！邀请人获得了${result.rewardPoints}积分奖励`,
            showCancel: false
          });
          
          // 清除邀请信息
          wx.removeStorageSync('inviteInfo');
          
          return result;
        }
      }
      
      return null;
    } catch (error) {
      console.error('处理邀请注册失败:', error);
      return null;
    }
  }

  /**
   * 获取邀请奖励配置
   */
  static async getInviteConfig() {
    try {
      const response = await request({
        url: '/blade-chat-open/invite/config',
        method: 'GET'
      });
      
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.msg || '获取邀请配置失败');
      }
    } catch (error) {
      console.error('获取邀请配置失败:', error);
      return {
        registerReward: 10,
        firstPostReward: 5
      };
    }
  }

  /**
   * 显示邀请成功动画
   */
  static showInviteSuccessAnimation() {
    wx.showToast({
      title: '邀请成功！',
      icon: 'success',
      duration: 2000
    });
  }

  /**
   * 复制邀请码到剪贴板
   */
  static copyInviteCode(inviteCode) {
    wx.setClipboardData({
      data: inviteCode,
      success: () => {
        wx.showToast({
          title: '邀请码已复制',
          icon: 'success'
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        });
      }
    });
  }

  /**
   * 保存邀请二维码到相册
   */
  static async saveQRCodeToAlbum(canvasId, component) {
    try {
      // 检查相册权限
      const authResult = await this.checkPhotoAlbumAuth();
      if (!authResult) {
        return false;
      }

      // 将canvas转为临时文件
      const tempFilePath = await new Promise((resolve, reject) => {
        wx.canvasToTempFilePath({
          canvasId,
          success: resolve,
          fail: reject
        }, component);
      });

      // 保存到相册
      await new Promise((resolve, reject) => {
        wx.saveImageToPhotosAlbum({
          filePath: tempFilePath.tempFilePath,
          success: resolve,
          fail: reject
        });
      });

      wx.showToast({
        title: '保存成功',
        icon: 'success'
      });

      return true;
    } catch (error) {
      console.error('保存二维码失败:', error);
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
      return false;
    }
  }

  /**
   * 检查相册权限
   */
  static async checkPhotoAlbumAuth() {
    try {
      const authSetting = await new Promise((resolve) => {
        wx.getSetting({
          success: resolve,
          fail: () => resolve({ authSetting: {} })
        });
      });

      if (authSetting.authSetting['scope.writePhotosAlbum'] === false) {
        // 用户之前拒绝了权限，引导用户去设置
        const modalResult = await new Promise((resolve) => {
          wx.showModal({
            title: '提示',
            content: '需要授权访问相册才能保存图片',
            confirmText: '去授权',
            success: resolve,
            fail: () => resolve({ confirm: false })
          });
        });

        if (modalResult.confirm) {
          wx.openSetting();
        }
        return false;
      }

      return true;
    } catch (error) {
      console.error('检查相册权限失败:', error);
      return false;
    }
  }
}

module.exports = InviteUtil;
