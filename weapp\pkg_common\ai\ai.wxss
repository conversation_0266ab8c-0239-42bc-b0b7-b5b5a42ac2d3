/* ai.wxss */
.container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f6f7;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}

/* 自定义导航栏 */
.custom-nav {
  width: 100%;
  background: #fff;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
}

.nav-content {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: #fff;
  padding: 0 32rpx;
}

.logo {
  width: 160rpx;
  height: 60rpx;
  display: block;
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.menu-btn, .target-btn {
  font-size: 40rpx;
  color: #333;
}

/* 日报区域样式 */
.daily-news {
  background: #fff;
  margin: 16rpx;
  margin-bottom: 24rpx;
  padding: 24rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  height: auto;
  min-height: 200rpx;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding: 0 4rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.date {
  display: flex;
  align-items: center;
}

.date-text {
  font-size: 26rpx;
  color: #666;
}

.weekday {
  margin-left: 6rpx;
  font-size: 24rpx;
  color: #999;
  background: #f5f6f7;
  padding: 2rpx 12rpx;
  border-radius: 6rpx;
}

/* 新闻列表样式 */
.news-list {
  display: flex;
  flex-direction: column;
  padding: 0 4rpx;
}

.news-item {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  padding: 16rpx 0;
}

.news-item:first-child {
  padding-top: 0;
}

.news-item:last-child {
  padding-bottom: 0;
}

.news-index {
  font-size: 24rpx;
  color: #999;
  min-width: 24rpx;
  font-weight: 400;
}

.news-content {
  font-size: 24rpx;
  color: #333;
  line-height: 1.4;
  flex: 1;
  font-weight: 400;
}

/* 输入框区域样式 */
.input-section {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 16rpx 24rpx;
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
  background: #f5f6f7;
  z-index: 100;
}

.input-box {
  display: flex;
  align-items: center;
  background: #fff;
  padding: 16rpx 20rpx;
  border-radius: 36rpx;
  gap: 12rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.input-box.voice-mode {
  background: #f5f6f7;
}

.voice-icon {
  width: 40rpx;
  height: 40rpx;
  opacity: 0.7;
  transition: all 0.3s;
}

.voice-icon.active {
  opacity: 1;
  color: #007AFF;
}

.chat-input {
  flex: 1;
  font-size: 28rpx;
  height: 40rpx;
  min-height: 40rpx;
}

.right-tools {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-left: 8rpx;
  padding-left: 16rpx;
  border-left: 1rpx solid rgba(0, 0, 0, 0.08);
}

.plus-icon, .send-icon {
  width: 48rpx;
  height: 48rpx;
  opacity: 0.7;
}

/* 语音输入按钮 */
.voice-input {
  flex: 1;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333;
  background: #fff;
  border-radius: 30rpx;
  transition: all 0.3s;
}

.voice-input:active {
  background: #e6e6e6;
}

/* 底部按钮区域 */
.bottom-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 24rpx;
  padding: 0 8rpx;
}

.action-button {
  font-size: 24rpx;
  color: #666;
  background: none;
  box-shadow: none;
  padding: 8rpx 0;
}

.action-button:active {
  opacity: 0.6;
}

.refresh-button {
  color: #4C6FFF;
}

/* 信息展示区域 */
.info-section {
  padding: 24rpx 32rpx;
  margin-bottom: 8rpx;
}

.info-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.info-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.info-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
} 