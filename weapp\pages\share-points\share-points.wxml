<!--分享积分与邀请好友页面-->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">分享积分与邀请好友</text>
    <text class="page-subtitle">分享内容获积分，邀请好友得奖励</text>
  </view>

  <!-- 积分统计卡片 -->
  <view class="stats-card">
    <view class="stats-header">
      <text class="stats-title">积分统计</text>
      <text class="refresh-btn" bindtap="refreshStats">刷新</text>
    </view>

    <view class="stats-content">
      <view class="stat-item">
        <text class="stat-value">{{stats.totalSharePoints || 0}}</text>
        <text class="stat-label">总分享积分</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{stats.totalInvitePoints || 0}}</text>
        <text class="stat-label">邀请积分</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{stats.inviteCount || 0}}</text>
        <text class="stat-label">邀请人数</text>
      </view>
    </view>
  </view>

  <!-- 邀请好友卡片 -->
  <view class="invite-card">
    <view class="invite-header">
      <view class="invite-title-section">
        <text class="invite-title">邀请好友注册</text>
        <text class="invite-reward">每邀请1人注册奖励{{inviteConfig.registerReward || 10}}积分</text>
      </view>
      <view class="invite-icon">👥</view>
    </view>

    <view class="invite-content">
      <view class="invite-stats">
        <view class="invite-stat-item">
          <text class="invite-stat-value">{{inviteStats.todayInvite || 0}}</text>
          <text class="invite-stat-label">今日邀请</text>
        </view>
        <view class="invite-stat-item">
          <text class="invite-stat-value">{{inviteStats.monthInvite || 0}}</text>
          <text class="invite-stat-label">本月邀请</text>
        </view>
        <view class="invite-stat-item">
          <text class="invite-stat-value">{{inviteStats.totalInvite || 0}}</text>
          <text class="invite-stat-label">累计邀请</text>
        </view>
      </view>

      <view class="invite-actions">
        <button class="invite-btn primary" bindtap="generateInviteQRCode">
          <text class="btn-icon">📱</text>
          <text class="btn-text">生成邀请二维码</text>
        </button>
        <button class="invite-btn secondary" bindtap="shareInviteLink">
          <text class="btn-icon">🔗</text>
          <text class="btn-text">分享邀请链接</text>
        </button>
      </view>
    </view>
  </view>

  <!-- 邀请记录列表 -->
  <view class="invite-records-card" wx:if="{{showInviteRecords}}">
    <view class="invite-records-header">
      <text class="invite-records-title">最近邀请记录</text>
      <text class="view-all-btn" bindtap="viewAllInviteRecords">查看全部</text>
    </view>

    <view class="invite-records-list">
      <view class="invite-record-item" wx:for="{{recentInviteRecords}}" wx:key="id">
        <view class="invite-record-avatar">
          <image class="avatar-img" src="{{item.inviteeAvatar || '/images/default-avatar.png'}}" mode="aspectFill"></image>
        </view>
        <view class="invite-record-info">
          <text class="invitee-name">{{item.inviteeName || '新用户'}}</text>
          <text class="invite-time">{{item.registerTime}}</text>
        </view>
        <view class="invite-record-reward">
          <text class="reward-points">+{{item.rewardPoints}}</text>
          <text class="reward-label">积分</text>
        </view>
      </view>

      <view class="empty-invite-records" wx:if="{{recentInviteRecords.length === 0}}">
        <text class="empty-text">暂无邀请记录</text>
        <text class="empty-tip">快去邀请好友注册吧！</text>
      </view>
    </view>
  </view>

  <!-- 今日分享情况 -->
  <view class="today-card">
    <view class="today-header">
      <text class="today-title">今日分享情况</text>
    </view>

    <view class="today-content">
      <view class="today-item" wx:for="{{todayStats}}" wx:key="type">
        <view class="today-type">
          <text class="type-name">{{item.typeName}}</text>
          <text class="type-points">+{{item.points}}积分/次</text>
        </view>
        <view class="today-progress">
          <text class="progress-text">{{item.count}}/{{item.limit}}</text>
          <view class="progress-bar">
            <view class="progress-fill" style="width: {{item.count / item.limit * 100}}%"></view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 筛选器 -->
  <view class="filter-bar">
    <view class="filter-tabs">
      <text class="filter-tab {{currentFilter === 'all' ? 'active' : ''}}"
            data-filter="all" bindtap="switchFilter">全部</text>
      <text class="filter-tab {{currentFilter === 'share' ? 'active' : ''}}"
            data-filter="share" bindtap="switchFilter">分享积分</text>
      <text class="filter-tab {{currentFilter === 'invite' ? 'active' : ''}}"
            data-filter="invite" bindtap="switchFilter">邀请积分</text>
      <text class="filter-tab {{currentFilter === 'post' ? 'active' : ''}}"
            data-filter="post" bindtap="switchFilter">帖子分享</text>
    </view>
  </view>

  <!-- 积分记录列表 -->
  <view class="records-list">
    <view class="record-item" wx:for="{{records}}" wx:key="id">
      <view class="record-icon">
        <text class="icon-share" wx:if="{{item.type === 'share'}}">📤</text>
        <text class="icon-invite" wx:if="{{item.type === 'invite'}}">👥</text>
        <text class="icon-default" wx:else>💰</text>
      </view>

      <view class="record-content">
        <view class="record-header">
          <text class="record-title">{{item.description}}</text>
          <text class="record-points">+{{item.points}}</text>
        </view>

        <view class="record-details">
          <text class="record-time">{{item.operateTime}}</text>
          <text class="record-type">{{item.businessType}}</text>
        </view>

        <view class="record-remark" wx:if="{{item.remark}}">
          <text class="remark-text">{{item.remark}}</text>
        </view>

        <!-- 邀请记录特殊信息 -->
        <view class="invite-info" wx:if="{{item.type === 'invite' && item.inviteeInfo}}">
          <view class="invitee-detail">
            <image class="invitee-avatar" src="{{item.inviteeInfo.avatar || '/images/default-avatar.png'}}" mode="aspectFill"></image>
            <text class="invitee-name">邀请了 {{item.inviteeInfo.name || '新用户'}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{records.length === 0 && !loading}}">
      <image class="empty-icon" src="/images/empty-share.png" mode="aspectFit"></image>
      <text class="empty-text">{{currentFilter === 'invite' ? '暂无邀请积分记录' : '暂无分享积分记录'}}</text>
      <text class="empty-tip">{{currentFilter === 'invite' ? '快去邀请好友注册吧！' : '快去分享内容获得积分吧！'}}</text>
    </view>

    <!-- 加载状态 -->
    <view class="loading-state" wx:if="{{loading}}">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMore && !loading}}" bindtap="loadMore">
      <text class="load-more-text">加载更多</text>
    </view>

    <!-- 没有更多 -->
    <view class="no-more" wx:if="{{!hasMore && records.length > 0}}">
      <text class="no-more-text">没有更多记录了</text>
    </view>
  </view>

  <!-- 积分规则说明 -->
  <view class="rules-card">
    <view class="rules-header" bindtap="toggleRules">
      <text class="rules-title">积分获取规则</text>
      <text class="rules-toggle">{{showRules ? '收起' : '展开'}}</text>
    </view>

    <view class="rules-content" wx:if="{{showRules}}">
      <!-- 分享积分规则 -->
      <view class="rule-section">
        <text class="rule-section-title">分享积分</text>
        <view class="rule-item" wx:for="{{shareConfig.sharePoints}}" wx:key="type">
          <text class="rule-type">{{shareConfig.shareTypes[item.type] || item.type}}</text>
          <text class="rule-points">+{{item.points}}积分</text>
          <text class="rule-limit">每日最多{{shareConfig.dailyLimit[item.type] || 0}}次</text>
        </view>
      </view>

      <!-- 邀请积分规则 -->
      <view class="rule-section">
        <text class="rule-section-title">邀请积分</text>
        <view class="rule-item">
          <text class="rule-type">邀请好友注册</text>
          <text class="rule-points">+{{inviteConfig.registerReward || 10}}积分</text>
          <text class="rule-limit">每人仅限一次</text>
        </view>
        <view class="rule-item" wx:if="{{inviteConfig.firstPostReward}}">
          <text class="rule-type">被邀请人首次发帖</text>
          <text class="rule-points">+{{inviteConfig.firstPostReward}}积分</text>
          <text class="rule-limit">每人仅限一次</text>
        </view>
      </view>

      <view class="rule-note">
        <text class="note-text">• 同一内容每天只能获得一次分享积分</text>
        <text class="note-text">• 邀请积分在好友成功注册后立即发放</text>
        <text class="note-text">• 恶意刷积分行为将被系统检测并扣除积分</text>
      </view>
    </view>
  </view>

  <!-- 快捷操作栏 -->
  <view class="quick-actions-bar">
    <button class="quick-action-btn primary" bindtap="generateInviteQRCode">
      <text class="btn-icon">📱</text>
      <text class="btn-text">生成邀请码</text>
    </button>
    <button class="quick-action-btn secondary" bindtap="shareInviteLink">
      <text class="btn-icon">🔗</text>
      <text class="btn-text">分享链接</text>
    </button>
    <button class="quick-action-btn tertiary" bindtap="quickShareApp">
      <text class="btn-icon">📤</text>
      <text class="btn-text">分享小程序</text>
    </button>
  </view>

  <!-- 邀请二维码弹窗 -->
  <view class="qrcode-modal" wx:if="{{showQRCodeModal}}" bindtap="hideQRCodeModal">
    <view class="qrcode-content" catchtap="stopPropagation">
      <view class="qrcode-header">
        <text class="qrcode-title">邀请好友注册</text>
        <text class="qrcode-close" bindtap="hideQRCodeModal">✕</text>
      </view>

      <view class="qrcode-body">
        <view class="qrcode-container">
          <canvas class="qrcode-canvas" canvas-id="inviteQRCode" style="width: {{qrcodeSize}}px; height: {{qrcodeSize}}px;"></canvas>
        </view>

        <view class="qrcode-info">
          <text class="qrcode-tip">扫描二维码或长按保存分享</text>
          <text class="invite-code">邀请码：{{inviteCode}}</text>
        </view>

        <view class="qrcode-actions">
          <button class="qrcode-btn save" bindtap="saveQRCodeToAlbum">保存到相册</button>
          <button class="qrcode-btn share" bindtap="shareQRCodeToFriend">分享给好友</button>
        </view>
      </view>
    </view>
  </view>
</view>
