# 本地页面位置信息传参修复说明

## 问题描述

页面打开时没有传递经纬度信息，附近查询也没有传递位置参数。从请求URL可以看出：
```
http://localhost/blade-chat-open/institution/page?current=1&size=10&sortType=latest
```

缺少了 `latitude`、`longitude` 等位置参数。

## 问题原因

原来的初始化流程中，位置获取是在数据加载之后异步执行的：

```javascript
// 原来的问题代码
async initPage() {
  // 先加载数据
  await this.loadInstitutionList(true);
  
  // 后获取位置（异步，不等待）
  this.getCurrentLocation();
}
```

这导致第一次请求时 `userLocation` 为 `null`，所以没有传递位置参数。

## 修复方案

### 1. 修改初始化流程

**修复前：**
```javascript
async initPage() {
  try {
    // 并行加载基础数据
    await Promise.all([
      this.loadBanners(),
      this.loadInstitutionTypes()
    ]);

    // 初始化时使用 isRefresh=true 来确保能够正常加载
    await this.loadInstitutionList(true);

    // 异步加载位置信息，不阻塞主流程
    this.getCurrentLocation();
  } catch (error) {
    this.handleInitError(error);
  }
}
```

**修复后：**
```javascript
async initPage() {
  try {
    // 并行加载基础数据和位置信息
    await Promise.all([
      this.loadBanners(),
      this.loadInstitutionTypes(),
      this.getCurrentLocation() // 确保位置信息在数据加载前获取
    ]);

    // 位置信息获取完成后再加载机构列表
    await this.loadInstitutionList(true);
  } catch (error) {
    this.handleInitError(error);
  }
}
```

### 2. 优化位置获取方法

**修复前：**
```javascript
async getCurrentLocation() {
  try {
    // 位置获取逻辑
  } catch (error) {
    // 错误处理，但没有返回值
  }
}
```

**修复后：**
```javascript
async getCurrentLocation() {
  try {
    const location = await this.getLocation();
    // 保存位置信息
    return location;
  } catch (error) {
    console.log('位置获取失败:', error.message);
    // 位置获取失败时返回null，不阻塞后续流程
    return null;
  }
}
```

### 3. 增强Tab切换逻辑

**新增功能：**
```javascript
// 主Tab切换（最新/附近）
switchMainTab(e) {
  const index = e.currentTarget.dataset.index;
  if (index === this.data.currentTab) return;

  this.setData({ currentTab: index });
  
  // 如果切换到附近查询但没有位置信息，先获取位置
  if (index === 1 && !this.data.userLocation) {
    this.getCurrentLocationAndRefresh();
  } else {
    this.refreshData();
  }
}

// 获取位置并刷新数据
async getCurrentLocationAndRefresh() {
  try {
    await this.getCurrentLocation();
    this.refreshData();
  } catch (error) {
    console.error('获取位置失败:', error);
    // 即使位置获取失败也要刷新数据
    this.refreshData();
  }
}
```

## 修复效果

### 修复前的请求参数
```
最新查询：
{
  current: 1,
  size: 10,
  sortType: 'latest'
  // ❌ 缺少 latitude、longitude
}

附近查询：
{
  current: 1,
  size: 10,
  sortType: 'nearby'
  // ❌ 缺少 latitude、longitude、searchLatitude、searchLongitude
}
```

### 修复后的请求参数
```
最新查询：
{
  current: 1,
  size: 10,
  sortType: 'latest',
  latitude: 用户纬度,     // ✅ 传递用户位置
  longitude: 用户经度    // ✅ 传递用户位置
}

附近查询：
{
  current: 1,
  size: 10,
  sortType: 'nearby',
  latitude: 用户纬度,           // ✅ 传递用户位置
  longitude: 用户经度,          // ✅ 传递用户位置
  searchLatitude: 用户纬度,     // ✅ 传递查询位置
  searchLongitude: 用户经度     // ✅ 传递查询位置
}
```

## 流程优化

### 页面初始化流程
1. **并行执行**：
   - 加载轮播图数据
   - 加载机构分类数据
   - **获取用户位置** ⭐

2. **等待完成**：确保位置信息获取完成

3. **加载数据**：使用完整的位置参数加载机构列表

### Tab切换流程
1. **检查位置**：切换到附近查询时检查是否有位置信息
2. **补充获取**：如果没有位置信息，先获取位置再刷新数据
3. **正常刷新**：如果有位置信息，直接刷新数据

## 容错处理

### 位置获取失败的处理
1. **不阻塞流程**：位置获取失败不影响页面正常加载
2. **降级处理**：没有位置信息时仍然可以进行基础查询
3. **用户提示**：显示"位置获取失败"提示用户

### 网络异常处理
1. **重试机制**：Tab切换时可以重新尝试获取位置
2. **用户操作**：用户可以手动切换Tab触发位置重新获取

## 测试验证

### 测试场景
1. **首次打开页面**：
   - ✅ 应该传递位置参数
   - ✅ 最新查询不传 searchLatitude/searchLongitude
   - ✅ 附近查询传递 searchLatitude/searchLongitude

2. **Tab切换**：
   - ✅ 切换到附近时应该有位置参数
   - ✅ 没有位置时应该先获取位置

3. **位置权限拒绝**：
   - ✅ 页面仍然可以正常加载
   - ✅ 显示位置获取失败提示

### 验证方法
1. 查看网络请求参数是否包含位置信息
2. 测试位置权限拒绝的情况
3. 测试网络异常情况下的表现

## 注意事项

1. **性能考虑**：位置获取可能需要时间，但不会阻塞页面渲染
2. **用户体验**：即使位置获取失败，页面功能仍然可用
3. **数据准确性**：有位置信息时查询结果更准确
4. **隐私保护**：遵循微信小程序位置权限规范

## 总结

通过修改初始化流程，确保位置信息在数据加载前获取完成，解决了页面打开时没有传递经纬度信息的问题。同时增强了容错处理和用户体验，确保在各种情况下页面都能正常工作。
