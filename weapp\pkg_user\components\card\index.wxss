.card {
  background: #fff;
  border-radius: 24rpx;
  margin: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06);
}

.card-shadow {
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 24rpx 24rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.card-extra {
  font-size: 26rpx;
  color: #ff6b6b;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background: rgba(255, 107, 107, 0.1);
  transition: all 0.3s ease;
}

.card-extra:active {
  background: rgba(255, 107, 107, 0.2);
  transform: scale(0.95);
}

.card-content {
  padding: 24rpx;
}

.card-footer {
  padding: 24rpx;
  border-top: 1rpx solid #f0f0f0;
} 