# 机构收藏功能修复说明

## 问题描述

机构详情页面的收藏功能报错，无法正常收藏/取消收藏机构。

## 问题原因分析

### 1. 接口配置问题
- `institutionDetailStore.js` 中使用了 `API.favorite` 接口
- 但在 `api.js` 配置文件中，机构相关的API配置缺少 `favorite` 字段
- 导致接口请求时 URL 为 `undefined`，引发请求错误

### 2. 接口不统一问题
- 后端已经提供了统一的数据操作接口：`/blade-chat/data-operate/toggle-favorite/{type}/{id}`
- 但前端机构收藏功能没有使用这个统一接口
- 而是尝试使用不存在的机构专用收藏接口

## 修复方案

### 1. 引入统一的数据操作Store

在 `institutionDetailStore.js` 中引入统一的数据操作功能：

```javascript
const { toggleFavorite: toggleFavoriteData, DATA_TYPES } = require('./dataOperateStore.js');
```

### 2. 修改收藏功能实现

将原来的收藏功能改为使用统一的数据操作接口：

```javascript
const toggleInstitutionFavorite = async (institutionId, isFavorite) => {
  try {
    if (!institutionId) {
      throw new Error('机构ID不能为空');
    }

    // 使用统一的数据操作接口
    const result = await toggleFavoriteData(institutionId, DATA_TYPES.INSTITUTION);
    
    if (result.success) {
      return result.isFavorited; // 返回新的收藏状态
    } else {
      throw new Error(result.message || '收藏操作失败');
    }
  } catch (error) {
    console.error('收藏操作失败:', error);
    throw error;
  }
};
```

### 3. 完善机构API配置

在 `api.js` 中补充机构相关的API配置：

```javascript
institution: {
  // 获取机构列表（分页）
  page: '/blade-chat-open/institution/page',    
  // 获取机构详情
  detail: '/blade-chat-open/institution/detail',
  // 获取机构服务列表
  services: '/blade-chat-open/institution/services',
  // 获取机构评价列表
  reviews: '/blade-chat-open/institution/reviews',
  // 预约服务
  book: '/blade-chat/institution/book',
  // 记录浏览
  view: '/blade-chat/institution/view',
  // 其他现有配置...
}
```

## 后端接口说明

### 统一数据操作接口

**接口地址：** `POST /blade-chat/data-operate/toggle-favorite/{type}/{id}`

**参数说明：**
- `type`: 数据类型，机构使用 `institution`
- `id`: 机构ID

**请求示例：**
```http
POST /blade-chat/data-operate/toggle-favorite/institution/123
```

**响应格式：**
```json
{
  "code": 200,
  "success": true,
  "data": {
    "success": true,
    "currentState": true,
    "action": "favorite",
    "message": "收藏成功",
    "targetId": 123,
    "type": "institution"
  }
}
```

**响应字段说明：**
- `currentState`: 当前收藏状态（true=已收藏，false=未收藏）
- `action`: 执行的操作（favorite=收藏，unfavorite=取消收藏）
- `message`: 操作结果消息

## 数据类型常量

在 `dataOperateStore.js` 中定义了数据类型常量：

```javascript
const DATA_TYPES = {
  POST: 'post',           // 帖子
  INSTITUTION: 'institution',  // 机构
  COMMENT: 'comment'      // 评论
};
```

## 修复效果

### 1. 收藏功能正常工作
- 用户可以正常收藏/取消收藏机构
- 收藏状态会实时更新
- 显示正确的操作反馈消息

### 2. 接口调用统一
- 使用统一的数据操作接口
- 与帖子、评论等其他功能保持一致
- 便于后续维护和扩展

### 3. 错误处理完善
- 提供详细的错误信息
- 网络异常时有友好的提示
- 控制台输出调试信息

## 测试建议

1. **基本功能测试**
   - 打开机构详情页面
   - 点击收藏按钮，检查是否成功收藏
   - 再次点击，检查是否成功取消收藏

2. **状态同步测试**
   - 收藏后刷新页面，检查收藏状态是否正确显示
   - 在其他页面查看收藏列表，确认机构已被收藏

3. **错误处理测试**
   - 断网情况下测试收藏功能
   - 检查错误提示是否友好
   - 查看控制台是否有详细的错误日志

4. **兼容性测试**
   - 确保修改不影响其他功能
   - 测试帖子收藏功能是否正常
   - 验证其他数据操作功能

## 技术要点

1. **统一接口设计**：使用统一的数据操作接口，避免重复开发
2. **类型安全**：使用常量定义数据类型，避免字符串拼写错误
3. **错误处理**：完善的异常捕获和用户友好的错误提示
4. **向后兼容**：保持原有函数签名，不影响调用方代码

## 相关文件

- `weapp/stores/institutionDetailStore.js` - 机构详情数据管理
- `weapp/stores/dataOperateStore.js` - 统一数据操作管理
- `weapp/config/api.js` - API接口配置
- `backend/src/main/java/org/springblade/miniapp/controller/WeChatDataOperateController.java` - 后端控制器
