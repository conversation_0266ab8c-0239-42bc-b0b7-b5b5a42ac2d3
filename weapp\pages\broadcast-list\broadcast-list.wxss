/* 广播列表页面样式 */
.broadcast-list-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

/* 筛选标签 */
.filter-tabs {
  display: flex;
  background-color: #fff;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 10;
}

.filter-tab {
  flex: 1;
  text-align: center;
  padding: 16rpx 20rpx;
  font-size: 26rpx;
  color: #666;
  border-radius: 20rpx;
  margin: 0 8rpx;
  transition: all 0.3s ease;
}

.filter-tab.active {
  background: linear-gradient(135deg, #ff6b6b, #ff8585);
  color: #fff;
  font-weight: 600;
}

/* 分类tab */
.filter-tabs-scroll {
  width: 100%;
  white-space: nowrap;
}

.filter-tabs {
  display: flex;
  padding: 18rpx 8rpx;
}

.filter-tab {
  padding: 10rpx 25rpx;
  font-size: 28rpx;
  color: #666;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  white-space: nowrap;
  transition: all 0.3s ease;
  text-align: center;
  flex-shrink: 0;
}

.filter-tab.active {
  color: #fff;
  background-color: #ff6b6b;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
}

/* 通知列表 */
.notice-list {
  box-sizing: border-box;
  flex: 1;
  padding: 20rpx;
}

.notice-item {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.notice-item:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
}

/* 通知头部 */
.notice-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.notice-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  width: 48rpx;
  text-align: center;
}

.notice-meta {
  flex: 1;
  min-width: 0;
}

.notice-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.notice-time {
  font-size: 22rpx;
  color: #999;
}

.notice-type {
  background-color: #f0f0f0;
  color: #666;
  font-size: 20rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  white-space: nowrap;
}

/* 通知内容 */
.notice-content {
  margin-bottom: 16rpx;
}

.notice-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 通知底部 */
.notice-footer {
  display: flex;
  align-items: center;
  padding-top: 16rpx;
  border-top: 1rpx solid #f0f0f0;
}

.author-avatar {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  margin-right: 12rpx;
}

.author-name {
  font-size: 24rpx;
  color: #666;
  margin-right: 16rpx;
}

.notice-category {
  margin-left: auto;
}

.category-text {
  font-size: 22rpx;
  color: #ff6b6b;
  background-color: rgba(255, 107, 107, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #ff6b6b;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 16rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 26rpx;
  color: #999;
}

/* 没有更多数据 */
.no-more-container {
  text-align: center;
  padding: 40rpx;
}

.no-more-text {
  font-size: 24rpx;
  color: #ccc;
}

/* 空状态 */
.empty-container {
  text-align: center;
  padding: 120rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 32rpx;
  color: #999;
  display: block;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #ccc;
  line-height: 1.5;
}
