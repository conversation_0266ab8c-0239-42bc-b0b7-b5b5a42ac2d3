/* pkg_user/pages/business-card/business-card.wxss */

.card-container {
  min-height: 100vh;
  background: #f5f5f5;
}

/* 顶部搜索栏 */
.search-header {
  background: #fff;
  padding: 20rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.search-box {
  flex: 1;
  display: flex;
  align-items: center;
  background: #f8f8f8;
  border-radius: 25rpx;
  padding: 0 20rpx;
}

.search-input {
  flex: 1;
  height: 70rpx;
  font-size: 28rpx;
  color: #333;
}

.search-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-left: 10rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-left: 12rpx;
  flex-shrink: 0; /* 防止图标缩小 */
}
.manage-btn {
  padding: 16rpx 24rpx;
  background: linear-gradient(135deg, #ff6b6b, #ff8585);
  color: #fff;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.manage-text {
  color: #fff;
  font-size: 28rpx;
  font-weight: 500;
}

/* 分类筛选 */
.category-filter {
  background: #fff;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.category-scroll {
  white-space: nowrap;
}

.category-list {
  display: flex;
  padding: 0 20rpx;
}

.category-item {
  flex-shrink: 0;
  padding: 12rpx 24rpx;
  margin-right: 20rpx;
  background: #f8f8f8;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s ease;
}

.category-item.active {
  background: #ff6b6b;
  color: #fff;
  transform: scale(1.05);
}

/* 名片列表 */
.card-list {
  padding: 20rpx;
}

.card-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: flex-start;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  overflow: hidden;
}

.card-item:active {
  transform: scale(0.98);
}

.card-item.expanded {
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  border: 2rpx solid #007aff;
}

/* 名片头像 */
.card-avatar {
  width: 100rpx;
  height: 100rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 3rpx solid #f0f0f0;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ff6b6b, #ff8585);
  color: #fff;
  font-size: 36rpx;
  font-weight: bold;
  border-radius: 50%;
}

/* 名片信息 */
.card-info {
  flex: 1;
  min-width: 0;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.card-left {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.card-right {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.card-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-right: 12rpx;
}

.card-remark {
  font-size: 24rpx;
  color: #999;
  background: #f0f0f0;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  max-width: 120rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-category {
  padding: 4rpx 12rpx;
  background: #e3f2fd;
  color: #1976d2;
  font-size: 22rpx;
  border-radius: 12rpx;
}

/* 展开指示器 */
.expand-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32rpx;
  height: 32rpx;
  transition: transform 0.3s ease;
}

.expand-indicator.expanded {
  transform: rotate(180deg);
}

.expand-arrow {
  font-size: 20rpx;
  color: #666;
}

.card-details {
  margin-bottom: 16rpx;
}

.card-position {
  font-size: 28rpx;
  color: #666;
  margin-right: 20rpx;
}

.card-company {
  font-size: 26rpx;
  color: #999;
}

.card-contact {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.contact-phone,
.contact-email {
  font-size: 24rpx;
  color: #888;
  display: flex;
  align-items: center;
}

/* 名片操作 */
.card-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: space-between;
  height: 100rpx;
  margin-left: 20rpx;
}

.action-time {
  font-size: 22rpx;
  color: #bbb;
}

.action-arrow {
  font-size: 32rpx;
  color: #ddd;
  font-weight: 300;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #ff6b6b;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-spinner.small {
  width: 40rpx;
  height: 40rpx;
  border-width: 3rpx;
  margin-bottom: 0;
  margin-right: 12rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.empty-tip {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 40rpx;
  text-align: center;
}

.empty-btn {
  padding: 16rpx 32rpx;
  background: linear-gradient(135deg, #ff6b6b, #ff8585);
  color: #fff;
  font-size: 28rpx;
  border-radius: 25rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
}

/* 加载更多 */
.load-more {
  display: flex;
  justify-content: center;
  padding: 40rpx 0;
}

.loading-more {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #666;
}

.load-more-text {
  font-size: 26rpx;
  color: #999;
}

.no-more {
  display: flex;
  justify-content: center;
  padding: 40rpx 0;
  font-size: 26rpx;
  color: #999;
}

/* 管理模式样式 */
.manage-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.edit-btn {
  background: rgba(0, 122, 255, 0.1);
}

.delete-btn {
  background: rgba(255, 59, 48, 0.1);
}

.action-icon {
  font-size: 28rpx;
}

.card-remark {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
}

.card-meta {
  margin-top: 12rpx;
}

.favorite-time {
  font-size: 24rpx;
  color: #999;
}

/* 编辑弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.edit-modal {
  width: 640rpx;
  max-height: 80vh;
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #999;
}

.modal-content {
  max-height: 60vh;
  overflow-y: auto;
  padding: 32rpx;
}

.card-preview {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
}

.preview-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 24rpx;
  background: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-image {
  width: 100%;
  height: 100%;
}

.preview-placeholder {
  font-size: 32rpx;
  color: #666;
  font-weight: 600;
}

.preview-info {
  flex: 1;
}

.preview-name {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.preview-position {
  font-size: 26rpx;
  color: #666;
}

.form-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.category-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 20rpx;
}

.category-option {
  padding: 16rpx 24rpx;
  background: #f5f5f5;
  color: #666;
  font-size: 26rpx;
  border-radius: 20rpx;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
}

.category-option.active {
  background: #007aff;
  color: white;
  border-color: #007aff;
}

.category-option.add-category {
  background: #e8f5e8;
  color: #4CAF50;
  border: 2rpx dashed #4CAF50;
}

.add-category-input {
  margin-top: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.category-input {
  width: 100%;
  padding: 16rpx;
  background: white;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  margin-bottom: 16rpx;
}

.input-actions {
  display: flex;
  gap: 16rpx;
  justify-content: flex-end;
}

.input-btn {
  padding: 12rpx 24rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  border: none;
}

.input-btn.cancel {
  background: #f5f5f5;
  color: #666;
}

.input-btn.confirm {
  background: #4CAF50;
  color: white;
}

.remark-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 16rpx;
  background: #f8f9fa;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  line-height: 1.5;
  box-sizing: border-box;
}

.input-count {
  display: block;
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

.modal-footer {
  display: flex;
  gap: 20rpx;
  padding: 32rpx;
  border-top: 1rpx solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 30rpx;
  border: none;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background: #007aff;
  color: white;
}

/* 卡片展开内容样式 */
.card-expanded-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease, opacity 0.3s ease, padding 0.3s ease;
  opacity: 0;
  margin-top: 0;
}

.card-expanded-content.show {
  max-height: 1000rpx;
  opacity: 1;
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 2rpx solid #f0f0f0;
}

/* 展开内容区块 */
.expanded-section {
  margin-bottom: 24rpx;
}

.expanded-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
  display: block;
}

/* 联系方式列表 */
.contact-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 12rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.contact-icon {
  font-size: 28rpx;
  width: 32rpx;
  text-align: center;
}

.contact-text {
  font-size: 26rpx;
  color: #333;
  flex: 1;
  word-break: break-all;
}

/* 商业简介 */
.business-profile {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  background: #f8f9fa;
  padding: 16rpx;
  border-radius: 12rpx;
  border-left: 4rpx solid #007aff;
}

/* 备注内容 */
.remark-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  background: #fff3cd;
  padding: 16rpx;
  border-radius: 12rpx;
  border-left: 4rpx solid #ffc107;
}

/* 展开操作按钮 */
.expanded-actions {
  display: flex;
  gap: 16rpx;
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 2rpx solid #f0f0f0;
}

.action-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 16rpx 20rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  border: none;
  transition: all 0.2s ease;
}

.action-button.primary {
  background: #007aff;
  color: white;
}

.action-button.primary:active {
  background: #0056cc;
}

.action-button.secondary {
  background: #f8f9fa;
  color: #333;
  border: 2rpx solid #e9ecef;
}

.action-button.secondary:active {
  background: #e9ecef;
}

.button-icon {
  font-size: 24rpx;
}

.button-text {
  font-size: 24rpx;
  font-weight: 500;
}
