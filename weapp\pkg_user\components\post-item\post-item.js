Component({
  properties: {
    post: {
      type: Object,
      value: {},
      observer: function(newVal) {
        if (newVal) {
          // 处理图片字符串
          const imageList = newVal.images ? newVal.images.split(',').filter(img => img.trim()).filter(img => img && img.startsWith('http')) : [];
          // 格式化时间
          const timeAgo = this.formatTimeAgo(newVal.publishTime);
          this.setData({
            imageList,
            timeAgo
          });
        }
      }
    },
    // 统计区域的按钮显示控制
    showLikeStat: {
      type: Boolean,
      value: true
    },
    showFavoriteStat: {
      type: Boolean,
      value: false
    },
    // 操作区域的按钮显示控制
    showLikeAction: {
      type: Boolean,
      value: false
    },
    showFavoriteAction: {
      type: Boolean,
      value: false
    },
    showDeleteAction: {
      type: Boolean,
      value: false
    },
    showCompletedAction: {
      type: Boolean,
      value: false
    },
    userId: {
      type: String,
      value: ''
    },
    showShare: {
      type: Boolean,
      value: true
    }
  },

  options: {
    // 启用分享功能
    addGlobalClass: true,
    multipleSlots: true,
    // 开启页面分享功能
    shareOptions: {
      shareAppMessage: true,
      shareTimeline: true
    }
  },

  data: {
    imageList: [],
    timeAgo: '',
    showActions: false
  },

  methods: {
    // 格式化时间为"多久以前"
    formatTimeAgo: function(dateString) {
      if (!dateString) return '';
      
      // 处理日期字符串，兼容 iOS
      let date;
      if (typeof dateString === 'string') {
        // 将 "yyyy-MM-dd HH:mm:ss" 转换为 "yyyy/MM/dd HH:mm:ss" 格式
        if (dateString.includes('-')) {
          dateString = dateString.replace(/-/g, '/');
        }
        
        // 尝试创建日期对象
        date = new Date(dateString);
        
        // 检查日期是否有效
        if (isNaN(date.getTime())) {
          console.warn('无效的日期格式:', dateString);
          return '未知时间';
        }
      } else {
        date = new Date(dateString);
      }
      
      const now = new Date();
      const diff = now - date;
      const minute = 60 * 1000;
      const hour = minute * 60;
      const day = hour * 24;
      const month = day * 30;
      const year = day * 365;

      if (diff < minute) {
        return '刚刚';
      } else if (diff < hour) {
        return Math.floor(diff / minute) + '分钟前';
      } else if (diff < day) {
        return Math.floor(diff / hour) + '小时前';
      } else if (diff < month) {
        return Math.floor(diff / day) + '天前';
      } else if (diff < year) {
        return Math.floor(diff / month) + '个月前';
      } else {
        return Math.floor(diff / year) + '年前';
      }
    },

    // 点击帖子跳转到详情
    onTapPost() {
      const { id } = this.data.post;
      wx.navigateTo({
        url: `/pkg_common/pages/post/detail/detail?id=${id}`
      });
    },

    // 预览图片
    previewImage(e) {
      const { current } = e.currentTarget.dataset;
      const { imageList } = this.data;
      wx.previewImage({
        current,
        urls: imageList
      });
    },

    // 点赞
    onTapLike() {
      const { post } = this.data;
      // 阻止事件冒泡，避免触发点击帖子的事件
      this.triggerEvent('like', { 
        id: post.id,
        liked: post.liked 
      }, { bubbles: false, composed: false });
    },

    // 收藏
    onTapFavorite() {
      const { post } = this.data;
      // 阻止事件冒泡，避免触发点击帖子的事件
      this.triggerEvent('favorite', {
        id: post.id,
        favorite: post.favorite
      }, { bubbles: false, composed: false });
    },

    // 点击更多操作
    onTapMore() {
      this.triggerEvent('more', { id: this.data.post.id });
    },

    // 编辑帖子
    onTapEdit() {
      this.setData({ showActions: false });
      this.triggerEvent('edit', { id: this.data.post.id });
    },

    // 删除帖子
    onTapDelete() {
      this.setData({ showActions: false });
      wx.showModal({
        title: '提示',
        content: '确定要删除这条帖子吗？',
        success: (res) => {
          if (res.confirm) {
            this.triggerEvent('delete', { id: this.data.post.id });
          }
        }
      });
    },

    // 取消收藏
    onTapCancelFavorite() {
      const { id } = this.data.post;
      wx.showModal({
        title: '提示',
        content: '确定要取消收藏这条帖子吗？',
        success: (res) => {
          if (res.confirm) {
            this.triggerEvent('cancelFavorite', { id });
          }
        }
      });
    },
    
    // 切换帖子完成状态
    onTapCompleted() {
      this.setData({ showActions: false });
      const { post } = this.data;
      wx.showModal({
        title: '提示',
        content: post.completed === 1 ? '确定要将帖子标记为未完成吗？' : '确定要将帖子标记为已完成吗？',
        success: (res) => {
          if (res.confirm) {
            this.triggerEvent('completed', { 
              id: post.id,
              completed: post.completed
            }, { bubbles: false, composed: false });
          }
        }
      });
    },

    // 拨打电话
    onTapContact() {
      const { post } = this.data;
      if (!post.contactPhone) {
        wx.showToast({
          title: '暂无联系电话',
          icon: 'none'
        });
        return;
      }
      
      wx.showModal({
        title: '提示',
        content: `是否拨打电话 ${post.contactPhone}？`,
        success: (res) => {
          if (res.confirm) {
            wx.makePhoneCall({
              phoneNumber: post.contactPhone,
              success: () => {
                // 记录拨打电话事件
                this.triggerEvent('contact', {
                  id: post.id,
                  phone: post.contactPhone
                });
              },
              fail: (error) => {
                console.error('拨打电话失败:', error);
                wx.showToast({
                  title: '拨打电话失败',
                  icon: 'none'
                });
              }
            });
          }
        }
      });
    },

    // 分享
    onTapShare() {
      // 触发分享事件
      this.triggerEvent('share', {
        id: this.data.post.id 
      });
    },
    
    // 分享给朋友
    onShareAppMessage() {
      const post = this.data.post;
      return {
        title: post.title,
        path: `/pkg_common/pages/post/detail/detail?id=${post.id}`,
        imageUrl: this.data.imageList[0] || ''
      };
    },
    
    // 分享到朋友圈
    onShareTimeline() {
      const post = this.data.post;
      return {
        title: post.title,
        query: `id=${post.id}`,
        imageUrl: this.data.imageList[0] || ''
      };
    },

    // 移动到草稿
    onTapMoveDraft() {
      this.setData({ showActions: false });
      this.triggerEvent('moveDraft', { id: this.data.post.id });
    }
  }
}) 