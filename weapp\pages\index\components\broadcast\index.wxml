<!-- 广播组件 -->
<view class="broadcast-container">
  <view class="broadcast-content">
    <view class="broadcast-left">
      <view class="broadcast-icon">📢</view>
      <text class="broadcast-title">广播</text>
    </view>

    <view class="broadcast-swiper-container">
      <swiper
        class="broadcast-swiper"
        vertical="{{true}}"
        autoplay="{{true}}"
        interval="{{3000}}"
        duration="{{500}}"
        circular="{{true}}"
        bindchange="onSwiperChange"
      >
        <swiper-item wx:for="{{notices}}" wx:key="id" wx:for-item="notice">
          <view class="broadcast-item" bindtap="onNoticeClick" data-notice="{{notice}}">
            <view class="notice-content">
              <text class="notice-text">{{notice.title}}</text>
            </view>
            <view class="notice-time">{{notice.timeText}}</view>
          </view>
        </swiper-item>
      </swiper>
    </view>

    <view class="broadcast-more" bindtap="onMoreClick">
      <text class="more-text">更多</text>
      <text class="more-arrow">></text>
    </view>
  </view>
</view>
