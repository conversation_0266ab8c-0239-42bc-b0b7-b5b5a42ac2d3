# 小程序开发规范文档

## 1. 引言

为了提高团队协作效率，保证项目代码的规范性、可读性和可维护性，特制定此开发规范。所有项目成员都应严格遵守本规范。

## 2. 核心原则

- **高内聚，低耦合**：模块功能应单一明确，模块之间的依赖关系应尽可能简单。
- **关注点分离**：视图（WXML/WXSS）、业务逻辑（JS）、数据状态（Stores）应清晰分离。
- **组件化思维**：积极将可复用的 UI 和业务逻辑抽象成组件，提高开发效率。

## 3. 目录结构规范

清晰的目录结构是项目规范的基础。我们约定以下核心目录结构：

```
weapp/
├── components/         # 全局通用组件
├── pages/              # 页面
│   └── index/
│       ├── components/     # index 页面私有组件
│       │   └── stats-bar/
│       │       ├── index.js
│       │       └── ...
│       ├── index.js
│       ├── index.json
│       ├── index.wxml
│       └── index.wxss
├── stores/             # 状态管理与数据请求
│   └── postStore.js
├── utils/              # 工具函数
└── app.js
```

## 4. 组件开发规范

### 4.1 全局组件 vs. 页面私有组件

- **全局组件**：可以在项目的**任何页面**中被复用的组件，应存放在根目录的 `components/` 文件夹下。例如，我们创建的 `category-card` 组件。
- **页面私有组件**：**只在特定单个页面**中使用的组件，为了实现高内聚，应存放在该页面目录下的 `components/` 子文件夹中。

#### 组件目录规范（补充）

1. **公共组件**
   - 放在 `weapp/components/` 目录下。
   - 命名简洁明了，供多个页面复用。
   - 例如：`weapp/components/category-card/`

2. **页面私有组件**
   - 只在某个页面使用的组件，**必须**放在该页面目录下的 `components/` 文件夹中。
   - 例如：
     ```
     weapp/pages/publish/form/components/xxx/
     weapp/pages/index/components/xxx/
     ```
   - 页面私有组件**禁止**放在全局 `components/` 下，避免污染和误用。

3. **使用方式**
   - 页面私有组件在页面的 `index.json` 里用相对路径引用，如：
     ```json
     {
       "usingComponents": {
         "my-block": "./components/my-block/index"
       }
     }
     ```
   - 公共组件用绝对路径引用，如：
     ```json
     {
       "usingComponents": {
         "category-card": "/components/category-card/index"
       }
     }
     ```

4. **目录结构示例**
```
weapp/
├── components/
│   └── category-card/
├── pages/
│   └── publish/
│       └── form/
│           ├── components/
│           │   └── my-block/
│           ├── index.js
│           ├── index.json
│           ├── index.wxml
│           └── index.wxss
```

**这样做的好处：**
- 结构清晰，易于维护。
- 删除页面时可直接删除其私有组件，不影响其他页面。
- 公共组件和私有组件职责分明，避免误用。

### 4.2 页面私有组件创建规则

**【强制】** 如果一个组件仅被一个页面使用，必须将该组件创建在所属页面的 `components` 文件夹内。

**示例**：
`stats-bar` 组件仅在 `index` 页面使用，那么它的存放路径应该是：
`pages/index/components/stats-bar/`

在 `index.json` 中引用时，应使用相对路径：
```json
{
  "usingComponents": {
    "stats-bar": "./components/stats-bar/index"
  }
}
```

**这样做的好处**：
- **结构清晰**：一眼就能看出页面的依赖和组成部分。
- **便于维护**：当修改或删除一个页面时，可以放心处理其私有的 `components` 文件夹，而不用担心影响到其他页面。
- **避免污染**：防止为特定页面编写的组件被其他开发者误用于不合适的场景。

## 5. 状态与数据请求规范

### 5.1 Stores 目录职责

**【强制】** 所有与后端接口的数据交互、复杂的数据处理逻辑，都必须抽离到 `stores/` 目录中进行统一管理。页面 `js` 文件本身不应包含 `wx.request` 等直接的接口调用代码。

`stores` 目录下的每个文件可以看作是一个独立的"数据仓库"，负责特定模块的数据（例如 `postStore.js` 负责帖子相关数据，`userStore.js` 负责用户相关数据）。

### 5.2 数据请求流程

1.  在 `stores/` 目录下创建对应模块的 `store` 文件（例如 `postStore.js`）。
2.  在 `store` 文件中封装数据请求和处理的函数。
3.  在页面 `js` 文件中，通过 `import` 引入需要的 `store`。
4.  在页面的生命周期函数（如 `onLoad`）或事件处理函数中，调用 `store` 中封装好的方法来获取和操作数据。

**示例**：

**a. 创建 `stores/postStore.js` (模拟)**

```javascript
// stores/postStore.js
import { request } from '../utils/request.js'; // 假设对 wx.request 进行了封装

const getPosts = (params) => {
  // 这里可以处理分页、参数拼接等逻辑
  return request({
    url: '/api/posts',
    method: 'GET',
    data: params
  });
};

const getPostDetail = (id) => {
  return request({
    url: `/api/posts/${id}`,
    method: 'GET'
  });
};

module.exports = {
  getPosts,
  getPostDetail
};
```

**b. 在 `pages/index/index.js` 中调用**

```javascript
// pages/index/index.js
import { getPosts } from '../../stores/postStore.js';

Page({
  data: {
    posts: []
  },
  onLoad() {
    this.loadPosts();
  },
  async loadPosts() {
    try {
      const res = await getPosts({ page: 1, limit: 10 });
      if (res.code === 200) {
        this.setData({
          posts: res.data.records
        });
      }
    } catch (error) {
      console.error("加载帖子失败", error);
    }
  }
  // ...
});
```

**这样做的好处**：
- **逻辑复用**：多个页面需要同样的数据时，只需调用 `store` 中相同的方法，无需重复编写请求代码。
- **易于维护**：后端接口变更时，只需修改 `stores` 中的对应文件，而无需改动每个调用该接口的页面。
- **关注点分离**：页面 `js` 文件更专注于处理视图交互和生命周期，数据逻辑则交给 `store`，代码更纯粹。 