/* pkg_user/pages/cooperation-policy/cooperation-policy.wxss */

.policy-container {
  min-height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 顶部标题区域 */
.header-section {
  background: linear-gradient(135deg, #FF8383, #ff8585);
  padding: 40rpx 30rpx;
  color: #fff;
  text-align: center;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 12rpx;
}

.header-subtitle {
  font-size: 26rpx;
  opacity: 0.9;
  line-height: 1.4;
}

/* 内容滚动区域 */
.content-scroll {
  flex: 1;
  padding: 20rpx;
}

/* 政策列表 */
.policy-list {
  margin-bottom: 20rpx;
}

.policy-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.policy-header {
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.policy-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  position: relative;
}

.policy-title::before {
  content: '';
  position: absolute;
  left: -10rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 32rpx;
  background: #FF8383;
  border-radius: 3rpx;
}

.policy-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.policy-text {
  font-size: 28rpx;
  color: #555;
  line-height: 1.6;
  padding-left: 20rpx;
}

/* 联系方式区域 */
.contact-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.contact-header {
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.contact-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  position: relative;
}

.contact-title::before {
  content: '';
  position: absolute;
  left: -10rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 32rpx;
  background: #FF8383;
  border-radius: 3rpx;
}

.contact-content {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.contact-item {
  display: flex;
  align-items: center;
}

.contact-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 16rpx;
  min-width: 160rpx;
}

.contact-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 温馨提示区域 */
.tips-section {
  background: #fff9e6;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border: 1rpx solid #ffe58f;
}

.tips-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.tips-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.tips-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #d48806;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.tips-text {
  font-size: 26rpx;
  color: #d48806;
  line-height: 1.5;
}

/* 底部操作区域 */
.action-section {
  background: #fff;
  padding: 30rpx;
  border-top: 1rpx solid #eee;
}

.action-row {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.action-btn:active {
  transform: scale(0.95);
}

.action-btn.primary {
  background: linear-gradient(135deg, #FF8383, #ff8585);
  color: #fff;
}

.action-btn.secondary {
  background: #fff;
  color: #FF8383;
  border: 2rpx solid #FF8383;
}

.action-text {
  font-size: 28rpx;
  font-weight: 600;
}
