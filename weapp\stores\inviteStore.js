const { request } = require('../utils/request');

/**
 * 邀请相关的数据请求和业务逻辑
 * 遵循小程序开发规范，将所有邀请相关的接口调用封装在此store中
 */
class InviteStore {
  
  /**
   * 生成邀请码
   * @param {string} userId 用户ID
   * @returns {Promise} 邀请码
   */
  static async generateInviteCode(userId) {
    try {
      const response = await request({
        url: '/blade-chat-open/invite/generate-code',
        method: 'POST',
      });

      if (response.code === 200) {
        return response.data.inviteCode;
      } else {
        throw new Error(response.msg || '生成邀请码失败');
      }
    } catch (error) {
      console.error('生成邀请码异常:', error);
      throw error;
    }
  }

  /**
   * 获取邀请统计数据
   * @param {string} userId 用户ID
   * @returns {Promise} 邀请统计数据
   */
  static async getInviteStats(userId) {
    try {
      const response = await request({
        url: '/blade-chat-open/invite/stats',
        method: 'GET',
        data: { userId }
      });

      if (response.code === 200) {
        return response.data || {};
      } else {
        console.error('获取邀请统计失败:', response.msg);
        return {};
      }
    } catch (error) {
      console.error('获取邀请统计异常:', error);
      return {};
    }
  }

  /**
   * 获取邀请配置
   * @returns {Promise} 邀请配置
   */
  static async getInviteConfig() {
    try {
      const response = await request({
        url: '/blade-chat-open/invite/config',
        method: 'GET'
      });

      if (response.code === 200) {
        return response.data || {};
      } else {
        console.error('获取邀请配置失败:', response.msg);
        return {};
      }
    } catch (error) {
      console.error('获取邀请配置异常:', error);
      return {};
    }
  }

  /**
   * 获取最近邀请记录
   * @param {string} userId 用户ID
   * @param {number} limit 限制数量
   * @returns {Promise} 邀请记录列表
   */
  static async getRecentInviteRecords(userId, limit = 5) {
    try {
      const response = await request({
        url: '/blade-chat-open/invite/recent-records',
        method: 'GET',
        data: { userId, limit }
      });

      if (response.code === 200) {
        return response.data || [];
      } else {
        console.error('获取邀请记录失败:', response.msg);
        return [];
      }
    } catch (error) {
      console.error('获取邀请记录异常:', error);
      return [];
    }
  }

  /**
   * 记录邀请分享行为
   * @param {string} userId 用户ID
   * @param {string} channel 分享渠道
   * @param {string} inviteCode 邀请码
   * @returns {Promise} 记录结果
   */
  static async recordInviteShare(userId, channel, inviteCode) {
    try {
      const response = await request({
        url: '/blade-chat-open/invite/record-share',
        method: 'POST',
        data: { userId, channel, inviteCode }
      });

      return response.code === 200;
    } catch (error) {
      console.error('记录邀请分享失败:', error);
      return false;
    }
  }

  /**
   * 使用邀请码注册
   * @param {string} inviteCode 邀请码
   * @param {string} newUserId 新用户ID
   * @returns {Promise} 注册结果
   */
  static async useInviteCode(inviteCode, newUserId) {
    try {
      const response = await request({
        url: '/blade-chat-open/invite/use-code',
        method: 'POST',
        data: { inviteCode, newUserId }
      });

      if (response.code === 200) {
        return response.data || { success: true };
      } else {
        return { success: false, message: response.msg };
      }
    } catch (error) {
      console.error('使用邀请码失败:', error);
      return { success: false, message: '网络错误' };
    }
  }

  /**
   * 验证邀请码有效性
   * @param {string} inviteCode 邀请码
   * @returns {Promise} 验证结果
   */
  static async validateInviteCode(inviteCode) {
    try {
      const response = await request({
        url: '/blade-chat-open/invite/validate-code',
        method: 'GET',
        data: { inviteCode }
      });

      if (response.code === 200) {
        return response.data || { valid: false };
      } else {
        return { valid: false, message: response.msg };
      }
    } catch (error) {
      console.error('验证邀请码失败:', error);
      return { valid: false, message: '网络错误' };
    }
  }

  /**
   * 获取邀请排行榜
   * @param {number} limit 限制数量
   * @returns {Promise} 排行榜数据
   */
  static async getInviteRanking(limit = 10) {
    try {
      const response = await request({
        url: '/blade-chat-open/invite/ranking',
        method: 'GET',
        data: { limit }
      });

      if (response.code === 200) {
        return response.data || [];
      } else {
        console.error('获取邀请排行榜失败:', response.msg);
        return [];
      }
    } catch (error) {
      console.error('获取邀请排行榜异常:', error);
      return [];
    }
  }

  /**
   * 获取用户的邀请详情
   * @param {string} userId 用户ID
   * @returns {Promise} 邀请详情
   */
  static async getUserInviteDetail(userId) {
    try {
      const response = await request({
        url: '/blade-chat-open/invite/user-detail',
        method: 'GET',
        data: { userId }
      });

      if (response.code === 200) {
        return response.data || {};
      } else {
        console.error('获取用户邀请详情失败:', response.msg);
        return {};
      }
    } catch (error) {
      console.error('获取用户邀请详情异常:', error);
      return {};
    }
  }
}

module.exports = InviteStore;
