/**
 * 地区数据缓存管理工具
 * 提供地区数据的缓存、获取、清理等功能
 */

const { request } = require('./request');

// 缓存键名常量
const CACHE_KEYS = {
  CITIES: 'region_cities',
  DISTRICTS: 'region_districts_',
  FULL_ADDRESS: 'region_full_address_',
  CURRENT_LOCATION: 'current_location'
};

// 缓存过期时间（毫秒）
const CACHE_EXPIRE_TIME = 24 * 60 * 60 * 1000; // 24小时

class RegionCache {
  
  /**
   * 设置缓存数据
   * @param {string} key 缓存键
   * @param {any} data 缓存数据
   */
  setCache(key, data) {
    const cacheData = {
      data: data,
      timestamp: Date.now(),
      expireTime: Date.now() + CACHE_EXPIRE_TIME
    };
    wx.setStorageSync(key, cacheData);
  }

  /**
   * 获取缓存数据
   * @param {string} key 缓存键
   * @returns {any|null} 缓存数据或null
   */
  getCache(key) {
    try {
      const cacheData = wx.getStorageSync(key);
      if (!cacheData) return null;

      // 检查是否过期
      if (Date.now() > cacheData.expireTime) {
        wx.removeStorageSync(key);
        return null;
      }

      return cacheData.data;
    } catch (error) {
      console.error('获取缓存失败:', error);
      return null;
    }
  }

  /**
   * 清除指定缓存
   * @param {string} key 缓存键
   */
  removeCache(key) {
    wx.removeStorageSync(key);
  }

  /**
   * 清除所有地区缓存
   */
  clearAllCache() {
    Object.values(CACHE_KEYS).forEach(key => {
      if (key.endsWith('_')) {
        // 对于带参数的缓存键，需要遍历所有存储项
        const info = wx.getStorageInfoSync();
        info.keys.forEach(storageKey => {
          if (storageKey.startsWith(key)) {
            wx.removeStorageSync(storageKey);
          }
        });
      } else {
        wx.removeStorageSync(key);
      }
    });
  }

  /**
   * 获取城市列表（所有开放的城市）
   * @returns {Promise<Array>} 城市列表
   */
  async getCities() {
    // 先从缓存获取
    let cities = this.getCache(CACHE_KEYS.CITIES);
    if (cities) {
      return cities;
    }

    // 从API获取
    try {
      const res = await request({
        url: '/miniapp/region/cities',
        method: 'GET'
      });

      if (res.success && res.data) {
        cities = res.data;
        this.setCache(CACHE_KEYS.CITIES, cities);
        return cities;
      }
    } catch (error) {
      console.error('获取城市数据失败:', error);
    }

    return [];
  }

  /**
   * 获取区县列表
   * @param {string} cityCode 城市编码
   * @returns {Promise<Array>} 区县列表
   */
  async getDistricts(cityCode) {
    if (!cityCode) return [];

    const cacheKey = CACHE_KEYS.DISTRICTS + cityCode;
    
    // 先从缓存获取
    let districts = this.getCache(cacheKey);
    if (districts) {
      return districts;
    }

    // 从API获取
    try {
      const res = await request({
        url: `/miniapp/region/districts?cityCode=${cityCode}`,
        method: 'GET'
      });

      if (res.success && res.data) {
        districts = res.data;
        this.setCache(cacheKey, districts);
        return districts;
      }
    } catch (error) {
      console.error('获取区县数据失败:', error);
    }

    return [];
  }



  /**
   * 获取完整地址信息
   * @param {string} regionCode 地区编码
   * @returns {Promise<Object|null>} 完整地址信息
   */
  async getFullAddress(regionCode) {
    if (!regionCode) return null;

    const cacheKey = CACHE_KEYS.FULL_ADDRESS + regionCode;
    
    // 先从缓存获取
    let fullAddress = this.getCache(cacheKey);
    if (fullAddress) {
      return fullAddress;
    }

    // 从API获取
    try {
      const res = await request({
        url: `/miniapp/region/full-address?regionCode=${regionCode}`,
        method: 'GET'
      });

      if (res.success && res.data) {
        fullAddress = res.data;
        this.setCache(cacheKey, fullAddress);
        return fullAddress;
      }
    } catch (error) {
      console.error('获取完整地址信息失败:', error);
    }

    return null;
  }

  /**
   * 搜索地区（仅搜索城市和区县）
   * @param {string} keyword 搜索关键词
   * @returns {Promise<Array>} 搜索结果
   */
  async searchRegions(keyword) {
    if (!keyword || keyword.trim() === '') return [];

    try {
      const res = await request({
        url: `/miniapp/region/search?keyword=${encodeURIComponent(keyword)}`,
        method: 'GET'
      });

      if (res.success && res.data) {
        // 只返回城市（level=2）和区县（level=3）
        return res.data.filter(item => item.level === 2 || item.level === 3);
      }
    } catch (error) {
      console.error('搜索地区失败:', error);
    }

    return [];
  }

  /**
   * 设置当前位置
   * @param {Object} location 位置信息
   */
  setCurrentLocation(location) {
    this.setCache(CACHE_KEYS.CURRENT_LOCATION, location);
  }

  /**
   * 获取当前位置
   * @returns {Object|null} 位置信息
   */
  getCurrentLocation() {
    return this.getCache(CACHE_KEYS.CURRENT_LOCATION);
  }
}

// 创建单例实例
const regionCache = new RegionCache();

module.exports = {
  regionCache,
  CACHE_KEYS
};
