<!-- pkg_user/pages/local/components/institution-card/institution-card.wxml -->
<view class="institution-card" bindtap="onTap">
  <!-- 机构头部信息 -->
  <view class="institution-header">
    <view class="institution-logo">
      <image 
        wx:if="{{institution.logo}}" 
        src="{{institution.logo}}" 
        class="logo-image"
        mode="aspectFill"
      />
      <text wx:else class="logo-placeholder">{{institution.name.charAt(0)}}</text>
    </view>
    
    <view class="institution-info">
      <text class="institution-name">{{institution.name}}</text>
      <view class="institution-tags">
        <text class="tag category-tag">{{institution.categoryName || '机构'}}</text>
        <text wx:if="{{institution.verified}}" class="tag verified-tag">已认证</text>
      </view>
    </view>
    
    <view class="institution-actions">
      <view 
        class="action-btn phone-btn"
        wx:if="{{institution.phone}}"
        bindtap="onCallPhone">
        📞
      </view>
      <view 
        class="action-btn location-btn"
        wx:if="{{institution.latitude && institution.longitude}}"
        bindtap="onViewLocation">
        📍
      </view>
    </view>
  </view>

  <!-- 机构详细信息 -->
  <view class="institution-details">
    <view wx:if="{{institution.address}}" class="detail-item">
      <text class="detail-icon">📍</text>
      <text class="detail-text">{{institution.address}}</text>
    </view>
    
    <view wx:if="{{institution.businessHours}}" class="detail-item">
      <text class="detail-icon">🕐</text>
      <text class="detail-text">{{institution.businessHours}}</text>
    </view>
    
    <view wx:if="{{institution.description}}" class="detail-item description">
      <text class="detail-text">{{institution.description}}</text>
    </view>
  </view>

  <!-- 机构统计信息 -->
  <view class="institution-stats">
    <view class="stat-item">
      <text class="stat-number">{{institution.rating || '4.5'}}</text>
      <text class="stat-label">评分</text>
    </view>
    <view class="stat-item">
      <text class="stat-number">{{institution.distance || '1.2km'}}</text>
      <text class="stat-label">距离</text>
    </view>
    <view class="stat-item">
      <text class="stat-number">{{institution.serviceCount || '10+'}}</text>
      <text class="stat-label">服务</text>
    </view>
  </view>
</view>
