<view class="signin-container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">每日签到</text>
    <text class="page-subtitle">坚持签到，积分不断</text>
  </view>

  <!-- 用户积分信息 -->
  <view class="points-section">
    <view class="points-card">
      <view class="points-info">
        <text class="points-label">我的积分</text>
        <text class="points-value">{{loading ? '--' : points}}</text>
      </view>
      <view class="points-actions">
        <view class="action-btn" bindtap="viewSigninDetail">
          <text class="action-text">签到明细</text>
        </view>
        <view class="action-btn" bindtap="viewPointsMall">
          <text class="action-text">积分商城</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 签到区域 -->
  <view class="signin-section">
    <view class="signin-card">
      <!-- 签到状态 -->
      <view class="signin-status">
        <view class="status-icon {{signinInfo.todaySigned ? '' : ''}}">
          <text class="icon-text">{{signinInfo.todaySigned ? '📅' : '📅'}}</text>
        </view>
        <view class="status-info">
          <text class="status-title">{{signinInfo.todaySigned ? '今日已签到' : '今日未签到'}}</text>
          <text class="status-desc">{{signinInfo.todaySigned ? '明天再来签到吧' : '签到即可获得积分奖励'}}</text>
        </view>
      </view>

      <!-- 签到按钮 -->
      <view class="signin-btn-wrap">
        <button 
          class="signin-btn {{signinInfo.todaySigned ? 'signed' : ''}} {{signing ? 'signing' : ''}}"
          bindtap="doSignin"
          disabled="{{signinInfo.todaySigned || signing}}"
        >
          <text wx:if="{{!signing && !signinInfo.todaySigned}}">立即签到</text>
          <text wx:elif="{{signing}}">签到中...</text>
          <text wx:else>已签到</text>
        </button>
      </view>

      <!-- 签到奖励 -->
      <view class="reward-info">
        <text class="reward-text">签到奖励：{{signinReward}}积分</text>
      </view>
    </view>
  </view>

  <!-- 连续签到信息 -->
  <view class="continuous-section">
    <view class="section-title">
      <text class="title-text">连续签到</text>
      <text class="title-desc">连续签到天数：{{signinInfo.continuousDays || 0}}天</text>
    </view>

    <view class="continuous-rewards">
      <view
        class="reward-item {{(signinInfo.continuousDays || 0) >= item.days ? 'achieved' : ''}}"
        wx:for="{{continuousRewards}}"
        wx:key="days"
      >
        <!-- 进度指示器 -->
        <view class="reward-indicator">
          <view class="indicator-icon {{(signinInfo.continuousDays || 0) >= item.days ? 'completed' : ''}}">
            <text class="icon-text">{{(signinInfo.continuousDays || 0) >= item.days ? '✓' : item.days}}</text>
          </view>
        </view>

        <view class="reward-days">
          <text class="days-text">{{item.days}}天</text>
        </view>
        <view class="reward-points">
          <text class="points-text">+{{item.reward}}</text>
        </view>
        <view class="reward-status">
          <text class="status-text">{{(signinInfo.continuousDays || 0) >= item.days ? '已达成' : '未达成'}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 本月签到记录 -->
  <view class="record-section">
    <view class="section-title">
      <text class="title-text">本月签到记录</text>
      <text class="title-desc">总签到：{{signinInfo.totalDays || 0}}天</text>
    </view>

    <view class="calendar-grid">
      <view class="calendar-header">
        <text class="header-item">日</text>
        <text class="header-item">一</text>
        <text class="header-item">二</text>
        <text class="header-item">三</text>
        <text class="header-item">四</text>
        <text class="header-item">五</text>
        <text class="header-item">六</text>
      </view>

      <view class="calendar-body">
        <view
          class="calendar-day {{item.isToday ? 'today' : ''}} {{item.isSigned ? 'signed' : ''}} {{item.isCurrentMonth ? '' : 'other-month'}}"
          wx:for="{{monthSigninRecord}}"
          wx:key="date"
        >
          <text class="day-text">{{item.day}}</text>
          <view wx:if="{{item.isSigned}}" class="signed-dot"></view>
        </view>
      </view>
    </view>
  </view>

  <!-- 签到规则 -->
  <view class="rules-section">
    <view class="section-title">
      <text class="title-text">签到规则</text>
    </view>
    
    <view class="rules-list">
      <view class="rule-item">
        <text class="rule-dot">•</text>
        <text class="rule-text">每日签到可获得{{signinReward}}积分</text>
      </view>
      <view class="rule-item">
        <text class="rule-dot">•</text>
        <text class="rule-text">连续签到可获得额外奖励积分</text>
      </view>
      <view class="rule-item">
        <text class="rule-dot">•</text>
        <text class="rule-text">中断签到后连续天数将重新计算</text>
      </view>
      <view class="rule-item">
        <text class="rule-dot">•</text>
        <text class="rule-text">积分可用于兑换商城礼品</text>
      </view>
    </view>
  </view>

  <!-- 分享获得积分 -->
  <view class="share-section">
    <view class="share-card">
      <view class="share-info">
        <text class="share-title">分享给好友</text>
        <text class="share-desc">邀请好友一起签到，获得更多积分</text>
      </view>
      <button style="width: 200rpx;" class="share-btn" open-type="share">
        <text class="share-text">立即分享</text>
      </button>
    </view>
  </view>
</view> 