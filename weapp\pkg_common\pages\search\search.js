const { getCategories, getPosts } = require('../../stores/indexStore.js');
const { getInstitutionList } = require('../../stores/institutionStore.js');
const { getInstitutionTypeList } = require('../../stores/institutionTypeStore.js');
const businessCardStore = require('../../stores/businessCardStore.js');


Page({
  data: {
    searchText: '',
    categories: [],
    activeCategory: '全部',
    posts: [],
    institutions: [],
    businessCards: [],
    loading: false,
    page: 1,
    pageSize: 10,
    hasMore: true,
    searchType: 'post', // 搜索类型：post(帖子), institution(机构), businessCard(名片)
    currentTab: 'post', // 当前选中的标签页
    placeholder: '输入关键词搜索' // 搜索框占位符
  },

  /**
   * 获取搜索类型对应的中文名称
   */
  getTypeDisplayName(searchType) {
    const typeNames = {
      'post': '帖子',
      'institution': '机构',
      'businessCard': '名片'
    };
    return typeNames[searchType] || '内容';
  },

  /**
   * 将数字类型转换为字符串类型
   */
  convertNumericType(type) {
    const typeMap = {
      '0': 'post',        // 0 是帖子
      '1': 'institution', // 1 是机构
      '2': 'businessCard' // 2 是名片
    };
    return typeMap[type] || 'post';
  },

  async onLoad(options) {
    console.log('搜索页面参数:', options);

    // 获取搜索类型参数，支持数字类型：0-帖子，1-机构，2-名片
    let searchType = options.searchType || options.type || '0';
    const originalType = searchType;

    // 如果传入的是数字类型，转换为对应的字符串类型
    if (typeof searchType === 'string' && /^\d+$/.test(searchType)) {
      searchType = this.convertNumericType(searchType);
    }

    console.log(`类型转换: ${originalType} -> ${searchType}`);
    console.log('最终搜索类型:', searchType);

    this.setData({
      searchType,
      currentTab: searchType
    });

    // 根据搜索类型设置页面标题和占位符
    this.setPageConfig(searchType);

    await this.loadCategories();
    
    // 根据搜索类型加载对应的推荐数据
    console.log('开始加载推荐数据，搜索类型:', searchType);
    console.log('当前页面数据状态:', {
      searchType: this.data.searchType,
      currentTab: this.data.currentTab,
      page: this.data.page,
      pageSize: this.data.pageSize
    });
    await this.loadRecommendData('全部', '', 'all');
    
    // 加载完成后检查数据状态
    console.log('推荐数据加载完成，当前数据状态:', {
      posts: this.data.posts.length,
      institutions: this.data.institutions.length,
      businessCards: this.data.businessCards.length
    });
  },

  /**
   * 根据搜索类型设置页面配置
   */
  setPageConfig(searchType) {
    let title = '搜索';
    let placeholder = '输入关键词搜索';

    switch (searchType) {
      case 'post':
        title = '搜索帖子';
        placeholder = '搜索帖子内容、标题...';
        break;
      case 'institution':
        title = '搜索机构';
        placeholder = '搜索机构名称、地址...';
        break;
      case 'businessCard':
        title = '搜索名片';
        placeholder = '搜索姓名、公司、职位...';
        break;
      default:
        title = '搜索';
        placeholder = '输入关键词搜索';
        break;
    }

    console.log(`设置页面标题: ${title}, 占位符: ${placeholder}`);

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: title,
      success: () => {
        console.log('导航栏标题设置成功:', title);
      },
      fail: (err) => {
        console.error('导航栏标题设置失败:', err);
      }
    });

    // 更新搜索框占位符
    this.setData({ placeholder });
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchText: e.detail.value
    });
  },

  // 清除搜索
  clearSearch() {
    this.setData({
      searchText: '',
      posts: [],
      institutions: [],
      businessCards: []
    });
  },

  // 执行搜索
  async onSearch() {
    if (!this.data.searchText.trim()) {
      return;
    }
    this.setData({ page: 1, hasMore: true });
    
    // 获取当前选中分类的ID
    const currentCategory = this.data.categories.find(cat => cat.name === this.data.activeCategory);
    const categoryId = currentCategory ? currentCategory.id : 'all';
    
    console.log('执行搜索，当前分类:', this.data.activeCategory, '分类ID:', categoryId, '关键词:', this.data.searchText);
    await this.loadSearchData(this.data.activeCategory, this.data.searchText, categoryId);
  },

  // 点击AI助手
  onTapAI() {
    wx.showToast({
      title: '正在开发中...',
      icon: 'none'
    });
    // wx.switchTab({ url: '/pages/ai/ai' });
  },

  // 点击分类
  async onTapCategory(e) {
    const category = e.currentTarget.dataset.category;
    const categoryId = e.currentTarget.dataset.categoryId;
    console.log('点击分类:', category, '分类ID:', categoryId);

    this.setData({
      activeCategory: category,
      page: 1,
      hasMore: true
    });

    // 如果有搜索关键词，执行搜索；否则加载推荐数据
    if (this.data.searchText.trim()) {
      console.log('执行搜索，分类:', category, '分类ID:', categoryId, '关键词:', this.data.searchText);
      await this.loadSearchData(category, this.data.searchText, categoryId);
    } else {
      console.log('加载推荐数据，分类:', category, '分类ID:', categoryId);
      await this.loadRecommendData(category, '', categoryId);
    }
  },

  // 切换标签页
  onTabChange(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      currentTab: tab,
      page: 1,
      hasMore: true
    });
    
    // 获取当前选中分类的ID
    const currentCategory = this.data.categories.find(cat => cat.name === this.data.activeCategory);
    const categoryId = currentCategory ? currentCategory.id : 'all';
    
    this.loadSearchData(this.data.activeCategory, this.data.searchText, categoryId);
  },

  // 点击机构
  onInstitutionTap(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/institution-detail/institution-detail?id=${id}`
    });
  },

  // 点击名片
  onBusinessCardTap(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pkg_user/pages/user-card/card-detail/card-detail?id=${id}`
    });
  },

  // 加载推荐数据（支持分类和关键词）
  async loadRecommendData(category = '全部', keyword = '', categoryId = 'all') {
    if (this.data.loading || !this.data.hasMore) return;
    this.setData({ loading: true });
    
    try {
      let params = {
        current: this.data.page,
        size: this.data.pageSize
      };
      
      // 根据搜索类型设置不同的分类参数
      if (category && category !== '全部' && categoryId && categoryId !== 'all') {
        if (this.data.searchType === 'institution') {
          // 机构搜索使用 typeId 参数
          params.typeId = categoryId;
        } else {
          // 帖子搜索使用 categoryId 参数
          params.categoryId = categoryId;
        }
      }

      if (keyword) {
        if (this.data.searchType === 'institution') {
          // 机构搜索使用 searchKeyWord 参数
          params.searchKeyWord = keyword;
        } else {
          // 帖子搜索使用 searchKeyWord 参数
          params.searchKeyWord = keyword;
        }
      }

      console.log('loadRecommendData 参数:', params);
      console.log('当前搜索类型:', this.data.searchType);
      
      let results = [];
      
      switch (this.data.searchType) {
        case 'post':
          results = await getPosts(params);
          console.log('帖子搜索结果:', results);
          this.setData({
            posts: this.data.page === 1 ? results : [...this.data.posts, ...results]
          });
          break;
          
        case 'institution':
          console.log('机构搜索参数:', params);
          const institutionResults = await getInstitutionList(params);
          console.log('机构接口返回的完整数据:', institutionResults);
          // 机构接口返回的是分页对象，需要提取 records
          results = institutionResults.records || institutionResults || [];
          console.log('机构搜索结果 (records):', results);
          console.log('机构搜索结果长度:', results.length);

          // 确保数据设置是同步的
          const newInstitutions = this.data.page === 1 ? results : [...this.data.institutions, ...results];
          console.log('新的 institutions 数据:', newInstitutions);
          
          this.setData({
            institutions: newInstitutions
          });
          
          // 验证数据是否设置成功
          setTimeout(() => {
            console.log('设置后的 institutions 数据:', this.data.institutions);
            console.log('设置后的 institutions 长度:', this.data.institutions.length);
          }, 100);
          break;
          
        case 'businessCard':
          results = await businessCardStore.getBusinessCardList(params);
          console.log('名片搜索结果:', results);
          this.setData({
            businessCards: this.data.page === 1 ? results : [...this.data.businessCards, ...results]
          });
          break;
      }
      
      console.log('最终 results 长度:', results.length);
      this.setData({
        hasMore: results.length === this.data.pageSize
      });
      
      // 如果是第一页且没有结果，清空推荐内容
      if (this.data.page === 1 && results.length === 0) {
        console.log('第一页无结果，清空推荐内容');
        this.setData({
          posts: [],
          institutions: [],
          businessCards: []
        });
      }
    } catch (e) {
      console.error('加载数据失败:', e);
      wx.showToast({ title: '加载失败', icon: 'none' });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 加载搜索数据
  async loadSearchData(category = '全部', keyword = '', categoryId = 'all') {
    if (this.data.loading || !this.data.hasMore) return;
    this.setData({ loading: true });
    
    try {
      let params = {
        current: this.data.page,
        size: this.data.pageSize
      };

      // 根据当前标签页设置不同的关键词参数
      if (keyword) {
        if (this.data.currentTab === 'institution') {
          // 机构搜索使用 searchKeyWord 参数
          params.searchKeyWord = keyword;
        } else {
          // 帖子搜索使用 searchKeyWord 参数
          params.searchKeyWord = keyword;
        }
      }
      
      // 根据当前标签页设置不同的分类参数
      if (category && category !== '全部' && categoryId && categoryId !== 'all') {
        if (this.data.currentTab === 'institution') {
          // 机构搜索使用 typeId 参数
          params.typeId = categoryId;
        } else {
          // 帖子搜索使用 categoryId 参数
          params.categoryId = categoryId;
        }
      }

      console.log('loadSearchData 参数:', params);

      let results = [];
      
      switch (this.data.currentTab) {
        case 'post':
          results = await getPosts(params);
          this.setData({
            posts: this.data.page === 1 ? results : [...this.data.posts, ...results]
          });
          break;
          
        case 'institution':
          console.log('机构搜索参数:', params);
          const institutionData = await getInstitutionList(params);
          // 机构接口返回的是分页对象，需要提取 records
          results = institutionData.records || institutionData || [];
          console.log('机构搜索结果:', results);
          this.setData({
            institutions: this.data.page === 1 ? results : [...this.data.institutions, ...results]
          });
          break;
          
        case 'businessCard':
          results = await businessCardStore.getBusinessCardList(params);
          this.setData({
            businessCards: this.data.page === 1 ? results : [...this.data.businessCards, ...results]
          });
          break;
      }
      
      this.setData({
        hasMore: results.length === this.data.pageSize
      });
      
      // 如果是第一页且没有结果，清空推荐内容
      if (this.data.page === 1 && results.length === 0) {
        this.setData({
          posts: [],
          institutions: [],
          businessCards: []
        });
      }
    } catch (e) {
      console.error('搜索数据失败:', e);
      wx.showToast({ title: '搜索失败', icon: 'none' });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 加载分类
  async loadCategories() {
    try {
      let categories = [];

      // 根据搜索类型加载对应的分类
      switch (this.data.searchType) {
        case 'post':
          // 加载帖子分类
          categories = await getCategories();
          break;

        case 'institution':
          // 加载机构分类
          categories = await getInstitutionTypeList();
          break;

        case 'businessCard':
          // 名片暂时不需要分类，使用空数组
          categories = [];
          break;

        default:
          // 默认加载帖子分类
          categories = await getCategories();
          break;
      }

      console.log(`加载${this.getTypeDisplayName(this.data.searchType)}分类:`, categories);

      // 处理分类数据
      const processedCategories = this.processCategories(categories);

      this.setData({
        categories: processedCategories,
        activeCategory: processedCategories[0]?.name || '全部'
      });

      console.log('分类设置完成:', {
        searchType: this.data.searchType,
        categories: processedCategories,
        activeCategory: processedCategories[0]?.name || '全部'
      });
    } catch (e) {
      console.error('加载分类失败:', e);
      // 设置默认分类
      this.setData({
        categories: [{ id: 'all', name: '全部' }],
        activeCategory: '全部'
      });
    }
  },

  /**
   * 处理分类数据，统一格式
   */
  processCategories(categories) {
    if (!categories || !Array.isArray(categories) || categories.length === 0) {
      return [{ id: 'all', name: '全部' }];
    }

    // 检查是否已经包含"全部"选项
    const hasAllOption = categories.some(cat => cat.id === 'all' || cat.name === '全部');

    if (hasAllOption) {
      return categories;
    } else {
      // 在开头添加"全部"选项
      return [
        { id: 'all', name: '全部' },
        ...categories
      ];
    }
  },

  // 下拉刷新
  async onPullDownRefresh() {
    this.setData({ page: 1, hasMore: true });
    
    // 获取当前选中分类的ID
    const currentCategory = this.data.categories.find(cat => cat.name === this.data.activeCategory);
    const categoryId = currentCategory ? currentCategory.id : 'all';
    
    if (this.data.searchText.trim()) {
      await this.loadSearchData(this.data.activeCategory, this.data.searchText, categoryId);
    } else {
      await this.loadRecommendData(this.data.activeCategory, '', categoryId);
    }
    wx.stopPullDownRefresh();
  },

  // 上拉加载更多
  async onReachBottom() {
    if (!this.data.hasMore || this.data.loading) return;
    this.setData({ page: this.data.page + 1 });
    
    // 获取当前选中分类的ID
    const currentCategory = this.data.categories.find(cat => cat.name === this.data.activeCategory);
    const categoryId = currentCategory ? currentCategory.id : 'all';
    
    if (this.data.searchText.trim()) {
      await this.loadSearchData(this.data.activeCategory, this.data.searchText, categoryId);
    } else {
      await this.loadRecommendData(this.data.activeCategory, '', categoryId);
    }
  }
}); 