/**
 * 地区管理工具
 * 用于管理用户选择的地区信息，并在请求头中添加地区标识
 */

const STORAGE_KEY = 'user_selected_region';

class RegionManager {
  /**
   * 保存用户选择的地区信息
   * @param {Object} regionInfo 地区信息
   * @param {string} regionInfo.regionCode 地区编码
   * @param {string} regionInfo.regionName 地区名称
   * @param {number} regionInfo.level 地区级别 (2-市, 3-区县)
   * @param {Array} regionInfo.selectedRegions 完整的地区选择路径
   */
  static saveUserRegion(regionInfo) {
    try {
      const regionData = {
        regionCode: regionInfo.regionCode,
        regionName: regionInfo.regionName,
        level: regionInfo.level,
        selectedRegions: regionInfo.selectedRegions || [],
        fullAddress: regionInfo.fullAddress || '',
        timestamp: Date.now()
      };

      wx.setStorageSync(STORAGE_KEY, regionData);
      console.log('用户地区信息已保存:', regionData);
      
      return true;
    } catch (error) {
      console.error('保存用户地区信息失败:', error);
      return false;
    }
  }

  /**
   * 获取用户选择的地区信息
   * @returns {Object|null} 地区信息
   */
  static getUserRegion() {
    try {
      const regionData = wx.getStorageSync(STORAGE_KEY);
      if (regionData && regionData.regionCode) {
        return regionData;
      }
      return null;
    } catch (error) {
      console.error('获取用户地区信息失败:', error);
      return null;
    }
  }

  /**
   * 获取用户地区编码（用于请求头）
   * @returns {string} 地区编码
   */
  static getUserRegionCode() {
    const regionData = this.getUserRegion();
    return regionData ? regionData.regionCode : '';
  }

  /**
   * 获取用户地区名称
   * @returns {string} 地区名称
   */
  static getUserRegionName() {
    const regionData = this.getUserRegion();
    return regionData ? regionData.regionName : '';
  }

  /**
   * 获取用户完整地址
   * @returns {string} 完整地址
   */
  static getUserFullAddress() {
    const regionData = this.getUserRegion();
    return regionData ? regionData.fullAddress : '';
  }

  /**
   * 清除用户地区信息
   */
  static clearUserRegion() {
    try {
      wx.removeStorageSync(STORAGE_KEY);
      console.log('用户地区信息已清除');
      return true;
    } catch (error) {
      console.error('清除用户地区信息失败:', error);
      return false;
    }
  }

  /**
   * 检查地区信息是否过期（可选功能）
   * @param {number} expireTime 过期时间（毫秒），默认30天
   * @returns {boolean} 是否过期
   */
  static isRegionExpired(expireTime = 30 * 24 * 60 * 60 * 1000) {
    const regionData = this.getUserRegion();
    if (!regionData || !regionData.timestamp) {
      return true;
    }

    const now = Date.now();
    return (now - regionData.timestamp) > expireTime;
  }

  /**
   * 更新地区信息的时间戳
   */
  static updateTimestamp() {
    const regionData = this.getUserRegion();
    if (regionData) {
      regionData.timestamp = Date.now();
      wx.setStorageSync(STORAGE_KEY, regionData);
    }
  }

  /**
   * 从位置信息保存地区（当用户通过定位获取地区时）
   * @param {Object} locationRegion 位置对应的地区信息
   */
  static saveLocationRegion(locationRegion) {
    if (locationRegion && locationRegion.regionCode) {
      const regionInfo = {
        regionCode: locationRegion.regionCode,
        regionName: locationRegion.regionName,
        level: locationRegion.level || 3,
        selectedRegions: locationRegion.selectedRegions || [],
        fullAddress: locationRegion.fullAddress || locationRegion.regionName,
        isFromLocation: true // 标记为来自定位
      };

      return this.saveUserRegion(regionInfo);
    }
    return false;
  }

  /**
   * 获取地区信息用于显示
   * @returns {Object} 显示用的地区信息
   */
  static getDisplayRegionInfo() {
    const regionData = this.getUserRegion();
    if (!regionData) {
      return {
        hasRegion: false,
        displayText: '请选择地区',
        regionCode: '',
        regionName: ''
      };
    }

    return {
      hasRegion: true,
      displayText: regionData.fullAddress || regionData.regionName,
      regionCode: regionData.regionCode,
      regionName: regionData.regionName,
      isFromLocation: regionData.isFromLocation || false
    };
  }
}

module.exports = RegionManager;
