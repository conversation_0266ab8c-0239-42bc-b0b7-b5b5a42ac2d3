/* 搜索页面样式 */
.container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 40rpx;
}

/* 搜索头部 */
.search-header {
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx;
  padding-right: 0;
  background: #fff;
  border-bottom: 1rpx solid #e5e5e5;
  position: sticky;
  top: 0;
  z-index: 100;
}

.search-bar {
  flex: 1;
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 24rpx;
  padding: 16rpx 20rpx;
  /* margin-right: 20rpx; */
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
  opacity: 0.6;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
}

.search-input::placeholder {
  color: #999;
}

.clear-btn {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 16rpx;
}

.clear-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.6;
}

.confirm-btn {
  font-size: 28rpx;
  color: #ff8382;
  padding: 8rpx 24rpx;
}

/* AI助手 */
.ai-helper-container {
  background-color: #fff;
  padding: 24rpx 28rpx;
}

.ai-helper {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  background: linear-gradient(90deg, #ff7b7b 0%, #ffb6b6 100%);
  border-radius: 16rpx;
  color: #fff;
}

.ai-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
}

.ai-text {
  font-size: 28rpx;
  flex: 1;
}

/* 标签页 */
.tab-section {
  background: #fff;
  padding: 0 32rpx;
  border-bottom: 1rpx solid #e5e5e5;
}

.tab-list {
  display: flex;
  align-items: center;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
  transition: all 0.3s ease;
}

.tab-item.active {
  color: #F18F01;
  font-weight: 600;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: #F18F01;
  border-radius: 2rpx;
}

/* 搜索类型提示 */
.search-type-tip {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 20rpx 32rpx;
  background: #fff;
  border-bottom: 1rpx solid #e5e5e5;
}

.tip-icon {
  width: 28rpx;
  height: 28rpx;
  opacity: 0.6;
}

.tip-text {
  font-size: 26rpx;
  color: #666;
}

/* 分类标签 */
.category-section {
  background: #fff;
  padding: 24rpx 32rpx;
}

.category-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.category-item {
  padding: 12rpx 24rpx;
  background: #f8f9fa;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #666;
  transition: all 0.3s ease;
}

.category-item.active {
  background: #ff7d7d;
  color: #fff;
}

/* 搜索结果 */
.search-results {
  padding: 26rpx;
  padding-top: 0;
  background-color: #fff;
}

.section-title {
  display: inline-block;
  position: relative;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  padding-bottom: 8rpx;
  margin-bottom: 24rpx;
  color: #ff6b6b;
  font-weight: bold;
}

.section-title::after {
  content: '';
  display: block;
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 6rpx;
  background: #ff6b6b;
  border-radius: 3rpx;
} 

/* 帖子列表 */
.post-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

/* 机构列表 */
.institution-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.institution-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.institution-item:active {
  transform: scale(0.98);
}

.institution-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.institution-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.institution-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.institution-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.institution-location {
  font-size: 24rpx;
  color: #999;
  display: flex;
  align-items: center;
}

.institution-location::before {
  content: '📍';
  margin-right: 8rpx;
}

/* 名片列表 */
.business-card-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.business-card-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.business-card-item:active {
  transform: scale(0.98);
}

.card-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.card-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.card-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.card-company {
  font-size: 26rpx;
  color: #666;
}

.card-position {
  font-size: 24rpx;
  color: #999;
}

/* 推荐内容 */
.recommend-section {
  padding: 26rpx;
  padding-top: 0;
  background-color: #fff;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #F18F01;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 24rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 无搜索结果 */
.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.no-results-icon {
  width: 120rpx;
  height: 120rpx;
  opacity: 0.3;
  margin-bottom: 32rpx;
}

.no-results-text {
  font-size: 28rpx;
  color: #999;
}
