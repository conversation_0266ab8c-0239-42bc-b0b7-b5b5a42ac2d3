# 搜索中心经纬度显示和地图滑动重新请求修复说明

## 问题描述

1. **搜索中心经纬度没有显示**：地图页面上方的搜索中心经纬度信息没有正确显示
2. **滑动地图没有重新从后端请求获取帖子**：用户拖拽地图时，没有根据新的地图区域重新加载帖子数据

## 问题原因分析

### 1. 搜索中心经纬度显示问题
- WXML中使用了 `{{searchLatitude.toFixed(4)}}` 语法，但小程序不支持在模板中直接调用JavaScript方法
- 条件判断 `wx:if="{{searchLatitude && searchLongitude}}"` 可能因为数据类型问题导致判断失败

### 2. 地图滑动重新请求问题
- `onRegionChange` 方法被重复定义了两次（第382行和第1023行），后面的定义覆盖了前面的
- 两个方法的逻辑不完全一致，导致功能异常
- 存在多个重复的辅助方法（`shouldLoadNewData`、`calculateSearchRadius`等）

## 修复方案

### 1. 修复搜索中心经纬度显示

#### 添加格式化的经纬度数据字段
```javascript
// 在data中添加格式化的经纬度字段
formattedSearchLatitude: '',
formattedSearchLongitude: ''
```

#### 在设置搜索经纬度时同时设置格式化值
```javascript
// 获取用户位置时
this.setData({
  searchLatitude: res.latitude,
  searchLongitude: res.longitude,
  // 格式化的搜索中心经纬度
  formattedSearchLatitude: res.latitude.toFixed(4),
  formattedSearchLongitude: res.longitude.toFixed(4)
});

// 地图区域变化时
this.setData({
  searchLatitude: region.northeast.latitude,
  searchLongitude: region.northeast.longitude,
  // 格式化的搜索中心经纬度
  formattedSearchLatitude: region.northeast.latitude.toFixed(4),
  formattedSearchLongitude: region.northeast.longitude.toFixed(4)
});
```

#### 更新WXML模板
```xml
<!-- 使用格式化的经纬度字段 -->
<cover-view wx:if="{{formattedSearchLatitude && formattedSearchLongitude}}" class="map-status-info">
  <cover-text class="status-text">搜索中心: {{formattedSearchLatitude}}, {{formattedSearchLongitude}}</cover-text>
  <cover-text class="status-text">搜索范围: {{searchRadiusText}}</cover-text>
</cover-view>
```

### 2. 修复地图滑动重新请求功能

#### 合并重复的onRegionChange方法
- 删除第1023行的重复定义
- 保留第382行的定义，并增强其功能
- 在地图区域变化时同时更新搜索中心经纬度

#### 统一搜索半径计算方法
- 删除重复的 `calculateSearchRadius` 方法
- 统一使用 `MapUtils.getRadiusByScale(scale)` 方法

#### 删除重复的辅助方法
- 删除重复的 `shouldLoadNewData` 方法
- 删除重复的 `debounceLoadRegionData` 和 `loadPostsInCurrentRegion` 方法

#### 优化的onRegionChange方法
```javascript
onRegionChange(e) {
  const { type, detail } = e;
  console.log('地图区域变化:', type, detail);

  if (type === 'begin') {
    // 开始拖拽时清除定时器
    if (this.regionChangeTimer) {
      clearTimeout(this.regionChangeTimer);
    }
  } else if (type === 'end') {
    const region = detail;
    console.log('地图区域变化结束:', region);

    // 更新当前区域信息和搜索中心经纬度
    this.setData({
      latitude: region.northeast.latitude,
      longitude: region.northeast.longitude,
      scale: region.scale,
      currentRegion: region,
      // 更新搜索中心经纬度
      searchLatitude: region.northeast.latitude,
      searchLongitude: region.northeast.longitude,
      // 格式化的搜索中心经纬度
      formattedSearchLatitude: region.northeast.latitude.toFixed(4),
      formattedSearchLongitude: region.northeast.longitude.toFixed(4)
    });

    // 根据缩放比例计算搜索半径
    const newRadius = MapUtils.getRadiusByScale(region.scale);
    
    // 更新搜索半径
    this.setData({
      searchRadius: newRadius
    });

    // 更新搜索范围文本显示
    this.updateSearchRadiusText(newRadius);

    // 检查是否需要加载新数据
    const shouldLoad = this.shouldLoadNewData(region);
    console.log('是否需要加载新数据:', shouldLoad);

    if (shouldLoad) {
      console.log('准备加载新数据...');
      // 防抖处理，避免频繁请求
      if (this.regionChangeTimer) {
        clearTimeout(this.regionChangeTimer);
      }

      this.regionChangeTimer = setTimeout(() => {
        console.log('开始加载区域数据...');
        this.loadPostsInRegion(region);
      }, 800); // 0.8秒后执行，提升响应速度
    } else {
      console.log('区域变化不足，跳过数据加载');
    }
  }
}
```

## 修复效果

### 1. 搜索中心经纬度正常显示
- 地图页面上方会显示当前搜索中心的经纬度（保留4位小数）
- 显示当前搜索范围（如：5km、2km等）
- 信息会在地图移动时实时更新

### 2. 地图滑动自动重新请求
- 用户拖拽地图时，会根据移动距离和缩放变化判断是否需要重新加载数据
- 使用防抖机制，避免频繁请求（800ms延迟）
- 根据地图缩放级别自动调整搜索半径
- 在控制台输出详细的调试信息，便于问题排查

## 技术要点

1. **小程序模板限制**：不能在WXML中直接调用JavaScript方法，需要在JS中预处理数据
2. **防抖机制**：避免用户快速拖拽地图时产生大量API请求
3. **智能加载判断**：只有当地图中心移动超过一定距离或缩放变化超过阈值时才重新加载
4. **代码去重**：删除重复的方法定义，避免功能冲突

## 测试建议

1. 打开地图页面，检查上方是否显示搜索中心经纬度
2. 拖拽地图到不同位置，观察经纬度是否实时更新
3. 拖拽地图后等待1秒，检查是否有新的帖子加载
4. 缩放地图，观察搜索范围是否相应调整
5. 查看控制台日志，确认区域变化检测和数据加载逻辑正常工作
