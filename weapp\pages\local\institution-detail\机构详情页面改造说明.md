# 机构详情页面改造说明

## 改造概述

将机构详情页面的"服务项目"Tab改为"发布的信息"Tab，显示该机构发布的所有帖子信息。

## 主要修改内容

### 1. JavaScript文件修改 (`institution-detail.js`)

#### 数据结构调整
```javascript
// 原来的服务列表改为帖子列表
data: {
  // 机构发布的帖子列表
  postList: [],
  // 当前Tab改为 info, posts, review
  currentTab: 'info', // info, posts, review
  // 帖子加载状态
  postsLoading: false
}
```

#### 新增功能方法

1. **加载机构帖子列表**
```javascript
async loadInstitutionPosts(institutionId) {
  // 调用帖子列表接口，按机构ID筛选
  const response = await request({
    url: '/blade-chat/post/list',
    method: 'GET',
    data: {
      institutionId: institutionId,
      current: 1,
      size: 20
    }
  });
}
```

2. **处理帖子数据**
```javascript
processPostList(rawPosts) {
  // 格式化帖子数据，包括时间格式化、图片处理等
  return rawPosts.map(post => ({
    id: post.id,
    title: post.title || '无标题',
    content: post.content || '',
    images: post.images ? post.images.split(',').filter(img => img.trim()) : [],
    author: post.user ? post.user.nickname || '匿名用户' : '匿名用户',
    // ... 其他字段处理
  }));
}
```

3. **帖子相关操作**
```javascript
// 查看帖子详情
onViewPost(e) {
  const postId = e.currentTarget.dataset.postId;
  wx.navigateTo({
    url: `/pages/post-detail/post-detail?id=${postId}`
  });
}

// 点赞帖子
async onLikePost(e) {
  // 切换点赞状态并更新UI
}
```

#### Tab切换逻辑更新
```javascript
onTabChange(e) {
  const tab = e.currentTarget.dataset.tab;
  this.setData({ currentTab: tab });

  if (tab === 'posts' && this.data.postList.length === 0) {
    this.loadInstitutionPosts(this.data.institutionId);
  }
  // ...
}
```

### 2. WXML文件修改 (`institution-detail.wxml`)

#### Tab导航更新
```xml
<!-- 将"服务项目"改为"发布的信息" -->
<view class="tab-item {{currentTab === 'posts' ? 'active' : ''}}"
      data-tab="posts" bindtap="onTabChange">
  发布的信息
</view>
```

#### 帖子列表内容
```xml
<!-- 发布的信息 -->
<view wx:elif="{{currentTab === 'posts'}}" class="posts-content">
  <!-- 加载状态 -->
  <view wx:if="{{postsLoading}}" class="loading-state">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
  
  <!-- 空状态 -->
  <view wx:elif="{{postList.length === 0}}" class="empty-state">
    <image class="empty-icon" src="/assets/images/common/empty.png" mode="aspectFit"/>
    <text class="empty-text">该机构暂未发布任何信息</text>
  </view>
  
  <!-- 帖子列表 -->
  <view wx:else class="post-list">
    <view wx:for="{{postList}}" wx:key="id" class="post-item"
          data-post-id="{{item.id}}" bindtap="onViewPost">
      <!-- 帖子头部：作者信息、分类 -->
      <view class="post-header">
        <view class="post-author">
          <image class="author-avatar" src="{{item.avatar}}" mode="aspectFill"/>
          <view class="author-info">
            <text class="author-name">{{item.author}}</text>
            <text class="post-time">{{item.createTime}}</text>
          </view>
        </view>
        <view wx:if="{{item.category}}" class="post-category">{{item.category}}</view>
      </view>

      <!-- 帖子内容：标题、正文、图片 -->
      <view class="post-content">
        <view wx:if="{{item.title}}" class="post-title">{{item.title}}</view>
        <view class="post-text">{{item.content}}</view>
        
        <view wx:if="{{item.images && item.images.length > 0}}" class="post-images">
          <image wx:for="{{item.images}}" wx:for-item="image" wx:key="*this"
                 class="post-image" src="{{image}}" mode="aspectFill" lazy-load="{{true}}"/>
        </view>
      </view>

      <!-- 帖子操作：点赞、评论 -->
      <view class="post-actions">
        <view class="action-item {{item.isLiked ? 'liked' : ''}}"
              data-post-id="{{item.id}}" data-index="{{index}}"
              bindtap="onLikePost" catchtap="onLikePost">
          <image class="action-icon" src="/assets/images/common/like.png" mode="aspectFit"/>
          <text class="action-text">{{item.likeCount || 0}}</text>
        </view>
        <view class="action-item">
          <image class="action-icon" src="/assets/images/common/comment.png" mode="aspectFit"/>
          <text class="action-text">{{item.commentCount || 0}}</text>
        </view>
      </view>
    </view>
  </view>
</view>
```

### 3. WXSS文件修改 (`institution-detail.wxss`)

#### 帖子列表样式
```css
/* 帖子内容 */
.posts-content {
  padding: 20rpx;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

/* 帖子列表 */
.post-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.post-item {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease;
}

.post-item:active {
  transform: scale(0.98);
}

/* 帖子头部样式 */
.post-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.post-author {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.author-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #f5f5f5;
}

/* 帖子内容样式 */
.post-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.post-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 20rpx;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 帖子图片 */
.post-images {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
  margin-top: 20rpx;
}

.post-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  background: #f5f5f5;
}

/* 帖子操作 */
.post-actions {
  display: flex;
  align-items: center;
  gap: 40rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.action-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 10rpx;
  border-radius: 20rpx;
  transition: background-color 0.2s ease;
}

.action-item.liked .action-text {
  color: #ff6b35;
}
```

## 接口对接

### 获取机构帖子接口
- **接口地址**: `GET /blade-chat/post/list`
- **请求参数**:
  ```javascript
  {
    institutionId: '机构ID',
    current: 1,        // 当前页码
    size: 20          // 每页数量
  }
  ```
- **响应格式**:
  ```javascript
  {
    code: 200,
    data: {
      records: [
        {
          id: '帖子ID',
          title: '帖子标题',
          content: '帖子内容',
          images: '图片URL,多个用逗号分隔',
          user: {
            nickname: '用户昵称',
            avatar: '用户头像'
          },
          category: {
            name: '分类名称'
          },
          likeCount: 0,
          commentCount: 0,
          createTime: '2025-01-01 12:00:00',
          isLiked: false,
          isFavorited: false
        }
      ]
    }
  }
  ```

## 功能特性

1. **懒加载**: 只有切换到"发布的信息"Tab时才加载帖子数据
2. **加载状态**: 显示加载动画和加载文本
3. **空状态**: 当机构没有发布帖子时显示友好的空状态提示
4. **帖子预览**: 显示帖子标题、内容预览（最多3行）、图片等
5. **交互操作**: 点击帖子跳转详情页，点赞功能
6. **时间格式化**: 智能显示相对时间（刚刚、几分钟前、几小时前等）
7. **响应式设计**: 适配不同屏幕尺寸

## 测试建议

1. **基本功能测试**:
   - 打开机构详情页面
   - 切换到"发布的信息"Tab
   - 检查帖子列表是否正确显示

2. **交互测试**:
   - 点击帖子项跳转到帖子详情页
   - 测试点赞功能
   - 测试加载状态显示

3. **边界情况测试**:
   - 机构没有发布帖子的情况
   - 网络异常时的错误处理
   - 图片加载失败的处理

4. **性能测试**:
   - 大量帖子时的滚动性能
   - 图片懒加载效果
   - 页面切换的流畅度
