/**
 * 数字精度处理测试和使用示例
 * 用于验证数字精度处理是否正常工作
 */
const NumberUtils = require('./numberUtils');

/**
 * 测试数字精度处理
 */
function testNumberPrecision() {
  console.log('=== 数字精度处理测试 ===');
  
  // 测试1: 大整数精度丢失
  console.log('\n1. 大整数精度测试:');
  const bigInt = 9007199254740992; // 超出安全范围
  console.log('原始数字:', bigInt);
  console.log('是否安全:', NumberUtils.isSafeNumber(bigInt));
  console.log('安全转换:', NumberUtils.safeParse(bigInt));
  
  // 测试2: 浮点数精度问题
  console.log('\n2. 浮点数精度测试:');
  const a = 0.1;
  const b = 0.2;
  console.log('0.1 + 0.2 =', a + b); // 0.30000000000000004
  console.log('精确加法:', NumberUtils.add(a, b)); // 0.3
  
  // 测试3: ID字段处理
  console.log('\n3. ID字段处理测试:');
  const testData = {
    id: 9007199254740993,
    userId: 123456789,
    name: '测试用户',
    posts: [
      { id: 9007199254740994, title: '帖子1' },
      { id: 9007199254740995, title: '帖子2' }
    ]
  };
  console.log('原始数据:', testData);
  console.log('处理后:', NumberUtils.processIdFields(testData));
  
  // 测试4: 模拟API响应数据
  console.log('\n4. API响应数据处理测试:');
  const apiResponse = {
    code: 200,
    data: {
      records: [
        {
          id: 9007199254740996,
          userId: 9007199254740997,
          postId: 9007199254740998,
          title: '测试帖子',
          latitude: 39.908823,
          longitude: 116.397470,
          createTime: '2024-01-01 12:00:00'
        }
      ],
      total: 1,
      current: 1,
      size: 10
    }
  };
  
  // 模拟请求拦截器处理
  const processedResponse = processApiResponse(apiResponse);
  console.log('处理后的API响应:', JSON.stringify(processedResponse, null, 2));
}

/**
 * 模拟API响应处理（类似request.js中的处理逻辑）
 */
function processApiResponse(data) {
  if (!data || typeof data !== 'object') {
    return data;
  }
  
  // 递归处理对象
  if (Array.isArray(data)) {
    return data.map(item => processApiResponse(item));
  }
  
  const result = {};
  for (const key in data) {
    if (data.hasOwnProperty(key)) {
      const value = data[key];
      
      if (typeof value === 'number') {
        // 检查是否超出安全整数范围
        if (!Number.isSafeInteger(value) && Number.isInteger(value)) {
          result[key] = value.toString();
        } else {
          result[key] = value;
        }
      } else if (typeof value === 'object' && value !== null) {
        result[key] = processApiResponse(value);
      } else {
        result[key] = value;
      }
    }
  }
  
  return result;
}

/**
 * 在页面中使用的示例
 */
const usageExample = {
  // 在页面的data中
  data: {
    postId: '', // 将作为字符串处理
    userInfo: null,
    coordinates: { lat: 0, lng: 0 }
  },
  
  // 处理API返回的数据
  handleApiData(apiData) {
    // 处理ID字段
    const processedData = NumberUtils.processIdFields(apiData);
    
    // 处理坐标等精度敏感数据
    if (processedData.latitude && processedData.longitude) {
      processedData.coordinates = {
        lat: NumberUtils.safeParse(processedData.latitude, 'number'),
        lng: NumberUtils.safeParse(processedData.longitude, 'number')
      };
    }
    
    return processedData;
  },
  
  // 计算距离时使用精确计算
  calculateDistance(lat1, lng1, lat2, lng2) {
    const R = 6371; // 地球半径（公里）
    
    // 使用精确计算避免浮点数误差
    const dLat = NumberUtils.multiply(
      NumberUtils.subtract(lat2, lat1),
      Math.PI / 180
    );
    const dLng = NumberUtils.multiply(
      NumberUtils.subtract(lng2, lng1),
      Math.PI / 180
    );
    
    const a = NumberUtils.add(
      Math.sin(dLat / 2) * Math.sin(dLat / 2),
      NumberUtils.multiply(
        NumberUtils.multiply(Math.cos(lat1 * Math.PI / 180), Math.cos(lat2 * Math.PI / 180)),
        Math.sin(dLng / 2) * Math.sin(dLng / 2)
      )
    );
    
    const c = NumberUtils.multiply(2, Math.atan2(Math.sqrt(a), Math.sqrt(1 - a)));
    return NumberUtils.multiply(R, c);
  }
};

// 导出测试函数和使用示例
module.exports = {
  testNumberPrecision,
  usageExample,
  processApiResponse
};

// 如果直接运行此文件，执行测试
if (typeof wx === 'undefined') {
  // 在Node.js环境中运行测试
  try {
    testNumberPrecision();
  } catch (error) {
    console.error('测试执行失败:', error);
  }
}
