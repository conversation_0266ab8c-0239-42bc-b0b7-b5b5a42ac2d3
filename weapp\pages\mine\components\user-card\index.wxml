<view class="user-card">
  <view class="avatar-wrap">
    <image class="avatar" src="{{avatar}}" mode="aspectFill"></image>
  </view>
  <view class="user-info">
    <view class="nickname">{{nickname}}</view>
    <view class="tags">
      <text wx:for="{{tags}}" wx:key="*this" class="tag {{item.type}}">{{item.text}}</text>
    </view>
  </view>
  <view class="edit-btn" bindtap="onEdit">
    <image src="/assets/images/mine/edit.png" mode="aspectFit"></image>
    <text>编辑资料</text>
  </view>
</view> 