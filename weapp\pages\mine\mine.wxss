/* mine.wxss */
page {
  height: 100vh;
  background: #f5f5f5;
}

/* 滚动容器 */
.scroll-container {
  position: relative;
  background: #f5f5f5;
  box-sizing: border-box;
}

/* 主要内容区域 */
.main-content {
  position: relative;
  z-index: 1;
  width: 100%;
  padding: 0 18rpx ;
  box-sizing: border-box;
  padding-bottom: env(safe-area-inset-bottom);
}

.container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 0;
  box-sizing: border-box;
  padding-bottom: 50rpx;
}

/* 用户信息区域 */
.user-info-section {
  display: flex;
  align-items: center;
  padding: 40rpx 32rpx;
  background: linear-gradient(to bottom, #ff6b6b, #ff8585);
  margin: 20rpx;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(255, 107, 107, 0.2);
}

.avatar-wrap {
  margin-right: 24rpx;
  position: relative;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.user-detail {
  flex: 1;
}

.name-wrap {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.nickname {
  font-size: 36rpx;
  font-weight: 600;
  color: #ffffff;
  line-height: 1.4;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.tags {
  display: flex;
  gap: 12rpx;
}

.tag {
  padding: 4rpx 16rpx;
  border-radius: 8rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.tag-blue {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
}

.tag-purple {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
}

.settings-btn {
  padding: 16rpx;
  margin: -16rpx;
}

.settings-btn image {
  width: 48rpx;
  height: 48rpx;
  opacity: 0.9;
  filter: brightness(0) invert(1);
}

/* 统计数据 */
.stats-section {
  display: flex;
  justify-content: space-between;
  padding: 32rpx;
  background: #ffffff;
  margin: 20rpx;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  flex: 1;
  position: relative;
}

.stats-item:not(:last-child)::after {
  content: "";
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 40rpx;
  background: rgba(0, 0, 0, 0.05);
}

.stats-num {
  font-size: 40rpx;
  font-weight: 600;
  color: #ff6b6b;
  line-height: 1.2;
}

.stats-label {
  font-size: 24rpx;
  color: #999999;
}

/* 功能菜单 */
.menu-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
  padding: 30rpx 20rpx;
  background: #ffffff;
  margin: 20rpx;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 160rpx;
  border: 1px solid #f0f0f0;
  border-radius: 12rpx;
  padding: 20rpx 10rpx;
  background: #ffffff;
  transition: all 0.3s ease;
}

.menu-item:active {
  transform: scale(0.95);
  background: #f8f8f8;
}

.menu-icon-wrap {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 88rpx;
  height: 88rpx;
  margin-bottom: 12rpx;
  background: linear-gradient(135deg, #ff6b6b, #ff8585);
  border-radius: 50%;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.2);
}

.menu-icon {
  width: 40rpx;
  height: 40rpx;
  filter: brightness(0) invert(1);
}

.menu-text {
  font-size: 24rpx;
  color: #333333;
  line-height: 1.4;
  text-align: center;
  font-weight: 500;
}

/* 我的发布列表 */
.my-posts-section {
  background: #ffffff;
  padding: 30rpx;
  margin: 20rpx;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.section-more {
  font-size: 28rpx;
  color: #ff6b6b;
  font-weight: 500;
}

.post-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.post-item {
  display: flex;
  gap: 24rpx;
  padding: 24rpx;
  background: #ffffff;
  border-radius: 16rpx;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.post-item:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.1);
}

.post-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  flex-shrink: 0;
}

.post-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.post-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  line-height: 1.4;
  margin-bottom: 12rpx;
}

.post-tags {
  margin-top: 16rpx;
  display: flex;
  gap: 12rpx;
  flex-wrap: wrap;
}

.post-tag {
  padding: 4rpx 12rpx;
  font-size: 22rpx;
  color: #ff6b6b;
  background: rgba(255, 107, 107, 0.1);
  border-radius: 12rpx;
}

.post-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16rpx;
}

.post-stats {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.view-icon,
.like-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.6;
}

.stats-text {
  font-size: 24rpx;
  color: #999999;
}

.post-actions {
  display: flex;
  gap: 16rpx;
}

.action-icon {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.6;
}

.delete-icon {
  opacity: 0.4;
}

/* 数据统计 */
.data-section-wrap {
  margin: 20rpx;
}

.data-section {
  background: #ffffff;
  padding: 30rpx;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.data-header {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 24rpx;
}

.data-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.data-item:last-child {
  border-bottom: none;
}

.data-label {
  font-size: 28rpx;
  color: #666666;
}

.data-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.data-value.success {
  color: #52c41a;
}

/* 每日任务 */
.task-section {
  background: #ffffff;
  padding: 30rpx;
  margin: 20rpx;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.task-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.task-points {
  font-size: 28rpx;
  color: #ff6b6b;
  font-weight: 600;
}

.task-progress {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-inner {
  height: 100%;
  background: linear-gradient(90deg, #ff6b6b, #ff8585);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.task-desc {
  font-size: 24rpx;
  color: #999999;
}

/* 点击反馈 */
.menu-item:active,
.post-item:active {
  opacity: 0.8;
  transform: scale(0.98);
}

.edit-icon:active,
.delete-icon:active {
  opacity: 0.9;
  transform: scale(0.95);
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 32rpx;
  color: #999999;
  font-size: 24rpx;
}

.no-data {
  text-align: center;
  padding: 48rpx 32rpx;
  color: #999999;
  font-size: 28rpx;
}

.bg-circle {
  position: absolute;
  top: -600rpx;
  left: -200rpx;
  right: -200rpx;
  height: 1000rpx;
  background: linear-gradient(180deg, #4c6fff 0%, #3d5ccc 100%);
  border-radius: 0 0 100% 100% / 0 0 50% 50%;
  transform: scaleX(1.2);
  z-index: 1;
  overflow: hidden;
}

.bg-circle::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 70% 30%,
      rgba(255, 255, 255, 0.2) 0%,
      transparent 60%
    ),
    radial-gradient(
      circle at 30% 60%,
      rgba(255, 255, 255, 0.15) 0%,
      transparent 50%
    );
  z-index: 1;
  transform: scaleX(0.8); /* 补偿父元素的拉伸 */
}

.bg-circle::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='rgba(255,255,255,0.05)' fill-rule='evenodd'/%3E%3C/svg%3E"),
    linear-gradient(
      45deg,
      rgba(255, 255, 255, 0.05) 25%,
      transparent 25%,
      transparent 75%,
      rgba(255, 255, 255, 0.05) 75%,
      rgba(255, 255, 255, 0.05)
    );
  background-size: 100px 100px, 60px 60px;
  opacity: 0.3;
  z-index: 2;
  transform: scaleX(0.8); /* 补偿父元素的拉伸 */
}

.user-info {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 140rpx 32rpx 32rpx;
}

.user-left {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.edit-profile {
  padding: 4rpx 16rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 24rpx;
  font-size: 24rpx;
  color: #fff;
}

.edit-profile:active {
  opacity: 0.8;
}

.action-wrap {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn .menu-icon {
  width: 48rpx;
  height: 48rpx;
  filter: brightness(0) invert(1);
}

/* 功能网格样式 */
.function-grid {
  position: relative;
  z-index: 2;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24rpx;
  margin: 0 32rpx 24rpx;
  padding: 32rpx;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16rpx;
  backdrop-filter: blur(10px);
}

.grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}

.grid-icon {
  width: 56rpx;
  height: 56rpx;
  filter: brightness(0) invert(1);
}

.grid-text {
  font-size: 24rpx;
  color: #fff;
}

/* 菜单列表样式 */
.menu-list {
  position: relative;
  z-index: 2;
  margin: 0 32rpx 24rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  overflow: hidden;
  backdrop-filter: blur(10px);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.menu-item:not(:last-child)::after {
  content: "";
  position: absolute;
  left: 24rpx;
  right: 24rpx;
  bottom: 0;
  height: 1rpx;
  background: rgba(0, 0, 0, 0.05);
}

.menu-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 24rpx;
}

.menu-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.arrow-icon {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.3;
}

/* 底部信息样式 */
.footer-info {
  position: relative;
  z-index: 2;
  padding: 48rpx 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}

.footer-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
}

.footer-links {
  display: flex;
  gap: 24rpx;
  margin-top: 12rpx;
}

.link-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  position: relative;
}

.link-text:not(:last-child)::after {
  content: "";
  position: absolute;
  right: -12rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 1rpx;
  height: 24rpx;
  background: rgba(255, 255, 255, 0.3);
}

.menu-item:active {
  opacity: 0.7;
}

.nickname-unlogin {
  color: rgba(255, 255, 255, 0.9);
}

.mine-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.avatar-wrapper {
  margin-right: 30rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
}

.user-detail {
  flex: 1;
}

.function-list {
  background-color: #ffffff;
  margin-bottom: 20rpx;
}

.function-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.function-item:last-child {
  border-bottom: none;
}

.item-text {
  font-size: 28rpx;
  color: #333;
}

.arrow {
  color: #999;
  font-size: 28rpx;
}

/* 退出登录按钮样式 */
.logout-section {
  padding: 40rpx 30rpx;
  margin-top: 32rpx;
}

.logout-btn {
  width: 100%;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  color: #ff5a5f;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 44rpx;
  border: none;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.logout-btn::after {
  border: none;
}

.logout-btn:active {
  opacity: 0.9;
  transform: scale(0.98);
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

.stats-container {
  display: flex;
  justify-content: space-around;
  padding: 30rpx;
  background: #fff;
  margin: 20rpx 0;
  border-radius: 12rpx;
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stats-num {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #666;
}
