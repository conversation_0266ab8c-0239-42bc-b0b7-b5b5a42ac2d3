Component({
  properties: {
    text: { type: String, value: '按钮' },
    active: { type: Boolean, value: true },
    activeColor: { type: String, value: 'linear-gradient(90deg, #ff6b6b, #ff8585)' },
    inactiveColor: { type: String, value: '#e5e5e5' },
    activeTextColor: { type: String, value: '#fff' },
    inactiveTextColor: { type: String, value: '#bbb' },
    width: { type: String, value: '100%' },
    height: { type: String, value: '56rpx' },
    radius: { type: String, value: '28rpx' },
   
  },
  data:{
    btnColor: 'linear-gradient(90deg, #ff6b6b, #ff8585)',
    btnTextColor: '#fff'
  },

  
  lifetimes: {
    attached() {
      this.setData({
        btnColor: this.data.active ? this.data.activeColor : this.data.inactiveColor,
        btnTextColor: this.data.active ? this.data.activeTextColor : this.data.inactiveTextColor
      });
    }
  },
  methods: {
    onTap(e) {
      if (!this.data.active) return;
      this.triggerEvent('tap', e);
    }
  }
});
