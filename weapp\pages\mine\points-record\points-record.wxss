.points-record-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 页面标题 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx 30rpx;
  color: white;
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
}

/* 筛选区域 */
.filter-section {
  background: white;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
}

.filter-label {
  font-size: 28rpx;
  color: #666;
}

.filter-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.filter-arrow {
  font-size: 24rpx;
  color: #999;
}

/* 记录列表 */
.records-list {
  padding: 0 30rpx;
}

.record-item {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.record-left {
  flex: 1;
  margin-right: 20rpx;
}

.record-type {
  margin-bottom: 10rpx;
}

.type-name {
  font-size: 32rpx;
  font-weight: 600;
}

.type-signin {
  color: #52c41a;
}

.type-exchange {
  color: #fa8c16;
}

.type-share {
  color: #1890ff;
}

.type-invite {
  color: #722ed1;
}

.type-admin {
  color: #eb2f96;
}

.type-other {
  color: #666;
}

.record-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.record-time {
  font-size: 24rpx;
  color: #999;
}

.record-right {
  text-align: right;
}

.record-points {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.record-points.positive {
  color: #52c41a;
}

.record-points.negative {
  color: #ff4d4f;
}

.record-balance {
  font-size: 24rpx;
  color: #999;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 40rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 没有更多数据 */
.no-more {
  text-align: center;
  padding: 40rpx 0;
}

.no-more-text {
  font-size: 24rpx;
  color: #999;
} 