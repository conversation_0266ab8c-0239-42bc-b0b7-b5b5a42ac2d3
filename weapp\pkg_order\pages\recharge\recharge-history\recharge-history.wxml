<!--pages/recharge-history/recharge-history.wxml-->
<view class="container">
  <view class="header">
    <text class="title">充值记录</text>
    <text class="subtitle">查看您的充值历史</text>
  </view>

  <!-- 记录列表 -->
  <view class="records-list" wx:if="{{records.length > 0}}">
    <view class="record-item" wx:for="{{records}}" wx:key="orderNo" bindtap="viewOrderDetail" data-order-no="{{item.orderNo}}">
      <view class="record-header">
        <view class="order-info">
          <text class="order-no">订单号: {{item.orderNo}}</text>
          <text class="order-time">{{formatTime(item.createTime)}}</text>
        </view>
        <view class="order-status {{getStatusClass(item.orderStatus, item.paymentStatus)}}">
          {{getStatusText(item.orderStatus, item.paymentStatus)}}
        </view>
      </view>
      
      <view class="record-content">
        <view class="amount-info">
          <text class="amount">¥{{item.rechargeAmount}}</text>
          <text class="payment-method">微信支付</text>
        </view>
        
        <view class="record-details">
          <view class="detail-item" wx:if="{{item.paymentTime}}">
            <text class="detail-label">支付时间:</text>
            <text class="detail-value">{{formatTime(item.paymentTime)}}</text>
          </view>
          
          <view class="detail-item" wx:if="{{item.successTime}}">
            <text class="detail-label">完成时间:</text>
            <text class="detail-value">{{formatTime(item.successTime)}}</text>
          </view>
          
          <view class="detail-item" wx:if="{{item.remark}}">
            <text class="detail-label">备注:</text>
            <text class="detail-value">{{item.remark}}</text>
          </view>
        </view>
      </view>
      
      <!-- 操作按钮 -->
      <view class="record-actions" wx:if="{{item.paymentStatus === 'UNPAID'}}">
        <button class="action-btn retry" bindtap="retryPayment" data-order-no="{{item.orderNo}}" catchtap="">
          重新支付
        </button>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && records.length === 0}}">
    <view class="empty-icon">📝</view>
    <text class="empty-text">暂无充值记录</text>
    <text class="empty-tip">您还没有进行过充值</text>
    <button class="empty-btn" bindtap="goToRecharge">立即充值</button>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading && records.length === 0}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{records.length > 0}}">
    <view class="load-more-content" wx:if="{{loading}}">
      <view class="loading-spinner small"></view>
      <text class="load-more-text">加载中...</text>
    </view>
    <view class="load-more-content" wx:elif="{{hasMore}}">
      <text class="load-more-text">上拉加载更多</text>
    </view>
    <view class="load-more-content" wx:else>
      <text class="load-more-text">已加载全部记录</text>
    </view>
  </view>

  <!-- 统计信息 -->
  <view class="stats-info" wx:if="{{records.length > 0}}">
    <text class="stats-text">共 {{total}} 条记录</text>
  </view>
</view>
