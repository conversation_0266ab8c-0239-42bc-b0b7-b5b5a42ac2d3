/* 颜色变量 */
:root {
  --primary-color: #FF7D7D;
  --primary-btn-color: #c32a2a;
  --secondary-color: #00C6FF;
  --text-color: #222222;
  --text-front-size: 24rpx;
  --text-light-color: #888888;
  --background-color: #f7f8fa;
  --card-background: #ffffff;
  --border-color: #eeeeee;
  --input-background: #f7f8fa;
  --input-border: #eeeeee;
  --input-text: #333333;
  --input-placeholder: #999999;
  --input-focus-border: #FF7D7D;
  --input-focus-shadow: 0 0 10rpx rgba(255, 125, 125, 0.3);
}

/* 深色模式 */
@media (prefers-color-scheme: dark) {
  :root {
    --text-color: #FFFFFF;
    --text-light-color: #CCCCCC;
    --background-color: #1E1E1E;
    --card-background: #2D2D2D;
    --border-color: #444444;
  }
}

/* 基础样式 */
page {
  background-color: #ffffff;
  color: var(--text-color);
  font-size: 28rpx;
  line-height: 1.6;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

view {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}