// pkg_user/pages/cooperation/cooperation.js
const { request } = require('../../../utils/request');

Page({
  data: {
    // 表单数据
    formData: {
      name: '',
      phone: '',
      wechat: '',
      cooperationType: 'agent',
      cooperationDetails: '',
      remarks: ''
    },
    // 合作类型选项
    cooperationTypes: [
      { id: 'agent', name: '代理商加盟', desc: '成为我们的区域代理商' },
      { id: 'advertising', name: '广告合作', desc: '品牌广告投放合作' },
      { id: 'sponsor', name: '赞助合作', desc: '活动赞助合作' },
      { id: 'promotion', name: '联合推广', desc: '品牌联合推广合作' },
      { id: 'other', name: '其他合作', desc: '其他形式的合作' }
    ],
    // 提交状态
    submitting: false,
    // 当前选中的合作类型详情
    selectedTypeInfo: null
  },

  onLoad() {
    console.log('合作申请页面加载');
    // 设置默认选中的合作类型信息
    this.updateSelectedTypeInfo('agent');
  },

  // 输入框变化
  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 合作类型选择
  onCooperationTypeChange(e) {
    const typeId = e.currentTarget.dataset.id;
    this.setData({
      'formData.cooperationType': typeId
    });
    this.updateSelectedTypeInfo(typeId);
  },

  // 更新选中的合作类型信息
  updateSelectedTypeInfo(typeId) {
    const typeInfo = this.data.cooperationTypes.find(item => item.id === typeId);
    this.setData({
      selectedTypeInfo: typeInfo
    });
  },

  // 获取微信信息
  onGetWechatInfo() {
    wx.getUserProfile({
      desc: '用于完善合作申请信息',
      success: (res) => {
        console.log('获取用户信息成功:', res);
        // 这里可以从用户信息中提取微信相关信息
        // 注意：实际项目中需要根据具体的用户信息结构来处理
        if (res.userInfo && res.userInfo.nickName) {
          this.setData({
            'formData.wechat': res.userInfo.nickName
          });
        }
      },
      fail: (err) => {
        console.log('获取用户信息失败:', err);
        wx.showToast({
          title: '获取信息失败',
          icon: 'none'
        });
      }
    });
  },

  // 提交合作申请
  async onSubmit() {
    if (this.data.submitting) return;

    // 表单验证
    if (!this.validateForm()) {
      return;
    }

    try {
      this.setData({ submitting: true });

      // 调用后端接口
      const result = await request({
        url: '/blade-chat/user/cooperation/save',
        method: 'POST',
        data: this.data.formData
      });

      if (result.code === 200) {
        wx.showModal({
          title: '申请提交成功',
          content: '您的合作申请已提交，我们将在3个工作日内联系您，请保持电话畅通。',
          showCancel: false,
          confirmText: '知道了',
          success: () => {
            // 返回上一页或跳转到成功页面
            wx.navigateBack();
          }
        });
      } else {
        throw new Error(result.msg || '提交失败');
      }

    } catch (error) {
      console.error('提交合作申请失败:', error);
      
      let errorMsg = '提交失败，请重试';
      if (error.message) {
        errorMsg = error.message;
      }
      
      wx.showModal({
        title: '提交失败',
        content: errorMsg,
        showCancel: false,
        confirmText: '重试'
      });
    } finally {
      this.setData({ submitting: false });
    }
  },

  // 表单验证
  validateForm() {
    const { formData } = this.data;
    
    if (!formData.name.trim()) {
      wx.showToast({
        title: '请输入联系人姓名',
        icon: 'none'
      });
      return false;
    }

    if (!formData.phone.trim()) {
      wx.showToast({
        title: '请输入联系电话',
        icon: 'none'
      });
      return false;
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(formData.phone)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      });
      return false;
    }

    if (!formData.cooperationDetails.trim()) {
      wx.showToast({
        title: '请填写合作详情',
        icon: 'none'
      });
      return false;
    }

    return true;
  },

  // 查看合作政策
  onViewPolicy() {
    wx.navigateTo({
      url: '/pkg_user/pages/cooperation-policy/cooperation-policy'
    });
  },

  // 联系客服
  onContactService() {
    wx.makePhoneCall({
      phoneNumber: '************',
      fail: () => {
        wx.showModal({
          title: '联系客服',
          content: '客服电话：************\n工作时间：9:00-18:00',
          showCancel: false
        });
      }
    });
  }
});
