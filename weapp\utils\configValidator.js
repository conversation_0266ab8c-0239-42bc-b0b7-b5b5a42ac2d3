/**
 * 支付配置验证工具
 * 用于验证支付配置是否正确
 */

const { getCurrentWechatPayConfig, validatePayConfig } = require('../config/payConfigProduction');

/**
 * 验证支付配置
 */
function validatePaymentConfig() {
  console.log('🔍 开始验证支付配置...\n');
  
  try {
    const config = getCurrentWechatPayConfig();
    
    // 基础信息验证
    console.log('📋 基础配置信息:');
    console.log(`   小程序ID: ${config.appId}`);
    console.log(`   商户号: ${config.mchId}`);
    console.log(`   证书序列号: ${config.merchantSerialNumber}`);
    console.log(`   当前环境: ${config.currentEnv}`);
    console.log('');
    
    // 必要字段验证
    console.log('✅ 必要字段检查:');
    const requiredFields = [
      { field: 'appId', name: '小程序ID', value: config.appId },
      { field: 'mchId', name: '商户号', value: config.mchId },
      { field: 'apiV3Key', name: 'APIv3密钥', value: config.apiV3Key },
      { field: 'merchantSerialNumber', name: '证书序列号', value: config.merchantSerialNumber },
      { field: 'notifyUrl', name: '回调地址', value: config.notifyUrl }
    ];
    
    let missingFields = [];
    requiredFields.forEach(({ field, name, value }) => {
      if (value && value !== 'YOUR_32_CHARACTER_API_KEY_HERE' && value !== 'https://yourdomain.com/api/pay/notify') {
        console.log(`   ✅ ${name}: 已配置`);
      } else {
        console.log(`   ❌ ${name}: 未配置或使用默认值`);
        missingFields.push(name);
      }
    });
    
    console.log('');
    
    // API密钥验证
    console.log('🔑 API密钥检查:');
    if (config.mchKey && config.mchKey !== 'YOUR_32_CHARACTER_API_KEY_HERE') {
      if (config.mchKey.length === 32) {
        console.log('   ✅ API密钥长度正确 (32位)');
      } else {
        console.log(`   ⚠️  API密钥长度异常 (${config.mchKey.length}位，应为32位)`);
      }
    } else {
      console.log('   ❌ API密钥未设置');
      missingFields.push('API密钥');
    }
    
    // APIv3密钥验证
    if (config.apiV3Key && config.apiV3Key.length === 32) {
      console.log('   ✅ APIv3密钥长度正确 (32位)');
    } else {
      console.log(`   ⚠️  APIv3密钥长度异常 (${config.apiV3Key?.length || 0}位，应为32位)`);
    }
    
    console.log('');
    
    // 证书序列号验证
    console.log('📜 证书序列号检查:');
    if (config.merchantSerialNumber) {
      if (/^[A-F0-9]{40}$/.test(config.merchantSerialNumber)) {
        console.log('   ✅ 证书序列号格式正确');
      } else {
        console.log('   ⚠️  证书序列号格式可能不正确');
      }
    } else {
      console.log('   ❌ 证书序列号未配置');
    }
    
    console.log('');
    
    // 回调地址验证
    console.log('🌐 回调地址检查:');
    if (config.notifyUrl && config.notifyUrl !== 'https://yourdomain.com/api/pay/notify') {
      if (config.notifyUrl.startsWith('https://')) {
        console.log('   ✅ 回调地址使用HTTPS协议');
      } else {
        console.log('   ⚠️  回调地址应使用HTTPS协议');
      }
    } else {
      console.log('   ❌ 回调地址未配置或使用默认值');
    }
    
    console.log('');
    
    // 证书文件检查
    console.log('📁 证书文件检查:');
    const certFiles = [
      { path: config.certPath, name: '商户证书 (PEM)' },
      { path: config.privateKeyPath, name: '商户私钥 (PEM)' },
      { path: config.keyPath, name: '商户证书 (P12)' }
    ];
    
    certFiles.forEach(({ path, name }) => {
      if (path && !path.includes('/path/to/')) {
        console.log(`   ✅ ${name}: 路径已配置 (${path})`);
      } else {
        console.log(`   ⚠️  ${name}: 路径未配置或使用默认值`);
      }
    });
    
    console.log('');
    
    // 环境配置检查
    console.log('🏗️ 环境配置检查:');
    console.log(`   当前环境: ${config.currentEnv}`);
    if (config.currentEnv === 'production') {
      console.log('   ✅ 使用生产环境配置');
    } else {
      console.log('   ⚠️  使用沙箱环境配置');
    }
    
    console.log('');
    
    // 总结
    console.log('📊 配置验证总结:');
    if (missingFields.length === 0) {
      console.log('   🎉 所有必要配置项都已设置！');
      console.log('   💡 建议：');
      console.log('      1. 确保API密钥在微信商户平台已正确设置');
      console.log('      2. 确保回调地址可以正常访问');
      console.log('      3. 确保证书文件已正确放置');
      console.log('      4. 在小程序后台配置域名白名单');
      console.log('      5. 进行小额测试验证支付流程');
    } else {
      console.log(`   ❌ 还有 ${missingFields.length} 个配置项需要设置:`);
      missingFields.forEach(field => {
        console.log(`      - ${field}`);
      });
      console.log('');
      console.log('   📖 请参考配置文档完成设置:');
      console.log('      weapp/docs/payment_config_setup.md');
    }
    
    // 执行框架验证
    validatePayConfig();
    
    return missingFields.length === 0;
    
  } catch (error) {
    console.error('❌ 配置验证失败:', error.message);
    return false;
  }
}

/**
 * 生成配置检查报告
 */
function generateConfigReport() {
  const config = getCurrentWechatPayConfig();
  
  const report = {
    timestamp: new Date().toISOString(),
    environment: config.currentEnv,
    appId: config.appId,
    mchId: config.mchId,
    hasApiKey: !!(config.mchKey && config.mchKey !== 'YOUR_32_CHARACTER_API_KEY_HERE'),
    hasApiV3Key: !!(config.apiV3Key && config.apiV3Key.length === 32),
    hasSerialNumber: !!(config.merchantSerialNumber && /^[A-F0-9]{40}$/.test(config.merchantSerialNumber)),
    hasNotifyUrl: !!(config.notifyUrl && config.notifyUrl !== 'https://yourdomain.com/api/pay/notify'),
    certPaths: {
      cert: config.certPath,
      key: config.privateKeyPath,
      p12: config.keyPath
    }
  };
  
  return report;
}

/**
 * 快速配置检查
 */
function quickCheck() {
  console.log('⚡ 快速配置检查\n');
  
  const config = getCurrentWechatPayConfig();
  const checks = [
    { name: '小程序ID', check: () => config.appId === 'wx5f0591468a438c48' },
    { name: '商户号', check: () => config.mchId === '1722243412' },
    { name: 'API密钥', check: () => config.mchKey && config.mchKey !== 'YOUR_32_CHARACTER_API_KEY_HERE' },
    { name: 'APIv3密钥', check: () => config.apiV3Key === 'asdqweqwezxc12312312313233215AA2' },
    { name: '证书序列号', check: () => config.merchantSerialNumber === '2C625B38494EB8D0FA6BFF9DDD78B4F4381BA75E' },
    { name: '回调地址', check: () => config.notifyUrl && config.notifyUrl !== 'https://yourdomain.com/api/pay/notify' }
  ];
  
  let passCount = 0;
  checks.forEach(({ name, check }) => {
    const passed = check();
    console.log(`${passed ? '✅' : '❌'} ${name}`);
    if (passed) passCount++;
  });
  
  console.log(`\n📊 通过率: ${passCount}/${checks.length} (${Math.round(passCount/checks.length*100)}%)`);
  
  if (passCount === checks.length) {
    console.log('🎉 配置检查通过！可以开始测试支付功能。');
  } else {
    console.log('⚠️  还有配置项需要完善，请查看详细验证报告。');
  }
  
  return passCount === checks.length;
}

module.exports = {
  validatePaymentConfig,
  generateConfigReport,
  quickCheck
};
