const { request } = require('../utils/request.js');

/**
 * 签到管理Store
 */
class SigninStore {
  /**
   * 获取签到信息
   */
  async getSigninInfo() {
    try {
      const result = await request({
        url: '/blade-chat/signin/info',
        method: 'GET'
      });

      if (result.code === 200) {
        return result.data;
      } else {
        throw new Error(result.msg || '获取签到信息失败');
      }
    } catch (error) {
      console.error('获取签到信息失败:', error);
      throw error;
    }
  }

  /**
   * 执行签到
   */
  async doSignin() {
    try {
      const result = await request({
        url: '/blade-chat/signin/do',
        method: 'POST'
      });

      if (result.code === 200) {
        return result.data;
      } else {
        throw new Error(result.msg || '签到失败');
      }
    } catch (error) {
      console.error('签到失败:', error);
      throw error;
    }
  }

  /**
   * 获取签到记录
   * @param {Object} params 查询参数
   * @param {number} params.year 年份
   * @param {number} params.month 月份
   */
  async getSigninRecord(params) {
    try {
      const result = await request({
        url: '/blade-chat/signin/record',
        method: 'GET',
        data: params
      });

      if (result.code === 200) {
        return result.data;
      } else {
        throw new Error(result.msg || '获取签到记录失败');
      }
    } catch (error) {
      console.error('获取签到记录失败:', error);
      throw error;
    }
  }

  /**
   * 获取连续签到奖励配置
   */
  async getContinuousRewards() {
    try {
      const result = await request({
        url: '/blade-chat/signin/rewards',
        method: 'GET'
      });

      if (result.code === 200) {
        return result.data;
      } else {
        throw new Error(result.msg || '获取连续签到奖励配置失败');
      }
    } catch (error) {
      console.error('获取连续签到奖励配置失败:', error);
      throw error;
    }
  }

  /**
   * 补签
   * @param {string} date 补签日期 (YYYY-MM-DD)
   */
  async makeUpSignin(date) {
    try {
      const result = await request({
        url: '/blade-chat/signin/makeup',
        method: 'POST',
        data: { date }
      });

      if (result.code === 200) {
        return result.data;
      } else {
        throw new Error(result.msg || '补签失败');
      }
    } catch (error) {
      console.error('补签失败:', error);
      throw error;
    }
  }

  /**
   * 获取签到统计
   */
  async getSigninStats() {
    try {
      const result = await request({
        url: '/blade-chat/signin/stats',
        method: 'GET'
      });

      if (result.code === 200) {
        return result.data;
      } else {
        throw new Error(result.msg || '获取签到统计失败');
      }
    } catch (error) {
      console.error('获取签到统计失败:', error);
      throw error;
    }
  }
}

module.exports = new SigninStore(); 
