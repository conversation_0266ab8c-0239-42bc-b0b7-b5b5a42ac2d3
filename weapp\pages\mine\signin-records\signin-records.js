// 签到记录页面
const signinRecordsStore = require('../../../stores/signinRecordsStore.js');

Page({
  data: {
    // 记录数据
    records: [],
    summary: {},
    loading: false,
    loadingMore: false,
    canLoadMore: false,

    // 筛选相关
    showDatePicker: false,
    startDate: '',
    endDate: '',
    dateRangeText: '全部记录',

    // 分页信息
    pagination: {
      page: 1,
      size: 20,
      total: 0
    },

    // 页面状态
    isFirstLoad: true
  },

  onLoad(options) {
    console.log('签到记录页面加载', options);
    this.initPage();
  },

  onShow() {
    // 页面显示时刷新数据（跳过首次加载）
    if (!this.data.isFirstLoad) {
      this.refreshData();
    } else {
      this.setData({ isFirstLoad: false });
    }
  },

  onReachBottom() {
    // 触底加载更多
    this.loadMore();
  },

  onPullDownRefresh() {
    // 下拉刷新
    this.refreshData().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 初始化页面
   */
  async initPage() {
    try {
      // 设置导航栏标题
      wx.setNavigationBarTitle({
        title: '签到记录'
      });

      // 加载数据
      await this.loadData();
    } catch (error) {
      console.error('初始化页面失败:', error);
    }
  },

  /**
   * 加载数据
   */
  async loadData() {
    this.setData({ loading: true });

    try {
      // 并行加载记录和统计数据
      const [recordsResult, summaryResult] = await Promise.all([
        signinRecordsStore.getSigninRecords({
          page: 1,
          size: this.data.pagination.size,
          startDate: this.data.startDate,
          endDate: this.data.endDate,
          refresh: true
        }),
        signinRecordsStore.getSigninSummary()
      ]);

      if (recordsResult.success) {
        const state = signinRecordsStore.getState();
        this.setData({
          records: state.records,
          pagination: state.pagination,
          canLoadMore: state.canLoadMore
        });
      }

      if (summaryResult.success) {
        this.setData({
          summary: summaryResult.data
        });
      }
    } catch (error) {
      console.error('加载数据失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    return this.loadData();
  },

  /**
   * 加载更多
   */
  async loadMore() {
    if (!this.data.canLoadMore || this.data.loadingMore) {
      return;
    }

    this.setData({ loadingMore: true });

    try {
      const result = await signinRecordsStore.loadMore({
        startDate: this.data.startDate,
        endDate: this.data.endDate
      });

      if (result.success) {
        const state = signinRecordsStore.getState();
        this.setData({
          records: state.records,
          pagination: state.pagination,
          canLoadMore: state.canLoadMore
        });
      }
    } catch (error) {
      console.error('加载更多失败:', error);
    } finally {
      this.setData({ loadingMore: false });
    }
  },

  /**
   * 显示日期选择器
   */
  showDatePicker() {
    this.setData({ showDatePicker: true });
  },

  /**
   * 隐藏日期选择器
   */
  hideDatePicker() {
    this.setData({ showDatePicker: false });
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡，防止点击内容区域关闭弹窗
  },

  /**
   * 开始日期变化
   */
  onStartDateChange(e) {
    this.setData({
      startDate: e.detail.value
    });
    this.updateDateRangeText();
  },

  /**
   * 结束日期变化
   */
  onEndDateChange(e) {
    this.setData({
      endDate: e.detail.value
    });
    this.updateDateRangeText();
  },

  /**
   * 选择最近一周
   */
  selectLastWeek() {
    const endDate = new Date();
    const startDate = new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    this.setData({
      startDate: this.formatDate(startDate),
      endDate: this.formatDate(endDate)
    });
    this.updateDateRangeText();
  },

  /**
   * 选择最近一月
   */
  selectLastMonth() {
    const endDate = new Date();
    const startDate = new Date(endDate.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    this.setData({
      startDate: this.formatDate(startDate),
      endDate: this.formatDate(endDate)
    });
    this.updateDateRangeText();
  },

  /**
   * 选择最近三月
   */
  selectLastThreeMonths() {
    const endDate = new Date();
    const startDate = new Date(endDate.getTime() - 90 * 24 * 60 * 60 * 1000);
    
    this.setData({
      startDate: this.formatDate(startDate),
      endDate: this.formatDate(endDate)
    });
    this.updateDateRangeText();
  },

  /**
   * 确认日期筛选
   */
  async confirmDateFilter() {
    this.hideDatePicker();
    this.updateDateRangeText();
    await this.loadData();
  },

  /**
   * 重置筛选
   */
  async resetFilter() {
    this.setData({
      startDate: '',
      endDate: '',
      dateRangeText: '全部记录'
    });
    await this.loadData();
  },

  /**
   * 更新日期范围文本
   */
  updateDateRangeText() {
    const { startDate, endDate } = this.data;
    
    if (!startDate && !endDate) {
      this.setData({ dateRangeText: '全部记录' });
    } else if (startDate && endDate) {
      this.setData({ 
        dateRangeText: `${startDate} 至 ${endDate}` 
      });
    } else if (startDate) {
      this.setData({ 
        dateRangeText: `${startDate} 之后` 
      });
    } else if (endDate) {
      this.setData({ 
        dateRangeText: `${endDate} 之前` 
      });
    }
  },

  /**
   * 格式化日期
   */
  formatDate(date) {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  /**
   * 跳转到签到页面
   */
  goToSignin() {
    wx.navigateTo({
      url: '/pages/mine/signin/signin'
    });
  },

  onUnload() {
    // 页面卸载时清理数据和重置状态
    signinRecordsStore.clear();
    this.setData({ isFirstLoad: true });
  }
});
