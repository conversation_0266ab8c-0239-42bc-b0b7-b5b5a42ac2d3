const { request } = require('../../../utils/request');

// 分类映射
const DEFAULT_IMAGES = {
  '寻物启事': '/assets/images/categoryDefaults/xunwu.png',
  '房屋出租': '/assets/images/categoryDefaults/fangwu.png',
  '本地招聘': '/assets/images/categoryDefaults/zhaopin.png',
  '教育培训': '/assets/images/categoryDefaults/jiaoyu.png',
  '二手交易': '/assets/images/categoryDefaults/ershou.png',
  '生活服务': '/assets/images/categoryDefaults/shenghuo.png',
  '顺风拼车': '/assets/images/categoryDefaults/pinche.png',
  '便民信息': '/assets/images/categoryDefaults/bianmin.png',
  '房产出售': '/assets/images/categoryDefaults/fangchan.png',
  '求职信息': '/assets/images/categoryDefaults/qiuzhi.png'
};

Page({
  data: {
    posts: [],
    page: 1,
    pageSize: 10,
    loading: false,
    loadingMore: false,
    refreshing: false,
    noMore: false,
    tab: 'all',
    showActionMenu: false,
    currentPostId: null,

    // 搜索相关
    showSearch: false,
    searchKeyword: '',
    searchFocus: false
  },
  onLoad() {
    this.loadPosts();
  },

  /**
   * 返回上一页
   */
  onBack() {
    wx.navigateBack();
  },

  /**
   * 显示搜索栏
   */
  onShowSearch() {
    this.setData({
      showSearch: true,
      searchFocus: true
    });
  },

  /**
   * 隐藏搜索栏
   */
  onHideSearch() {
    this.setData({
      showSearch: false,
      searchKeyword: '',
      searchFocus: false
    });
    // 如果有搜索关键词，清空后重新加载
    if (this.data.searchKeyword) {
      this.setData({ page: 1, posts: [], noMore: false }, () => {
        this.loadPosts();
      });
    }
  },

  /**
   * 搜索输入
   */
  onSearchInput(e) {
    this.setData({ searchKeyword: e.detail.value });
  },

  /**
   * 搜索确认
   */
  onSearch() {
    this.setData({ page: 1, posts: [], noMore: false }, () => {
      this.loadPosts();
    });
  },

  /**
   * 清空搜索
   */
  onClearSearch() {
    this.setData({ searchKeyword: '' });
    this.setData({ page: 1, posts: [], noMore: false }, () => {
      this.loadPosts();
    });
  },
  onTabChange(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({ tab, page: 1, posts: [], noMore: false }, () => {
      this.loadPosts();
    });
  },
  async loadPosts(refresh = false) {
    if (this.data.loading || this.data.noMore) return;

    const isRefresh = refresh || this.data.page === 1;
    this.setData({
      loading: isRefresh,
      loadingMore: !isRefresh
    });

    try {
      const params = {
        page: this.data.page,
        size: this.data.pageSize
      };

      // 添加状态筛选
      if (this.data.tab !== 'all') {
        if (this.data.tab === 'published') {
          params.publishStatus = '1';
        } else if (this.data.tab === 'draft') {
          params.status = '1';
        } else if (this.data.tab === 'completed') {
          params.completed = '1';
        }
      }

      // 添加搜索关键词
      if (this.data.searchKeyword) {
        params.keyword = this.data.searchKeyword;
      }

      const res = await request({
        url: '/blade-chat/post/my',
        method: 'GET',
        data: params
      });

      if (res.code === 200) {
        let newPosts = res.data.records || [];
        // 处理每个帖子
        newPosts = newPosts.map(post => {
          // 判断 images 字段是否有图片
          let hasImages = false;
          if (Array.isArray(post.images)) {
            hasImages = post.images.length > 0;
          } else if (typeof post.images === 'string') {
            // 兼容字符串类型，且去除空字符串
            post.images = post.images.split(',').filter(Boolean);
            hasImages = post.images.length > 0;
          }
          if (!hasImages) {
            const categoryName = post.category && post.category.name;
            post.images = [DEFAULT_IMAGES[categoryName] || '/assets/images/defaults/default.png'];
          }
          return post;
        });

        const total = Number(res.data.total || 0);
        const allPosts = this.data.page === 1 ? newPosts : this.data.posts.concat(newPosts);

        // 判断已加载数量是否大于等于总数
        const noMore = allPosts.length >= total || newPosts.length < this.data.pageSize;

        this.setData({
          posts: allPosts,
          noMore
        });
      }
    } catch (error) {
      console.error('加载帖子失败:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({
        loading: false,
        loadingMore: false,
        refreshing: false
      });
    }
  },
  /**
   * 下拉刷新
   */
  onRefresh() {
    this.setData({
      refreshing: true,
      page: 1,
      posts: [],
      noMore: false
    }, () => {
      this.loadPosts(true);
    });
  },

  /**
   * 上拉加载更多
   */
  onReachBottom() {
    if (!this.data.noMore && !this.data.loadingMore) {
      this.setData({ page: this.data.page + 1 }, () => {
        this.loadPosts();
      });
    }
  },

  /**
   * 获取空状态描述
   */
  getEmptyDesc(tab) {
    switch (tab) {
      case 'published':
        return '还没有发布的帖子\n快去分享你的精彩内容吧';
      case 'draft':
        return '草稿箱是空的\n可以先保存草稿再发布';
      case 'completed':
        return '还没有已完成的帖子\n完成帖子后会显示在这里';
      default:
        return '还没有发布任何帖子\n快去分享你的精彩内容吧';
    }
  },

  /**
   * 去发布
   */
  onGoPublish() {
    wx.navigateTo({
      url: '/pages/publish/form/index'
    });
  },
  onEditPost(e) {
    const id = e.detail.id;
    wx.navigateTo({ url: `/pages/publish/form?id=${id}` });
  },
  onMore(e) {
    this.setData({
      showActionMenu: true,
      currentPostId: e.detail.id
    });
  },
  hideActionMenu() {
    this.setData({ showActionMenu: false, currentPostId: null });
  },
  onDeletePostMenu() {
    const id = this.data.currentPostId;
    this.hideActionMenu();
    this.onDeletePost({ detail: { id } });
  },
  onEditPostMenu() {
    const id = this.data.currentPostId;
    this.hideActionMenu();
    this.onEditPost({ detail: { id } });
  },
  onMoveDraftMenu() {
    const id = this.data.currentPostId;
    this.hideActionMenu();
    this.onMoveDraft({ detail: { id } });
  },
  onCompletedPostMenu() {
    const id = this.data.currentPostId;
    this.hideActionMenu();
    this.onCompletedPost({ detail: { id, completed: false } });
  },
  async onDeletePost(e) {
    const id = e.detail.id;
    try {
      wx.showModal({
        title: '确认删除',
        content: '确定要删除这条帖子吗？删除后无法恢复。',
        success: async (res) => {
          if (res.confirm) {
            wx.showLoading({ title: '删除中...' });
            const result = await request({
              url: `/blade-chat/post/${id}`,
              method: 'DELETE'
            });
            wx.hideLoading();
            if (result.code === 200) {
              wx.showToast({ title: '删除成功', icon: 'success' });
              this.setData({ page: 1, posts: [], noMore: false }, () => {
                this.loadPosts(true);
              });
            } else {
              wx.showToast({ title: result.msg || '删除失败', icon: 'none' });
            }
          }
        }
      });
    } catch (error) {
      wx.hideLoading();
      console.error('删除帖子失败:', error);
      wx.showToast({ title: '删除失败', icon: 'none' });
    }
  },
  
  async onMoveDraft(e) {
    const id = e.detail.id;
    try {
      wx.showModal({
        title: '确认移动',
        content: '确定要将这条帖子移动到草稿箱吗？',
        success: async (res) => {
          if (res.confirm) {
            wx.showLoading({ title: '移动中...' });
            const result = await request({
              url: `/blade-chat/post/move-draft/${id}`,
              method: 'POST'
            });
            wx.hideLoading();
            if (result.code === 200) {
              wx.showToast({ title: '已移到草稿箱', icon: 'success' });
              this.setData({ page: 1, posts: [], noMore: false }, () => {
                this.loadPosts(true);
              });
            } else {
              wx.showToast({ title: result.msg || '移动失败', icon: 'none' });
            }
          }
        }
      });
    } catch (error) {
      wx.hideLoading();
      console.error('移动草稿失败:', error);
      wx.showToast({ title: '移动失败', icon: 'none' });
    }
  },
  
  async onCompletedPost(e) {
    const { id, completed } = e.detail;
    try {
      wx.showLoading({ title: '更新中...' });
      const result = await request({
        url: `/blade-chat/post/completed/${id}`,
        method: 'POST'
      });
      wx.hideLoading();
      if (result.code === 200) {
        const message = completed ? '已标记未完成' : '已标记已完成';
        wx.showToast({ title: message, icon: 'success' });
        this.setData({ page: 1, posts: [], noMore: false }, () => {
          this.loadPosts(true);
        });
      } else {
        wx.showToast({ title: result.msg || '更新失败', icon: 'none' });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('更新完成状态失败:', error);
      wx.showToast({ title: '更新失败', icon: 'none' });
    }
  }
}); 