/* 评论输入容器 */
.comment-input-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-top: 1rpx solid #e5e5e5;
  z-index: 1000;
  padding: 20rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

/* 输入头部 */
.input-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
  padding-bottom: 12rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.reply-hint {
  font-size: 24rpx;
  color: #007aff;
  background: #f0f8ff;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.cancel-btn {
  font-size: 24rpx;
  color: #666;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  background: #f5f5f5;
}

.cancel-btn:active {
  background: #e5e5e5;
}

/* 输入内容区域 */
.input-content {
  margin-bottom: 16rpx;
}

.text-input-area {
  position: relative;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 16rpx;
}

.comment-textarea {
  width: 100%;
  min-height: 80rpx;
  max-height: 200rpx;
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
}

.input-counter {
  position: absolute;
  bottom: 8rpx;
  right: 12rpx;
}

.counter-text {
  font-size: 20rpx;
  color: #999;
}

/* 图片预览 */
.image-preview {
  margin-top: 16rpx;
}

.preview-container {
  position: relative;
  display: inline-block;
}

.preview-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  object-fit: cover;
}

.remove-image {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 32rpx;
  height: 32rpx;
  background: #ff4757;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-icon {
  color: #fff;
  font-size: 20rpx;
  font-weight: bold;
}

/* 上传状态 */
.uploading-status {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-top: 16rpx;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid #e5e5e5;
  border-top: 3rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.uploading-text {
  font-size: 24rpx;
  color: #666;
}

/* 工具栏 */
.input-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.toolbar-left {
  display: flex;
  gap: 24rpx;
}

.tool-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rpx;
  padding: 8rpx;
  border-radius: 8rpx;
  transition: background-color 0.2s;
}

.tool-item:active {
  background: #f5f5f5;
}

.tool-icon {
  width: 32rpx;
  height: 32rpx;
}

.emoji-icon {
  font-size: 32rpx;
}

.tool-text {
  font-size: 20rpx;
  color: #666;
}

/* 提交按钮 */
.submit-btn {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  border: none;
  transition: all 0.2s;
}

.submit-btn.active {
  background: #007aff;
  color: #fff;
}

.submit-btn.disabled {
  background: #f5f5f5;
  color: #ccc;
}

.submit-text,
.submitting-text {
  font-size: 26rpx;
}

/* 表情面板 */
.emoji-panel {
  margin-top: 16rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border-top: 1rpx solid #e5e5e5;
}

.emoji-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.emoji-item {
  font-size: 40rpx;
  padding: 8rpx;
  border-radius: 8rpx;
  transition: background-color 0.2s;
}

.emoji-item:active {
  background: #e5e5e5;
}

/* 遮罩层 */
.input-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 999;
}
