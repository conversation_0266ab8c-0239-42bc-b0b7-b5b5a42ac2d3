/**
 * 群组相关API服务
 */
const { request } = require('../../../utils/request');

class GroupApi {
  
  /**
   * 获取群分类列表
   * @returns {Promise} API响应
   */
  static async getCategoryList() {
    try {
      const response = await request({
        url: '/blade-chat/group/category/page?current=1&size=100',
        method: 'GET',
      });
      return response;
    } catch (error) {
      console.error('获取群分类列表失败:', error);
      throw error;
    }
  }

  /**
   * 根据分类获取群组列表
   * @param {Object} params 查询参数
   * @param {string} params.groupType 群组类型（分类名称）
   * @param {number} params.current 当前页码
   * @param {number} params.size 每页大小
   * @returns {Promise} API响应
   */
  static async getGroupListByCategory(params = {}) {
    try {
      const response = await request({
        url: '/blade-chat/group/page',
        method: 'GET',
        data: {
          current: params.current || 1,
          size: params.size || 20,
          ...(params.groupType && { groupType: params.groupType })
        }
      });
      return response;
    } catch (error) {
      console.error('获取群组列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取所有群组列表（不分类）
   * @param {Object} params 查询参数
   * @param {number} params.current 当前页码
   * @param {number} params.size 每页大小
   * @returns {Promise} API响应
   */
  static async getAllGroupList(params = {}) {
    try {
      const response = await request({
        url: '/blade-chat/group/category/page',
        method: 'GET',
        data: {
          current: params.current || 1,
          size: params.size || 20
        }
      });
      return response;
    } catch (error) {
      console.error('获取所有群组列表失败:', error);
      throw error;
    }
  }

  /**
   * 搜索群组
   * @param {Object} params 搜索参数
   * @param {string} params.keyword 搜索关键词
   * @param {string} params.groupType 群组类型（可选）
   * @param {number} params.current 当前页码
   * @param {number} params.size 每页大小
   * @returns {Promise} API响应
   */
  static async searchGroups(params = {}) {
    try {
      const response = await request({
        url: '/blade-chat/group/page',
        method: 'GET',
        data: {
          current: params.current || 1,
          size: params.size || 20,
          ...(params.keyword && { groupName: params.keyword }),
          ...(params.groupType && { groupType: params.groupType })
        }
      });
      return response;
    } catch (error) {
      console.error('搜索群组失败:', error);
      throw error;
    }
  }
}

module.exports = GroupApi;
