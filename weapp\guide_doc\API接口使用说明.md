# API接口使用说明

## 概述

本文档详细说明了发布页面中API接口的使用方法，包括接口调用时机、参数传递、错误处理等。

## 1. 分类与标签绑定机制

### 1.1 分类标签关联关系

每个分类都有其对应的标签集合，标签与分类是一对多的关系：

```
分类A (二手交易)
├── 标签1: 热门
├── 标签2: 急售
├── 标签3: 面交
└── 标签4: 包邮

分类B (数码产品)
├── 标签1: 新品
├── 标签2: 官方
├── 标签3: 推荐
└── 标签4: 极速转
```

### 1.2 标签类型

- **系统标签**: 由管理员预设的标签，所有用户可见
- **自定义标签**: 用户根据分类创建的个性化标签

## 2. 接口调用流程

### 2.1 页面初始化流程

```javascript
// 1. 页面加载时获取分类列表
async initializePage(options) {
  // 加载分类列表
  await this.loadCategories();
  
  // 处理从上一个页面传递的分类参数
  if (options.category) {
    const categoryName = decodeURIComponent(options.category);
    this.setData({ category: categoryName });
    
    // 根据分类名称找到对应的分类ID
    const categoryObj = this.data.categories.find(cat => cat.name === categoryName);
    if (categoryObj) {
      this.setData({ categoryId: categoryObj.id });
      // 加载该分类下的标签
      await this.loadTagsByCategory(categoryObj.id);
    }
  }
}
```

### 2.2 分类选择流程

```javascript
// 用户选择分类时
onCategorySelect(e) {
  const category = e.currentTarget.dataset.category;
  
  // 更新分类信息
  this.setData({ 
    category: category.name,
    categoryId: category.id,
    selectedTags: [] // 清空已选标签
  });
  
  // 动态加载该分类下的标签
  this.loadTagsByCategory(category.id);
}
```

### 2.3 标签加载流程

```javascript
// 根据分类ID加载对应的标签
async loadTagsByCategory(categoryId) {
  try {
    const result = await PublishStore.getTagsByCategory(categoryId);
    
    if (result.success) {
      this.setData({ 
        categoryTags: result.data,
        tagList: result.data.map(tag => tag.name) // 兼容原有逻辑
      });
    }
  } catch (error) {
    console.error('加载分类标签失败:', error);
  }
}
```

## 3. 接口调用示例

### 3.1 获取分类列表

```javascript
// 调用Store方法
const result = await PublishStore.loadCategories();

if (result.success) {
  // 分类数据结构
  const categories = result.data;
  // [
  //   {
  //     id: "1",
  //     name: "二手交易",
  //     code: "second_hand",
  //     icon: "https://example.com/icon1.png",
  //     children: [...]
  //   }
  // ]
}
```

### 3.2 根据分类获取标签

```javascript
// 调用Store方法
const result = await PublishStore.getTagsByCategory(categoryId);

if (result.success) {
  // 标签数据结构
  const tags = result.data;
  // [
  //   {
  //     id: "1",
  //     name: "热门",
  //     code: "hot",
  //     color: "#FF6B6B",
  //     icon: "https://example.com/hot.png",
  //     type: "system"
  //   }
  // ]
}
```

### 3.3 创建自定义标签

```javascript
// 用户输入自定义标签
const tagData = {
  name: "自定义标签",
  categoryId: "1",
  color: "#4ECDC4"
};

const result = await PublishStore.createCustomTag(tagData);

if (result.success) {
  // 标签创建成功，重新加载分类标签
  await this.loadTagsByCategory(this.data.categoryId);
}
```

### 3.4 发布帖子

```javascript
// 准备发布数据
const postData = {
  title: "帖子标题",
  description: "帖子内容",
  categoryId: "1",
  categoryName: "二手交易",
  tags: ["热门", "急售"],
  images: [...],
  contactName: "联系人",
  contactType: "phone",
  contactNumber: "13800138000",
  location: "北京市朝阳区",
  latitude: 39.9042,
  longitude: 116.4074
};

const result = await PublishStore.createPost(postData);

if (result.success) {
  // 发布成功
  wx.showToast({ title: '发布成功', icon: 'success' });
}
```

## 4. 错误处理

### 4.1 网络错误处理

```javascript
try {
  const result = await PublishStore.loadCategories();
  if (result.success) {
    // 处理成功结果
  } else {
    // 处理业务错误
    wx.showToast({
      title: result.message,
      icon: 'none'
    });
  }
} catch (error) {
  // 处理网络错误
  console.error('接口调用失败:', error);
  wx.showToast({
    title: '网络错误，请稍后重试',
    icon: 'none'
  });
}
```

### 4.2 常见错误码处理

```javascript
// 在Store层统一处理错误码
static async loadCategories() {
  try {
    const res = await request({
      url: '/blade-chat/category/list',
      method: 'GET'
    });

    if (res.code === 200) {
      return { success: true, data: res.data.records };
    } else if (res.code === 401) {
      // 未授权，跳转登录
      wx.navigateTo({ url: '/pages/login/login' });
      return { success: false, message: '请先登录' };
    } else if (res.code === 403) {
      return { success: false, message: '权限不足' };
    } else {
      return { success: false, message: res.msg || '加载失败' };
    }
  } catch (error) {
    return { success: false, message: '网络错误' };
  }
}
```

## 5. 数据缓存策略

### 5.1 分类数据缓存

```javascript
// 分类数据相对稳定，可以缓存
async loadCategories() {
  // 先检查缓存
  const cached = wx.getStorageSync('categories_cache');
  const cacheTime = wx.getStorageSync('categories_cache_time');
  
  // 缓存1小时有效
  if (cached && cacheTime && (Date.now() - cacheTime < 3600000)) {
    return { success: true, data: cached };
  }
  
  // 从服务器获取
  const result = await PublishStore.loadCategories();
  
  if (result.success) {
    // 更新缓存
    wx.setStorageSync('categories_cache', result.data);
    wx.setStorageSync('categories_cache_time', Date.now());
  }
  
  return result;
}
```

### 5.2 标签数据缓存

```javascript
// 标签数据按分类缓存
async loadTagsByCategory(categoryId) {
  const cacheKey = `tags_cache_${categoryId}`;
  const cached = wx.getStorageSync(cacheKey);
  const cacheTime = wx.getStorageSync(`${cacheKey}_time`);
  
  // 缓存30分钟有效
  if (cached && cacheTime && (Date.now() - cacheTime < 1800000)) {
    return { success: true, data: cached };
  }
  
  const result = await PublishStore.getTagsByCategory(categoryId);
  
  if (result.success) {
    wx.setStorageSync(cacheKey, result.data);
    wx.setStorageSync(`${cacheKey}_time`, Date.now());
  }
  
  return result;
}
```

## 6. 性能优化建议

### 6.1 接口调用优化

1. **批量请求**: 避免频繁的单次请求
2. **数据缓存**: 合理使用本地缓存
3. **请求去重**: 避免重复请求相同数据
4. **错误重试**: 网络错误时自动重试

### 6.2 用户体验优化

1. **加载状态**: 显示加载提示
2. **错误提示**: 友好的错误信息
3. **离线处理**: 网络异常时的降级处理
4. **数据预加载**: 提前加载可能用到的数据

## 7. 测试建议

### 7.1 接口测试

```javascript
// 测试分类接口
async testCategoryAPI() {
  const result = await PublishStore.loadCategories();
  console.log('分类接口测试:', result);
  
  if (result.success && result.data.length > 0) {
    const firstCategory = result.data[0];
    const tagResult = await PublishStore.getTagsByCategory(firstCategory.id);
    console.log('标签接口测试:', tagResult);
  }
}

// 测试发布接口
async testPublishAPI() {
  const testData = {
    title: "测试帖子",
    description: "这是一个测试帖子",
    categoryId: "1",
    tags: ["测试"],
    contactName: "测试用户",
    contactType: "phone",
    contactNumber: "13800138000"
  };
  
  const result = await PublishStore.createPost(testData);
  console.log('发布接口测试:', result);
}
```

### 7.2 错误场景测试

1. **网络异常**: 断网情况下的处理
2. **参数错误**: 传递错误参数的处理
3. **权限不足**: 未登录或权限不足的处理
4. **服务器错误**: 500错误的处理

## 8. 注意事项

1. **分类ID一致性**: 确保前端使用的分类ID与后端一致
2. **标签唯一性**: 同一分类下标签名称不能重复
3. **数据同步**: 自定义标签创建后及时更新本地数据
4. **错误恢复**: 网络错误后能够正常恢复
5. **用户体验**: 加载过程中提供适当的反馈

## 9. 更新日志

### v1.0.0 (2024-01-01)
- 初始版本，支持基础的分发发布功能
- 实现分类与标签的绑定机制
- 支持自定义标签创建

### v1.1.0 (2024-01-15)
- 优化标签加载性能
- 添加数据缓存机制
- 改进错误处理逻辑 