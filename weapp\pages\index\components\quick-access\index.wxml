<view class="quick-access-wrapper">
  <swiper class="swiper" indicator-dots indicator-color="rgba(0, 0, 0, .1)" indicator-active-color="#ff6b6b">
    <swiper-item wx:for="{{pagedList}}" wx:key="pageIndex" wx:for-item="page" wx:for-index="pageIndex">
      <view class="grid-container">
        <view class="access-item" wx:for="{{page}}" wx:key="itemIndex" wx:for-item="item" wx:for-index="itemIndex" bindtap="onItemTap" data-item="{{item}}">
          <view class="icon-background" style="background-color: {{item.color || '#FF7800FF'}};">
            <image class="icon" src="{{item.icon}}" />
          </view>
          <text class="title">{{item.title}}</text>
        </view>
      </view>
    </swiper-item>
  </swiper>
</view>
