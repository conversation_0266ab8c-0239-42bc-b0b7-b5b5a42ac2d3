// 名片相关服务
const { request } = require('../utils/auth');

/**
 * 名片服务
 */
class CardService {
  
  /**
   * 获取公开名片列表
   * @param {Object} params 查询参数
   * @returns {Promise<Object>} 名片列表
   */
  async getPublicCardList(params = {}) {
    try {
      const response = await request({
        url: '/blade-chat-open/card/page',
        method: 'GET',
        data: {
          current: params.current || 1,
          size: params.size || 10,
          ...params
        }
      });
      
      if (response.code === 200) {
        return {
          success: true,
          data: response.data.records || [],
          total: response.data.total || 0,
          current: response.data.current || 1,
          size: response.data.size || 10
        };
      } else {
        console.error('获取公开名片列表失败:', response.msg);
        return {
          success: false,
          data: [],
          total: 0,
          message: response.msg || '获取名片列表失败'
        };
      }
    } catch (error) {
      console.error('获取公开名片列表失败:', error);
      return {
        success: false,
        data: [],
        total: 0,
        message: '网络错误，请重试'
      };
    }
  }

  /**
   * 获取我的收藏名片列表
   * @param {Object} params 查询参数
   * @returns {Promise<Object>} 收藏名片列表
   */
  async getMyFavoriteCardList(params = {}) {
    try {
      const response = await request({
        url: '/blade-chat/card/favorite/list',
        method: 'GET',
        data: {
          current: params.current || 1,
          size: params.size || 10,
          ...params
        }
      });
      
      if (response.code === 200) {
        return {
          success: true,
          data: response.data.records || [],
          total: response.data.total || 0,
          current: response.data.current || 1,
          size: response.data.size || 10
        };
      } else {
        console.error('获取收藏名片列表失败:', response.msg);
        return {
          success: false,
          data: [],
          total: 0,
          message: response.msg || '获取收藏名片列表失败'
        };
      }
    } catch (error) {
      console.error('获取收藏名片列表失败:', error);
      return {
        success: false,
        data: [],
        total: 0,
        message: '网络错误，请重试'
      };
    }
  }

  /**
   * 收藏名片（使用通用数据操作接口）
   * @param {string|number} cardId 名片ID
   * @param {string} category 收藏分类
   * @param {string} remark 收藏备注
   * @returns {Promise<Object>} 操作结果
   */
  async favoriteCard(cardId, category = '', remark = '') {
    try {
      // 使用通用数据操作接口切换收藏状态
      const response = await request({
        url: `/blade-chat/data-operate/toggle-favorite/2/${cardId}`,
        method: 'POST'
      });

      if (response.code === 200 && response.data.success) {
        // 如果收藏成功，保存扩展信息
        if (response.data.currentState) {
          await this.saveCardFavoriteExt(cardId, category, remark);
        }

        return {
          success: true,
          isFavorited: response.data.currentState,
          message: response.data.message || (response.data.currentState ? '收藏成功' : '取消收藏成功')
        };
      } else {
        console.error('收藏名片失败:', response.msg);
        return {
          success: false,
          message: response.msg || '收藏失败'
        };
      }
    } catch (error) {
      console.error('收藏名片失败:', error);
      return {
        success: false,
        message: '网络错误，请重试'
      };
    }
  }

  /**
   * 保存名片收藏扩展信息
   * @param {string|number} cardId 名片ID
   * @param {string} category 收藏分类
   * @param {string} remark 收藏备注
   * @returns {Promise<Object>} 操作结果
   */
  async saveCardFavoriteExt(cardId, category = '', remark = '') {
    try {
      const response = await request({
        url: `/blade-chat/card/favorite/${cardId}`,
        method: 'POST',
        data: {
          category: category,
          remark: remark
        }
      });

      return response.code === 200;
    } catch (error) {
      console.error('保存收藏扩展信息失败:', error);
      return false;
    }
  }

  /**
   * 取消收藏名片（使用通用数据操作接口）
   * @param {string|number} cardId 名片ID
   * @returns {Promise<Object>} 操作结果
   */
  async unfavoriteCard(cardId) {
    try {
      // 使用通用数据操作接口切换收藏状态
      const response = await request({
        url: `/blade-chat/data-operate/toggle-favorite/2/${cardId}`,
        method: 'POST'
      });

      if (response.code === 200 && response.data.success) {
        return {
          success: true,
          isFavorited: response.data.currentState,
          message: response.data.message || '取消收藏成功'
        };
      } else {
        console.error('取消收藏失败:', response.msg);
        return {
          success: false,
          message: response.msg || '取消收藏失败'
        };
      }
    } catch (error) {
      console.error('取消收藏失败:', error);
      return {
        success: false,
        message: '网络错误，请重试'
      };
    }
  }

  /**
   * 切换名片点赞状态（使用通用数据操作接口）
   * @param {string|number} cardId 名片ID
   * @returns {Promise<Object>} 操作结果
   */
  async toggleCardLike(cardId) {
    try {
      // 使用通用数据操作接口切换点赞状态
      const response = await request({
        url: `/blade-chat/data-operate/toggle-like/2/${cardId}`,
        method: 'POST'
      });

      if (response.code === 200 && response.data.success) {
        return {
          success: true,
          isLiked: response.data.currentState,
          message: response.data.message || (response.data.currentState ? '点赞成功' : '取消点赞成功')
        };
      } else {
        console.error('点赞操作失败:', response.msg);
        return {
          success: false,
          message: response.msg || '点赞操作失败'
        };
      }
    } catch (error) {
      console.error('点赞操作失败:', error);
      return {
        success: false,
        message: '网络错误，请重试'
      };
    }
  }

  /**
   * 获取名片状态（使用通用数据操作接口）
   * @param {string|number} cardId 名片ID
   * @returns {Promise<Object>} 名片状态
   */
  async getCardStatus(cardId) {
    try {
      // 并行获取点赞和收藏状态
      const [likeResponse, favoriteResponse] = await Promise.all([
        request({
          url: `/blade-chat/data-operate/check-like/2/${cardId}`,
          method: 'GET'
        }),
        request({
          url: `/blade-chat/data-operate/check-favorite/2/${cardId}`,
          method: 'GET'
        })
      ]);

      if (likeResponse.code === 200 && favoriteResponse.code === 200) {
        return {
          success: true,
          isLiked: likeResponse.data.isLiked || false,
          isFavorited: favoriteResponse.data.isFavorited || false
        };
      } else {
        console.error('获取名片状态失败:', likeResponse.msg || favoriteResponse.msg);
        return {
          success: false,
          isFavorited: false,
          isLiked: false,
          message: '获取名片状态失败'
        };
      }
    } catch (error) {
      console.error('获取名片状态失败:', error);
      return {
        success: false,
        isFavorited: false,
        isLiked: false,
        message: '网络错误，请重试'
      };
    }
  }

  /**
   * 格式化名片数据
   * @param {Array} cards 原始名片数据
   * @returns {Array} 格式化后的名片数据
   */
  formatCardList(cards) {
    if (!Array.isArray(cards)) {
      return [];
    }
    
    return cards.map(card => ({
      ...card,
      createTime: this.formatTime(card.createTime),
      likeCount: card.likeCount || 0,
      favoriteCount: card.favoriteCount || 0,
      viewCount: card.viewCount || 0,
      isLiked: card.isLiked || false,
      isFavorited: card.isFavorited || false,
      distance: card.distance ? parseFloat(card.distance).toFixed(1) : null
    }));
  }

  /**
   * 格式化时间
   * @param {string} timeStr 时间字符串
   * @returns {string} 格式化后的时间
   */
  formatTime(timeStr) {
    if (!timeStr) return '';
    
    const time = new Date(timeStr);
    const now = new Date();
    const diff = now - time;
    
    const minute = 60 * 1000;
    const hour = 60 * minute;
    const day = 24 * hour;
    
    if (diff < hour) {
      return Math.floor(diff / minute) + '分钟前';
    } else if (diff < day) {
      return Math.floor(diff / hour) + '小时前';
    } else if (diff < 7 * day) {
      return Math.floor(diff / day) + '天前';
    } else {
      return time.toLocaleDateString();
    }
  }

  /**
   * 检查是否有名片权限
   * @returns {Promise<boolean>} 是否有权限
   */
  async hasCardPermission() {
    try {
      // 这里可以检查用户是否有查看名片的权限
      return true;
    } catch (error) {
      console.error('检查名片权限失败:', error);
      return false;
    }
  }
}

// 创建单例实例
const cardService = new CardService();

module.exports = cardService;
