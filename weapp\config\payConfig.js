/**
 * 微信支付配置
 * 参考 WxJava 的配置方式
 */

const payConfig = {
  // 微信支付配置
  wechatPay: {
    // 小程序应用ID
    appId: 'wx5f0591468a438c48',

    // 商户号
    mchId: '1722243412',

    // 商户密钥（API密钥）- 需要在微信商户平台设置
    mchKey: 'your_api_key_32_characters_here',

    // 商户证书路径（用于退款等需要证书的接口）
    keyPath: './cert/apiclient_cert.p12',

    // API v3 密钥（用于支付 v3 接口）
    apiV3Key: 'asdqweqwezxc12312312313233215AA2',

    // 商户私钥路径（用于 v3 接口签名）
    privateKeyPath: './cert/apiclient_key.pem',

    // 商户证书路径（PEM格式）
    certPath: './cert/apiclient_cert.pem',

    // 商户证书序列号（用于 v3 接口）
    merchantSerialNumber: '2C625B38494EB8D0FA6BFF9DDD78B4F4381BA75E',
    
    // 支付回调地址
    notifyUrl: 'https://your-domain.com/api/pay/notify',
    
    // 退款回调地址
    refundNotifyUrl: 'https://your-domain.com/api/pay/refund-notify',
    
    // 交易类型
    tradeType: {
      JSAPI: 'JSAPI',     // 小程序支付
      NATIVE: 'NATIVE',   // 扫码支付
      APP: 'APP',         // APP支付
      H5: 'MWEB'          // H5支付
    },
    
    // 货币类型
    feeType: 'CNY',
    
    // 签名类型
    signType: 'MD5',
    
    // API版本
    apiVersion: {
      V2: 'v2',
      V3: 'v3'
    },
    
    // 当前环境（生产环境）
    currentEnv: 'production',

    // 生产环境配置
    production: {
      baseUrl: 'https://api.mch.weixin.qq.com',
      mchId: '1722243412',
      appId: 'wx5f0591468a438c48'
    }
  },
  
  // 支付宝配置（可选）
  alipay: {
    appId: 'your_alipay_app_id',
    privateKey: 'your_alipay_private_key',
    publicKey: 'alipay_public_key',
    notifyUrl: 'https://your-domain.com/api/alipay/notify',
    returnUrl: 'https://your-domain.com/api/alipay/return',
    signType: 'RSA2',
    charset: 'UTF-8',
    gatewayUrl: 'https://openapi.alipay.com/gateway.do'
  },
  
  // 订单配置
  order: {
    // 订单号前缀
    orderPrefix: 'EF',
    
    // 订单超时时间（分钟）
    timeoutMinutes: 30,
    
    // 最小支付金额（分）
    minAmount: 1,
    
    // 最大支付金额（分）
    maxAmount: 100000000,
    
    // 订单状态
    status: {
      PENDING: 'PENDING',       // 待支付
      PAID: 'PAID',            // 已支付
      CANCELLED: 'CANCELLED',   // 已取消
      REFUNDED: 'REFUNDED',    // 已退款
      EXPIRED: 'EXPIRED'       // 已过期
    }
  },
  
  // 业务类型配置
  businessType: {
    POST_PROMOTION: 'post_promotion',     // 帖子推广
    VIP_UPGRADE: 'vip_upgrade',          // VIP升级
    REWARD: 'reward',                    // 打赏
    SERVICE_FEE: 'service_fee',          // 服务费
    DEPOSIT: 'deposit'                   // 保证金
  },
  
  // 支付方式
  paymentMethod: {
    WECHAT: 'wechat',
    ALIPAY: 'alipay',
    BALANCE: 'balance'  // 余额支付
  }
};

/**
 * 获取当前环境的微信支付配置
 */
function getCurrentWechatPayConfig() {
  const { wechatPay } = payConfig;
  const currentConfig = wechatPay[wechatPay.currentEnv];
  
  return {
    ...wechatPay,
    ...currentConfig
  };
}

/**
 * 验证支付配置
 */
function validatePayConfig() {
  const config = getCurrentWechatPayConfig();
  const requiredFields = ['appId', 'mchId', 'mchKey', 'notifyUrl'];
  
  for (const field of requiredFields) {
    if (!config[field]) {
      throw new Error(`微信支付配置缺少必要字段: ${field}`);
    }
  }
  
  return true;
}

/**
 * 获取支付回调URL
 */
function getNotifyUrl(paymentMethod = 'wechat') {
  switch (paymentMethod) {
    case 'wechat':
      return payConfig.wechatPay.notifyUrl;
    case 'alipay':
      return payConfig.alipay.notifyUrl;
    default:
      throw new Error(`不支持的支付方式: ${paymentMethod}`);
  }
}

/**
 * 生成订单号
 */
function generateOrderNo() {
  const prefix = payConfig.order.orderPrefix;
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  
  return `${prefix}${timestamp}${random}`;
}

/**
 * 验证订单金额
 */
function validateAmount(amount) {
  const { minAmount, maxAmount } = payConfig.order;
  
  if (amount < minAmount) {
    throw new Error(`支付金额不能小于 ${minAmount / 100} 元`);
  }
  
  if (amount > maxAmount) {
    throw new Error(`支付金额不能大于 ${maxAmount / 100} 元`);
  }
  
  return true;
}

/**
 * 格式化金额（元转分）
 */
function yuanToFen(yuan) {
  return Math.round(parseFloat(yuan) * 100);
}

/**
 * 格式化金额（分转元）
 */
function fenToYuan(fen) {
  return (parseInt(fen) / 100).toFixed(2);
}

module.exports = {
  payConfig,
  getCurrentWechatPayConfig,
  validatePayConfig,
  getNotifyUrl,
  generateOrderNo,
  validateAmount,
  yuanToFen,
  fenToYuan
};
