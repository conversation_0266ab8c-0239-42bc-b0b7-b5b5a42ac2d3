<!--pages/recharge/recharge.wxml-->
<view class="recharge-container">
  <!-- 自定义导航栏 -->
  <layout
  title="钱包充值"
  showSearch="{{false}}"
  showLocation="{{false}}"
  background="linear-gradient(to bottom, #ff6b6b, #ff8585)"
  showBack="{{true}}"
  />

  <!-- 内容区域 -->
  <scroll-view 
    scroll-y 
    class="scroll-container"
    style="margin-top: {{navBarHeight}}px; height: calc(100vh - {{navBarHeight}}px);"
  >
    <view class="main-content">
      <!-- 当前余额 -->
      <view class="balance-card">
        <view class="balance-header">
          <text class="balance-title">当前余额</text>
          <view class="balance-actions">
            <text class="balance-link" bindtap="viewBalanceDetail">明细</text>
          </view>
        </view>
        <view class="balance-amount">
          <text class="currency">¥</text>
          <text class="amount">{{currentBalance}}</text>
        </view>
      </view>

      <!-- 充值金额选择 -->
      <view class="amount-section">
        <view class="section-title">
          <text>选择充值金额</text>
        </view>

        <!-- 快捷金额 -->
        <view class="normal-amounts">
          <view class="quick-amounts">
            <view
              wx:for="{{suggestedAmounts}}"
              wx:key="index"
              class="amount-item {{selectedAmount == item ? 'selected' : ''}}"
              bindtap="selectAmount"
              data-amount="{{item}}"
            >
              <text class="amount-text">{{item}}元</text>
            </view>
          </view>
        </view>

        <!-- 自定义金额 -->
        <view class="custom-amount">
          <view class="custom-amount-header">
            <text class="custom-title">自定义金额</text>
            <text class="amount-range">
              {{minAmount}}-{{maxAmount}}元
            </text>
          </view>
          <view class="custom-input-wrapper">
            <text class="input-prefix">¥</text>
            <input 
              class="custom-input"
              type="digit"
              placeholder="请输入充值金额"
              value="{{customAmount}}"
              bindinput="onCustomAmountInput"
              bindblur="onCustomAmountBlur"
              maxlength="8"
            />
          </view>
        </view>
      </view>

      <!-- 支付方式 -->
      <view class="payment-section">
        <view class="section-title">支付方式</view>
        <view class="payment-methods">
          <view 
            class="payment-item {{selectedPayMethod == 'wechat' ? 'selected' : ''}}"
            bindtap="selectPayMethod"
            data-method="wechat"
          >
            <view class="payment-info">
              <view class="payment-icon wechat-icon">💚</view>
              <text class="payment-name">微信支付</text>
            </view>
            <view class="payment-radio">
              <view class="radio-dot {{selectedPayMethod == 'wechat' ? 'checked' : ''}}"></view>
            </view>
          </view>
        </view>
      </view>

      <!-- 充值说明 -->
      <view class="notice-section">
        <view class="section-title">充值说明</view>
        <view class="notice-content">
          <view class="notice-item">• 充值金额将实时到账</view>
          <view class="notice-item">• 充值金额可用于平台内各项服务</view>
          <view class="notice-item">• 如有疑问请联系客服</view>
        </view>
      </view>
    </view>
  </scroll-view>

  <!-- 底部操作栏 -->
  <view class="bottom-bar">
    <view class="amount-info">
      <text class="pay-amount">支付金额：¥{{finalAmount}}</text>
    </view>
    <button 
      class="recharge-btn {{canRecharge ? 'active' : 'disabled'}}"
      bindtap="startRecharge"
      disabled="{{!canRecharge || isProcessing}}"
      loading="{{isProcessing}}"
    >
      {{isProcessing ? '处理中...' : '立即充值'}}
    </button>
  </view>

  <!-- 支付结果弹窗 -->
  <view class="result-modal {{showResultModal ? 'show' : ''}}" catchtap="{{paymentResult.isPolling ? '' : 'hideResultModal'}}">
    <view class="result-content" catchtap="">
      <view class="result-icon">
        <view class="icon-emoji" wx:if="{{!paymentResult.isPolling}}">
          {{paymentResult.success ? '✅' : '❌'}}
        </view>
        <view class="loading-icon" wx:if="{{paymentResult.isPolling}}">
          <view class="spinner"></view>
        </view>
      </view>
      <view class="result-title">
        <text wx:if="{{paymentResult.isPolling}}">订单确认中</text>
        <text wx:elif="{{paymentResult.success && paymentResult.isCompleted}}">充值成功</text>
        <text wx:elif="{{paymentResult.success}}">支付成功</text>
        <text wx:else>充值失败</text>
      </view>
      <view class="result-message">{{paymentResult.message}}</view>

      <!-- 轮询进度提示 -->
      <view class="polling-tip" wx:if="{{paymentResult.isPolling}}">
        <text>正在确认订单状态，请稍候...</text>
      </view>

      <view class="result-actions" wx:if="{{!paymentResult.isPolling}}">
        <button class="result-btn primary" bindtap="handleResultConfirm">
          <text wx:if="{{paymentResult.success && paymentResult.isCompleted}}">查看余额</text>
          <text wx:elif="{{paymentResult.isTimeout}}">查看记录</text>
          <text wx:else>重新充值</text>
        </button>
        <button class="result-btn secondary" bindtap="hideResultModal" wx:if="{{!paymentResult.isTimeout}}">关闭</button>
      </view>
    </view>
  </view>

  <!-- 加载遮罩 -->
  <view class="loading-mask {{showLoading ? 'show' : ''}}" wx:if="{{showLoading}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">{{loadingText}}</text>
    </view>
  </view>
</view>
