<!-- pkg_user/pages/card-detail/card-detail.wxml -->
<view class="card-detail-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">正在加载名片...</text>
  </view>

  <!-- 名片内容 -->
  <view wx:else class="card-content">
    <!-- 头部信息区域 -->
    <view class="header-section">
      <!-- 头像 -->
      <view class="avatar-container">
        <image
          src="{{cardInfo.avatar || '/assets/images/bot-avatar.png'}}"
          class="avatar-image"
          mode="aspectFill"
        />
      </view>

      <!-- 基本信息 -->
      <view class="basic-info">
        <view class="name-row">
          <text class="name">{{cardInfo.name}}</text>
          <text wx:if="{{cardInfo.gender === 1}}" class="gender male">♂</text>
          <text wx:elif="{{cardInfo.gender === 2}}" class="gender female">♀</text>
        </view>
        <text class="position">{{cardInfo.position}}</text>
        <text class="company">{{cardInfo.company}}</text>
        <text class="user-id">ID: {{cardInfo.userId}}</text>
      </view>

      <!-- 状态标签 -->
      <view class="status-tags">
        <text wx:if="{{cardInfo.isPublic}}" class="tag public">已认证</text>
        <text class="tag open">对外公开</text>
      </view>
    </view>

    <!-- 业务简介 -->
    <view class="intro-section" wx:if="{{cardInfo.businessIntro}}">
      <text class="intro-text">{{cardInfo.businessIntro}}</text>
      <text class="update-time">更新于 {{cardInfo.remark}}</text>
    </view>

    <!-- 联系方式 -->
    <view class="contact-section">
      <view class="contact-row">
        <view class="contact-item" wx:if="{{cardInfo.phone}}" bindtap="onCallPhone">
          <text class="contact-icon">📱</text>
          <view class="contact-info">
            <text class="contact-label">电话</text>
            <text class="contact-value">{{cardInfo.phone}}</text>
          </view>
        </view>

        <view class="contact-item" wx:if="{{cardInfo.wechat}}" bindtap="onCopyWechat">
          <text class="contact-icon">💬</text>
          <view class="contact-info">
            <text class="contact-label">微信</text>
            <text class="contact-value">{{cardInfo.wechat}}</text>
          </view>
        </view>
      </view>

      <view class="contact-row">
        <view class="contact-item" wx:if="{{cardInfo.email}}" bindtap="onSendEmail">
          <text class="contact-icon">📧</text>
          <view class="contact-info">
            <text class="contact-label">邮箱</text>
            <text class="contact-value">{{cardInfo.email}}</text>
          </view>
        </view>

        <view class="contact-item" wx:if="{{cardInfo.website}}" bindtap="onViewWebsite">
          <text class="contact-icon">🌐</text>
          <view class="contact-info">
            <text class="contact-label">网站</text>
            <text class="contact-value">{{cardInfo.website}}</text>
          </view>
        </view>
      </view>

      <view class="contact-item full-width" wx:if="{{cardInfo.address}}" bindtap="onViewAddress">
        <text class="contact-icon">📍</text>
        <view class="contact-info">
          <text class="contact-label">地址</text>
          <text class="contact-value">{{cardInfo.address}}</text>
        </view>
      </view>
    </view>

    <!-- 相关媒体 -->
    <view class="media-section" wx:if="{{cardInfo.images && cardInfo.images.length > 0}}">
      <text class="section-title">相关媒体</text>
      <view class="media-grid">
        <view class="media-item" wx:for="{{cardInfo.images}}" wx:key="index">
          <image src="{{item}}" class="media-image" mode="aspectFill" bindtap="onPreviewImage" data-src="{{item}}" />
          <view class="media-overlay">
            <text class="play-icon">▶️</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 名片类型 -->
    <view class="footer-section">
      <view class="footer-row">
        <text class="footer-label">名片类型</text>
        <text class="footer-value owner">房产经纪人</text>
      </view>
    </view>
  </view>
</view>
