// 收藏相关服务
const { request } = require('../utils/auth');

/**
 * 收藏服务
 */
class FavoriteService {
  
  /**
   * 获取收藏标签列表
   * @returns {Promise<Array>} 收藏标签列表
   */
  async getFavoriteTags() {
    try {
      const response = await request({
        url: '/blade-chat/post/favorite/tags',
        method: 'GET'
      });
      
      if (response.code === 200) {
        return response.data || [];
      } else {
        console.error('获取收藏标签失败:', response.msg);
        return [];
      }
    } catch (error) {
      console.error('获取收藏标签失败:', error);
      return [];
    }
  }

  /**
   * 获取收藏的帖子列表
   * @param {Object} params 查询参数
   * @returns {Promise<Object>} 收藏帖子列表
   */
  async getFavoritePosts(params = {}) {
    try {
      const response = await request({
        url: '/blade-chat/post/favorite',
        method: 'GET',
        data: {
          current: params.current || 1,
          size: params.size || 10,
          ...params
        }
      });
      
      if (response.code === 200) {
        return {
          success: true,
          data: response.data.records || [],
          total: response.data.total || 0,
          current: response.data.current || 1,
          size: response.data.size || 10
        };
      } else {
        console.error('获取收藏帖子失败:', response.msg);
        return {
          success: false,
          data: [],
          total: 0,
          message: response.msg || '获取收藏帖子失败'
        };
      }
    } catch (error) {
      console.error('获取收藏帖子失败:', error);
      return {
        success: false,
        data: [],
        total: 0,
        message: '网络错误，请重试'
      };
    }
  }

  /**
   * 切换收藏状态
   * @param {string|number} postId 帖子ID
   * @returns {Promise<Object>} 操作结果
   */
  async toggleFavorite(postId) {
    try {
      const response = await request({
        url: `/blade-chat/post/favorite/${postId}`,
        method: 'POST'
      });
      
      if (response.code === 200) {
        return {
          success: true,
          isFavorited: response.data,
          message: response.data ? '收藏成功' : '取消收藏成功'
        };
      } else {
        console.error('收藏操作失败:', response.msg);
        return {
          success: false,
          message: response.msg || '收藏操作失败'
        };
      }
    } catch (error) {
      console.error('收藏操作失败:', error);
      return {
        success: false,
        message: '网络错误，请重试'
      };
    }
  }

  /**
   * 清空收藏
   * @returns {Promise<Object>} 操作结果
   */
  async clearFavorites() {
    try {
      const response = await request({
        url: '/blade-chat/post/favorite/clear',
        method: 'POST'
      });
      
      if (response.code === 200) {
        return {
          success: true,
          message: '清空收藏成功'
        };
      } else {
        console.error('清空收藏失败:', response.msg);
        return {
          success: false,
          message: response.msg || '清空收藏失败'
        };
      }
    } catch (error) {
      console.error('清空收藏失败:', error);
      return {
        success: false,
        message: '网络错误，请重试'
      };
    }
  }

  /**
   * 格式化收藏标签数据
   * @param {Array} tags 原始标签数据
   * @returns {Array} 格式化后的标签数据
   */
  formatFavoriteTags(tags) {
    if (!Array.isArray(tags)) {
      return [];
    }
    
    return tags.map((tag, index) => ({
      text: tag,
      selected: false,
      id: index
    }));
  }

  /**
   * 检查是否有收藏内容
   * @returns {Promise<boolean>} 是否有收藏内容
   */
  async hasFavoriteContent() {
    try {
      const result = await this.getFavoritePosts({ size: 1 });
      return result.success && result.total > 0;
    } catch (error) {
      console.error('检查收藏内容失败:', error);
      return false;
    }
  }
}

// 创建单例实例
const favoriteService = new FavoriteService();

module.exports = favoriteService;
