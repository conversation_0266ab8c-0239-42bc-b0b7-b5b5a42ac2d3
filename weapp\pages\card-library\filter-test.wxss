/* 筛选功能测试页面样式 */
.test-container {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.test-header {
  text-align: center;
  padding: 40rpx 0;
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.test-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.filter-preview {
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.preview-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

/* 筛选栏样式 */
.filter-bar {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  background: #f8f9fa;
}

.filter-scroll {
  flex: 1;
  white-space: nowrap;
}

.filter-item {
  display: inline-block;
  padding: 12rpx 24rpx;
  margin: 0 10rpx;
  background: #f0f0f0;
  color: #666;
  font-size: 26rpx;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.filter-item.active {
  background: #ff6b6b;
  color: #fff;
}

.filter-more {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
}

.filter-icon {
  width: 32rpx;
  height: 32rpx;
}

.feature-description {
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.desc-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.desc-list {
  padding: 0;
}

.desc-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
  padding: 12rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.desc-item:last-child {
  margin-bottom: 0;
}

.desc-label {
  font-size: 26rpx;
  font-weight: 600;
  color: #007aff;
  min-width: 100rpx;
  margin-right: 12rpx;
}

.desc-text {
  flex: 1;
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.changes-summary {
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
}

.summary-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.change-list {
  padding: 0;
}

.change-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  padding: 12rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.change-item:last-child {
  margin-bottom: 0;
}

.change-type {
  padding: 4rpx 12rpx;
  font-size: 22rpx;
  font-weight: 600;
  border-radius: 12rpx;
  margin-right: 12rpx;
  min-width: 80rpx;
  text-align: center;
}

.change-item:nth-child(1) .change-type {
  background: #ffe6e6;
  color: #ff4757;
}

.change-item:nth-child(2) .change-type {
  background: #e6f7ff;
  color: #1890ff;
}

.change-item:nth-child(3) .change-type {
  background: #f6ffed;
  color: #52c41a;
}

.change-item:nth-child(4) .change-type {
  background: #fff7e6;
  color: #fa8c16;
}

.change-desc {
  flex: 1;
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}
