<view class="stats-bar-ui">
  <view class="stats-left">
    <view class="stat-item">
      <image class="icon" src="/assets/images/detail/eye-icon.png" />
      <text>{{views}}</text>
    </view>

    <view class="stat-item like-item {{isLiked ? 'liked' : ''}}" bindtap="onLikeTap">
      <image
        class="icon like-icon {{likeLoading ? 'loading' : ''}}"
        src="{{isLiked ? '/assets/images/detail/heart-filled.png' : '/assets/images/detail/heart-icon.png'}}"
      />
      <text class="like-text">{{likes}}</text>
    </view>

    <view class="stat-item">
      <image class="icon" src="/assets/images/detail/comment-icon.png" />
      <text>{{comments}}</text>
    </view>
  </view>

  <view class="stats-right">
    <view class="stat-item" bindtap="onFavoriteTap">
      <!-- <image class="icon" src="/assets/images/detail/star-icon.png" /> -->
      <image
        class="icon"
        src="{{isFavorited ? '/assets/images/detail/star-filled.png' : '/assets/images/detail/star-icon.png'}}"
      />
    </view>
    <view class="stat-item" bindtap="onMoreTap">
      <image class="icon" src="/assets/images/detail/more-icon.png" />
    </view>
  </view>
</view>