<!--mine.wxml-->
<!-- 导航栏固定在顶部 -->
<custom-nav 
  id="custom-nav"
  title="我的"
  background="transparent"
  text-color="#000"
  show-location="{{false}}"
  show-back="{{false}}"
  show-search="{{false}}"
  fixed="{{true}}"
  bind:navReady="onNavReady">
</custom-nav>

<!-- 内容区域 -->
<scroll-view
  scroll-y
  class="scroll-container"
  style="margin-top: {{navBarHeight}}px;height: calc(100vh - {{navBarHeight}}px);"
  bindscroll="onScroll"
>
  <view class="main-content">
    <view class="container">
      <user-card 
        avatar="{{userInfo.avatar || '/assets/images/default-avatar.png'}}"
        nickname="{{userInfo.nickname || '新用户'}}"
        tags="{{userBadges}}"
        bind:edit="editProfile"
      />
      <!-- 数据展示 -->
      <stats-bar
        points="{{stats.points}}"
        weeklyPosts="{{stats.weeklyPosts}}"
        walletBalance="{{stats.walletBalance}}"
        bind:wallettap="onWalletTap"
        bind:pointstap="onPointsTap"
      />
      <!-- 菜单栏 -->
      <menu-grid 
        menus="{{menus}}"
        bind:navigate="navigateTo"
      />
      <!-- 发布记录 -->
      <post-list 
        posts="{{posts}}"
        bind:more="goToMyPosts"
        bind:posttap="goToPostDetail"
        bind:edit="editPost"
        bind:delete="deletePost"
      />
      <!-- 收藏夹 -->
      <favorite-tags
        tags="{{favoriteTags}}"
        posts="{{favoritePosts}}"
        bind:more="goMyCollections"
        bind:tagtap="onFavoriteTagTap"
        bind:posttap="goToPostDetail"
      />
      <!-- AI周报 -->
      <ai-report-card 
        desc="本周发布{{stats.weeklyPosts}}条，曝光量较上周增长{{stats.exposureGrowth}}%"
        suggest="尝试在周末发布内容，可能获得更多互动"
        bind:tap="goAIReport"
      />
      <!-- 每日发布 -->
      <task-progress 
        title="每日发布"
        bindtap="goToUserDetail"
        points="50"
        progress="{{stats.taskProgress}}"
        desc="再发{{stats.remainingTasks}}条得50积分"
        bind:detail="goTaskDetail"
      />
    </view>
  </view>
</scroll-view>

<custom-tabbar show="{{showTabbar}}" /> 
