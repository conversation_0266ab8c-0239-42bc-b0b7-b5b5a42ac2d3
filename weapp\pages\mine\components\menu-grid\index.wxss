.menu-grid-card {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: flex-start;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 24rpx rgba(255,107,107,0.08);
  padding: 24rpx 0;
  margin-bottom: 32rpx;
}
.menu-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-bottom: 16rpx;
}
.menu-icon-wrap {
  width: 64rpx;
  height: 64rpx;
  margin-bottom: 8rpx;
  background: #fff0f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.menu-icon {
  width: 40rpx;
  height: 40rpx;
}
.menu-text {
  font-size: 24rpx;
  color: #333;
  margin-top: 4rpx;
}
.menu-item:active .menu-icon-wrap {
  background: #ff6b6b;
}
.menu-item:active .menu-text {
  color: #ff6b6b;
} 