/**
 * 地图标记诊断工具
 * 用于诊断和修复标记不显示的问题
 */

/**
 * 诊断标记显示问题
 * @param {Object} pageInstance 页面实例
 */
function diagnoseMarkerIssues(pageInstance) {
  console.log('=== 开始标记诊断 ===');
  
  const data = pageInstance.data;
  const posts = data.data?.posts || [];
  const markers = data.map?.markers || [];
  
  console.log('基本信息:');
  console.log(`- 帖子数量: ${posts.length}`);
  console.log(`- 标记数量: ${markers.length}`);
  console.log(`- 地图中心: ${data.map?.latitude?.toFixed(6)}, ${data.map?.longitude?.toFixed(6)}`);
  console.log(`- 缩放级别: ${data.map?.scale}`);
  
  // 检查数据一致性
  const issues = [];
  
  if (posts.length > 0 && markers.length === 0) {
    issues.push('有帖子数据但没有标记');
  }
  
  if (posts.length !== markers.length) {
    issues.push(`帖子和标记数量不匹配 (帖子: ${posts.length}, 标记: ${markers.length})`);
  }
  
  // 检查帖子数据完整性
  const invalidPosts = posts.filter((post, index) => {
    if (!post.id) return true;
    if (typeof post.latitude !== 'number' || isNaN(post.latitude)) return true;
    if (typeof post.longitude !== 'number' || isNaN(post.longitude)) return true;
    return false;
  });
  
  if (invalidPosts.length > 0) {
    issues.push(`${invalidPosts.length} 个帖子数据不完整`);
    console.log('不完整的帖子:', invalidPosts);
  }
  
  // 检查标记数据完整性
  const invalidMarkers = markers.filter((marker, index) => {
    if (!marker.id) return true;
    if (typeof marker.latitude !== 'number' || isNaN(marker.latitude)) return true;
    if (typeof marker.longitude !== 'number' || isNaN(marker.longitude)) return true;
    if (!marker.iconPath) return true;
    return false;
  });
  
  if (invalidMarkers.length > 0) {
    issues.push(`${invalidMarkers.length} 个标记数据不完整`);
    console.log('不完整的标记:', invalidMarkers);
  }
  
  // 检查标记位置是否在合理范围内
  const outOfRangeMarkers = markers.filter(marker => {
    const lat = marker.latitude;
    const lng = marker.longitude;
    return lat < -90 || lat > 90 || lng < -180 || lng > 180;
  });
  
  if (outOfRangeMarkers.length > 0) {
    issues.push(`${outOfRangeMarkers.length} 个标记位置超出有效范围`);
    console.log('位置超出范围的标记:', outOfRangeMarkers);
  }
  
  // 检查标记是否在当前视野范围内
  const mapCenter = {
    lat: data.map?.latitude || 0,
    lng: data.map?.longitude || 0
  };
  
  const visibleMarkers = markers.filter(marker => {
    const distance = calculateDistance(
      mapCenter.lat, mapCenter.lng,
      marker.latitude, marker.longitude
    );
    // 假设50公里为可见范围
    return distance <= 50000;
  });
  
  console.log(`可见范围内的标记: ${visibleMarkers.length}/${markers.length}`);
  
  if (issues.length === 0) {
    console.log('✅ 未发现明显问题');
  } else {
    console.log('❌ 发现以下问题:');
    issues.forEach((issue, index) => {
      console.log(`${index + 1}. ${issue}`);
    });
  }
  
  console.log('=== 诊断完成 ===');
  
  return {
    posts: posts.length,
    markers: markers.length,
    issues: issues,
    invalidPosts: invalidPosts.length,
    invalidMarkers: invalidMarkers.length,
    visibleMarkers: visibleMarkers.length
  };
}

/**
 * 计算两点间距离（米）
 */
function calculateDistance(lat1, lng1, lat2, lng2) {
  const R = 6371000; // 地球半径（米）
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLng = (lng2 - lng1) * Math.PI / 180;
  const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
            Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
            Math.sin(dLng/2) * Math.sin(dLng/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}

/**
 * 自动修复标记问题
 * @param {Object} pageInstance 页面实例
 */
function autoFixMarkerIssues(pageInstance) {
  console.log('=== 开始自动修复 ===');
  
  const diagnostic = diagnoseMarkerIssues(pageInstance);
  
  if (diagnostic.issues.length === 0) {
    console.log('无需修复');
    return false;
  }
  
  let fixed = false;
  
  // 如果有帖子但没有标记，重新生成标记
  if (diagnostic.posts > 0 && diagnostic.markers === 0) {
    console.log('重新生成标记...');
    if (pageInstance.checkAndFixMarkers) {
      pageInstance.checkAndFixMarkers();
      fixed = true;
    }
  }
  
  // 如果标记数量不匹配，重新生成
  if (diagnostic.posts !== diagnostic.markers) {
    console.log('修复标记数量不匹配...');
    if (pageInstance.checkAndFixMarkers) {
      pageInstance.checkAndFixMarkers();
      fixed = true;
    }
  }
  
  // 如果有无效标记，强制刷新
  if (diagnostic.invalidMarkers > 0) {
    console.log('修复无效标记...');
    if (pageInstance.forceRefreshMarkers) {
      pageInstance.forceRefreshMarkers();
      fixed = true;
    }
  }
  
  console.log(`=== 修复完成 (${fixed ? '已修复' : '无需修复'}) ===`);
  
  return fixed;
}

/**
 * 生成标记诊断报告
 * @param {Object} pageInstance 页面实例
 */
function generateDiagnosticReport(pageInstance) {
  const diagnostic = diagnoseMarkerIssues(pageInstance);
  const data = pageInstance.data;
  
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      posts: diagnostic.posts,
      markers: diagnostic.markers,
      issues: diagnostic.issues.length,
      status: diagnostic.issues.length === 0 ? 'healthy' : 'issues_found'
    },
    details: {
      mapCenter: {
        latitude: data.map?.latitude,
        longitude: data.map?.longitude,
        scale: data.map?.scale
      },
      dataIntegrity: {
        invalidPosts: diagnostic.invalidPosts,
        invalidMarkers: diagnostic.invalidMarkers,
        visibleMarkers: diagnostic.visibleMarkers
      },
      issues: diagnostic.issues
    },
    recommendations: []
  };
  
  // 生成修复建议
  if (diagnostic.posts > 0 && diagnostic.markers === 0) {
    report.recommendations.push('调用 checkAndFixMarkers() 重新生成标记');
  }
  
  if (diagnostic.posts !== diagnostic.markers) {
    report.recommendations.push('标记数量不匹配，需要重新生成标记');
  }
  
  if (diagnostic.invalidMarkers > 0) {
    report.recommendations.push('存在无效标记，建议调用 forceRefreshMarkers()');
  }
  
  if (diagnostic.visibleMarkers < diagnostic.markers * 0.5) {
    report.recommendations.push('大部分标记不在可见范围内，检查地图中心位置');
  }
  
  return report;
}

// 导出工具函数
module.exports = {
  diagnoseMarkerIssues,
  autoFixMarkerIssues,
  generateDiagnosticReport,
  calculateDistance
};

// 在小程序环境中提供全局访问
if (typeof wx !== 'undefined') {
  // 可以通过控制台调用这些方法
  console.log('标记诊断工具已加载');
  console.log('可用方法: diagnoseMarkerIssues, autoFixMarkerIssues, generateDiagnosticReport');
}
