Component({
  properties: {
    points: {
      type: Number,
      value: 0
    },
    weeklyPosts: {
      type: Number,
      value: 0
    },
    walletBalance: {
      type: Number,
      value: 0
    }
  },

  methods: {
    /**
     * 点击钱包余额
     */
    onWalletTap() {
      this.triggerEvent('wallettap');
    },

    /**
     * 点击积分
     */
    onPointsTap() {
      this.triggerEvent('pointstap');
    }
  }
});