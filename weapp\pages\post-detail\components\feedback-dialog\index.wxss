.feedback-dialog-mask {
  position: fixed;
  left: 0; right: 0; top: 0; bottom: 0;
  background: rgba(0,0,0,0.35);
  z-index: 3000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.feedback-dialog {
  background: #fff;
  border-radius: 18rpx;
  padding: 40rpx 32rpx 32rpx 32rpx;
  min-width: 540rpx;
  max-width: 90vw;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.12);
  display: flex;
  flex-direction: column;
  align-items: stretch;
}
.dialog-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 28rpx;
  text-align: center;
}
.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 18rpx;
  margin-bottom: 28rpx;
  justify-content: center;
}
.tag-item {
  padding: 12rpx 32rpx;
  border-radius: 32rpx;
  background: #f5f5f5;
  color: #666;
  font-size: 28rpx;
  border: 2rpx solid #f5f5f5;
  transition: all 0.2s;
}
.tag-item.active {
  background: #ff6b6b;
  color: #fff;
  border-color: #ff6b6b;
}
.feedback-textarea {
  min-height: 90rpx;
  border-radius: 12rpx;
  border: 1rpx solid #eee;
  padding: 18rpx;
  font-size: 28rpx;
  margin-bottom: 28rpx;
  resize: none;
}
.dialog-actions {
  display: flex;
  gap: 32rpx;
  justify-content: center;
} 