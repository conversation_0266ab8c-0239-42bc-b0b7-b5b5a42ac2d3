/* pages/recharge/recharge.wxss */
.recharge-container {
  width: 100%;
  height: 100vh;
  background-color: #f5f6f7;
  display: flex;
  flex-direction: column;
}

.scroll-container {
  flex: 1;
  overflow: hidden;
}

.main-content {
  padding: 24rpx;
  padding-bottom: 200rpx; /* 为底部操作栏留出空间 */
}

/* 余额卡片 */
.balance-card {
  background: linear-gradient(135deg, #ff7d7d 0%, #ff6b6b 100%);
  border-radius: 24rpx;
  padding: 40rpx 32rpx;
  margin-bottom: 32rpx;
  color: white;
}

.balance-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.balance-title {
  font-size: 28rpx;
  opacity: 0.9;
}

.balance-link {
  font-size: 24rpx;
  opacity: 0.8;
  text-decoration: underline;
}

.balance-amount {
  display: flex;
  align-items: baseline;
}

.currency {
  font-size: 32rpx;
  margin-right: 8rpx;
}

.amount {
  font-size: 56rpx;
  font-weight: bold;
}

/* 金额选择区域 */
.amount-section {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}



/* 快捷金额 */
.quick-amounts {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.amount-item {
  flex: 1;
  min-width: 140rpx;
  height: 80rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  transition: all 0.3s ease;
}

.amount-item.selected {
  border-color: #ff7d7d;
  background: #ff7d7d;
  color: white;
}



.amount-text {
  font-size: 28rpx;
  font-weight: 500;
}

/* 自定义金额 */
.custom-amount {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 24rpx;
}

.custom-amount-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.custom-title {
  font-size: 28rpx;
  color: #333;
}

.amount-range {
  font-size: 24rpx;
  color: #999;
}

.custom-input-wrapper {
  display: flex;
  align-items: center;
  border: 2rpx solid #e5e5e5;
  border-radius: 16rpx;
  padding: 0 20rpx;
  height: 80rpx;
  background: #fafafa;
}

.input-prefix {
  font-size: 28rpx;
  color: #666;
  margin-right: 8rpx;
}

.custom-input {
  flex: 1;
  font-size: 28rpx;
  height: 100%;
}

/* 支付方式 */
.payment-section {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
}

.payment-methods {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.payment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 16rpx;
  background: #fafafa;
  transition: all 0.3s ease;
}

.payment-item.selected {
  border-color: #ff7d7d;
  background: #fff5f5;
}

.payment-info {
  display: flex;
  align-items: center;
}

.payment-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}

.wechat-icon {
  border-radius: 8rpx;
  color: white;
}

.payment-name {
  font-size: 28rpx;
  color: #333;
}

.payment-radio {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.radio-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: transparent;
  transition: all 0.3s ease;
}

.radio-dot.checked {
  background: #ff7d7d;
}

/* 充值说明 */
.notice-section {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
}

.notice-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.notice-item {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  gap: 24rpx;
  z-index: 100;
}

.amount-info {
  flex: 1;
}

.pay-amount {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

.recharge-btn {
  width: 240rpx;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.recharge-btn.active {
  background: #ff7d7d;
  color: white;
}

.recharge-btn.disabled {
  background: #f0f0f0;
  color: #ccc;
}

/* 支付结果弹窗 */
.result-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.result-modal.show {
  opacity: 1;
  visibility: visible;
}

.result-content {
  background: white;
  border-radius: 24rpx;
  padding: 48rpx 32rpx;
  margin: 0 48rpx;
  text-align: center;
  max-width: 600rpx;
  width: 100%;
}

.result-icon {
  margin-bottom: 24rpx;
}

.icon-emoji {
  font-size: 120rpx;
  line-height: 1;
}

.result-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.result-message {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 32rpx;
  line-height: 1.5;
}

.result-actions {
  display: flex;
  gap: 16rpx;
}

.result-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.result-btn.primary {
  background: #ff7d7d;
  color: white;
}

.result-btn.secondary {
  background: #f0f0f0;
  color: #666;
}

/* 轮询状态样式 */
.loading-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 120rpx;
}

.spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #ff7d7d;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.polling-tip {
  background: #fff5f5;
  border: 1rpx solid #ffe6e6;
  border-radius: 12rpx;
  padding: 16rpx 20rpx;
  margin: 16rpx 0 24rpx 0;
  font-size: 24rpx;
  color: #ff7d7d;
  line-height: 1.4;
}

.polling-tip text {
  display: block;
}

/* 加载遮罩 */
.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.loading-mask.show {
  opacity: 1;
  visibility: visible;
}

.loading-content {
  background: white;
  border-radius: 16rpx;
  padding: 48rpx 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #ff7d7d;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}
