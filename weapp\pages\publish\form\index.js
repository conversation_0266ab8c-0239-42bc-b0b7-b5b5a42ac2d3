Page({
  data: {
    categoryName: '',
    title: '',
    description: '',
    contactName: '',
    contactNumber: ''
  },
  onLoad(options) {
    this.setData({
      categoryName: options.category || ''
    });
  },
  onTitleInput(e) {
    this.setData({ title: e.detail.value });
  },
  onDescriptionInput(e) {
    this.setData({ description: e.detail.value });
  },
  onContactNameInput(e) {
    this.setData({ contactName: e.detail.value });
  },
  onContactNumberInput(e) {
    this.setData({ contactNumber: e.detail.value });
  },
  onSubmit() {
    const { title, description, contactName, contactNumber, categoryName } = this.data;
    if (!title.trim() || !description.trim() || !contactName.trim() || !contactNumber.trim()) {
      wx.showToast({ title: '请填写完整信息', icon: 'none' });
      return;
    }
    // 这里可调用后端接口提交发布内容
    wx.showToast({ title: '发布成功', icon: 'success' });
    setTimeout(() => {
      wx.navigateBack({ delta: 2 }); // 返回到首页或上一级
    }, 1200);
  }
}); 