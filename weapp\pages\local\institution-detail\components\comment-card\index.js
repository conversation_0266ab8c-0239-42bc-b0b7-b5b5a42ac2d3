Component({
  properties: {
    feedbackId: { type: String, value: '' },
    avatar: { type: String, value: '' },
    nickname: { type: String, value: '' },
    content: { type: String, value: '' },
    time: { type: String, value: '' },
    likes: { type: Number, value: 0 },
    isHelpful: { type: Boolean, value: false },
    // 新增回复相关属性
    replyToUserName: { type: String, value: '' },
    image: { type: String, value: '' },
    replyCount: { type: Number, value: 0 },
    expanded: { type: Boolean, value: false },
    replies: { type: Array, value: [] },
    hasMoreReplies: { type: Boolean, value: false },
    allRepliesLoaded: { type: Boolean, value: false },
    totalReplyCount: { type: Number, value: 0 },
    commentId: { type: String, value: '' },
    institutionId: { type: Number, value: 0 }, // 机构ID，替代postId
    userId: { type: String, value: '' },
    formattedContent: { type: String, value: '' }
  },
  data: {},
  methods: {
    onLikeTap() {
      // 只能点赞，不能取消点赞
      if (this.properties.isHelpful) {
        return;
      }
      this.triggerEvent('like', {
        feedbackId: this.properties.feedbackId,
        isHelpful: this.properties.isHelpful
      });
    },

    // 回复评论
    onReplyTap() {
      this.triggerEvent('reply', {
        commentId: (this.properties.commentId || this.properties.feedbackId).toString(),
        parentId: (this.properties.commentId || this.properties.feedbackId).toString(),
        institutionId: this.properties.institutionId.toString(), // 使用机构ID
        replyToComment: {
          userId: this.properties.userId.toString(),
          nickname: this.properties.nickname,
          content: this.properties.content
        }
      });
    },

    // 切换回复展开状态
    onToggleReplies() {
      this.triggerEvent('toggleReplies', {
        commentId: this.properties.commentId || this.properties.feedbackId,
        expanded: this.properties.expanded
      });
    },

    // 加载回复
    onLoadReplies() {
      this.triggerEvent('loadReplies', {
        commentId: this.properties.commentId || this.properties.feedbackId
      });
    },

    // 点赞回复
    onReplyLike(e) {
      const replyId = e.currentTarget.dataset.replyId;
      this.triggerEvent('likeReply', {
        replyId: replyId,
        commentId: this.properties.commentId || this.properties.feedbackId
      });
    },

    // 回复某个回复
    onReplyToReply(e) {
      const reply = e.currentTarget.dataset.reply;
      this.triggerEvent('reply', {
        commentId: (this.properties.commentId || this.properties.feedbackId).toString(),
        parentId: (this.properties.commentId || this.properties.feedbackId).toString(),
        institutionId: this.properties.institutionId.toString(),
        replyToComment: {
          userId: reply.userId.toString(),
          nickname: reply.nickname,
          content: reply.content
        }
      });
    },

    // 预览评论图片
    onPreviewImage(e) {
      const src = e.currentTarget.dataset.src;
      wx.previewImage({
        current: src,
        urls: [src]
      });
    },

    // 预览回复图片
    onPreviewReplyImage(e) {
      const src = e.currentTarget.dataset.src;
      wx.previewImage({
        current: src,
        urls: [src]
      });
    },

    // 加载更多回复
    onLoadMoreReplies() {
      this.triggerEvent('loadMoreReplies', {
        commentId: this.properties.commentId || this.properties.feedbackId
      });
    },

    // 格式化内容方法
    formatContent() {
      const content = this.properties.content;
      if (!content) {
        this.setData({ formattedContent: '' });
        return;
      }

      // 处理@用户高亮
      const formattedContent = content.replace(
        /@([^\s@]+)/g,
        '<span style="color: #007aff; font-weight: 500;">@$1</span>'
      );

      this.setData({
        formattedContent: formattedContent
      });
    }
  },

  // 组件生命周期
  lifetimes: {
    attached() {
      // 处理@用户高亮显示
      if (this.properties.content) {
        this.formatContent();
      }
    }
  },

  // 监听属性变化
  observers: {
    'content': function(newContent) {
      if (newContent) {
        this.formatContent();
      }
    }
  }
});
