Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示进度条
    show: {
      type: Boolean,
      value: false
    },
    // 当前进度
    progress: {
      type: Number,
      value: 0
    },
    // 上传状态
    status: {
      type: String,
      value: 'uploading' // uploading, success, error
    },
    // 状态文本
    statusText: {
      type: String,
      value: '上传中...'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    progressWidth: '0%'
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 更新进度
     */
    updateProgress(progress) {
      this.setData({
        progress: progress,
        progressWidth: `${progress}%`
      });
    },

    /**
     * 设置状态
     */
    setStatus(status, text) {
      this.setData({
        status: status,
        statusText: text
      });
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件实例被放入页面节点树时执行
    },
    detached() {
      // 组件实例被从页面节点树移除时执行
    }
  },

  /**
   * 组件所在页面的生命周期
   */
  pageLifetimes: {
    show() {
      // 页面被展示时执行
    },
    hide() {
      // 页面被隐藏时执行
    }
  }
}); 