const UploadHelper = require('../../../../utils/upload');

const FileValidator = require('../../../../utils/fileValidator');

Component({
  properties: {
    logo: String
  },
  methods: {
    onChooseLogo() {
      wx.chooseMedia({
        count: 1,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          if (res.tempFiles && res.tempFiles.length > 0) {
            const file = res.tempFiles[0];
            const validation = FileValidator.validateImage(file.tempFilePath, file.size);
            if (!validation.isValid) {
              wx.showToast({ title: validation.errors[0], icon: 'none' });
              return;
            }
            this.triggerEvent('change', { logo: file.tempFilePath });
          }
        }
      });
    },
    onDeleteLogo() {
      this.triggerEvent('change', { logo: '' });
    }
  }
}); 