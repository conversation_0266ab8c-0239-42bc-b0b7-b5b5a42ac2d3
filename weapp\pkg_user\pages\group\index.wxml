<view class="group-page">
  <!-- 自定义导航栏 -->
  <layout
    title="同城群聊"
    showSearch="{{false}}"
    showLocation="{{false}}"
    background="linear-gradient(135deg, #ff6b6b 0%, #ff8a8a 100%)"
    showBack="{{true}}"
  >
  </layout>

  <!-- 页面内容容器 -->
  <scroll-view
    scroll-y
    class="scroll-container"
    style="margin-top: {{navBarHeight}}px; height: calc(100vh - {{navBarHeight}}px - 110rpx);"
    bindscroll="onScroll"
  >
    <view class="page-content">
      <view class="group-search-bar">
    <image class="search-icon" src="/assets/images/common/search.png" mode="aspectFit" />
    <input class="search-input" placeholder="关键词搜索" bindinput="onSearchInput" value="{{searchKeyword}}" />
    <view class="search-btn" bindtap="onSearch">搜索</view>
  </view>
  <view class="group-category-list">
    <view class="category-item {{selectedCategory === item.name ? 'active' : ''}}"
          wx:for="{{categories}}"
          wx:key="id"
          bindtap="onCategoryTap"
          data-name="{{item.name}}">
      <image class="category-icon" src="{{item.icon}}" mode="aspectFit" />
      <text class="category-label">{{item.name}}</text>
    </view>
  </view>
  <!-- 加载状态 -->
  <view wx:if="{{loading && groups.length === 0}}" class="loading-container">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 群组列表 -->
  <view class="group-list" wx:else>
    <block wx:for="{{groups}}" wx:key="id">
      <view class="group-card">
        <view class="group-card-icon">
          <block wx:if="{{item.icon}}">
            <image class="group-card-img" src="{{item.icon}}" mode="aspectFit" />
          </block>
          <block wx:else>
            <view class="group-card-text-icon">{{item.subtitle}}</view>
          </block>
        </view>
        <view class="group-card-content">
          <view class="group-card-title">{{item.title}}</view>
          <view class="group-card-desc">{{item.desc}}</view>
        </view>
        <view class="group-card-btn" bindtap="onShowQRCode" data-qr="{{item.qr}}">查看群聊</view>
      </view>
    </block>

    <!-- 空状态 -->
    <view wx:if="{{groups.length === 0 && !loading}}" class="empty-container">
      <view class="empty-text">暂无群组数据</view>
    </view>

    <!-- 加载更多 -->
    <view wx:if="{{loading && groups.length > 0}}" class="load-more">
      <view class="load-more-text">加载中...</view>
    </view>

    <!-- 没有更多数据 -->
    <view wx:if="{{!hasMore && groups.length > 0}}" class="no-more">
      <view class="no-more-text">没有更多数据了</view>
    </view>
  </view>
  <!-- 二维码弹窗 -->
  <view wx:if="{{showQRCode}}" class="qr-modal-mask" catchtap="onHideQRCode">
    <view class="qr-modal" catchtap="stopPropagation">
      <image class="qr-image" src="{{currentQRCode}}" mode="aspectFit" />
      <view class="qr-tip">点击保存图片，微信扫码加入</view>
      <button class="qr-save-btn" bindtap="onSaveQRCode">保存图片</button>
      <button class="qr-close-btn" bindtap="onHideQRCode">关闭</button>
    </view>
  </view>

    </view> <!-- 关闭 page-content -->
  </scroll-view>
</view>
