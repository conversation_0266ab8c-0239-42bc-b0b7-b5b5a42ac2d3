/**
 * 申请入驻页面
 * 遵循小程序开发规范：关注点分离、高内聚低耦合
 * <AUTHOR>
 * @since 2024
 */

// 引入数据层
const { applyInstitution, getInstitutionTypes } = require('../../api/institution.js');
const PublishStore = require('../../../stores/publishStore');

/**
 * 申请入驻页面配置
 */
Page({
  /**
   * 页面数据
   */
  data: {
    // 申请表单数据
    formData: {
      name: '', // 机构名称
      typeId: '', // 机构分类ID
      contactPerson: '', // 联系人姓名
      phone: '', // 联系电话
      email: '', // 电子邮箱
      wechat: '', // 微信号
      licenseNo: '', // 营业执照号码
      licenseImage: '', // 营业执照照片
      legalPerson: '', // 法人代表姓名
      province: '', // 省
      city: '', // 市
      district: '', // 区
      detailAddress: '', // 详细地址
      description: '', // 机构简介
      images: [], // 商家图片数组
      businessHours: '', // 营业时间
      specialServices: '', // 特色服务
      paymentMethods: '', // 支付方式
      isStore: true, // 是否支持到店
      hasDelivery: false, // 是否支持外卖
      serviceRadius: 5000, // 服务半径(米)
      latitude: '', // 纬度
      longitude: '', // 经度
      location: '' // 位置描述
    },

    // UI 状态数据
    institutionTypes: [], // 机构分类列表
    selectedTypeName: '', // 选中的分类名称
    submitting: false, // 提交状态

    // 配置常量
    maxImageCount: 9, // 最大图片数量
    maxFileSize: 5 * 1024 * 1024 // 最大文件大小 5MB
  },

  // ==================== 生命周期方法 ====================

  /**
   * 页面加载
   * @param {Object} options 页面参数
   */
  async onLoad(options) {
    console.log('=== 机构申请入驻页面加载 ===');
    console.log('页面参数:', options);

    try {
      // 初始化页面
      await this._initializePage();
    } catch (error) {
      console.error('页面初始化失败:', error);
      this._showError('页面初始化失败，请重试');
    }
  },

  /**
   * 页面显示
   */
  onShow() {
    // 页面显示时的逻辑
  },

  /**
   * 页面隐藏
   */
  onHide() {
    // 页面隐藏时的逻辑
  },

  /**
   * 页面卸载
   */
  onUnload() {
    // 清理资源
  },

  // ==================== 初始化方法 ====================

  /**
   * 初始化页面
   * @private
   */
  async _initializePage() {
    // 加载机构分类
    await this._loadInstitutionTypes();
  },

  // ==================== 数据加载方法 ====================

  /**
   * 加载机构分类
   * @private
   */
  async _loadInstitutionTypes() {
    console.log('=== 开始加载机构分类 ===');

    try {
      wx.showLoading({ title: '加载分类中...' });

      const result = await getInstitutionTypes();
      console.log('机构分类加载结果:', result);

      if (result.code === 200 && result.data) {
        this.setData({
          institutionTypes: result.data
        });
        console.log('✅ 机构分类加载成功，共', result.data.length, '个分类');
      } else {
        throw new Error(result.message || '加载机构分类失败');
      }
    } catch (error) {
      console.error('❌ 加载机构分类失败:', error);
      this._showError(error.message || '加载分类失败');

      // 设置默认分类
      this.setData({
        institutionTypes: []
      });
    } finally {
      wx.hideLoading();
    }
  },

  // ==================== 表单处理方法 ====================

  /**
   * 输入框变化处理
   * @param {Object} e 事件对象
   */
  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;

    this.setData({
      [`formData.${field}`]: value
    });

    console.log(`表单字段 ${field} 更新为:`, value);
  },

  /**
   * 提交申请
   */
  async onSubmit() {
    if (this.data.submitting) return;

    // 表单验证
    if (!this._validateForm()) {
      return;
    }

    try {
      this.setData({ submitting: true });
      wx.showLoading({ title: '提交申请中...' });

      // 1. 先上传所有未上传的图片（本地路径）
      let images = this.data.formData.images || [];
      // 只保留已上传的图片URL
      let uploadedImages = images.filter(img => typeof img === 'string' && (img.startsWith('http://') || img.startsWith('https://')));
      // 需要上传的本地图片路径（不是 http/https 开头的）
      let localImages = images.filter(img => typeof img === 'string' && !(img.startsWith('http://') || img.startsWith('https://')));
      if (localImages.length > 0) {
        const uploadResult = await this._uploadMultipleImages(localImages);
        if (Array.isArray(uploadResult)) {
          uploadedImages = [...uploadedImages, ...uploadResult];
        } else if (uploadResult && uploadResult.success && Array.isArray(uploadResult.data)) {
          uploadedImages = [...uploadedImages, ...uploadResult.data.map(item => item.url || item.path)];
        }
      }
      // 更新 formData.images 为全部已上传的图片URL
      this.setData({
        'formData.images': uploadedImages
      });

      // 2. 准备提交数据（images 字段为逗号分隔的线上url字符串）
      const submitData = this._prepareSubmitData();
      console.log('提交数据:', submitData);

      // 3. 调用申请入驻的API
      const result = await applyInstitution(submitData);

      if (result.code === 200) {
        this._handleSubmitSuccess();
      } else {
        this._showError(result.msg || '提交失败，请重试');
      }

    } catch (error) {
      console.error('提交申请失败:', error);
      this._showError('提交失败，请重试');
    } finally {
      this.setData({ submitting: false });
      wx.hideLoading();
    }
  },

  // ==================== 选择器事件处理 ====================

  /**
   * 选择机构分类
   */
  onSelectType() {
    const { institutionTypes } = this.data;
    if (institutionTypes.length === 0) {
      this._showError('暂无分类数据');
      return;
    }

    wx.showActionSheet({
      itemList: institutionTypes.map(type => type.name),
      success: (res) => {
        const selectedType = institutionTypes[res.tapIndex];
        this.setData({
          'formData.typeId': selectedType.id,
          selectedTypeName: selectedType.name
        });
        console.log('选择分类:', selectedType.name);
      }
    });
  },

  // ==================== 表单验证方法 ====================

  /**
   * 表单验证
   * @private
   * @returns {boolean} 验证结果
   */
  _validateForm() {
    const { formData } = this.data;

    // 基础信息验证
    if (!this._validateBasicInfo(formData)) return false;

    // 联系信息验证
    if (!this._validateContactInfo(formData)) return false;

    // 证照信息验证
    if (!this._validateLicenseInfo(formData)) return false;

    // 地址信息验证
    if (!this._validateAddressInfo(formData)) return false;

    return true;
  },

  /**
   * 验证基础信息
   * @private
   */
  _validateBasicInfo(formData) {
    if (!formData.name.trim()) {
      this._showError('请输入机构名称');
      return false;
    }

    if (!formData.typeId) {
      this._showError('请选择机构分类');
      return false;
    }

    return true;
  },

  /**
   * 验证联系信息
   * @private
   */
  _validateContactInfo(formData) {
    if (!formData.contactPerson.trim()) {
      this._showError('请输入联系人');
      return false;
    }

    if (!formData.phone.trim()) {
      this._showError('请输入联系电话');
      return false;
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(formData.phone)) {
      this._showError('请输入正确的手机号');
      return false;
    }

    return true;
  },

  /**
   * 验证证照信息
   * @private
   */
  _validateLicenseInfo(formData) {
    if (!formData.licenseNo.trim()) {
      this._showError('请输入营业执照号码');
      return false;
    }

    if (!formData.legalPerson.trim()) {
      this._showError('请输入法人代表姓名');
      return false;
    }

    return true;
  },

  /**
   * 验证地址信息
   * @private
   */
  _validateAddressInfo(formData) {
    if (!formData.detailAddress.trim()) {
      this._showError('请输入详细地址');
      return false;
    }

    // 验证位置信息
    if (!formData.latitude || !formData.longitude) {
      this._showError('请选择店铺位置');
      return false;
    }

    return true;
  },

  /**
   * 组装提交数据，图片用逗号拼接（只取线上url）
   */
  _prepareSubmitData() {
    const { formData } = this.data;
    // 只取线上url
    const imagesArr = (formData.images || []).filter(img => typeof img === 'string' && (img.startsWith('http') || img.startsWith('https')));
    return {
      ...formData,
      images: imagesArr.join(','),
      // 其他字段可按需补充
    };
  },

  /**
   * 处理提交成功
   * @private
   */
  _handleSubmitSuccess() {
    wx.showModal({
      title: '申请提交成功',
      content: '您的入驻申请已提交，我们将在3个工作日内审核并联系您。',
      showCancel: false,
      success: () => {
        wx.navigateBack();
      }
    });
  },

  // ==================== 工具方法 ====================

  /**
   * 显示错误信息
   * @private
   * @param {string} message 错误信息
   */
  _showError(message) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 3000
    });
  },

  // ==================== 图片上传方法 ====================

  /**
   * 上传营业执照图片（只允许上传一张）
   */
  async onUploadLicense() {
    if (this.data.formData.licenseImage) {
      this._showError('只能上传一张营业执照');
      return;
    }
    try {
      const res = await this._chooseImage(1);
      if (res.tempFiles && res.tempFiles.length > 0) {
        const { validFiles, invalidCount } = this._validateAndFilterFiles(res.tempFiles);
        if (invalidCount > 0) {
          this._showError('图片超过5MB限制');
        }
        if (validFiles.length > 0) {
          // 上传图片
          const url = await this._uploadLicenseImage(validFiles[0]);
          if (url) {
            this.setData({
              'formData.licenseImage': typeof url === 'string' ? url : (url.url || url.path || '')
            });
          }
        }
      }
    } catch (error) {
      if (!error.message.includes('cancel')) {
        this._showError('选择图片失败');
      }
    }
  },

  /**
   * 选择商家图片（只选择，不上传）
   */
  async onUploadImages() {
    const currentCount = this.data.formData.images.length;
    const { maxImageCount } = this.data;

    if (currentCount >= maxImageCount) {
      this._showError(`最多上传${maxImageCount}张图片`);
      return;
    }

    try {
      const res = await this._chooseImage(maxImageCount - currentCount);
      if (res.tempFiles && res.tempFiles.length > 0) {
        // 验证和过滤文件
        const { validFiles, invalidCount } = this._validateAndFilterFiles(res.tempFiles);
        if (invalidCount > 0) {
          this._showError(`有${invalidCount}张图片超过5MB限制`);
        }
        if (validFiles.length > 0) {
          // 只将本地路径添加到 images，不直接上传
          const currentImages = this.data.formData.images || [];
          const newImages = [...currentImages, ...validFiles];
          this.setData({
            'formData.images': newImages
          });
        }
      }
    } catch (error) {
      if (!error.message.includes('cancel')) {
        this._showError('选择图片失败');
      }
    }
  },

  /**
   * 选择图片的通用方法
   * @private
   * @param {number} count 选择图片数量
   * @returns {Promise} 选择结果
   */
  _chooseImage(count = 1) {
    return new Promise((resolve, reject) => {
      wx.chooseMedia({
        count: count,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        success: resolve,
        fail: reject
      });
    });
  },

  /**
   * 验证文件大小
   * @private
   * @param {number} fileSize 文件大小
   * @returns {boolean} 验证结果
   */
  _validateFileSize(fileSize) {
    if (fileSize > this.data.maxFileSize) {
      this._showError('图片大小不能超过5MB');
      return false;
    }
    return true;
  },

  /**
   * 验证和过滤文件
   * @private
   * @param {Array} tempFiles 临时文件数组
   * @returns {Object} 验证结果
   */
  _validateAndFilterFiles(tempFiles) {
    const validFiles = [];
    let invalidCount = 0;

    tempFiles.forEach((file, index) => {
      console.log(`图片${index + 1}信息:`, {
        path: file.tempFilePath,
        size: file.size,
        type: file.type
      });

      if (file.size > this.data.maxFileSize) {
        invalidCount++;
      } else {
        validFiles.push(file.tempFilePath);
      }
    });

    return { validFiles, invalidCount };
  },

  /**
   * 上传营业执照图片
   * @private
   * @param {string} filePath 文件路径
   */
  async _uploadLicenseImage(filePath) {
    wx.showLoading({ title: '上传营业执照中...' });

    try {
      console.log('开始上传营业执照:', filePath);

      // 构建图片对象，与发布页面保持一致
      const imageData = [{
        path: filePath,
        isTemp: true,
        size: 0,
        type: 'image/jpeg',
        name: `license_${Date.now()}.jpg`
      }];

      // 使用 PublishStore 的上传方法
      const result = await PublishStore.uploadImages(
        imageData,
        'institution_license'
      );

      console.log('营业执照上传结果:', result);

      if (result.success && result.data.length > 0) {
        // 获取上传后的图片URL
        const uploadedImage = result.data[0];
        const imageUrl = uploadedImage.url || uploadedImage.path;

        this.setData({
          'formData.licenseImage': imageUrl
        });

        wx.showToast({
          title: '营业执照上传成功',
          icon: 'success'
        });
        return imageUrl; // 返回上传成功的URL
      } else {
        throw new Error(result.message || '上传失败');
      }
    } catch (error) {
      this._handleUploadError(error, '营业执照上传');
      return null; // 上传失败返回null
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 上传多张图片（返回图片URL数组）
   * @private
   * @param {Array} filePaths 文件路径数组
   * @returns {Array} 上传后的图片URL数组
   */
  async _uploadMultipleImages(filePaths) {
    if (!filePaths || filePaths.length === 0) return [];
    wx.showLoading({ title: `上传中 0/${filePaths.length}` });
    try {
      const imageDataArray = filePaths.map((filePath, index) => ({
        path: filePath,
        isTemp: true,
        size: 0,
        type: 'image/jpeg',
        name: `institution_image_${Date.now()}_${index}.jpg`
      }));
      const result = await PublishStore.uploadImages(
        imageDataArray,
        'institution_images'
      );
      if (result.success && result.data.length > 0) {
        // 返回图片URL数组
        return result.data.map(item => item.url || item.path);
      } else {
        throw new Error(result.message || '批量上传失败');
      }
    } catch (error) {
      this._handleUploadError(error, '批量图片上传');
      throw error;
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 处理上传错误
   * @private
   * @param {Error} error 错误对象
   * @param {string} context 上传上下文
   */
  _handleUploadError(error, context = '上传') {
    console.error(`${context}失败:`, error);

    let errorMessage = '上传失败，请重试';

    if (error.message) {
      if (error.message.includes('network')) {
        errorMessage = '网络连接异常，请检查网络';
      } else if (error.message.includes('timeout')) {
        errorMessage = '上传超时，请重试';
      } else if (error.message.includes('token')) {
        errorMessage = '登录已过期，请重新登录';
      } else if (error.message.includes('size')) {
        errorMessage = '文件大小超出限制';
      } else if (error.message.includes('format')) {
        errorMessage = '文件格式不支持';
      } else {
        errorMessage = error.message;
      }
    }

    this._showError(errorMessage);
    return errorMessage;
  },

  // 选择位置
  onChooseLocation() {
    // 检查位置权限
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.userLocation'] === false) {
          // 用户拒绝了位置权限，引导用户开启
          wx.showModal({
            title: '需要位置权限',
            content: '选择店铺位置需要获取您的位置权限，请在设置中开启',
            confirmText: '去设置',
            success: (modalRes) => {
              if (modalRes.confirm) {
                wx.openSetting();
              }
            }
          });
          return;
        }

        // 调用位置选择
        this.chooseLocationAction();
      }
    });
  },

  // 执行位置选择
  chooseLocationAction() {
    wx.chooseLocation({
      success: (res) => {
        console.log('选择位置成功：', res);
        this.setData({
          'formData.location': res.address || res.name,
          'formData.latitude': res.latitude,
          'formData.longitude': res.longitude,
          'formData.detailAddress': res.name || res.address
        });

        // 自动填充地址信息
        this.parseLocationInfo(res);

        wx.showToast({
          title: '位置选择成功',
          icon: 'success'
        });
      },
      fail: (err) => {
        console.error('选择位置失败:', err);
        if (err.errMsg.includes('cancel')) {
          // 用户取消选择
          return;
        }

        if (err.errMsg.includes('auth deny')) {
          wx.showModal({
            title: '需要位置权限',
            content: '选择店铺位置需要获取您的位置权限',
            confirmText: '重新授权',
            success: (modalRes) => {
              if (modalRes.confirm) {
                wx.openSetting();
              }
            }
          });
          return;
        }

        wx.showToast({
          title: '位置选择失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 解析位置信息并自动填充
  parseLocationInfo(locationRes) {
    // 尝试从地址中解析省市区信息
    const address = locationRes.address || '';

    // 简单的地址解析（可以根据实际需要优化）
    const provinceMatch = address.match(/^([^省市区]+[省市区])/);
    const cityMatch = address.match(/([^省市区]+市)/);
    const districtMatch = address.match(/([^省市区]+[区县])/);

    const updates = {};

    if (provinceMatch && !this.data.formData.province) {
      updates['formData.province'] = provinceMatch[1];
    }

    if (cityMatch && !this.data.formData.city) {
      updates['formData.city'] = cityMatch[1];
    }

    if (districtMatch && !this.data.formData.district) {
      updates['formData.district'] = districtMatch[1];
    }

    if (Object.keys(updates).length > 0) {
      this.setData(updates);
    }
  },

  // 删除已上传的图片
  async onDeleteImage(e) {
    const { index } = e.currentTarget.dataset;
    const images = [...this.data.formData.images];

    try {
      // 显示确认对话框
      const res = await new Promise((resolve) => {
        wx.showModal({
          title: '确认删除',
          content: '确定要删除这张图片吗？',
          success: resolve
        });
      });

      if (!res.confirm) {
        return;
      }

      // 从数组中移除图片
      images.splice(index, 1);

      this.setData({
        'formData.images': images
      });

      wx.showToast({
        title: '删除成功',
        icon: 'success'
      });

      // 注意：这里不删除服务器上的文件，因为可能还在其他地方使用
      // 如果需要删除服务器文件，可以调用 PublishStore.deleteServerFile()

    } catch (error) {
      console.error('删除图片失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      });
    }
  },

  // 删除营业执照图片
  onDeleteLicenseImage() {
    this.setData({
      'formData.licenseImage': ''
    });
  },

  // 跳转到合作申请页面
  onGoCooperation() {
    wx.navigateTo({
      url: '/pkg_user/pages/cooperation/cooperation'
    });
  },

  /**
   * 测试上传功能
   * 用于调试上传问题
   */
  async testUploadFunction() {
    console.log('=== 开始测试上传功能 ===');

    try {
      // 选择测试图片
      console.log('请选择测试图片...');
      const res = await this._chooseImage(1);

      if (res.tempFiles && res.tempFiles.length > 0) {
        const tempFile = res.tempFiles[0];
        console.log('测试文件信息:', {
          path: tempFile.tempFilePath,
          size: tempFile.size,
          type: tempFile.type
        });

        // 验证文件
        if (!this._validateFileSize(tempFile.size)) {
          console.log('❌ 文件大小验证失败');
          return;
        }

        // 开始上传测试
        console.log('开始上传测试...');
        wx.showLoading({ title: '测试上传中...' });

        try {
          const result = await this._uploadLicenseImage(tempFile.tempFilePath);
          console.log('✅ 上传测试成功:', result);

          wx.showModal({
            title: '测试成功',
            content: '文件上传功能正常',
            showCancel: false
          });

        } catch (uploadError) {
          console.log('❌ 上传测试失败:', uploadError);

          wx.showModal({
            title: '测试失败',
            content: `上传失败: ${uploadError.message || '未知错误'}`,
            showCancel: false
          });
        } finally {
          wx.hideLoading();
        }
      }

    } catch (error) {
      console.error('测试上传功能异常:', error);
      wx.hideLoading();

      wx.showModal({
        title: '测试异常',
        content: `测试过程异常: ${error.message || '未知错误'}`,
        showCancel: false
      });
    }

    console.log('=== 上传功能测试完成 ===');
  },

  // 处理上传错误
  handleUploadError(error, context = '上传') {
    console.error(`${context}失败:`, error);

    let errorMessage = '上传失败，请重试';

    if (error.message) {
      if (error.message.includes('network')) {
        errorMessage = '网络连接异常，请检查网络';
      } else if (error.message.includes('timeout')) {
        errorMessage = '上传超时，请重试';
      } else if (error.message.includes('token')) {
        errorMessage = '登录已过期，请重新登录';
      } else if (error.message.includes('size')) {
        errorMessage = '文件大小超出限制';
      } else if (error.message.includes('format')) {
        errorMessage = '文件格式不支持';
      } else {
        errorMessage = error.message;
      }
    }

    wx.showToast({
      title: errorMessage,
      icon: 'none',
      duration: 3000
    });

    return errorMessage;
  },
});
