Component({
  properties: {
    items: Array,
    dynamicFormData: Object
  },
  methods: {
    isChecked(field, value) {
      const arr = this.data.dynamicFormData[field] || [];
      return arr.indexOf(value) > -1;
    },
    getSelectIndex(item) {
      const value = this.data.dynamicFormData[item.field];
      const options = (item.componentProps && item.componentProps.options) || [];
      const idx = options.findIndex(opt => opt.value === value);
      return idx >= 0 ? idx : 0;
    },
    getSelectLabel(item) {
      const value = this.data.dynamicFormData[item.field];
      const options = (item.componentProps && item.componentProps.options) || [];
      const found = options.find(opt => opt.value === value);
      console.log(found);
      return found ? found.label : '';
    },
    onDynamicInput(e) {
      let field, value;
      if (e.detail && typeof e.detail === 'object' && 'field' in e.detail && 'value' in e.detail) {
        field = e.detail.field;
        value = e.detail.value;
      } else if (e.currentTarget && e.currentTarget.dataset && e.currentTarget.dataset.field) {
        field = e.currentTarget.dataset.field;
        const type = e.currentTarget.dataset.type;
        console.log(e.currentTarget.dataset);
        if (type === 'checkbox') {
          value = e.detail.value || [];
        } else if (type === 'radio') {
          value = e.detail.value; // radio 直接是字符串
        } else if (e.currentTarget.dataset.options) {
          // select
          console.log(e.currentTarget.dataset.options);
          const options = e.currentTarget.dataset.options;
          const index = e.detail.value;
          value = options && options[index] ? options[index].value : '';
        } else {
          value = e.detail.value;
        }
      }
      if (field !== undefined) {
        this.triggerEvent('dynamicinput', { field, value }, { bubbles: true, composed: true });
      }
    },
    // 兼容picker/select/date/switch/checkbox事件
    onDynamicSelectChange(e) { this.onDynamicInput(e); },
    onDynamicDateChange(e) { this.onDynamicInput(e); },
    onDynamicSwitchChange(e) { this.onDynamicInput(e); },
    onDynamicCheckboxChange(e) { this.onDynamicInput(e); },
    onDynamicRadioChange(e) { this.onDynamicInput(e); },
    getOptionsByField(field) {
      // 查找当前items中field对应的options
      const findOptions = (items) => {
        for (let i = 0; i < items.length; i++) {
          const item = items[i];
          if (item.field === field && item.componentProps && item.componentProps.options) {
            return item.componentProps.options;
          }
          if (item.children) {
            const res = findOptions(item.children);
            if (res) return res;
          }
        }
        return null;
      };
      return findOptions(this.data.items) || [];
    }
  }
}); 