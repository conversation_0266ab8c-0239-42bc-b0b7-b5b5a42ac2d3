# 地图移动功能实现说明

## 功能描述
支持地图移动功能，当用户拖动地图时，将地图中心的经纬度信息传入 `searchLatitude` 和 `searchLongitude` 参数，搜索范围根据地图缩放比例反比例调整，最大范围为50公里。

## 核心特性
1. **动态搜索中心** - 地图移动时更新搜索经纬度
2. **智能搜索范围** - 根据缩放比例自动调整搜索半径
3. **最大范围限制** - 搜索范围最大不超过50公里
4. **防抖加载** - 避免频繁的数据请求
5. **状态显示** - 实时显示当前搜索中心和范围

## 技术实现

### 1. 数据结构扩展
```javascript
data: {
  searchLatitude: null,   // 搜索中心纬度
  searchLongitude: null,  // 搜索中心经度
  searchRadius: 5000,     // 搜索半径（米）
  currentRegion: null,    // 当前地图区域
}
```

### 2. 地图事件监听
```xml
<map
  bindregionchange="onRegionChange"
  enable-scroll="{{true}}"
  enable-zoom="{{true}}"
>
</map>
```

### 3. 区域变化处理
```javascript
// 地图区域变化事件
onRegionChange(e) {
  const { type, region } = e.detail;
  if (type === 'end') {
    this.handleRegionChangeEnd(region);
  }
}

// 处理地图区域变化结束
handleRegionChangeEnd(region) {
  // 更新搜索中心
  this.setData({
    searchLatitude: region.centerLocation.latitude,
    searchLongitude: region.centerLocation.longitude
  });

  // 计算新的搜索半径
  const newRadius = this.calculateSearchRadius(region.scale);
  this.setData({ searchRadius: newRadius });

  // 防抖加载数据
  this.debounceLoadRegionData();
}
```

### 4. 智能搜索半径计算
```javascript
calculateSearchRadius(scale) {
  let radius;
  if (scale >= 18) {
    radius = 200;      // 街道级别 - 200m
  } else if (scale >= 17) {
    radius = 500;      // 很高缩放 - 500m
  } else if (scale >= 16) {
    radius = 1000;     // 高缩放 - 1km
  } else if (scale >= 15) {
    radius = 1500;     // 较高缩放 - 1.5km
  } else if (scale >= 14) {
    radius = 2000;     // 中等缩放 - 2km
  } else if (scale >= 13) {
    radius = 3000;     // 中等偏低 - 3km
  } else if (scale >= 12) {
    radius = 5000;     // 较低缩放 - 5km
  } else if (scale >= 11) {
    radius = 8000;     // 低缩放 - 8km
  } else if (scale >= 10) {
    radius = 12000;    // 很低缩放 - 12km
  } else if (scale >= 9) {
    radius = 20000;    // 极低缩放 - 20km
  } else if (scale >= 8) {
    radius = 30000;    // 超低缩放 - 30km
  } else if (scale >= 7) {
    radius = 40000;    // 最低缩放 - 40km
  } else {
    radius = 50000;    // 超出范围 - 50km
  }

  // 确保不超过最大范围50公里
  return Math.min(radius, 50000);
}
```

### 5. API参数传递
```javascript
// 所有API调用都传递搜索经纬度
const params = {
  latitude: searchLat,
  longitude: searchLng,
  searchLatitude: searchLat,    // 新增参数
  searchLongitude: searchLng,   // 新增参数
  radius: radius,
  // ... 其他参数
};
```

### 6. 防抖机制
```javascript
// 防抖加载区域数据
debounceLoadRegionData() {
  if (this.regionChangeTimer) {
    clearTimeout(this.regionChangeTimer);
  }

  this.regionChangeTimer = setTimeout(() => {
    this.loadPostsInCurrentRegion();
  }, 800); // 800ms 防抖延迟
}
```

## 缩放比例与搜索范围对应关系

| 缩放级别 | 显示范围 | 搜索半径 | 适用场景 |
|---------|---------|---------|---------|
| 18+ | 街道级别 | 200m | 精确定位 |
| 17 | 小区级别 | 500m | 周边查找 |
| 16 | 社区级别 | 1km | 附近区域 |
| 15 | 片区级别 | 1.5km | 扩大搜索 |
| 14 | 区域级别 | 2km | 中等范围 |
| 13 | 大区域 | 3km | 较大范围 |
| 12 | 城区级别 | 5km | 城区搜索 |
| 11 | 城市级别 | 8km | 城市范围 |
| 10 | 大城市 | 12km | 大城市 |
| 9 | 都市圈 | 20km | 都市圈 |
| 8 | 城市群 | 30km | 城市群 |
| 7 | 省市级 | 40km | 省市级 |
| <7 | 省际级 | 50km | 最大范围 |

## 用户体验优化

### 1. 状态显示
```xml
<!-- 地图状态信息 -->
<cover-view class="map-status-info">
  <cover-text>搜索中心: {{searchLatitude.toFixed(4)}}, {{searchLongitude.toFixed(4)}}</cover-text>
  <cover-text>搜索范围: {{searchRadiusText}}</cover-text>
</cover-view>
```

### 2. 智能加载策略
- 检查区域变化是否足够大才重新加载
- 距离变化超过搜索半径的一半时重新加载
- 缩放变化超过2级时重新加载

### 3. 防抖优化
- 地图移动时800ms防抖延迟
- 避免用户拖动过程中频繁请求
- 给用户足够时间调整地图位置

## 性能优化

### 1. 请求优化
- 防抖机制减少API调用频率
- 智能判断是否需要重新加载数据
- 复用现有数据当变化较小时

### 2. 内存优化
- 及时清理定时器
- 避免内存泄漏
- 合理的数据缓存策略

### 3. 用户体验
- 平滑的状态更新
- 清晰的范围显示
- 合理的加载提示

## 测试验证

### 功能测试
1. ✅ 拖动地图，搜索中心应更新
2. ✅ 缩放地图，搜索范围应调整
3. ✅ 搜索范围不应超过50公里
4. ✅ 防抖机制应正常工作
5. ✅ 状态显示应实时更新

### 边界测试
1. ✅ 极限缩放级别测试
2. ✅ 快速连续拖动测试
3. ✅ 网络异常情况测试
4. ✅ 数据为空时的处理

### 性能测试
1. ✅ API调用频率控制
2. ✅ 内存使用情况
3. ✅ 响应速度测试
4. ✅ 低端设备兼容性

## 注意事项
1. 搜索经纬度参数需要后端API支持
2. 防抖时间可根据实际需要调整
3. 缩放级别与搜索范围的映射可以优化
4. 状态显示可以根据UI设计调整位置和样式
