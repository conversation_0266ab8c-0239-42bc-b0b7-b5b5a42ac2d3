<view class="points-record-page">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">积分记录</text>
  </view>

  <!-- 类型筛选 -->
  <view class="filter-section">
    <picker 
      mode="selector" 
      range="{{typeOptions}}" 
      range-key="label"
      bindchange="onTypeChange"
    >
      <view class="filter-picker">
        <text class="filter-label">积分类型：</text>
        <text class="filter-value">{{typeOptions[0].label}}</text>
        <text class="filter-arrow">></text>
      </view>
    </picker>
  </view>

  <!-- 积分记录列表 -->
  <view class="records-list">
    <block wx:if="{{records.length > 0}}">
      <view 
        class="record-item" 
        wx:for="{{records}}" 
        wx:key="id"
      >
        <view class="record-left">
          <view class="record-type">
            <text class="type-name type-signin" wx:if="{{item.type === 'SIGNIN'}}">
              {{item.typeName}}
            </text>
            <text class="type-name type-exchange" wx:elif="{{item.type === 'EXCHANGE'}}">
              {{item.typeName}}
            </text>
            <text class="type-name type-share" wx:elif="{{item.type === 'SHARE'}}">
              {{item.typeName}}
            </text>
            <text class="type-name type-invite" wx:elif="{{item.type === 'INVITE'}}">
              {{item.typeName}}
            </text>
            <text class="type-name type-admin" wx:elif="{{item.type === 'ADMIN'}}">
              {{item.typeName}}
            </text>
            <text class="type-name type-other" wx:else>
              {{item.typeName}}
            </text>
          </view>
          <view class="record-desc">{{item.description}}</view>
          <view class="record-time">{{item.operateTimeStr}}</view>
        </view>
        <view class="record-right">
          <view class="record-points {{item.points > 0 ? 'positive' : 'negative'}}">
            {{item.points > 0 ? '+' + item.points : item.points}}
          </view>
          <view class="record-balance">
            余额：{{item.afterPoints}}
          </view>
        </view>
      </view>
    </block>

    <!-- 空状态 -->
    <view wx:else class="empty-state">
      <image class="empty-icon" src="/assets/images/common/empty.png" mode="aspectFit"></image>
      <text class="empty-text">暂无积分记录</text>
    </view>

    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading-state">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 没有更多数据 -->
    <view wx:if="{{!hasMore && records.length > 0}}" class="no-more">
      <text class="no-more-text">没有更多数据了</text>
    </view>
  </view>
</view> 