.custom-tabbar {
  position: fixed;
  left: 0; right: 0; bottom: 0;
  height: 110rpx;
  background: #fff;
  box-shadow: 0 -2rpx 16rpx rgba(0,0,0,0.08);
  display: flex;
  align-items: center;
  justify-content: space-around;
  z-index: 9999;
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  backdrop-filter: blur(10rpx);
}

.custom-tabbar.hide {
  transform: translateY(100%);
}

.custom-tabbar.show {
  transform: translateY(0);
}

.custom-tabbar.navigating {
  pointer-events: none;
}
.tabbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 22rpx;
  position: relative;
  transition: color 0.2s ease;
}

.tabbar-item.active {
  color: #ff6b6b;
}

.tabbar-item:active {
  opacity: 0.7;
}

.tabbar-icon {
  width: 44rpx;
  height: 44rpx;
  margin-bottom: 4rpx;
}

.tabbar-text {
  font-weight: 400;
}

.tabbar-item.active .tabbar-text {
  font-weight: 500;
}

/* 底部指示器 */
.tab-indicator {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%) scaleX(0);
  width: 30rpx;
  height: 3rpx;
  background: #ff6b6b;
  border-radius: 2rpx;
  transition: transform 0.2s ease;
}

.tabbar-item.active .tab-indicator {
  transform: translateX(-50%) scaleX(1);
}
.tabbar-publish {
  width: 80rpx;
  height: 80rpx;
  background: #ff6b6b;
  border-radius: 50%;
  margin-top: -20rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.2s ease;
}

.tabbar-publish.active {
  background: #ff5555;
}

.tabbar-publish:active {
  transform: scale(0.95);
}

.tabbar-publish-icon {
  width: 50rpx;
  height: 50rpx;
  transition: none;
}

/* 页面切换加载动画已移除 */

/* 加载容器样式已移除 */

/* 主加载动画样式已移除 */

/* 加载文字样式已移除 */

/* 点状进度指示器和底部提示样式已移除 */

/* 自定义错误提示 */
.custom-error-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8rpx);
  z-index: 10001;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.2s ease-out;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50rpx 40rpx;
  background: rgba(255, 255, 255, 0.98);
  border-radius: 30rpx;
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(20rpx);
  border: 2rpx solid rgba(255, 107, 107, 0.2);
  max-width: 500rpx;
  margin: 0 40rpx;
}

.error-icon {
  font-size: 60rpx;
  margin-bottom: 20rpx;
  animation: errorShake 0.5s ease-in-out;
}

.error-message {
  font-size: 30rpx;
  color: #ff6b6b;
  font-weight: 500;
  text-align: center;
  margin-bottom: 15rpx;
  line-height: 1.4;
}

.error-tip {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  opacity: 0.8;
}

@keyframes errorShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5rpx); }
  75% { transform: translateX(5rpx); }
}

@keyframes dotPulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* quickFadeIn动画已移除 */

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Tab项加载状态 */
.tabbar-item.loading,
.tabbar-publish.loading {
  opacity: 0.6;
}

.mini-loading {
  width: 44rpx;
  height: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 4rpx;
}

.mini-spinner {
  width: 24rpx;
  height: 24rpx;
  border: 3rpx solid #f3f3f3;
  border-top: 3rpx solid #ff6b6b;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式适配 */
@media (max-width: 320px) {
  .tabbar-item {
    font-size: 20rpx;
  }

  .tabbar-icon {
    width: 40rpx;
    height: 40rpx;
  }

  .tabbar-publish {
    width: 80rpx;
    height: 80rpx;
  }

  .tabbar-publish-icon {
    width: 60rpx;
    height: 60rpx;
  }
}