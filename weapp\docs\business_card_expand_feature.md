# 名片卡片弹性展开功能说明

## 📋 功能概述

为收藏名片页面添加了点击卡片弹性展开显示详细信息的功能，用户可以通过点击名片卡片来查看更多详细信息，包括商业简介、完整联系方式等。

## 🎯 主要功能

### 1. 点击展开/收起
- **点击卡片**：展开或收起详细信息
- **动画效果**：平滑的展开/收起动画
- **视觉反馈**：展开时卡片边框高亮，箭头旋转

### 2. 详细信息展示
- **联系方式**：邮箱、微信、地址等完整联系信息
- **商业简介**：显示 businessProfile 字段内容
- **备注信息**：显示用户添加的备注
- **操作按钮**：查看详情、编辑收藏等快捷操作

### 3. 交互优化
- **长按功能**：长按卡片跳转到详情页面
- **事件隔离**：展开内容中的按钮不会触发卡片点击
- **管理模式**：管理模式下禁用展开功能

## 🔧 技术实现

### JavaScript 核心逻辑

```javascript
// 数据结构
data: {
  expandedCardIds: [], // 存储已展开的卡片ID
}

// 切换展开状态
toggleCardExpand(cardId) {
  const expandedCardIds = [...this.data.expandedCardIds];
  const index = expandedCardIds.indexOf(cardId);
  
  if (index > -1) {
    expandedCardIds.splice(index, 1); // 收起
  } else {
    expandedCardIds.push(cardId); // 展开
  }
  
  this.setData({ expandedCardIds });
}

// 点击事件处理
onCardTap(e) {
  if (this.data.isManageMode) return;
  
  const card = e.currentTarget.dataset.item;
  const cardId = card.id || card.cardId;
  this.toggleCardExpand(cardId);
}
```

### WXML 模板结构

```xml
<!-- 名片卡片 -->
<view class="card-item {{expandedCardIds.indexOf(item.id) > -1 ? 'expanded' : ''}}"
      bindtap="onCardTap"
      bindlongpress="onCardLongPress">
  
  <!-- 基础信息 -->
  <view class="card-info">
    <view class="card-header">
      <view class="card-left">
        <text class="card-name">{{item.fullName}}</text>
      </view>
      <view class="card-right">
        <text class="card-category">{{item.category}}</text>
        <view class="expand-indicator {{expandedCardIds.indexOf(item.id) > -1 ? 'expanded' : ''}}">
          <text class="expand-arrow">▼</text>
        </view>
      </view>
    </view>
    
    <!-- 展开的详细内容 -->
    <view class="card-expanded-content {{expandedCardIds.indexOf(item.id) > -1 ? 'show' : ''}}">
      <!-- 详细信息内容 -->
    </view>
  </view>
</view>
```

### CSS 动画样式

```css
/* 卡片展开状态 */
.card-item.expanded {
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  border: 2rpx solid #007aff;
}

/* 展开内容动画 */
.card-expanded-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease, opacity 0.3s ease;
  opacity: 0;
}

.card-expanded-content.show {
  max-height: 1000rpx;
  opacity: 1;
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 2rpx solid #f0f0f0;
}

/* 箭头旋转动画 */
.expand-indicator {
  transition: transform 0.3s ease;
}

.expand-indicator.expanded {
  transform: rotate(180deg);
}
```

## 📱 用户体验

### 展开前（基础信息）
- 姓名和备注
- 职位和公司
- 主要电话号码
- 收藏时间

### 展开后（详细信息）
- **联系方式区块**
  - 📧 邮箱地址
  - 💬 微信号
  - 📍 地址信息

- **商业简介区块**
  - 完整的商业简介内容
  - 特殊样式突出显示

- **备注区块**
  - 用户添加的备注信息
  - 黄色背景突出显示

- **操作按钮**
  - 👁️ 查看详情（跳转详情页）
  - ✏️ 编辑收藏（打开编辑弹窗）

## 🎨 视觉设计

### 展开状态指示
- **边框高亮**：展开时显示蓝色边框
- **阴影加深**：增强立体感
- **箭头旋转**：180度旋转表示展开状态

### 内容区块设计
- **分区明确**：不同类型信息用不同背景色区分
- **图标辅助**：使用 emoji 图标增强可读性
- **层次清晰**：标题、内容层次分明

### 动画效果
- **展开动画**：max-height 和 opacity 同时变化
- **按钮反馈**：点击时的缩放效果
- **过渡平滑**：0.3s 的缓动动画

## 🔄 交互流程

### 正常模式
1. **点击卡片** → 展开/收起详细信息
2. **长按卡片** → 跳转到详情页面
3. **点击"查看详情"** → 跳转到详情页面
4. **点击"编辑收藏"** → 打开编辑弹窗

### 管理模式
1. **点击卡片** → 无响应（管理模式下禁用）
2. **点击编辑按钮** → 打开编辑弹窗
3. **点击删除按钮** → 删除确认

## 📊 性能优化

### 状态管理
- 使用数组存储展开的卡片ID，支持多个卡片同时展开
- 避免在每个卡片中存储展开状态，减少数据冗余

### 动画优化
- 使用 CSS transition 而非 JavaScript 动画
- 合理设置 max-height 避免性能问题
- 使用 transform 实现箭头旋转，利用硬件加速

### 事件处理
- 使用 catchtap 阻止事件冒泡
- 合理的事件委托减少事件监听器数量

## 🐛 注意事项

### 兼容性
- 确保在不同设备上的动画效果一致
- 处理长文本内容的显示问题

### 用户体验
- 管理模式下禁用展开功能避免误操作
- 提供长按跳转详情的备选方案
- 展开内容过长时的滚动处理

### 数据处理
- 处理缺失字段的显示问题
- 确保 businessProfile 等字段的安全显示

## 🚀 扩展可能

### 功能扩展
- 添加收藏夹分组展开
- 支持批量操作模式
- 添加快速拨号功能

### 交互优化
- 支持手势滑动展开
- 添加震动反馈
- 自定义展开动画效果

---

**版本信息：** v1.0.0  
**更新时间：** 2024-08-04  
**适用页面：** pkg_user/pages/business-card/business-card
