const { submitFeedback } = require('../../../stores/feedbackStore.js');
const FileUploadStore = require('../../../stores/fileUploadStore.js');
const FileValidator = require('../../../utils/fileValidator.js');
const commentService = require('../../../services/commentService.js');

Page({
  data: {
    content: '',
    contactInfo: '',
    images: [],
    maxImageCount: 6,
    submitting: false,
    // 评论回复相关
    replyMode: false,
    replyToComment: null,
    parentId: null,
    postId: null,
    showMentionPanel: false,
    mentionUsers: [],
    mentionedUsers: []
  },

  onLoad(options) {
    // 页面加载时的初始化
    if (options && options.replyMode === 'true') {
      this.setData({
        replyMode: true,
        parentId: options.parentId,
        postId: options.postId,
        replyToComment: options.replyToComment ? JSON.parse(decodeURIComponent(options.replyToComment)) : null
      });

      // 设置导航栏标题
      wx.setNavigationBarTitle({
        title: '回复评论'
      });
    } else if (options && options.postId) {
      this.setData({
        postId: options.postId
      });
    }

    // 加载可@的用户列表
    this.loadMentionUsers();
  },

  // 输入内容变化
  onContentInput(e) {
    this.setData({
      content: e.detail.value
    });
  },

  // 联系方式输入
  onContactInfoInput(e) {
    this.setData({
      contactInfo: e.detail.value
    });
  },

  // 选择图片
  onChooseImage() {
    const currentCount = this.data.images.length;
    
    if (currentCount >= this.data.maxImageCount) {
      wx.showToast({
        title: `最多上传${this.data.maxImageCount}张图片`,
        icon: 'none'
      });
      return;
    }

    wx.chooseMedia({
      count: this.data.maxImageCount - currentCount,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 验证选择的图片
        const validImages = [];
        const invalidImages = [];
        
        res.tempFiles.forEach(file => {
          const validation = FileValidator.validateImage(file.tempFilePath, file.size);
          
          if (validation.isValid) {
            validImages.push({
              path: file.tempFilePath,
              size: file.size,
              type: file.type,
              isTemp: true,
              name: FileValidator.generateSafeFileName(file.tempFilePath)
            });
          } else {
            invalidImages.push({
              path: file.tempFilePath,
              errors: validation.errors
            });
          }
        });
        
        // 显示验证错误
        if (invalidImages.length > 0) {
          const errorMessage = invalidImages[0].errors[0];
          wx.showToast({
            title: errorMessage,
            icon: 'none',
            duration: 3000
          });
        }
        
        // 添加有效图片
        if (validImages.length > 0) {
          const updatedImages = [...this.data.images, ...validImages];
          this.setData({ images: updatedImages });
        }
      },
      fail: (error) => {
        console.error('选择图片失败:', error);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  // 删除图片
  onDeleteImage(e) {
    const index = e.currentTarget.dataset.index;
    const images = this.data.images;
    images.splice(index, 1);
    this.setData({ images });
  },

  // 预览图片
  onPreviewImage(e) {
    const { src } = e.currentTarget.dataset;
    const urls = this.data.images.map(img => img.path);
    wx.previewImage({
      current: src,
      urls
    });
  },

  // 提交反馈
  async onSubmit() {
    const { content, contactInfo, images, submitting, replyMode, parentId, postId, mentionedUsers } = this.data;

    if (submitting) return;

    if (!content.trim()) {
      wx.showToast({
        title: replyMode ? '请输入回复内容' : '请输入反馈内容',
        icon: 'none'
      });
      return;
    }

    this.setData({ submitting: true });

    try {
      // 先上传图片
      let imageUrl = '';
      if (images.length > 0) {
        wx.showLoading({ title: '正在上传图片...' });

        try {
          const uploadResult = await this.uploadImages();
          if (uploadResult.success && uploadResult.data.length > 0) {
            // 只取第一张图片的URL
            imageUrl = uploadResult.data[0].url;
          }
        } catch (error) {
          console.error('图片上传失败:', error);
          wx.showToast({
            title: '图片上传失败',
            icon: 'none'
          });
          this.setData({ submitting: false });
          wx.hideLoading();
          return;
        }
        wx.hideLoading();
      }

      // 提交评论或回复
      wx.showLoading({ title: replyMode ? '正在发布回复...' : '正在提交...' });

      let result;
      if (replyMode) {
        // 提交回复
        const replyData = {
          postId: parseInt(postId),
          parentId: parseInt(parentId),
          content: content.trim(),
          image: imageUrl,
          contactInfo: contactInfo.trim(),
          mentionedUserIds: mentionedUsers.map(user => user.userId || user.id),
          mentionedUsers: mentionedUsers.map(user => ({
            userId: user.userId || user.id,
            nickname: user.nickname
          }))
        };

        if (this.data.replyToComment) {
          replyData.replyToUserId = this.data.replyToComment.userId;
          replyData.replyToUserName = this.data.replyToComment.nickname;
        }

        result = await this.submitReply(replyData);
      } else {
        // 提交评论或反馈
        const commentData = {
          postId: postId,
          content: content.trim(),
          image: imageUrl,
          contactInfo: contactInfo.trim(),
          mentionedUserIds: mentionedUsers.map(user => user.userId || user.id),
          mentionedUsers: mentionedUsers.map(user => ({
            userId: user.userId || user.id,
            nickname: user.nickname
          }))
        };

        if (postId) {
          result = await this.submitComment(commentData);
        } else {
          // 如果没有postId，说明是普通反馈，不是评论
          result = await submitFeedback({
            content: content.trim(),
            contactInfo: contactInfo.trim(),
            image: imageUrl
          });
        }
      }

      if (result && (result.code === 200 || result.success)) {
        wx.showToast({
          title: replyMode ? '回复发布成功' : '提交成功',
          icon: 'success'
        });

        // 清空表单
        this.setData({
          content: '',
          contactInfo: '',
          images: [],
          mentionedUsers: []
        });

        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        throw new Error(result?.msg || result?.message || '提交失败');
      }

    } catch (error) {
      console.error('提交失败:', error);
      wx.showToast({
        title: error.message || '提交失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ submitting: false });
      wx.hideLoading();
    }
  },

  // 上传图片（参考发布页面的实现）
  async uploadImages() {
    if (!this.data.images || this.data.images.length === 0) {
      return { success: true, data: [] };
    }

    try {
      // 过滤出需要上传的临时图片
      const tempImages = this.data.images.filter(image => image.isTemp);
      
      if (tempImages.length === 0) {
        // 如果没有临时图片，直接返回现有图片数据
        return {
          success: true,
          data: this.data.images.map(image => ({
            fileId: image.fileId || image.id,
            url: image.url || image.path,
            size: image.size || 0,
            type: image.type || 'image/jpeg',
            name: image.name || `image_${Date.now()}.jpg`
          }))
        };
      }

      // 上传临时图片
      const uploadPromises = tempImages.map(async (image) => {
        try {
          const result = await FileUploadStore.uploadFile(
            image.path,
            'miniapp',
            'feedback',
            null
          );
          
          if (result.success) {
            return {
              fileId: result.data.id || result.data.fileId,
              url: result.data.accessUrl || result.data.path,
              size: image.size || 0,
              type: image.type || 'image/jpeg',
              name: result.data.name || `image_${Date.now()}.jpg`
            };
          } else {
            throw new Error(result.message);
          }
        } catch (error) {
          console.error('上传图片失败:', error);
          throw error;
        }
      });

      const uploadedImages = await Promise.all(uploadPromises);
      
      // 合并已上传的图片和现有图片
      const existingImages = this.data.images.filter(image => !image.isTemp).map(image => ({
        fileId: image.fileId || image.id,
        url: image.url || image.path,
        size: image.size || 0,
        type: image.type || 'image/jpeg',
        name: image.name || `image_${Date.now()}.jpg`
      }));

      return {
        success: true,
        data: [...existingImages, ...uploadedImages]
      };
    } catch (error) {
      console.error('批量上传图片失败:', error);
      return {
        success: false,
        message: '图片上传失败'
      };
    }
  },

  // 评论回复相关方法

  // 加载可@的用户列表
  async loadMentionUsers() {
    try {
      // 这里应该调用API获取可@的用户列表
      // 暂时使用模拟数据
      const mentionUsers = [
        { id: 1, nickname: '用户1', avatar: '/assets/images/default-avatar.png' },
        { id: 2, nickname: '用户2', avatar: '/assets/images/default-avatar.png' },
        { id: 3, nickname: '用户3', avatar: '/assets/images/default-avatar.png' }
      ];

      this.setData({ mentionUsers });
    } catch (error) {
      console.error('加载用户列表失败:', error);
      this.setData({ mentionUsers: [] });
    }
  },

  // 显示@用户面板
  showMentionPanel() {
    this.setData({ showMentionPanel: true });
  },

  // 关闭@用户面板
  closeMentionPanel() {
    this.setData({ showMentionPanel: false });
  },

  // 选择@用户
  selectMentionUser(e) {
    const user = e.currentTarget.dataset.user;
    const mentionedUsers = [...this.data.mentionedUsers];

    // 检查是否已经@过该用户
    const existingIndex = mentionedUsers.findIndex(u => u.userId === user.id);
    if (existingIndex === -1) {
      mentionedUsers.push({
        userId: user.id,
        nickname: user.nickname
      });

      this.setData({
        mentionedUsers,
        showMentionPanel: false
      });
    } else {
      wx.showToast({
        title: '已经@过该用户',
        icon: 'none'
      });
    }
  },

  // 移除@用户
  removeMentionUser(e) {
    const index = e.currentTarget.dataset.index;
    const mentionedUsers = [...this.data.mentionedUsers];
    mentionedUsers.splice(index, 1);
    this.setData({ mentionedUsers });
  },

  // 取消回复
  cancelReply() {
    this.setData({
      replyMode: false,
      replyToComment: null,
      parentId: null
    });

    // 重置导航栏标题
    wx.setNavigationBarTitle({
      title: '意见反馈'
    });
  },

  // 提交评论
  async submitComment(commentData) {
    return await commentService.addComment(commentData);
  },

  // 提交回复
  async submitReply(replyData) {
    return await commentService.replyComment(replyData);
  }
});