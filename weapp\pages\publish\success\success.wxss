.success-container {
  padding: 48rpx 32rpx 0 32rpx;
  text-align: center;
  background: #f7f8fa;
  min-height: 100vh;
}
.icon-success {
  width: 140rpx;
  height: 140rpx;
  margin: 0 auto 40rpx auto;
  background: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 32rpx #d2eafc;
}
.icon-success::after {
  content: '';
  display: block;
  width: 100%;
  height: 100%;
  background: url('https://img.icons8.com/color/144/26e07f/ok--v2.png') no-repeat center/cover;
  border-radius: 50%;
}
.title {
  font-size: 44rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  color: #222;
}
.desc {
  color: #666;
  margin-bottom: 36rpx;
  font-size: 28rpx;
}
.card {
  background: #fff;
  border-radius: 28rpx;
  padding: 36rpx 32rpx 28rpx 32rpx;
  margin-bottom: 48rpx;
  box-shadow: 0 4rpx 24rpx #e6f0fa;
  text-align: left;
}
.post-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
  color: #FF7D7D;
}
.post-desc {
  color: #444;
  font-size: 28rpx;
  margin-bottom: 8rpx;
}
.post-time {
  color: #bbb;
  font-size: 24rpx;
}
.btn-primary {
  background: #FF7D7D;
  color: #fff;
  border-radius: 48rpx;
  font-size: 32rpx;
  margin-bottom: 24rpx;
  height: 88rpx;
  line-height: 88rpx;
  box-shadow: 0 4rpx 16rpx #b3d6ff;
}
.btn-secondary {
  background: #f2f4f7;
  color: #FF7D7D;
  border-radius: 48rpx;
  font-size: 32rpx;
  margin-bottom: 40rpx;
  height: 88rpx;
  line-height: 88rpx;
}
.tips {
  text-align: left;
  margin-top: 100rpx;
  color: #999;
  font-size: 24rpx;
  line-height: 36rpx;
} 