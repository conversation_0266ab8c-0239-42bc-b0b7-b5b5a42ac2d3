# 地图页面分类过滤功能

## 功能概述

为地图页面添加了分类过滤功能，用户可以根据不同的分类来过滤显示地图上的帖子，提升用户体验和内容发现效率。

## 新增功能

### 1. 分类过滤按钮
- 位置：地图右上角控制按钮组
- 图标：使用 `filter-color-main.png` 图标
- 状态指示：当选择了分类时，显示红色小圆点徽章

### 2. 分类过滤器弹窗
- 触发：点击分类过滤按钮
- 样式：底部弹出式设计，符合移动端交互习惯
- 内容：显示所有可用分类，包括"全部"选项

### 3. 分类信息指示器
- 位置：地图顶部中央
- 显示：当前选中的分类名称
- 操作：点击"×"按钮可快速清除分类过滤

### 4. 实时数据过滤
- 选择分类后自动重新加载地图数据
- 支持与现有的位置搜索功能结合使用
- 保持用户当前的地图位置和缩放级别

## 技术实现

### 数据结构
```javascript
// 新增的数据字段
data: {
  selectedCategoryId: '',        // 选中的分类ID
  selectedCategoryName: '全部',   // 选中的分类名称
  categoryList: [],             // 分类列表
  showCategoryFilter: false,    // 显示分类过滤器
}
```

### 核心方法
1. `loadCategoryList()` - 加载分类列表
2. `showCategoryFilter()` - 显示分类过滤器
3. `onCategorySelect()` - 选择分类
4. `clearCategoryFilter()` - 清除分类过滤

### API集成
- 使用现有的 `getCategoryList()` 方法获取分类数据
- 在地图数据请求中传递 `category` 参数进行过滤

## 用户交互流程

1. 用户点击地图右上角的分类过滤按钮
2. 底部弹出分类选择器，显示所有可用分类
3. 用户选择目标分类
4. 系统自动重新加载地图数据，只显示该分类的帖子
5. 地图顶部显示当前分类信息
6. 用户可以点击"×"按钮或重新选择"全部"来清除过滤

## 样式设计

### 主题色适配
- 使用项目主题色 `#FF7D7D` 作为主色调
- 选中状态使用主题色高亮显示
- 按钮和指示器都采用主题色设计

### 响应式设计
- 弹窗最大高度为 80vh，适配不同屏幕尺寸
- 支持滚动查看更多分类
- 底部安全区域适配

### 动画效果
- 弹窗使用滑入动画 (slideUp)
- 按钮点击有缩放反馈效果
- 过渡动画使用 0.3s 缓动

## 兼容性

- 与现有地图功能完全兼容
- 不影响原有的搜索和定位功能
- 支持与关键词搜索组合使用
- 保持原有的性能优化策略

## 使用说明

### 开发者
1. 确保 `categoryStore.js` 正常工作
2. 分类数据需要包含 `id`、`name`、`icon` 等字段
3. 后端API需要支持 `category` 参数过滤

### 用户
1. 点击过滤按钮选择分类
2. 查看过滤后的地图内容
3. 使用清除按钮重置过滤条件
