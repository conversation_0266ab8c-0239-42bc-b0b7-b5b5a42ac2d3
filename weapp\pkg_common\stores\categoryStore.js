const { request } = require('../utils/request.js');

// 获取分类列表（从接口获取）
const getCategoryListFromAPI = async () => {
  try {
    const response = await request({
      url: '/blade-chat-open/categories',
      method: 'GET'
    });
    
    if (response.code === 200 && response.success) {
      const categories = response.data || [];
      console.log(categories);

      // 处理分类数据，转换为组件需要的格式
      return processCategories(categories);
    } else {
      console.error('获取分类列表失败:', response.msg);
      return [];
    }
  } catch (error) {
    console.error('请求分类接口失败:', error);
    return [];
  }
};

// 处理分类数据，转换为组件需要的格式
const processCategories = (categories) => {
  if (!categories || !Array.isArray(categories)) {
    return [];
  }
  
  return categories
    .filter(category => category.enabled === 1 && category.status === 1) // 只显示启用的分类
    .map(category => ({
      id: category.id,
      name: category.name,
      icon: category.icon || '/assets/images/common/default.png',
      description: category.description || '',
      tip: category.tip || '',
      maxImages: category.maxImages || -1,
      tags: category.tags || [],
      parentId: category.parentId,
      sort: category.sort || 0,
      badge: getCategoryBadge(category.name), // 根据分类名称设置徽章
      children: category.children || []
    }))
    .sort((a, b) => a.sort - b.sort); // 按排序字段排序
};

// 根据分类名称设置徽章
const getCategoryBadge = (categoryName) => {
  const badgeMap = {
    '求职招聘': '热门',
    '二手交易': '活跃',
    '房屋租赁': '极速转',
    '失物招领': '紧急',
    '寻物启事': '紧急'
  };
  
  return badgeMap[categoryName] || '';
};

/**
 * 获取分类列表（优先从接口获取，失败时使用模拟数据）
 * @returns {Promise<Array>} 分类数组
 */
const getCategoryList = async () => {
  try {
    return await getCategoryListFromAPI();
  } catch (error) {
    console.error('获取分类列表失败，使用模拟数据:', error);
    return [];
  }
};

module.exports = {
  getCategoryList,
  getCategoryListFromAPI,
  processCategories
}; 