<!-- 广播列表页面 -->
<layout title="广播通知" show-back="{{true}}">
  <view class="broadcast-list-container">
    <!-- 筛选标签 -->
    <scroll-view scroll-x class="filter-tabs-scroll" show-scrollbar="false" id="category-tabs-scroll">
      <view class="filter-tabs">
        <view 
          class="filter-tab {{currentFilter === '' ? 'active' : ''}}"
          bindtap="onFilterChange"
          data-filter=""
        >
          全部
        </view>
        <view 
          class="filter-tab {{currentFilter === 'post' ? 'active' : ''}}"
          bindtap="onFilterChange"
          data-filter="post"
        >
          帖子发布
        </view>
        <view 
          class="filter-tab {{currentFilter === 'institution' ? 'active' : ''}}"
          bindtap="onFilterChange"
          data-filter="institution"
        >
          机构入驻
        </view>
        <view 
          class="filter-tab {{currentFilter === 'checkin' ? 'active' : ''}}"
          bindtap="onFilterChange"
          data-filter="checkin"
        >
          签到
        </view>
        <view 
          class="filter-tab {{currentFilter === 'announcement' ? 'active' : ''}}"
          bindtap="onFilterChange"
          data-filter="announcement"
        >
          公告
        </view>
      </view>
    </scroll-view>

    <!-- 通知列表 -->
    <scroll-view 
      class="notice-list" 
      scroll-y="{{true}}"
      bindscrolltolower="onLoadMore"
      enable-back-to-top="{{true}}"
    >
      <view class="notice-item" wx:for="{{notices}}" wx:key="id" bindtap="onNoticeClick" data-notice="{{item}}">
        <view class="notice-header">
          <view class="notice-icon">{{item.icon}}</view>
          <view class="notice-meta">
            <text class="notice-title">{{item.title}}</text>
            <text class="notice-time">{{item.timeText}}</text>
          </view>
          <view class="notice-type">{{item.typeText}}</view>
        </view>
        
        <view class="notice-content">
          <text class="notice-text">{{item.content}}</text>
        </view>
        
        <view class="notice-footer" wx:if="{{item.author}}">
          <image class="author-avatar" src="{{item.authorAvatar}}" mode="aspectFill"></image>
          <text class="author-name">{{item.author}}</text>
          <view class="notice-category" wx:if="{{item.categoryName}}">
            <text class="category-text">{{item.categoryName}}</text>
          </view>
        </view>
      </view>

      <!-- 加载状态 -->
      <view class="loading-container" wx:if="{{loading}}">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 没有更多数据 -->
      <view class="no-more-container" wx:if="{{!hasMore && notices.length > 0}}">
        <text class="no-more-text">没有更多通知了</text>
      </view>

      <!-- 空状态 -->
      <view class="empty-container" wx:if="{{notices.length === 0 && !loading}}">
        <view class="empty-icon">📢</view>
        <text class="empty-text">暂无广播通知</text>
        <text class="empty-desc">当有新的通知时，会在这里显示</text>
      </view>
    </scroll-view>
  </view>
</layout>
