/**
 * 微信支付工具类
 * 参考 WxJava 的 WxPayService 实现
 */

// 注意：小程序环境中不能直接使用 Node.js 的 crypto 模块
// 签名和加密功能应该由后端处理
// 使用生产配置
const { getCurrentWechatPayConfig, validatePayConfig } = require('../config/payConfigProduction');

class WechatPayUtil {
  constructor() {
    this.config = getCurrentWechatPayConfig();
    validatePayConfig();
  }

  /**
   * 生成随机字符串
   * @param {number} length 长度
   * @returns {string} 随机字符串
   */
  generateNonceStr(length = 32) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * 生成时间戳
   * @returns {string} 时间戳
   */
  generateTimeStamp() {
    return Math.floor(Date.now() / 1000).toString();
  }

  /**
   * 生成签名（由后端处理）
   * @param {Object} params 参数对象
   * @param {string} signType 签名类型
   * @returns {string} 签名占位符
   */
  generateSign(params, signType = 'MD5') {
    // 在小程序环境中，签名应该由后端生成
    // 这里返回一个占位符，实际签名由后端API处理
    console.warn('签名生成应该由后端处理，当前返回占位符');
    return 'SIGN_PLACEHOLDER';
  }

  /**
   * 验证签名
   * @param {Object} params 参数对象
   * @param {string} signType 签名类型
   * @returns {boolean} 验证结果
   */
  verifySign(params, signType = 'MD5') {
    const sign = params.sign;
    if (!sign) {
      return false;
    }

    const calculatedSign = this.generateSign(params, signType);
    return sign === calculatedSign;
  }

  /**
   * 对象转XML
   * @param {Object} obj 对象
   * @returns {string} XML字符串
   */
  objectToXml(obj) {
    let xml = '<xml>';
    Object.keys(obj).forEach(key => {
      const value = obj[key];
      if (typeof value === 'number' || /^\d+$/.test(value)) {
        xml += `<${key}>${value}</${key}>`;
      } else {
        xml += `<${key}><![CDATA[${value}]]></${key}>`;
      }
    });
    xml += '</xml>';
    return xml;
  }

  /**
   * XML转对象
   * @param {string} xml XML字符串
   * @returns {Object} 对象
   */
  xmlToObject(xml) {
    const result = {};
    const regex = /<(\w+)>(?:<!\[CDATA\[)?(.*?)(?:\]\]>)?<\/\1>/g;
    let match;
    
    while ((match = regex.exec(xml)) !== null) {
      const key = match[1];
      const value = match[2];
      result[key] = value;
    }
    
    return result;
  }

  /**
   * 构建统一下单参数
   * @param {Object} orderInfo 订单信息
   * @returns {Object} 统一下单参数
   */
  buildUnifiedOrderParams(orderInfo) {
    const {
      outTradeNo,
      body,
      totalFee,
      openid,
      tradeType = 'JSAPI',
      timeExpire,
      attach,
      detail,
      goodsTag,
      productId
    } = orderInfo;

    const params = {
      appid: this.config.appId,
      mch_id: this.config.mchId,
      nonce_str: this.generateNonceStr(),
      body: body,
      out_trade_no: outTradeNo,
      total_fee: totalFee,
      spbill_create_ip: '127.0.0.1', // 在实际应用中应该获取真实IP
      notify_url: this.config.notifyUrl,
      trade_type: tradeType,
      sign_type: this.config.signType || 'MD5'
    };

    // 小程序支付必须传openid
    if (tradeType === 'JSAPI' && openid) {
      params.openid = openid;
    }

    // Native支付必须传product_id
    if (tradeType === 'NATIVE' && productId) {
      params.product_id = productId;
    }

    // 可选参数
    if (timeExpire) params.time_expire = timeExpire;
    if (attach) params.attach = attach;
    if (detail) params.detail = detail;
    if (goodsTag) params.goods_tag = goodsTag;

    // 生成签名
    params.sign = this.generateSign(params, params.sign_type);

    return params;
  }

  /**
   * 构建小程序支付参数
   * @param {string} prepayId 预支付交易会话标识
   * @returns {Object} 小程序支付参数
   */
  buildMiniProgramPayParams(prepayId) {
    const timeStamp = this.generateTimeStamp();
    const nonceStr = this.generateNonceStr();
    const packageStr = `prepay_id=${prepayId}`;
    const signType = 'MD5';

    const params = {
      appId: this.config.appId,
      timeStamp: timeStamp,
      nonceStr: nonceStr,
      package: packageStr,
      signType: signType
    };

    // 生成支付签名
    params.paySign = this.generateSign(params, signType);

    return params;
  }

  /**
   * 构建查询订单参数
   * @param {string} outTradeNo 商户订单号
   * @param {string} transactionId 微信订单号
   * @returns {Object} 查询订单参数
   */
  buildOrderQueryParams(outTradeNo, transactionId) {
    const params = {
      appid: this.config.appId,
      mch_id: this.config.mchId,
      nonce_str: this.generateNonceStr(),
      sign_type: this.config.signType || 'MD5'
    };

    if (outTradeNo) {
      params.out_trade_no = outTradeNo;
    } else if (transactionId) {
      params.transaction_id = transactionId;
    } else {
      throw new Error('商户订单号和微信订单号不能同时为空');
    }

    params.sign = this.generateSign(params, params.sign_type);
    return params;
  }

  /**
   * 构建关闭订单参数
   * @param {string} outTradeNo 商户订单号
   * @returns {Object} 关闭订单参数
   */
  buildCloseOrderParams(outTradeNo) {
    const params = {
      appid: this.config.appId,
      mch_id: this.config.mchId,
      out_trade_no: outTradeNo,
      nonce_str: this.generateNonceStr(),
      sign_type: this.config.signType || 'MD5'
    };

    params.sign = this.generateSign(params, params.sign_type);
    return params;
  }

  /**
   * 构建退款参数
   * @param {Object} refundInfo 退款信息
   * @returns {Object} 退款参数
   */
  buildRefundParams(refundInfo) {
    const {
      outTradeNo,
      transactionId,
      outRefundNo,
      totalFee,
      refundFee,
      refundDesc,
      refundAccount
    } = refundInfo;

    const params = {
      appid: this.config.appId,
      mch_id: this.config.mchId,
      nonce_str: this.generateNonceStr(),
      out_refund_no: outRefundNo,
      total_fee: totalFee,
      refund_fee: refundFee,
      sign_type: this.config.signType || 'MD5'
    };

    if (outTradeNo) {
      params.out_trade_no = outTradeNo;
    } else if (transactionId) {
      params.transaction_id = transactionId;
    } else {
      throw new Error('商户订单号和微信订单号不能同时为空');
    }

    if (refundDesc) params.refund_desc = refundDesc;
    if (refundAccount) params.refund_account = refundAccount;
    if (this.config.refundNotifyUrl) params.notify_url = this.config.refundNotifyUrl;

    params.sign = this.generateSign(params, params.sign_type);
    return params;
  }

  /**
   * 处理支付回调数据
   * @param {string} xmlData 回调XML数据
   * @returns {Object} 处理结果
   */
  processNotifyData(xmlData) {
    try {
      const data = this.xmlToObject(xmlData);
      
      // 验证签名
      if (!this.verifySign(data)) {
        return {
          success: false,
          message: '签名验证失败',
          data: null
        };
      }

      // 验证返回状态
      if (data.return_code !== 'SUCCESS') {
        return {
          success: false,
          message: data.return_msg || '通信失败',
          data: data
        };
      }

      // 验证业务结果
      if (data.result_code !== 'SUCCESS') {
        return {
          success: false,
          message: data.err_code_des || '业务失败',
          data: data
        };
      }

      return {
        success: true,
        message: '支付成功',
        data: data
      };
    } catch (error) {
      return {
        success: false,
        message: `处理回调数据失败: ${error.message}`,
        data: null
      };
    }
  }

  /**
   * 生成回调响应XML
   * @param {boolean} success 处理是否成功
   * @param {string} message 响应消息
   * @returns {string} 响应XML
   */
  generateNotifyResponse(success = true, message = 'OK') {
    const response = {
      return_code: success ? 'SUCCESS' : 'FAIL',
      return_msg: message
    };
    
    return this.objectToXml(response);
  }
}

module.exports = WechatPayUtil;
