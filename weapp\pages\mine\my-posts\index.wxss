/* 我的帖子页面样式 */
.my-posts-page {
  min-height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  padding: 16rpx 24rpx;
  padding-top: calc(16rpx + env(safe-area-inset-top));
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  z-index: 100;
}

.header-back {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 32rpx;
  height: 32rpx;
}

.header-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.header-action {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
}

/* 搜索栏 */
.search-container {
  display: flex;
  align-items: center;
  background: #fff;
  padding: 16rpx 24rpx;
  gap: 16rpx;
}

.search-box {
  flex: 1;
  display: flex;
  align-items: center;
  background: #f8f8f8;
  border-radius: 24rpx;
  padding: 12rpx 20rpx;
  gap: 12rpx;
}

.search-box .search-icon {
  width: 28rpx;
  height: 28rpx;
  opacity: 0.6;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  background: transparent;
}

.input-placeholder {
  color: #999;
}

.clear-btn {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ddd;
  border-radius: 50%;
}

.clear-icon {
  font-size: 24rpx;
  color: #fff;
  line-height: 1;
}

.search-cancel {
  font-size: 28rpx;
  color: #ff6b6b;
}

/* 筛选栏 */
.filter-container {
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-scroll {
  padding: 16rpx 24rpx;
  white-space: nowrap;
}

.filter-item {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 32rpx;
  margin-right: 20rpx;
  background: #f5f5f5;
  color: #666;
  font-size: 28rpx;
  border-radius: 32rpx;
  transition: all 0.3s ease;
  white-space: nowrap;
  min-width: 80rpx;
  height: 64rpx;
  box-sizing: border-box;
}

.filter-item.active {
  background: linear-gradient(135deg, #ff6b6b 0%, #ff8a8a 100%);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
}

/* 帖子列表 */
.posts-scroll {
  flex: 1;
  background: #f5f5f5;
}

.posts-list {
  padding: 20rpx 0;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #ff6b6b;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner.small {
  width: 40rpx;
  height: 40rpx;
  border-width: 3rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 26rpx;
  color: #999;
}

/* 加载更多 */
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 32rpx 0;
}

/* 没有更多 */
.no-more {
  display: flex;
  justify-content: center;
  padding: 32rpx 0;
}

.no-more-text {
  font-size: 26rpx;
  color: #999;
}

/* 空状态 */
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  color: #666;
  font-weight: 500;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
  text-align: center;
  line-height: 1.5;
}

.empty-action {
  padding: 16rpx 32rpx;
  background: #ff6b6b;
  color: #fff;
  border-radius: 24rpx;
  margin-top: 16rpx;
}

.action-text {
  font-size: 28rpx;
  color: #fff;
}

/* 遮罩层 */
.action-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

/* 底部弹出菜单 */
.action-sheet {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: transparent;
  z-index: 1001;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.action-sheet.show {
  transform: translateY(0);
}

.action-sheet-content {
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 32rpx 0 16rpx 0;
  margin: 0 16rpx 16rpx 16rpx;
}

.action-item {
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 24rpx 32rpx;
  transition: background 0.3s ease;
}

.action-item:active {
  background: #f8f8f8;
}

.action-item.delete {
  color: #ff4d4f;
}

.action-item.delete .action-text {
  color: #ff4d4f;
}

.action-icon {
  width: 40rpx;
  height: 40rpx;
}

.action-item .action-text {
  font-size: 30rpx;
  color: #333;
}

.action-cancel {
  background: #fff;
  border-radius: 24rpx;
  margin: 0 16rpx 16rpx 16rpx;
  padding: 24rpx 0;
  text-align: center;
}

.cancel-text {
  font-size: 30rpx;
  color: #666;
}
.post-card {
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 24rpx rgba(0,0,0,0.06);
  margin: 24rpx 24rpx 0 24rpx;
  overflow: hidden;
  position: relative;
}
.action-mask {
  position: fixed;
  left: 0; right: 0; top: 0; bottom: 0;
  background: rgba(0,0,0,0.35);
  z-index: 1000;
  animation: fadeIn 0.25s;
}
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}
.hidden{
  display: none;
}
.action-sheet {
  position: fixed;
  left: 0; right: 0;
  bottom: 0;
  z-index: 1001;
  pointer-events: none;
}
.action-sheet.show {
  pointer-events: auto;
}
.action-sheet-inner {
  background: #fff;
  border-top-left-radius: 32rpx;
  border-top-right-radius: 32rpx;
  box-shadow: 0 -8rpx 32rpx rgba(0,0,0,0.08);
  margin: 0 24rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  animation: slideUp 0.25s;
}
@keyframes slideUp {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}
.action-btn {
  padding: 36rpx 0;
  text-align: center;
  font-size: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: #fff;
  transition: background 0.2s;
}
.action-btn:last-child {
  border-bottom: none;
}
.delete { color: #ff4d4f; }
.edit { color: #4C6FFF; }
.move { color: #ffc53d; }
.complete { color: #07c160; }
.cancel {
  color: #888;
  background: #f7f8fa;
  border-radius: 32rpx;
  margin: 24rpx 24rpx 0 24rpx;
  font-weight: bold;
  font-size: 34rpx;
} 