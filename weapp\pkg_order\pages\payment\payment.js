// pages/payment/payment.js
const paymentStore = require('../../../stores/paymentStore');
const { payConfig } = require('../../../config/payConfig');

Page({
  data: {
    // 订单信息
    orderInfo: {
      orderNo: '',
      description: '',
      amount: '0.00',
      businessType: '',
      businessId: ''
    },
    
    // 业务类型文本
    businessTypeText: '',
    
    // 支付方式列表
    paymentMethods: [],
    
    // 选中的支付方式
    selectedMethod: 'wechat',
    
    // 优惠券相关
    showDiscount: false,
    selectedCoupon: null,
    
    // 协议同意状态
    agreedToTerms: false,
    
    // 最终支付金额
    finalAmount: '0.00',
    
    // 是否可以支付
    canPay: false,
    
    // 处理状态
    isProcessing: false,
    
    // 支付结果弹窗
    showResultModal: false,
    paymentResult: {
      success: false,
      message: ''
    },
    
    // 加载状态
    showLoading: false,
    loadingText: '处理中...'
  },

  onLoad(options) {
    console.log('支付页面参数:', options);
    
    // 从参数中获取订单信息
    this.initOrderInfo(options);
    
    // 初始化支付方式
    this.initPaymentMethods();
    
    // 初始化业务类型文本
    this.initBusinessTypeText();
    
    // 检查支付环境
    this.checkPaymentEnvironment();
  },

  /**
   * 初始化订单信息
   */
  initOrderInfo(options) {
    const orderInfo = {
      orderNo: options.orderNo || '',
      description: options.description || '商品支付',
      amount: options.amount || '0.00',
      businessType: options.businessType || '',
      businessId: options.businessId || ''
    };

    this.setData({
      orderInfo: orderInfo,
      finalAmount: orderInfo.amount
    });

    // 更新支付按钮状态
    this.updatePayButtonState();
  },

  /**
   * 初始化支付方式
   */
  initPaymentMethods() {
    const methods = paymentStore.getPaymentMethods();
    this.setData({
      paymentMethods: methods
    });
  },

  /**
   * 初始化业务类型文本
   */
  initBusinessTypeText() {
    const businessTypeConfig = paymentStore.getBusinessTypeConfig();
    const businessType = this.data.orderInfo.businessType;
    const typeConfig = businessTypeConfig[businessType];
    
    this.setData({
      businessTypeText: typeConfig ? typeConfig.name : '未知类型'
    });
  },

  /**
   * 检查支付环境
   */
  checkPaymentEnvironment() {
    if (!paymentStore.validatePaymentEnvironment()) {
      wx.showModal({
        title: '支付环境异常',
        content: '当前环境不支持支付功能，请在微信小程序中使用',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
    }
  },

  /**
   * 选择支付方式
   */
  onSelectPaymentMethod(e) {
    const method = e.currentTarget.dataset.method;
    const methodItem = this.data.paymentMethods.find(item => item.id === method);
    
    if (!methodItem || !methodItem.enabled) {
      wx.showToast({
        title: '该支付方式暂不可用',
        icon: 'none'
      });
      return;
    }

    this.setData({
      selectedMethod: method
    });

    this.updatePayButtonState();
  },

  /**
   * 选择优惠券
   */
  onSelectCoupon() {
    // 跳转到优惠券选择页面
    wx.navigateTo({
      url: '/pages/coupon/coupon-list?select=true'
    });
  },

  /**
   * 切换协议同意状态
   */
  onToggleAgreement() {
    this.setData({
      agreedToTerms: !this.data.agreedToTerms
    });

    this.updatePayButtonState();
  },

  /**
   * 查看协议
   */
  onViewAgreement(e) {
    const type = e.currentTarget.dataset.type;
    let url = '';
    
    switch (type) {
      case 'payment':
        url = '/pages/agreement/payment-agreement';
        break;
      case 'service':
        url = '/pages/agreement/service-agreement';
        break;
      default:
        return;
    }

    wx.navigateTo({ url });
  },

  /**
   * 更新支付按钮状态
   */
  updatePayButtonState() {
    const canPay = this.data.selectedMethod && 
                   this.data.agreedToTerms && 
                   parseFloat(this.data.finalAmount) > 0;
    
    this.setData({ canPay });
  },

  /**
   * 确认支付
   */
  async onConfirmPayment() {
    if (!this.data.canPay || this.data.isProcessing) {
      return;
    }

    this.setData({ 
      isProcessing: true,
      showLoading: true,
      loadingText: '创建订单中...'
    });

    try {
      // 构建支付数据
      const paymentData = {
        businessType: this.data.orderInfo.businessType,
        businessId: this.data.orderInfo.businessId,
        amount: this.data.finalAmount,
        description: this.data.orderInfo.description,
        paymentMethod: this.data.selectedMethod
      };

      // 创建支付订单
      const createResult = await paymentStore.createPayment(paymentData);

      if (!createResult.success) {
        throw new Error(createResult.message);
      }

      this.setData({
        loadingText: '启动支付...'
      });

      // 根据支付方式处理
      let paymentResult;
      switch (this.data.selectedMethod) {
        case 'wechat':
          paymentResult = await this.handleWechatPayment(createResult.data);
          break;
        case 'balance':
          paymentResult = await this.handleBalancePayment(createResult.data);
          break;
        default:
          throw new Error('不支持的支付方式');
      }

      // 显示支付结果
      this.showPaymentResult(paymentResult);

    } catch (error) {
      console.error('支付失败:', error);
      this.showPaymentResult({
        success: false,
        message: error.message || '支付失败，请重试'
      });
    } finally {
      this.setData({ 
        isProcessing: false,
        showLoading: false
      });
    }
  },

  /**
   * 处理微信支付
   */
  async handleWechatPayment(orderData) {
    try {
      const { payParams } = orderData;
      return await paymentStore.requestWechatPayment(payParams);
    } catch (error) {
      return {
        success: false,
        message: error.message || '微信支付失败'
      };
    }
  },

  /**
   * 处理余额支付
   */
  async handleBalancePayment(orderData) {
    // 余额支付通常在创建订单时就已经完成
    return {
      success: true,
      message: '余额支付成功'
    };
  },

  /**
   * 显示支付结果
   */
  showPaymentResult(result) {
    this.setData({
      showResultModal: true,
      paymentResult: result
    });
  },

  /**
   * 关闭结果弹窗
   */
  onCloseResultModal() {
    this.setData({
      showResultModal: false
    });
  },

  /**
   * 返回首页
   */
  onBackToHome() {
    if (this.data.paymentResult.success) {
      // 支付成功，返回首页或相关页面
      wx.switchTab({
        url: '/pages/index/index'
      });
    } else {
      // 支付失败，关闭弹窗重新支付
      this.onCloseResultModal();
    }
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    return {
      title: '易贴易找 - 便民服务平台',
      path: '/pages/index/index'
    };
  },

  /**
   * 页面分享到朋友圈
   */
  onShareTimeline() {
    return {
      title: '易贴易找 - 便民服务平台'
    };
  }
});
