const { request } = require('../utils/request');
const config = require('../config/api');

/**
 * 分享积分相关的数据请求和业务逻辑
 * 遵循小程序开发规范，将所有分享积分相关的接口调用封装在此store中
 */
class SharePointsStore {
  
  /**
   * 分享获得积分
   * @param {string} userId 用户ID
   * @param {string} shareType 分享类型（post-帖子，institution-机构，qrcode-二维码）
   * @param {number} businessId 业务ID
   * @param {string} shareChannel 分享渠道（wechat-微信，moments-朋友圈等）
   * @returns {Promise} 分享结果
   */
  static async shareForPoints(userId, shareType, businessId, shareChannel) {
    try {
      const response = await request({
        url: config.sharePoints.share,
        method: 'POST',
        data: {
          userId,
          shareType,
          businessId,
          shareChannel
        }
      });
      
      if (response.success) {
        // 分享成功，显示积分奖励提示
        if (response.data && response.data.success && response.data.points > 0) {
          wx.showToast({
            title: `分享成功，获得${response.data.points}积分`,
            icon: 'success',
            duration: 2000
          });
        }
        
        return response.data;
      } else {
        // 分享失败或无积分奖励
        if (response.msg) {
          wx.showToast({
            title: response.msg,
            icon: 'none',
            duration: 2000
          });
        }
        return response.data || { success: false, message: response.msg };
      }
    } catch (error) {
      console.error('分享获得积分失败:', error);
      wx.showToast({
        title: '分享失败，请重试',
        icon: 'none'
      });
      return { success: false, message: '网络错误' };
    }
  }

  /**
   * 检查是否可以分享获得积分
   * @param {string} userId 用户ID
   * @param {string} shareType 分享类型
   * @param {number} businessId 业务ID
   * @returns {Promise} 检查结果
   */
  static async canShareForPoints(userId, shareType, businessId) {
    try {
      const response = await request({
        url: config.sharePoints.canShare,
        method: 'GET',
        data: {
          userId,
          shareType,
          businessId
        }
      });
      
      return response.success ? response.data : { canShare: false, reason: response.msg };
    } catch (error) {
      console.error('检查分享权限失败:', error);
      return { canShare: false, reason: '网络错误' };
    }
  }

  /**
   * 获取分享积分记录
   * @param {string} userId 用户ID
   * @param {string} shareType 分享类型（可选）
   * @param {number} current 当前页
   * @param {number} size 每页大小
   * @returns {Promise} 分享积分记录
   */
  static async getSharePointsRecords(userId, shareType = null, current = 1, size = 10) {
    try {
      const params = {
        userId,
        current,
        size
      };
      
      if (shareType) {
        params.shareType = shareType;
      }
      
      const response = await request({
        url: config.sharePoints.records,
        method: 'GET',
        data: params
      });
      
      return response.success ? response.data : { records: [], total: 0 };
    } catch (error) {
      console.error('获取分享积分记录失败:', error);
      return { records: [], total: 0 };
    }
  }

  /**
   * 获取分享积分统计
   * @param {string} userId 用户ID
   * @returns {Promise} 分享积分统计
   */
  static async getSharePointsStats(userId) {
    try {
      const response = await request({
        url: config.sharePoints.stats,
        method: 'GET',
        data: { userId }
      });
      
      return response.success ? response.data : {};
    } catch (error) {
      console.error('获取分享积分统计失败:', error);
      return {};
    }
  }

  /**
   * 获取分享积分配置
   * @returns {Promise} 分享积分配置
   */
  static async getSharePointsConfig() {
    try {
      const response = await request({
        url: config.sharePoints.config,
        method: 'GET'
      });
      
      return response.success ? response.data : {};
    } catch (error) {
      console.error('获取分享积分配置失败:', error);
      return {};
    }
  }

  /**
   * 记录分享行为（不获得积分）
   * @param {string} userId 用户ID
   * @param {string} shareType 分享类型
   * @param {number} businessId 业务ID
   * @param {string} shareChannel 分享渠道
   * @returns {Promise} 记录结果
   */
  static async recordShareAction(userId, shareType, businessId, shareChannel) {
    try {
      const response = await request({
        url: config.sharePoints.record,
        method: 'POST',
        data: {
          userId,
          shareType,
          businessId,
          shareChannel
        }
      });
      
      return response.success;
    } catch (error) {
      console.error('记录分享行为失败:', error);
      return false;
    }
  }

  /**
   * 获取今日分享次数
   * @param {string} userId 用户ID
   * @param {string} shareType 分享类型（可选）
   * @returns {Promise} 今日分享次数
   */
  static async getTodayShareCount(userId, shareType = null) {
    try {
      const params = { userId };
      if (shareType) {
        params.shareType = shareType;
      }
      
      const response = await request({
        url: config.sharePoints.todayCount,
        method: 'GET',
        data: params
      });
      
      return response.success ? response.data : {};
    } catch (error) {
      console.error('获取今日分享次数失败:', error);
      return {};
    }
  }

  /**
   * 批量分享获得积分
   * @param {string} userId 用户ID
   * @param {Array} shareItems 分享项目列表
   * @returns {Promise} 批量分享结果
   */
  static async batchShareForPoints(userId, shareItems) {
    try {
      const response = await request({
        url: config.sharePoints.batchShare,
        method: 'POST',
        data: {
          userId,
          shareItems
        }
      });
      
      if (response.success && response.data) {
        const { success, totalPoints } = response.data;
        if (success > 0) {
          wx.showToast({
            title: `批量分享成功，共获得${totalPoints}积分`,
            icon: 'success',
            duration: 2000
          });
        }
      }
      
      return response.success ? response.data : { success: 0, failed: 0, totalPoints: 0 };
    } catch (error) {
      console.error('批量分享失败:', error);
      return { success: 0, failed: 0, totalPoints: 0 };
    }
  }

  /**
   * 触发分享（小程序原生分享）
   * @param {Object} shareOptions 分享选项
   * @param {string} userId 用户ID
   * @param {string} shareType 分享类型
   * @param {number} businessId 业务ID
   * @returns {Promise} 分享结果
   */
  static async triggerShare(shareOptions, userId, shareType, businessId) {
    return new Promise((resolve) => {
      wx.showShareMenu({
        withShareTicket: true,
        success: () => {
          // 监听分享成功
          wx.onShareAppMessage(() => {
            // 记录分享行为并尝试获得积分
            this.shareForPoints(userId, shareType, businessId, 'wechat').then(result => {
              resolve(result);
            });
            
            return {
              title: shareOptions.title || '分享内容',
              path: shareOptions.path || '/pages/index/index',
              imageUrl: shareOptions.imageUrl || ''
            };
          });
        },
        fail: (error) => {
          console.error('显示分享菜单失败:', error);
          resolve({ success: false, message: '分享失败' });
        }
      });
    });
  }

  /**
   * 分享到朋友圈
   * @param {Object} shareOptions 分享选项
   * @param {string} userId 用户ID
   * @param {string} shareType 分享类型
   * @param {number} businessId 业务ID
   * @returns {Promise} 分享结果
   */
  static async shareToMoments(shareOptions, userId, shareType, businessId) {
    return new Promise((resolve) => {
      wx.shareToWeRun({
        title: shareOptions.title || '分享内容',
        query: shareOptions.query || '',
        success: () => {
          // 分享成功，尝试获得积分
          this.shareForPoints(userId, shareType, businessId, 'moments').then(result => {
            resolve(result);
          });
        },
        fail: (error) => {
          console.error('分享到朋友圈失败:', error);
          resolve({ success: false, message: '分享失败' });
        }
      });
    });
  }

  /**
   * 复制链接分享
   * @param {string} link 分享链接
   * @param {string} userId 用户ID
   * @param {string} shareType 分享类型
   * @param {number} businessId 业务ID
   * @returns {Promise} 分享结果
   */
  static async copyLinkShare(link, userId, shareType, businessId) {
    return new Promise((resolve) => {
      wx.setClipboardData({
        data: link,
        success: () => {
          wx.showToast({
            title: '链接已复制',
            icon: 'success'
          });
          
          // 记录分享行为并尝试获得积分
          this.shareForPoints(userId, shareType, businessId, 'copy').then(result => {
            resolve(result);
          });
        },
        fail: (error) => {
          console.error('复制链接失败:', error);
          resolve({ success: false, message: '复制失败' });
        }
      });
    });
  }
}

module.exports = SharePointsStore;
