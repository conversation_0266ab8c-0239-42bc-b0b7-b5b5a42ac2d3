const { regionCache } = require('../../utils/regionCache');
const RegionManager = require('../../../utils/regionManager');

Component({
  properties: {
    // 是否显示弹窗
    show: {
      type: Boolean,
      value: false
    },
    // 选择层级 2-市 3-县（固定为3，只选择到区县）
    maxLevel: {
      type: Number,
      value: 3
    },
    // 默认选中的地区编码
    defaultValue: {
      type: String,
      value: ''
    }
  },

  data: {
    currentLevel: 2, // 从城市开始选择
    selectedRegions: [], // 已选择的地区路径
    regionData: {
      cities: [],
      districts: []
    },
    loading: false,
    tabs: [
      { level: 2, name: '城市', key: 'cities' },
      { level: 3, name: '区县', key: 'districts' }
    ],
    showAnim: false // 新增动画控制
  },

  lifetimes: {
    attached() {
      // 延迟加载，避免初始化时的错误
      setTimeout(() => {
        this.loadCities();
      }, 100);
    }
  },

  observers: {
    'show': function(show) {
      if (show) {
        if (this.data.defaultValue) {
          this.initDefaultValue();
        }
        // 动画：显示时短暂延迟后showAnim=true
        setTimeout(() => {
          this.setData({ showAnim: true });
        }, 10);
        // 通知页面隐藏底部导航栏
        this.triggerEvent('showchange', { show: true });
      } else {
        // 动画：隐藏时showAnim=false，延迟再触发showchange
        this.setData({ showAnim: false });
        setTimeout(() => {
          // 通知页面显示底部导航栏
          this.triggerEvent('showchange', { show: false });
        }, 300);
      }
    }
  },

  methods: {
    // 加载城市数据（所有开放的城市）
    async loadCities() {
      try {
        this.setData({ loading: true });

        const cities = await regionCache.getCities();

        this.setData({
          'regionData.cities': cities,
          loading: false
        });

        if (cities.length === 0) {
          wx.showToast({
            title: '暂无开放城市',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('加载城市数据失败:', error);
        this.setData({ loading: false });
        wx.showToast({
          title: '加载城市数据失败',
          icon: 'none'
        });
      }
    },



    // 加载区县数据
    async loadDistricts(cityCode) {
      try {
        this.setData({ loading: true });

        const districts = await regionCache.getDistricts(cityCode);

        this.setData({
          'regionData.districts': districts,
          loading: false
        });

        if (districts.length === 0) {
          wx.showToast({
            title: '该城市暂无开放区县',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('加载区县数据失败:', error);
        this.setData({ loading: false });
        wx.showToast({
          title: '加载区县数据失败',
          icon: 'none'
        });
      }
    },



    // 切换标签页
    switchTab(e) {
      const level = e.currentTarget.dataset.level;
      this.setData({ currentLevel: level });
    },

    // 选择地区
    async selectRegion(e) {
      const region = e.currentTarget.dataset.region;
      const level = region.level;

      // 更新选中的地区路径
      let selectedRegions = [...this.data.selectedRegions];

      if (level === 2) {
        // 选择城市
        selectedRegions[0] = region;
        selectedRegions = selectedRegions.slice(0, 1); // 清除区县选择
      } else if (level === 3) {
        // 选择区县
        selectedRegions[1] = region;
      }

      this.setData({ selectedRegions });

      // 如果选择的是城市，加载对应的区县
      if (level === 2 && region.hasChildren) {
        this.setData({ currentLevel: 3 });
        await this.loadDistricts(region.regionCode);
      } else if (level === 3) {
        // 选择区县后直接完成选择
        this.confirmSelection();
      }
    },

    // 确认选择
    confirmSelection() {
      const selectedRegions = this.data.selectedRegions;
      if (!selectedRegions[1]) {
        wx.showToast({
          title: '请选择区县',
          icon: 'none'
        });
        return;
      }
      const lastRegion = selectedRegions[selectedRegions.length - 1];

      if (!lastRegion) {
        wx.showToast({
          title: '请选择地区',
          icon: 'none'
        });
        return;
      }

      // 构建完整地址
      const fullAddress = selectedRegions.map(region => region.regionName).join('');

      // 保存用户选择的地区信息到本地存储
      const regionInfo = {
        regionCode: lastRegion.regionCode,
        regionName: lastRegion.regionName,
        level: lastRegion.level,
        selectedRegions: selectedRegions,
        fullAddress: fullAddress
      };

      RegionManager.saveUserRegion(regionInfo);
      console.log('用户选择地区已保存:', regionInfo);

      this.triggerEvent('confirm', {
        selectedRegions,
        lastRegion,
        fullAddress
      });

      this.close();
    },

    // 关闭弹窗
    close() {
      this.triggerEvent('close');
    },

    // 阻止冒泡
    preventBubble() {
      // 阻止事件冒泡
    },

    // 初始化默认值
    async initDefaultValue() {
      if (!this.data.defaultValue) return;

      try {
        const fullAddress = await regionCache.getFullAddress(this.data.defaultValue);
        if (fullAddress) {
          const selectedRegions = [];

          // 如果有城市信息
          if (fullAddress.cityCode) {
            selectedRegions.push({
              regionCode: fullAddress.cityCode,
              regionName: fullAddress.cityName,
              level: 2
            });
          }

          // 如果有区县信息
          if (fullAddress.districtCode) {
            selectedRegions.push({
              regionCode: fullAddress.districtCode,
              regionName: fullAddress.districtName,
              level: 3
            });
            // 加载对应城市的区县数据
            if (fullAddress.cityCode) {
              await this.loadDistricts(fullAddress.cityCode);
            }
          }

          this.setData({
            selectedRegions,
            currentLevel: selectedRegions.length > 0 ? (selectedRegions.length === 1 ? 3 : 2) : 2
          });
        }
      } catch (error) {
        console.error('加载默认地区信息失败:', error);
      }
    }
  }
});
