const { request } = require('../utils/request');

/**
 * 二维码生成相关的数据请求和业务逻辑
 * 遵循小程序开发规范，将所有二维码生成相关的接口调用封装在此store中
 */
class QRCodeStore {
  
  /**
   * 生成邀请二维码
   * @param {string} userId 用户ID
   * @param {string} inviteCode 邀请码
   * @returns {Promise} 二维码信息
   */
  static async generateInviteQRCode(userId, inviteCode) {
    try {
      wx.showLoading({
        title: '生成二维码中...',
        mask: true
      });

      const response = await request({
        url: '/blade-chat-open/qrcode/invite',
        method: 'POST',
        data: { userId, inviteCode }
      });

      wx.hideLoading();

      if (response.code === 200) {
        return response.data || {};
      } else {
        console.error('生成邀请二维码失败:', response.msg);
        wx.showToast({
          title: response.msg || '生成二维码失败',
          icon: 'none'
        });
        return { success: false };
      }
    } catch (error) {
      wx.hideLoading();
      console.error('生成邀请二维码异常:', error);
      wx.showToast({
        title: '生成二维码失败',
        icon: 'none'
      });
      return { success: false };
    }
  }

  /**
   * 生成分享积分二维码
   * @param {string} userId 用户ID
   * @param {string} shareType 分享类型
   * @returns {Promise} 二维码信息
   */
  static async generateSharePointsQRCode(userId, shareType) {
    try {
      wx.showLoading({
        title: '生成二维码中...',
        mask: true
      });

      const response = await request({
        url: '/blade-chat-open/qrcode/share-points',
        method: 'POST',
        data: { userId, shareType }
      });

      wx.hideLoading();

      if (response.code === 200) {
        return response.data || {};
      } else {
        console.error('生成分享积分二维码失败:', response.msg);
        wx.showToast({
          title: response.msg || '生成二维码失败',
          icon: 'none'
        });
        return { success: false };
      }
    } catch (error) {
      wx.hideLoading();
      console.error('生成分享积分二维码异常:', error);
      wx.showToast({
        title: '生成二维码失败',
        icon: 'none'
      });
      return { success: false };
    }
  }

  /**
   * 批量生成二维码
   * @param {string} userId 用户ID
   * @param {string} inviteCode 邀请码
   * @param {Array} shareTypes 分享类型列表
   * @returns {Promise} 批量生成结果
   */
  static async batchGenerateQRCode(userId, inviteCode, shareTypes) {
    try {
      wx.showLoading({
        title: '生成二维码中...',
        mask: true
      });

      const response = await request({
        url: '/blade-chat-open/qrcode/batch',
        method: 'POST',
        data: {
          userId,
          inviteCode,
          shareTypes: shareTypes || ['qrcode', 'post', 'institution']
        }
      });

      wx.hideLoading();

      if (response.code === 200) {
        return response.data || {};
      } else {
        console.error('批量生成二维码失败:', response.msg);
        wx.showToast({
          title: response.msg || '生成二维码失败',
          icon: 'none'
        });
        return { success: false };
      }
    } catch (error) {
      wx.hideLoading();
      console.error('批量生成二维码异常:', error);
      wx.showToast({
        title: '生成二维码失败',
        icon: 'none'
      });
      return { success: false };
    }
  }

  /**
   * 获取或生成二维码（带缓存）
   * @param {string} type 二维码类型
   * @param {string} userId 用户ID
   * @param {Object} params 参数
   * @returns {Promise} 二维码信息
   */
  static async getOrGenerateQRCode(type, userId, params = {}) {
    try {
      // 检查本地缓存
      const cacheKey = `qrcode_${type}_${userId}_${JSON.stringify(params)}`;
      const cachedQRCode = wx.getStorageSync(cacheKey);

      // 如果缓存存在且未过期（24小时）
      if (cachedQRCode && cachedQRCode.createTime) {
        const now = Date.now();
        const cacheTime = cachedQRCode.createTime;
        const expireTime = 24 * 60 * 60 * 1000; // 24小时

        if (now - cacheTime < expireTime) {
          console.log('使用缓存的二维码:', cacheKey);
          return cachedQRCode;
        }
      }

      // 生成新的二维码
      let result = {};
      switch (type) {
        case 'invite':
          result = await this.generateInviteQRCode(userId, params.inviteCode);
          break;
        case 'sharePoints':
          result = await this.generateSharePointsQRCode(userId, params.shareType);
          break;
        case 'batch':
          result = await this.batchGenerateQRCode(userId, params.inviteCode, params.shareTypes);
          break;
        default:
          throw new Error('未知的二维码类型: ' + type);
      }

      // 缓存结果
      if (result.success !== false) {
        result.createTime = Date.now();
        wx.setStorageSync(cacheKey, result);
      }

      return result;
    } catch (error) {
      console.error('获取或生成二维码失败:', error);
      return { success: false, message: error.message };
    }
  }

  /**
   * 清除二维码缓存
   * @param {string} type 二维码类型（可选）
   * @param {string} userId 用户ID（可选）
   */
  static clearQRCodeCache(type = null, userId = null) {
    try {
      const storage = wx.getStorageInfoSync();
      const keys = storage.keys;
      
      keys.forEach(key => {
        if (key.startsWith('qrcode_')) {
          // 如果指定了类型和用户ID，只清除匹配的缓存
          if (type && userId) {
            if (key.includes(`qrcode_${type}_${userId}`)) {
              wx.removeStorageSync(key);
            }
          } else if (type) {
            if (key.includes(`qrcode_${type}_`)) {
              wx.removeStorageSync(key);
            }
          } else {
            // 清除所有二维码缓存
            wx.removeStorageSync(key);
          }
        }
      });
      
      console.log('二维码缓存清除完成');
    } catch (error) {
      console.error('清除二维码缓存失败:', error);
    }
  }

  /**
   * 保存二维码到相册
   * @param {string} qrCodeUrl 二维码URL
   * @returns {Promise} 保存结果
   */
  static async saveQRCodeToAlbum(qrCodeUrl) {
    try {
      wx.showLoading({
        title: '保存中...',
        mask: true
      });

      // 下载二维码图片
      const downloadResult = await new Promise((resolve, reject) => {
        wx.downloadFile({
          url: qrCodeUrl,
          success: resolve,
          fail: reject
        });
      });

      // 保存到相册
      await new Promise((resolve, reject) => {
        wx.saveImageToPhotosAlbum({
          filePath: downloadResult.tempFilePath,
          success: resolve,
          fail: reject
        });
      });

      wx.hideLoading();
      wx.showToast({
        title: '保存成功',
        icon: 'success'
      });

      return { success: true };
    } catch (error) {
      wx.hideLoading();
      console.error('保存二维码失败:', error);
      
      if (error.errMsg && error.errMsg.includes('auth deny')) {
        wx.showModal({
          title: '提示',
          content: '需要您授权保存图片到相册',
          showCancel: false
        });
      } else {
        wx.showToast({
          title: '保存失败',
          icon: 'none'
        });
      }
      
      return { success: false, error: error.errMsg };
    }
  }
}

module.exports = QRCodeStore;
