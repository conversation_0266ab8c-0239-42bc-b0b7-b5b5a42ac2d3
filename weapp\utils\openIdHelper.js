/**
 * OpenID 辅助工具
 * 用于处理微信支付中的 OpenID 获取和验证
 */

const { request } = require('./request');

/**
 * 获取用户的OpenID
 * @returns {Promise<string>} OpenID
 */
const getOpenId = async () => {
  try {
    // 1. 先从本地存储获取
    let openId = wx.getStorageSync('openId');
    
    if (openId && openId !== 'test_openid') {
      console.log('从本地存储获取OpenID:', openId);
      return openId;
    }
    
    // 2. 从用户信息接口获取
    console.log('从服务器获取用户信息...');
    const response = await request({
      url: '/blade-chat/user/info',
      method: 'GET'
    });
    
    if (response.success && response.data && response.data.openId) {
      openId = response.data.openId;
      // 保存到本地存储
      wx.setStorageSync('openId', openId);
      console.log('从服务器获取OpenID:', openId);
      return openId;
    }
    
    // 3. 如果还是没有，提示用户重新登录
    throw new Error('无法获取OpenID，请重新登录');
    
  } catch (error) {
    console.error('获取OpenID失败:', error);
    throw error;
  }
};

/**
 * 验证OpenID是否有效
 * @param {string} openId 
 * @returns {boolean}
 */
const validateOpenId = (openId) => {
  if (!openId) {
    return false;
  }
  
  // 检查是否是测试用的无效openId
  if (openId === 'test_openid') {
    return false;
  }
  
  // 微信OpenID通常是28位字符串
  if (openId.length !== 28) {
    return false;
  }
  
  return true;
};

/**
 * 清除OpenID缓存
 */
const clearOpenId = () => {
  wx.removeStorageSync('openId');
  console.log('已清除OpenID缓存');
};

/**
 * 获取OpenID用于支付
 * 专门用于支付场景，包含完整的错误处理和用户提示
 * @returns {Promise<string>}
 */
const getOpenIdForPayment = async () => {
  try {
    const openId = await getOpenId();
    
    if (!validateOpenId(openId)) {
      throw new Error('OpenID无效，请重新登录');
    }
    
    return openId;
    
  } catch (error) {
    console.error('获取支付OpenID失败:', error);
    
    // 显示用户友好的错误提示
    wx.showModal({
      title: '登录状态异常',
      content: '无法获取用户信息，请重新登录后再试',
      showCancel: false,
      confirmText: '重新登录',
      success: (res) => {
        if (res.confirm) {
          // 清除所有登录相关的缓存
          const authKeys = ['token', 'refreshToken', 'userInfo', 'openId'];
          authKeys.forEach(key => {
            wx.removeStorageSync(key);
          });
          
          // 跳转到登录页面
          wx.reLaunch({
            url: '/pages/login/login'
          });
        }
      }
    });
    
    throw error;
  }
};

/**
 * 调试用：显示当前OpenID信息
 */
const debugOpenId = async () => {
  console.log('=== OpenID 调试信息 ===');
  
  // 本地存储的OpenID
  const localOpenId = wx.getStorageSync('openId');
  console.log('本地存储OpenID:', localOpenId);
  console.log('本地OpenID有效性:', validateOpenId(localOpenId));
  
  // 用户信息中的OpenID
  try {
    const userInfo = wx.getStorageSync('userInfo');
    console.log('用户信息:', userInfo);
    console.log('用户信息中的OpenID:', userInfo?.openId);
  } catch (error) {
    console.log('获取本地用户信息失败:', error);
  }
  
  // 服务器用户信息中的OpenID
  try {
    const response = await request({
      url: '/blade-chat/user/info',
      method: 'GET'
    });
    console.log('服务器用户信息:', response);
    console.log('服务器OpenID:', response?.data?.openId);
  } catch (error) {
    console.log('获取服务器用户信息失败:', error);
  }
  
  console.log('=== 调试信息结束 ===');
};

module.exports = {
  getOpenId,
  validateOpenId,
  clearOpenId,
  getOpenIdForPayment,
  debugOpenId
};
