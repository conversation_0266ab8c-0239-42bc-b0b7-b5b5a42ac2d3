/**
 * 支付服务类
 * 参考 WxJava 的 WxPayService 实现
 */

const { request } = require('../utils/request');
const WechatPayUtil = require('../utils/wechatPayUtil');
// 使用生产配置
const { generateOrderNo, validateAmount, yuanToFen } = require('../config/payConfigProduction');

class PaymentService {
  constructor() {
    this.wechatPayUtil = new WechatPayUtil();
  }

  /**
   * 创建支付订单
   * @param {Object} orderData 订单数据
   * @returns {Promise} 支付结果
   */
  async createPaymentOrder(orderData) {
    try {
      const {
        businessType,
        businessId,
        amount,
        description,
        openid,
        attach,
        timeExpire
      } = orderData;

      // 验证金额
      const totalFee = yuanToFen(amount);
      validateAmount(totalFee);

      // 生成订单号
      const outTradeNo = generateOrderNo();

      // 构建订单信息
      const orderInfo = {
        outTradeNo: outTradeNo,
        body: description,
        totalFee: totalFee,
        openid: openid,
        tradeType: 'JSAPI',
        attach: attach || JSON.stringify({ businessType, businessId }),
        timeExpire: timeExpire
      };

      // 调用统一下单接口
      const unifiedOrderResult = await this.unifiedOrder(orderInfo);

      if (unifiedOrderResult.success) {
        // 构建小程序支付参数
        const payParams = this.wechatPayUtil.buildMiniProgramPayParams(
          unifiedOrderResult.data.prepay_id
        );

        // 保存订单到数据库
        await this.saveOrderToDatabase({
          outTradeNo: outTradeNo,
          businessType: businessType,
          businessId: businessId,
          amount: amount,
          totalFee: totalFee,
          description: description,
          openid: openid,
          status: 'PENDING',
          prepayId: unifiedOrderResult.data.prepay_id,
          createdAt: new Date()
        });

        return {
          success: true,
          data: {
            orderNo: outTradeNo,
            payParams: payParams
          },
          message: '订单创建成功'
        };
      } else {
        throw new Error(unifiedOrderResult.message);
      }
    } catch (error) {
      console.error('创建支付订单失败:', error);
      return {
        success: false,
        message: error.message || '创建订单失败'
      };
    }
  }

  /**
   * 统一下单
   * @param {Object} orderInfo 订单信息
   * @returns {Promise} 统一下单结果
   */
  async unifiedOrder(orderInfo) {
    try {
      // 构建请求参数
      const params = this.wechatPayUtil.buildUnifiedOrderParams(orderInfo);
      const xmlData = this.wechatPayUtil.objectToXml(params);

      // 发送请求到微信支付API
      const response = await this.sendWechatPayRequest('/pay/unifiedorder', xmlData);

      if (response.return_code === 'SUCCESS' && response.result_code === 'SUCCESS') {
        return {
          success: true,
          data: response,
          message: '统一下单成功'
        };
      } else {
        return {
          success: false,
          message: response.err_code_des || response.return_msg || '统一下单失败'
        };
      }
    } catch (error) {
      console.error('统一下单失败:', error);
      return {
        success: false,
        message: error.message || '统一下单失败'
      };
    }
  }

  /**
   * 查询订单
   * @param {string} outTradeNo 商户订单号
   * @param {string} transactionId 微信订单号
   * @returns {Promise} 查询结果
   */
  async queryOrder(outTradeNo, transactionId) {
    try {
      const params = this.wechatPayUtil.buildOrderQueryParams(outTradeNo, transactionId);
      const xmlData = this.wechatPayUtil.objectToXml(params);

      const response = await this.sendWechatPayRequest('/pay/orderquery', xmlData);

      if (response.return_code === 'SUCCESS' && response.result_code === 'SUCCESS') {
        return {
          success: true,
          data: response,
          message: '查询成功'
        };
      } else {
        return {
          success: false,
          message: response.err_code_des || response.return_msg || '查询失败'
        };
      }
    } catch (error) {
      console.error('查询订单失败:', error);
      return {
        success: false,
        message: error.message || '查询订单失败'
      };
    }
  }

  /**
   * 关闭订单
   * @param {string} outTradeNo 商户订单号
   * @returns {Promise} 关闭结果
   */
  async closeOrder(outTradeNo) {
    try {
      const params = this.wechatPayUtil.buildCloseOrderParams(outTradeNo);
      const xmlData = this.wechatPayUtil.objectToXml(params);

      const response = await this.sendWechatPayRequest('/pay/closeorder', xmlData);

      if (response.return_code === 'SUCCESS' && response.result_code === 'SUCCESS') {
        // 更新数据库订单状态
        await this.updateOrderStatus(outTradeNo, 'CANCELLED');

        return {
          success: true,
          message: '订单关闭成功'
        };
      } else {
        return {
          success: false,
          message: response.err_code_des || response.return_msg || '关闭订单失败'
        };
      }
    } catch (error) {
      console.error('关闭订单失败:', error);
      return {
        success: false,
        message: error.message || '关闭订单失败'
      };
    }
  }

  /**
   * 申请退款
   * @param {Object} refundInfo 退款信息
   * @returns {Promise} 退款结果
   */
  async refund(refundInfo) {
    try {
      const params = this.wechatPayUtil.buildRefundParams(refundInfo);
      const xmlData = this.wechatPayUtil.objectToXml(params);

      // 退款接口需要证书
      const response = await this.sendWechatPayRequest('/secapi/pay/refund', xmlData, true);

      if (response.return_code === 'SUCCESS' && response.result_code === 'SUCCESS') {
        // 保存退款记录到数据库
        await this.saveRefundToDatabase({
          outTradeNo: refundInfo.outTradeNo,
          outRefundNo: refundInfo.outRefundNo,
          refundId: response.refund_id,
          totalFee: refundInfo.totalFee,
          refundFee: refundInfo.refundFee,
          status: 'PROCESSING',
          createdAt: new Date()
        });

        return {
          success: true,
          data: response,
          message: '退款申请成功'
        };
      } else {
        return {
          success: false,
          message: response.err_code_des || response.return_msg || '退款申请失败'
        };
      }
    } catch (error) {
      console.error('申请退款失败:', error);
      return {
        success: false,
        message: error.message || '申请退款失败'
      };
    }
  }

  /**
   * 处理支付回调
   * @param {string} xmlData 回调XML数据
   * @returns {Promise} 处理结果
   */
  async handlePaymentNotify(xmlData) {
    try {
      const result = this.wechatPayUtil.processNotifyData(xmlData);

      if (result.success) {
        const { data } = result;
        
        // 更新订单状态
        await this.updateOrderStatus(data.out_trade_no, 'PAID', {
          transactionId: data.transaction_id,
          paidAt: new Date(),
          totalFee: data.total_fee,
          cashFee: data.cash_fee
        });

        // 处理业务逻辑
        await this.handleBusinessLogic(data);

        return {
          success: true,
          response: this.wechatPayUtil.generateNotifyResponse(true, 'OK')
        };
      } else {
        console.error('支付回调处理失败:', result.message);
        return {
          success: false,
          response: this.wechatPayUtil.generateNotifyResponse(false, result.message)
        };
      }
    } catch (error) {
      console.error('处理支付回调异常:', error);
      return {
        success: false,
        response: this.wechatPayUtil.generateNotifyResponse(false, '处理异常')
      };
    }
  }

  /**
   * 发送微信支付请求
   * @param {string} endpoint 接口端点
   * @param {string} xmlData XML数据
   * @param {boolean} needCert 是否需要证书
   * @returns {Promise} 响应结果
   */
  async sendWechatPayRequest(endpoint, xmlData, needCert = false) {
    // 这里应该调用后端API，由后端处理与微信支付的通信
    const response = await request({
      url: `/api/payment/wechat${endpoint}`,
      method: 'POST',
      data: {
        xmlData: xmlData,
        needCert: needCert
      }
    });

    if (response.code === 200) {
      return this.wechatPayUtil.xmlToObject(response.data);
    } else {
      throw new Error(response.message || '请求失败');
    }
  }

  /**
   * 保存订单到数据库
   * @param {Object} orderData 订单数据
   */
  async saveOrderToDatabase(orderData) {
    return await request({
      url: '/api/payment/orders',
      method: 'POST',
      data: orderData
    });
  }

  /**
   * 更新订单状态
   * @param {string} outTradeNo 商户订单号
   * @param {string} status 订单状态
   * @param {Object} updateData 更新数据
   */
  async updateOrderStatus(outTradeNo, status, updateData = {}) {
    return await request({
      url: `/api/payment/orders/${outTradeNo}/status`,
      method: 'PUT',
      data: {
        status: status,
        ...updateData
      }
    });
  }

  /**
   * 保存退款记录到数据库
   * @param {Object} refundData 退款数据
   */
  async saveRefundToDatabase(refundData) {
    return await request({
      url: '/api/payment/refunds',
      method: 'POST',
      data: refundData
    });
  }

  /**
   * 处理业务逻辑
   * @param {Object} paymentData 支付数据
   */
  async handleBusinessLogic(paymentData) {
    try {
      const attach = JSON.parse(paymentData.attach || '{}');
      const { businessType, businessId } = attach;

      switch (businessType) {
        case 'post_promotion':
          await this.handlePostPromotion(businessId, paymentData);
          break;
        case 'vip_upgrade':
          await this.handleVipUpgrade(businessId, paymentData);
          break;
        case 'reward':
          await this.handleReward(businessId, paymentData);
          break;
        default:
          console.log('未知业务类型:', businessType);
      }
    } catch (error) {
      console.error('处理业务逻辑失败:', error);
    }
  }

  /**
   * 处理帖子推广
   */
  async handlePostPromotion(postId, paymentData) {
    // 实现帖子推广逻辑
    console.log('处理帖子推广:', postId, paymentData);
  }

  /**
   * 处理VIP升级
   */
  async handleVipUpgrade(userId, paymentData) {
    // 实现VIP升级逻辑
    console.log('处理VIP升级:', userId, paymentData);
  }

  /**
   * 处理打赏
   */
  async handleReward(targetId, paymentData) {
    // 实现打赏逻辑
    console.log('处理打赏:', targetId, paymentData);
  }
}

module.exports = PaymentService;
