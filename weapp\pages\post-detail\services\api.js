/**
 * API服务模块
 */

const { API_ENDPOINTS, HTTP_STATUS, BUSINESS_CODE } = require('../config/api');
const { MESSAGES } = require('../config/constants');
const { request } = require('../../../utils/request');

/**
 * 基础请求方法
 */
class ApiService {
  /**
   * 发送HTTP请求
   * @param {string} url 请求URL
   * @param {object} options 请求选项
   * @returns {Promise} 请求结果
   */
  static async request(url, options = {}) {
    const {
      method = 'GET',
      data = {},
      showLoading = false,
      loadingText = '加载中...'
    } = options;

    // 显示加载提示
    if (showLoading) {
      wx.showLoading({ title: loadingText, mask: true });
    }

    try {
      // 使用工具类的request方法替代原生wx.request
      const responseData = await request({
        url,
        method,
        data
      });

      // 隐藏加载提示
      if (showLoading) {
        wx.hideLoading();
      }

      return responseData;
    } catch (error) {
      // 隐藏加载提示
      if (showLoading) {
        wx.hideLoading();
      }

      // 统一错误处理
      console.error('API请求失败:', error);
      throw error;
    }
  }

  /**
   * GET请求
   */
  static get(url, params = {}, options = {}) {
    const queryString = Object.keys(params)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&');
    
    const fullUrl = queryString ? `${url}?${queryString}` : url;
    
    return this.request(fullUrl, {
      method: 'GET',
      ...options
    });
  }

  /**
   * POST请求
   */
  static post(url, data = {}, options = {}) {
    return this.request(url, {
      method: 'POST',
      data,
      ...options
    });
  }

  /**
   * PUT请求
   */
  static put(url, data = {}, options = {}) {
    return this.request(url, {
      method: 'PUT',
      data,
      ...options
    });
  }

  /**
   * DELETE请求
   */
  static delete(url, data = {}, options = {}) {
    return this.request(url, {
      method: 'DELETE',
      data,
      ...options
    });
  }
}

/**
 * 帖子相关API
 */
class PostApi {
  /**
   * 获取帖子详情
   * @param {string} postId 帖子ID
   * @returns {Promise} 帖子详情
   */
  static getDetail(postId) {
    return ApiService.get(API_ENDPOINTS.post.detail, { id: postId }, {
      showLoading: true,
      loadingText: MESSAGES.LOADING.POST_DETAIL
    });
  }

  /**
   * 点赞/取消点赞帖子
   * @param {string} postId 帖子ID
   * @param {boolean} isLike 是否点赞
   * @returns {Promise} 操作结果
   */
  static toggleLike(postId, isLike) {
    return ApiService.post(API_ENDPOINTS.post.like, {
      postId,
      isLike
    });
  }

  /**
   * 收藏/取消收藏帖子
   * @param {string} postId 帖子ID
   * @param {boolean} isFavorite 是否收藏
   * @returns {Promise} 操作结果
   */
  static toggleFavorite(postId, isFavorite) {
    return ApiService.post(API_ENDPOINTS.post.favorite, {
      postId,
      isFavorite
    });
  }

  /**
   * 分享帖子
   * @param {string} postId 帖子ID
   * @returns {Promise} 分享结果
   */
  static share(postId) {
    return ApiService.post(API_ENDPOINTS.post.share, { postId });
  }
}

/**
 * 反馈相关API
 */
class FeedbackApi {
  /**
   * 获取反馈列表
   * @param {object} params 查询参数
   * @returns {Promise} 反馈列表
   */
  static getList(params) {
    return ApiService.get(API_ENDPOINTS.feedback.page, params, {
      showLoading: true,
      loadingText: MESSAGES.LOADING.FEEDBACK_LIST
    });
  }

  /**
   * 提交反馈
   * @param {object} feedbackData 反馈数据
   * @returns {Promise} 提交结果
   */
  static submit(feedbackData) {
    return ApiService.post(API_ENDPOINTS.feedback.submit, feedbackData, {
      showLoading: true,
      loadingText: MESSAGES.LOADING.SUBMITTING
    });
  }

  /**
   * 点赞反馈（使用评论点赞接口）
   * @param {string} feedbackId 反馈ID
   * @returns {Promise} 点赞结果
   */
  static like(feedbackId) {
    return ApiService.post(API_ENDPOINTS.feedback.like, { commentId: feedbackId });
  }

  /**
   * 获取反馈标签
   * @param {string} category 分类
   * @returns {Promise} 标签列表
   */
  static getTags(category) {
    return ApiService.get(API_ENDPOINTS.feedback.tags, { category });
  }

  /**
   * 获取热门标签
   * @returns {Promise} 热门标签列表
   */
  static getHotTags() {
    return ApiService.get(API_ENDPOINTS.feedback.hotTags);
  }
}

/**
 * 评论相关API
 */
class CommentApi {
  /**
   * 获取评论列表
   * @param {object} params 查询参数
   * @returns {Promise} 评论列表
   */
  static getList(params) {
    return ApiService.get(API_ENDPOINTS.comment.list, params);
  }

  /**
   * 获取回复列表
   * @param {string} commentId 评论ID
   * @returns {Promise} 回复列表
   */
  static getReplies(commentId) {
    return ApiService.get(API_ENDPOINTS.comment.replies, { commentId }, {
      showLoading: true,
      loadingText: MESSAGES.LOADING.REPLY_LIST
    });
  }

  /**
   * 添加评论
   * @param {object} commentData 评论数据
   * @returns {Promise} 添加结果
   */
  static add(commentData) {
    return ApiService.post(API_ENDPOINTS.comment.add, commentData, {
      showLoading: true,
      loadingText: MESSAGES.LOADING.SUBMITTING
    });
  }

  /**
   * 回复评论
   * @param {object} replyData 回复数据
   * @returns {Promise} 回复结果
   */
  static reply(replyData) {
    return ApiService.post(API_ENDPOINTS.comment.reply, replyData, {
      showLoading: true,
      loadingText: MESSAGES.LOADING.SUBMITTING
    });
  }

  /**
   * 点赞评论
   * @param {string} commentId 评论ID
   * @returns {Promise} 点赞结果
   */
  static like(commentId) {
    return ApiService.post(API_ENDPOINTS.comment.like, { commentId });
  }

  /**
   * 删除评论
   * @param {string} commentId 评论ID
   * @returns {Promise} 删除结果
   */
  static remove(commentId) {
    return ApiService.delete(API_ENDPOINTS.comment.remove, { commentId });
  }
}

module.exports = {
  ApiService,
  PostApi,
  FeedbackApi,
  CommentApi
};
