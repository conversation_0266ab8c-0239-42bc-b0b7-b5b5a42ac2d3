const FileUploadStore = require('../stores/fileUploadStore');
const config = require('../config/api');

/**
 * 文件上传工具类 - 小程序版本
 * 遵循小程序开发规范，封装文件上传相关的业务逻辑
 */
class UploadHelper {
  constructor() {
    this.baseUrl = config.baseUrl;
  }

  /**
   * 上传单个文件到服务器
   * @param {string} filePath 文件路径
   * @param {string} uploadSource 上传来源
   * @param {string} businessType 业务类型
   * @param {string|number} businessId 业务ID
   * @returns {Promise}
   */
  async uploadFile(filePath, uploadSource = 'miniapp', businessType = null, businessId = null) {
    // 直接使用FileUploadStore中的方法
    return await FileUploadStore.uploadFile(filePath, uploadSource, businessType, businessId);
  }

  /**
   * 批量上传文件到服务器
   * @param {Array} filePaths 文件路径数组
   * @param {string} uploadSource 上传来源
   * @param {string} businessType 业务类型
   * @param {string|number} businessId 业务ID
   * @returns {Promise}
   */
  async uploadFiles(filePaths, uploadSource = 'miniapp', businessType = null, businessId = null) {
    // 直接使用FileUploadStore中的方法
    return await FileUploadStore.uploadFiles(filePaths, uploadSource, businessType, businessId);
  }

  /**
   * 选择并上传图片
   * @param {number} count 选择数量
   * @param {string} uploadSource 上传来源
   * @param {string} businessType 业务类型
   * @param {string|number} businessId 业务ID
   * @returns {Promise}
   */
  async chooseAndUploadImages(count = 9, uploadSource = 'miniapp', businessType = null, businessId = null) {
    return new Promise((resolve, reject) => {
      wx.chooseMedia({
        count: count,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        success: async (res) => {
          try {
            const filePaths = res.tempFiles.map(file => file.tempFilePath);
            const uploadResult = await this.uploadFiles(filePaths, uploadSource, businessType, businessId);
            resolve(uploadResult);
          } catch (error) {
            resolve({
              success: false,
              message: '图片上传失败'
            });
          }
        },
        fail: (error) => {
          console.error('选择图片失败:', error);
          resolve({
            success: false,
            message: '选择图片失败'
          });
        }
      });
    });
  }

  /**
   * 选择并上传视频
   * @param {string} uploadSource 上传来源
   * @param {string} businessType 业务类型
   * @param {string|number} businessId 业务ID
   * @returns {Promise}
   */
  async chooseAndUploadVideo(uploadSource = 'miniapp', businessType = null, businessId = null) {
    return new Promise((resolve, reject) => {
      wx.chooseMedia({
        mediaType: ['video'],
        sourceType: ['album', 'camera'],
        maxDuration: 60,
        camera: 'back',
        success: async (res) => {
          try {
            const uploadResult = await this.uploadFile(res.tempFiles[0].tempFilePath, uploadSource, businessType, businessId);
            resolve(uploadResult);
          } catch (error) {
            resolve({
              success: false,
              message: '视频上传失败'
            });
          }
        },
        fail: (error) => {
          console.error('选择视频失败:', error);
          resolve({
            success: false,
            message: '选择视频失败'
          });
        }
      });
    });
  }

  /**
   * 预览文件
   * @param {string} url 文件URL
   * @param {string} fileType 文件类型
   */
  previewFile(url, fileType = 'image') {
    if (fileType === 'image') {
      wx.previewImage({
        urls: [url],
        current: url
      });
    } else if (fileType === 'video') {
      // 视频预览
      wx.navigateTo({
        url: `/pages/video-preview/video-preview?url=${encodeURIComponent(url)}`
      });
    } else {
      // 其他文件类型，复制链接
      wx.setClipboardData({
        data: url,
        success: () => {
          wx.showToast({
            title: '链接已复制',
            icon: 'success'
          });
        }
      });
    }
  }

  /**
   * 格式化文件大小
   * @param {number} bytes 字节数
   * @returns {string}
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 获取文件类型图标
   * @param {string} fileCategory 文件分类
   * @returns {string}
   */
  getFileIcon(fileCategory) {
    const iconMap = {
      'image': '/assets/images/file-icons/image.png',
      'document': '/assets/images/file-icons/document.png',
      'video': '/assets/images/file-icons/video.png',
      'audio': '/assets/images/file-icons/audio.png',
      'text': '/assets/images/file-icons/text.png',
      'other': '/assets/images/file-icons/other.png'
    };
    return iconMap[fileCategory] || iconMap['other'];
  }

  /**
   * 获取文件访问URL
   * @param {string} fileId 文件ID
   * @returns {Promise}
   */
  async getFileUrl(fileId) {
    return await FileUploadStore.getFileUrl(fileId);
  }

  /**
   * 删除文件
   * @param {string|Array} ids 文件ID或ID数组
   * @returns {Promise}
   */
  async removeFile(ids) {
    return await FileUploadStore.removeFile(ids);
  }

  /**
   * 获取文件列表
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  async getFileList(params = {}) {
    return await FileUploadStore.getFileList(params);
  }

  /**
   * 根据业务类型获取文件列表
   * @param {string} businessType 业务类型
   * @param {string|number} businessId 业务ID
   * @returns {Promise}
   */
  async getFilesByBusiness(businessType, businessId) {
    return await FileUploadStore.getFilesByBusiness(businessType, businessId);
  }
}

// 创建单例实例
const uploadHelper = new UploadHelper();

module.exports = uploadHelper; 