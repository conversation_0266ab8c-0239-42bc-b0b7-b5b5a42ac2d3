/* 我的收藏页面样式 */
.collections-page {
  min-height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
  padding-top: env(safe-area-inset-top);
}

/* 页面头部 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
  z-index: 100;
  min-height: 88rpx;
}

.header-back {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.05);
}

.back-icon {
  width: 32rpx;
  height: 32rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
  flex: 1;
  line-height: 1.2;
}

.header-action {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 107, 107, 0.1);
}

.clear-text {
  font-size: 28rpx;
  color: #ff6b6b;
  font-weight: 500;
}

/* 标签筛选 */
.tags-filter {
  display: flex;
  align-items: center;
  background: #fff;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
  z-index: 99;
}

.tags-scroll {
  overflow: hidden;
  white-space: nowrap;
  padding: 0 0 0 32rpx;
  display: flex;
  align-items: center;
}

.tag-item {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 32rpx;
  margin-right: 20rpx;
  background: #f5f5f5;
  color: #666;
  font-size: 28rpx;
  border-radius: 32rpx;
  transition: all 0.3s ease;
  white-space: nowrap;
  min-width: 80rpx;
  height: 64rpx;
  box-sizing: border-box;
}

.tag-item.active {
  background: linear-gradient(135deg, #ff6b6b 0%, #ff8a8a 100%);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
}

.tag-action {
  width: 155rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  /* background: rgba(255, 107, 107, 0.1); */
}

/* 收藏列表滚动区域 */
.collections-scroll {
  flex: 1;
  padding: 0;
  background: #f8f9fa;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 120rpx 0;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #ff6b6b;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner.small {
  width: 40rpx;
  height: 40rpx;
  border-width: 4rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #666;
  font-size: 28rpx;
}

/* 帖子列表 */
.posts-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  padding: 24rpx;
}

.post-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  gap: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  position: relative;
  border: 1rpx solid #f0f0f0;
}

.post-item:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

/* 帖子图片 */
.post-image-container {
  position: relative;
  width: 160rpx;
  height: 120rpx;
  flex-shrink: 0;
}

.post-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  background: #f5f5f5;
}

.post-category {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  background: rgba(255, 107, 107, 0.9);
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  backdrop-filter: blur(4rpx);
}

/* 帖子信息 */
.post-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.post-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  word-break: break-all;
}

.post-meta {
  display: flex;
  gap: 24rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.meta-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.6;
}

.meta-text {
  font-size: 24rpx;
  color: #999;
}

/* 操作按钮 */
.post-actions {
  display: flex;
  align-items: center;
  justify-content: center;
}

.unfavorite-btn {
  padding: 12rpx 20rpx;
  background: #fff;
  border: 1rpx solid #ff6b6b;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #ff6b6b;
  transition: all 0.3s ease;
  min-width: 120rpx;
  text-align: center;
}

.unfavorite-btn:active {
  background: #ff6b6b;
  color: #fff;
  transform: scale(0.95);
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 160rpx 60rpx;
  text-align: center;
  min-height: 60vh;
  background: #fff;
  margin: 24rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.empty-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 40rpx;
  opacity: 0.7;
  filter: grayscale(0.3);
}

.empty-title {
  font-size: 36rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 600;
}

.empty-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 60rpx;
  max-width: 400rpx;
}

.empty-action {
  padding: 24rpx 48rpx;
  background: linear-gradient(135deg, #ff6b6b 0%, #ff8a8a 100%);
  color: #fff;
  border-radius: 32rpx;
  transition: all 0.3s ease;
  font-size: 30rpx;
  font-weight: 500;
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 107, 0.3);
}

.empty-action:active {
  background: linear-gradient(135deg, #e55555 0%, #e66666 100%);
  transform: scale(0.95);
  box-shadow: 0 4rpx 16rpx rgba(255, 107, 107, 0.4);
}

.action-text {
  font-size: 28rpx;
  font-weight: 500;
}

/* 加载更多 */
.load-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0 60rpx 0;
}

.loading-more {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.load-more-text {
  font-size: 28rpx;
  color: #666;
}

/* 没有更多 */
.no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0 60rpx 0;
}

.no-more-text {
  font-size: 28rpx;
  color: #999;
  position: relative;
}

.no-more-text::before,
.no-more-text::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 60rpx;
  height: 1rpx;
  background: #e0e0e0;
}

.no-more-text::before {
  left: -80rpx;
}

.no-more-text::after {
  right: -80rpx;
}
