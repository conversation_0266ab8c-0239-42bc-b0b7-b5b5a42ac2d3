<!-- 筛选功能测试页面 -->
<view class="test-container">
  <view class="test-header">
    <text class="test-title">筛选功能测试</text>
  </view>

  <!-- 筛选栏预览 -->
  <view class="filter-preview">
    <text class="preview-title">筛选栏效果预览</text>
    <view class="filter-bar">
      <scroll-view class="filter-scroll" scroll-x="true">
        <view class="filter-item active">
          附近
        </view>
        <view class="filter-item">
          最新
        </view>
      </scroll-view>
      <view class="filter-more">
        <image class="filter-icon" src="/assets/images/common/filter-color-main.png" mode="aspectFit"/>
      </view>
    </view>
  </view>

  <!-- 功能说明 -->
  <view class="feature-description">
    <text class="desc-title">功能说明</text>
    <view class="desc-list">
      <view class="desc-item">
        <text class="desc-label">附近：</text>
        <text class="desc-text">显示用户附近10公里范围内的名片，按距离排序</text>
      </view>
      <view class="desc-item">
        <text class="desc-label">最新：</text>
        <text class="desc-text">显示最新发布的名片，按创建时间倒序排列</text>
      </view>
      <view class="desc-item">
        <text class="desc-label">默认选中：</text>
        <text class="desc-text">页面加载时默认选中"附近"筛选</text>
      </view>
    </view>
  </view>

  <!-- 修改内容 -->
  <view class="changes-summary">
    <text class="summary-title">本次修改内容</text>
    <view class="change-list">
      <view class="change-item">
        <text class="change-type">移除</text>
        <text class="change-desc">移除了"全部"和"热门"筛选选项</text>
      </view>
      <view class="change-item">
        <text class="change-type">保留</text>
        <text class="change-desc">保留了"附近"和"最新"两个筛选选项</text>
      </view>
      <view class="change-item">
        <text class="change-type">调整</text>
        <text class="change-desc">默认选中"附近"筛选，自动获取用户位置</text>
      </view>
      <view class="change-item">
        <text class="change-type">优化</text>
        <text class="change-desc">添加了"最新"筛选的排序逻辑</text>
      </view>
    </view>
  </view>
</view>
