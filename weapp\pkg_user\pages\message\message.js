// 消息通知页面
Page({
  data: {
    tabs: [
      {
        type: 'system',
        label: '系统通知',
        icon: '/assets/images/mine/service.png', // 替换为主色icon
        hasDot: true
      },
      {
        type: 'service',
        label: '服务消息',
        icon: '/assets/images/mine/service.png',
        hasDot: true
      },
      {
        type: 'activity',
        label: '活动动态',
        icon: '/assets/images/mine/service.png',
        hasDot: true
      },
      {
        type: 'notice',
        label: '公告',
        icon: '/assets/images/mine/service.png',
        hasDot: false
      }
    ],
    currentTab: 'system',
    msgList: [
      {
        id: 1,
        type: 'chat',
        avatar: '/assets/images/user.png', // 替换为实际头像
        name: '光头强',
        time: '07/02',
        content: '华景苑楼梯房五楼带阁楼顶复两层只要...',
      },
      {
        id: 2,
        type: 'chat',
        avatar: '/assets/images/user.png', // 替换为实际头像
        name: '光头强',
        time: '07/02',
        content: '华景苑楼梯房五楼带阁楼顶复两层只要...',
      },
      {
        id: 3,
        type: 'chat',
        avatar: '/assets/images/user.png', // 替换为实际头像
        name: '光头强',
        time: '07/02',
        content: '华景苑楼梯房五楼带阁楼顶复两层只要...',
      },
      {
        id: 4,
        type: 'chat',
        avatar: '/assets/images/user.png', // 替换为实际头像
        name: '光头强',
        time: '07/02',
        content: '华景苑楼梯房五楼带阁楼顶复两层只要...',
      },
      {
        id: 5,
        type: 'chat',
        avatar: '/assets/images/user.png',
        name: '李明',
        time: '07/01',
        content: '您好，请问房子还在吗？',
      }
    ]
  },

  onTabDetail() {
    wx.navigateTo({
      url: '/pages/follow/detail/setting/setting'
    })
  },

  toDetail(){
    wx.navigateTo({
      url: '/pages/follow/detail/message/message'
    })
  }
}) 