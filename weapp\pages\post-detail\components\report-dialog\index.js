Component({
  properties: {
    visible: { type: Boolean, value: false },
    reason: { type: String, value: '' },
    loading: { type: Boolean, value: false }
  },
  data: {
    content: '',
    images: []
  },
  methods: {
    onContentInput(e) {
      this.setData({ content: e.detail.value });
    },

    onImageAdd() {
      const { images } = this.data;
      if (images.length >= 3) {
        wx.showToast({ title: '最多上传3张图片', icon: 'none' });
        return;
      }

      wx.chooseMedia({
        count: 3 - images.length,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          // 验证选择的图片
          const validImages = [];
          const invalidImages = [];
          
          res.tempFiles.forEach(file => {
            const FileValidator = require('../../../../utils/fileValidator.js');
            const validation = FileValidator.validateImage(file.tempFilePath, file.size);
            
            if (validation.isValid) {
              validImages.push({
                path: file.tempFilePath,
                size: file.size,
                type: file.type,
                isTemp: true,
                name: FileValidator.generateSafeFileName(file.tempFilePath)
              });
            } else {
              invalidImages.push({
                path: file.tempFilePath,
                errors: validation.errors
              });
            }
          });
          
          // 显示验证错误
          if (invalidImages.length > 0) {
            const errorMessage = invalidImages[0].errors[0];
            wx.showToast({
              title: errorMessage,
              icon: 'none',
              duration: 3000
            });
          }
          
          // 添加有效图片
          if (validImages.length > 0) {
            const updatedImages = [...images, ...validImages];
            this.setData({ images: updatedImages });
            // 显示成功提示
            if (validImages.length > 0) {
              wx.showToast({
                title: `已选择${validImages.length}张图片`,
                icon: 'success',
                duration: 1500
              });
            }
          }
        },
        fail: (error) => {
          console.error('选择图片失败:', error);
          wx.showToast({
            title: '选择图片失败',
            icon: 'none'
          });
        }
      });
    },

    onImageRemove(e) {
      const { index } = e.currentTarget.dataset;
      const { images } = this.data;
      const newImages = images.filter((_, i) => i !== index);
      this.setData({ images: newImages });
    },

    onSubmitTap() {
      if (!this.data.content.trim()) {
        wx.showToast({ title: '请输入举报内容', icon: 'none' });
        return;
      }

      this.triggerEvent('submit', {
        content: this.data.content,
        images: this.data.images.map(img => img.path || img)
      });
    },

    onCancelTap() {
      this.triggerEvent('cancel');
      this.resetForm();
    },

    resetForm() {
      this.setData({
        content: '',
        images: []
      });
    }
  },

  observers: {
    'visible': function(visible) {
      if (!visible) {
        this.resetForm();
      }else{
      }
    }
  }
}); 