<view wx:if="{{show}}">
  <view class="region-picker-mask" bindtap="close"></view>
  <view class="region-picker-popup {{showAnim ? 'show' : ''}}" catchtap="preventBubble">
    <!-- 标题栏 -->
    <view class="region-picker-header">
      <view class="header-title">选择地区</view>
      <view class="header-close" bindtap="close">
        <text class="iconfont icon-close"></text>
      </view>
    </view>
    <!-- 标签页 -->
    <view class="region-picker-tabs">
      <view
        wx:for="{{tabs}}"
        wx:key="level"
        class="tab-item {{currentLevel === item.level ? 'active' : ''}}"
        data-level="{{item.level}}"
        bindtap="switchTab">
        <text class="tab-name">{{selectedRegions[item.level - 2] ? selectedRegions[item.level - 2].regionName : item.name}}</text>
        <text wx:if="{{selectedRegions[item.level - 2]}}" class="tab-selected">✓</text>
      </view>
    </view>
    <!-- 内容区域 -->
    <view class="region-picker-content">
      <!-- 加载状态 -->
      <view wx:if="{{loading}}" class="loading-container">
        <text class="loading-text">加载中...</text>
      </view>
      <!-- 城市列表 -->
      <scroll-view
        wx:if="{{currentLevel === 2 && !loading}}"
        class="region-list"
        scroll-y="true">
        <view
          wx:for="{{regionData.cities}}"
          wx:key="regionCode"
          class="region-item {{selectedRegions[0] && selectedRegions[0].regionCode === item.regionCode ? 'selected' : ''}}"
          data-region="{{item}}"
          bindtap="selectRegion">
          <text class="region-name">{{item.regionName}}</text>
          <text wx:if="{{selectedRegions[0] && selectedRegions[0].regionCode === item.regionCode}}" class="region-selected">✓</text>
        </view>
      </scroll-view>
      <!-- 区县列表 -->
      <scroll-view
        wx:if="{{currentLevel === 3 && !loading}}"
        class="region-list"
        scroll-y="true">
        <view
          wx:for="{{regionData.districts}}"
          wx:key="regionCode"
          class="region-item {{selectedRegions[1] && selectedRegions[1].regionCode === item.regionCode ? 'selected' : ''}}"
          data-region="{{item}}"
          bindtap="selectRegion">
          <text class="region-name">{{item.regionName}}</text>
          <text wx:if="{{selectedRegions[1] && selectedRegions[1].regionCode === item.regionCode}}" class="region-selected">✓</text>
        </view>
      </scroll-view>
      <!-- 空状态 - 城市 -->
      <view wx:if="{{!loading && currentLevel == 2 && regionData.cities.length == 0}}" class="empty-container">
        <text class="empty-text">暂无开放城市</text>
      </view>
      <!-- 空状态 - 区县 -->
      <view wx:if="{{!loading && currentLevel == 3 && regionData.districts.length == 0}}" class="empty-container">
        <text class="empty-text">该城市暂无开放区县</text>
      </view>
    </view>
    <!-- 底部按钮 -->
    <view class="region-picker-footer">
      <button class="confirm-btn" bindtap="confirmSelection" disabled="{{!selectedRegions[1]}}">确认选择</button>
    </view>
  </view>
</view>
