# API统一优化说明

## 优化目标
统一使用 `getNearbyPosts` 方法进行所有帖子查询，去掉 `getPostsInRegion` 方法，简化API调用逻辑。

## 主要变更

### 1. 删除 `getPostsInRegion` 方法
- 从 `mapApi.js` 中完全移除 `getPostsInRegion` 方法
- 统一使用 `getNearbyPosts` 方法处理所有查询场景

### 2. 统一API参数格式
所有 `getNearbyPosts` 调用现在使用统一的参数格式：

```javascript
const params = {
  latitude: centerLat,           // 用户位置纬度
  longitude: centerLng,          // 用户位置经度
  searchLatitude: centerLat,     // 搜索中心纬度
  searchLongitude: centerLng,    // 搜索中心经度
  scope: Math.round(radius / 1000), // 搜索范围（公里，四舍五入）
  current: 1,                    // 当前页码
  size: 100,                     // 每页大小
  category: this.data.category,  // 分类ID
  keyword: this.data.keyword     // 搜索关键词
};
```

### 3. 范围参数优化
- **新增 `scope` 参数**：以公里为单位的搜索范围
- **保留 `radius` 参数**：兼容旧版本，以米为单位
- **自动转换**：`scope = Math.round(radius / 1000)`

### 4. API方法更新

#### 更新前
```javascript
// 使用两个不同的方法
await MapApi.getNearbyPosts(params);     // 附近帖子
await MapApi.getPostsInRegion(params);   // 区域帖子
```

#### 更新后
```javascript
// 统一使用一个方法
await MapApi.getNearbyPosts(params);     // 所有场景
```

## 代码变更详情

### 1. `mapApi.js` 变更

#### 删除的方法
```javascript
// 已删除
static async getPostsInRegion(params = {}) {
  // ... 区域查询逻辑
}
```

#### 更新的方法
```javascript
static async getNearbyPosts(params = {}) {
  try {
    const response = await request({
      url: '/blade-chat-open/post/home-list',
      method: 'GET',
      data: {
        latitude: params.latitude,
        longitude: params.longitude,
        searchLatitude: params.searchLatitude,     // 新增
        searchLongitude: params.searchLongitude,   // 新增
        scope: params.scope || Math.round((params.radius || 5000) / 1000), // 新增
        current: params.current || 1,
        size: params.size || 50,
        category: params.category || '',           // 新增
        keyword: params.keyword || '',
        sortType: 'distance'
      }
    });
    return response;
  } catch (error) {
    console.error('获取附近帖子列表失败:', error);
    throw error;
  }
}
```

### 2. `map.js` 变更

#### 统一参数构建
所有调用 `getNearbyPosts` 的地方都使用相同的参数格式：

```javascript
// loadNearbyPosts 方法
const params = {
  latitude: searchLat,
  longitude: searchLng,
  searchLatitude: searchLat,
  searchLongitude: searchLng,
  scope: Math.round(radius / 1000),
  current: 1,
  size: 100,
  category: this.data.category,
  keyword: this.data.keyword
};

// loadPostsInRegion 方法（现在也使用 getNearbyPosts）
const params = {
  latitude: region.centerLocation.latitude,
  longitude: region.centerLocation.longitude,
  searchLatitude: region.centerLocation.latitude,
  searchLongitude: region.centerLocation.longitude,
  scope: Math.round(radius / 1000),
  current: 1,
  size: 100,
  category: this.data.category,
  keyword: this.data.keyword
};
```

#### 清理无用代码
- 删除了 `bounds` 计算逻辑（不再需要）
- 移除了复杂的区域边界参数

## 优化效果

### 1. 代码简化
- ✅ 减少了一个API方法
- ✅ 统一了参数格式
- ✅ 简化了调用逻辑
- ✅ 减少了代码重复

### 2. 参数优化
- ✅ 使用公里单位更直观（`scope`）
- ✅ 保持向后兼容（`radius`）
- ✅ 自动单位转换
- ✅ 参数命名更清晰

### 3. 维护性提升
- ✅ 只需维护一个查询方法
- ✅ 参数格式统一
- ✅ 错误处理统一
- ✅ 日志记录统一

### 4. 性能优化
- ✅ 减少了API端点数量
- ✅ 统一的缓存策略
- ✅ 简化的请求逻辑
- ✅ 更好的错误恢复

## 使用示例

### 基本查询
```javascript
const params = {
  latitude: 39.9042,
  longitude: 116.4074,
  searchLatitude: 39.9042,
  searchLongitude: 116.4074,
  scope: 5, // 5公里范围
  current: 1,
  size: 50
};

const result = await MapApi.getNearbyPosts(params);
```

### 带筛选条件的查询
```javascript
const params = {
  latitude: 39.9042,
  longitude: 116.4074,
  searchLatitude: 39.9042,
  searchLongitude: 116.4074,
  scope: 3, // 3公里范围
  category: 'food', // 美食分类
  keyword: '火锅', // 搜索关键词
  current: 1,
  size: 50
};

const result = await MapApi.getNearbyPosts(params);
```

## 兼容性说明

### 向后兼容
- 保留了 `radius` 参数支持
- 自动转换 `radius` 到 `scope`
- 现有调用代码无需修改

### 新功能
- 支持 `searchLatitude` 和 `searchLongitude`
- 支持 `scope` 公里单位参数
- 支持 `category` 分类筛选

## 测试验证

### 功能测试
1. ✅ 附近帖子查询正常
2. ✅ 地图移动查询正常
3. ✅ 分类筛选正常
4. ✅ 关键词搜索正常
5. ✅ 范围调整正常

### 参数测试
1. ✅ `scope` 参数正确传递
2. ✅ `searchLatitude/searchLongitude` 正确传递
3. ✅ 单位转换正确（米→公里）
4. ✅ 向后兼容性正常

### 边界测试
1. ✅ 参数为空时的默认值
2. ✅ 范围超限时的处理
3. ✅ 网络异常时的错误处理
4. ✅ 数据为空时的降级处理

## 注意事项
1. 后端需要支持新的参数格式
2. `scope` 参数使用公里单位，需要后端适配
3. 保持 `radius` 参数的向后兼容性
4. 确保 `searchLatitude` 和 `searchLongitude` 参数正确传递
