/**
 * 数字精度处理工具类
 * 解决JavaScript中数字精度丢失的问题
 */
class NumberUtils {
  
  /**
   * 最大安全整数
   */
  static MAX_SAFE_INTEGER = Number.MAX_SAFE_INTEGER; // 9007199254740991
  
  /**
   * 最小安全整数
   */
  static MIN_SAFE_INTEGER = Number.MIN_SAFE_INTEGER; // -9007199254740991
  
  /**
   * 安全的数字转换
   * @param {any} value 要转换的值
   * @param {string} type 转换类型：'string' | 'number' | 'auto'
   * @returns {any} 转换后的值
   */
  static safeParse(value, type = 'auto') {
    if (value === null || value === undefined || value === '') {
      return value;
    }
    
    // 如果已经是字符串且看起来像数字，尝试解析
    if (typeof value === 'string' && /^-?\d+(\.\d+)?$/.test(value.trim())) {
      const num = Number(value);
      
      if (isNaN(num)) {
        return value; // 无法解析，返回原值
      }
      
      // 检查是否超出安全范围
      if (Number.isInteger(num) && !Number.isSafeInteger(num)) {
        console.warn(`数字 ${value} 超出安全范围，保持为字符串`);
        return value; // 保持字符串格式
      }
      
      return type === 'string' ? value : num;
    }
    
    // 如果是数字类型
    if (typeof value === 'number') {
      if (isNaN(value)) {
        return value;
      }
      
      // 检查是否超出安全范围
      if (Number.isInteger(value) && !Number.isSafeInteger(value)) {
        const result = value.toString();
        console.warn(`数字 ${value} 超出安全范围，已转为字符串: ${result}`);
        return result;
      }
      
      return type === 'string' ? value.toString() : value;
    }
    
    return value;
  }
  
  /**
   * 处理ID字段（通常需要转为字符串保持精度）
   * @param {any} data 数据对象
   * @param {string[]} idFields ID字段名数组
   * @returns {any} 处理后的数据
   */
  static processIdFields(data, idFields = [
    'id', 'userId', 'postId', 'parentId', 'categoryId', 
    'institutionId', 'relevancyId', 'commentId', 'feedbackId',
    'replyId', 'markerId', 'regionId'
  ]) {
    if (!data || typeof data !== 'object') {
      return data;
    }
    
    if (Array.isArray(data)) {
      return data.map(item => this.processIdFields(item, idFields));
    }
    
    const result = { ...data };
    
    idFields.forEach(field => {
      if (result[field] !== undefined && result[field] !== null) {
        result[field] = this.safeParse(result[field], 'string');
      }
    });
    
    return result;
  }
  
  /**
   * 检查数字是否安全
   * @param {number} num 要检查的数字
   * @returns {boolean} 是否安全
   */
  static isSafeNumber(num) {
    if (typeof num !== 'number' || isNaN(num)) {
      return false;
    }
    
    return Number.isSafeInteger(num) || !Number.isInteger(num);
  }
  
  /**
   * 浮点数精确加法
   * @param {number} a 加数
   * @param {number} b 被加数
   * @returns {number} 结果
   */
  static add(a, b) {
    const precision = Math.max(
      this.getDecimalLength(a),
      this.getDecimalLength(b)
    );
    const factor = Math.pow(10, precision);
    return (Math.round(a * factor) + Math.round(b * factor)) / factor;
  }
  
  /**
   * 浮点数精确减法
   * @param {number} a 被减数
   * @param {number} b 减数
   * @returns {number} 结果
   */
  static subtract(a, b) {
    return this.add(a, -b);
  }
  
  /**
   * 浮点数精确乘法
   * @param {number} a 乘数
   * @param {number} b 被乘数
   * @returns {number} 结果
   */
  static multiply(a, b) {
    const precisionA = this.getDecimalLength(a);
    const precisionB = this.getDecimalLength(b);
    const factorA = Math.pow(10, precisionA);
    const factorB = Math.pow(10, precisionB);
    
    return (Math.round(a * factorA) * Math.round(b * factorB)) / (factorA * factorB);
  }
  
  /**
   * 浮点数精确除法
   * @param {number} a 被除数
   * @param {number} b 除数
   * @returns {number} 结果
   */
  static divide(a, b) {
    if (b === 0) {
      throw new Error('除数不能为0');
    }
    
    const precisionA = this.getDecimalLength(a);
    const precisionB = this.getDecimalLength(b);
    const factorA = Math.pow(10, precisionA);
    const factorB = Math.pow(10, precisionB);
    
    return (Math.round(a * factorA) / Math.round(b * factorB));
  }
  
  /**
   * 获取小数位数
   * @param {number} num 数字
   * @returns {number} 小数位数
   */
  static getDecimalLength(num) {
    const str = num.toString();
    const decimalIndex = str.indexOf('.');
    return decimalIndex === -1 ? 0 : str.length - decimalIndex - 1;
  }
  
  /**
   * 格式化数字显示
   * @param {number|string} num 数字
   * @param {number} decimals 小数位数
   * @returns {string} 格式化后的字符串
   */
  static format(num, decimals = 2) {
    const number = typeof num === 'string' ? parseFloat(num) : num;
    
    if (isNaN(number)) {
      return num.toString();
    }
    
    return number.toFixed(decimals);
  }
  
  /**
   * 比较两个数字是否相等（考虑浮点数精度）
   * @param {number} a 数字a
   * @param {number} b 数字b
   * @param {number} epsilon 精度阈值
   * @returns {boolean} 是否相等
   */
  static isEqual(a, b, epsilon = 1e-10) {
    return Math.abs(a - b) < epsilon;
  }
}

module.exports = NumberUtils;
