# 支付系统资源文件指南

## 📁 目录结构

建议在项目中创建以下资源文件目录：

```
weapp/
├── images/
│   ├── payment/              # 支付相关图标
│   │   ├── wechat.png       # 微信支付图标
│   │   ├── alipay.png       # 支付宝图标
│   │   └── balance.png      # 余额支付图标
│   └── business/            # 业务类型图标
│       ├── promotion.png    # 推广图标
│       ├── vip.png         # VIP图标
│       ├── reward.png      # 打赏图标
│       └── service.png     # 服务图标
```

## 🎨 图标规格

### 支付方式图标
- **尺寸**：60rpx × 60rpx (30px × 30px)
- **格式**：PNG，支持透明背景
- **风格**：简洁、清晰、符合品牌规范

### 业务类型图标
- **尺寸**：80rpx × 80rpx (40px × 40px)
- **格式**：PNG，支持透明背景
- **风格**：统一设计风格，易于识别

## 🖼️ 图标说明

### 1. 微信支付图标 (wechat.png)
```
建议使用微信官方提供的支付图标
- 绿色主色调
- 包含微信logo元素
- 符合微信品牌规范
```

### 2. 支付宝图标 (alipay.png)
```
建议使用支付宝官方提供的图标
- 蓝色主色调
- 包含支付宝logo元素
- 符合支付宝品牌规范
```

### 3. 余额支付图标 (balance.png)
```
自定义设计图标
- 钱包或余额相关图形
- 与应用主色调保持一致
- 简洁明了的设计
```

### 4. 业务类型图标

#### 推广图标 (promotion.png)
```
- 喇叭或扩音器图形
- 表示宣传推广含义
- 橙色或红色主色调
```

#### VIP图标 (vip.png)
```
- 皇冠或钻石图形
- 表示高级会员含义
- 金色或紫色主色调
```

#### 打赏图标 (reward.png)
```
- 红包或金币图形
- 表示打赏奖励含义
- 红色或金色主色调
```

#### 服务图标 (service.png)
```
- 工具或齿轮图形
- 表示服务功能含义
- 蓝色或灰色主色调
```

## 🎯 使用示例

### 在WXML中使用

```xml
<!-- 支付方式图标 -->
<image class="payment-icon" src="/images/payment/wechat.png" mode="aspectFit"></image>

<!-- 业务类型图标 -->
<image class="business-icon" src="/images/business/vip.png" mode="aspectFit"></image>
```

### 在WXSS中设置样式

```css
.payment-icon {
  width: 60rpx;
  height: 60rpx;
}

.business-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
}
```

### 在JavaScript中动态设置

```javascript
// 根据支付方式设置图标
const getPaymentIcon = (method) => {
  const iconMap = {
    wechat: '/images/payment/wechat.png',
    alipay: '/images/payment/alipay.png',
    balance: '/images/payment/balance.png'
  };
  return iconMap[method] || '/images/payment/default.png';
};

// 根据业务类型设置图标
const getBusinessIcon = (type) => {
  const iconMap = {
    post_promotion: '/images/business/promotion.png',
    vip_upgrade: '/images/business/vip.png',
    reward: '/images/business/reward.png',
    service_fee: '/images/business/service.png'
  };
  return iconMap[type] || '/images/business/default.png';
};
```

## 🔄 图标替换

如需替换图标，请确保：

1. **保持文件名不变**，直接替换文件内容
2. **保持尺寸规格**，避免显示异常
3. **测试显示效果**，确保在不同设备上正常显示
4. **符合平台规范**，特别是第三方支付图标

## 📱 适配说明

### 不同屏幕密度适配

可以提供多套图标资源：

```
images/
├── payment/
│   ├── wechat.png          # 标准密度 (1x)
│   ├── <EMAIL>       # 高密度 (2x)
│   └── <EMAIL>       # 超高密度 (3x)
```

### 深色模式适配

如果应用支持深色模式，可以提供对应的深色版本图标：

```
images/
├── payment/
│   ├── light/              # 浅色模式图标
│   │   ├── wechat.png
│   │   └── alipay.png
│   └── dark/               # 深色模式图标
│       ├── wechat.png
│       └── alipay.png
```

## 🎨 设计建议

### 1. 统一风格
- 保持所有图标的设计风格一致
- 使用统一的圆角、阴影等视觉元素
- 颜色搭配与应用主题保持协调

### 2. 清晰度
- 确保图标在小尺寸下依然清晰可辨
- 避免过于复杂的细节
- 使用适当的对比度

### 3. 品牌一致性
- 第三方支付图标遵循官方品牌规范
- 自定义图标体现应用品牌特色
- 保持专业和可信的视觉形象

## 📋 检查清单

在使用图标前，请确认：

- [ ] 图标文件已正确放置在指定目录
- [ ] 图标尺寸符合规格要求
- [ ] 图标在不同设备上显示正常
- [ ] 图标符合相关品牌规范
- [ ] 图标与应用整体设计风格协调
- [ ] 已测试图标的加载性能

## 🔗 相关资源

### 官方图标资源
- **微信支付**：https://pay.weixin.qq.com/wiki/doc/api/
- **支付宝**：https://opendocs.alipay.com/

### 图标设计工具
- **Sketch**：专业的UI设计工具
- **Figma**：在线协作设计工具
- **Adobe Illustrator**：矢量图形设计工具

### 图标库资源
- **Iconfont**：阿里巴巴矢量图标库
- **Feather Icons**：简洁的开源图标集
- **Material Icons**：Google Material Design图标

---

**注意**：请确保使用的图标符合相关版权和使用条款。
