/**
 * 统一API服务层
 * 提供统一的错误处理、重试机制和缓存策略
 */
const { request } = require('../../../utils/request');

/**
 * API错误类型
 */
const API_ERROR_TYPES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  SERVER_ERROR: 'SERVER_ERROR',
  AUTH_ERROR: 'AUTH_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR'
};

/**
 * 统一API服务类
 */
class ApiService {
  constructor() {
    this.retryConfig = {
      maxRetries: 3,
      retryDelay: 1000,
      retryableErrors: [API_ERROR_TYPES.NETWORK_ERROR, API_ERROR_TYPES.TIMEOUT_ERROR]
    };
    
    this.cache = new Map();
    this.cacheConfig = {
      defaultTTL: 5 * 60 * 1000, // 5分钟
      maxSize: 100
    };
  }

  /**
   * 统一API调用方法
   * @param {Object} config 请求配置
   * @param {Object} options 额外选项
   * @returns {Promise} API响应
   */
  async call(config, options = {}) {
    const {
      enableCache = false,
      cacheKey = null,
      cacheTTL = this.cacheConfig.defaultTTL,
      enableRetry = true,
      showLoading = false,
      loadingText = '加载中...'
    } = options;

    // 检查缓存
    if (enableCache && cacheKey) {
      const cachedData = this.getFromCache(cacheKey);
      if (cachedData) {
        console.log('使用缓存数据:', cacheKey);
        return cachedData;
      }
    }

    // 显示加载状态
    if (showLoading) {
      wx.showLoading({ title: loadingText });
    }

    try {
      const response = await this.executeRequest(config, enableRetry);
      
      // 缓存响应数据
      if (enableCache && cacheKey && response) {
        this.setCache(cacheKey, response, cacheTTL);
      }

      return response;
    } catch (error) {
      const apiError = this.handleError(error);
      throw apiError;
    } finally {
      if (showLoading) {
        wx.hideLoading();
      }
    }
  }

  /**
   * 执行请求（带重试机制）
   * @param {Object} config 请求配置
   * @param {boolean} enableRetry 是否启用重试
   * @returns {Promise} 请求响应
   */
  async executeRequest(config, enableRetry = true) {
    let lastError = null;
    const maxAttempts = enableRetry ? this.retryConfig.maxRetries + 1 : 1;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        console.log(`API请求尝试 ${attempt}/${maxAttempts}:`, config.url);
        const response = await request(config);
        
        if (response && response.success) {
          return response;
        } else {
          throw new Error(response?.msg || '服务器返回错误');
        }
      } catch (error) {
        lastError = error;
        console.error(`API请求失败 (尝试 ${attempt}/${maxAttempts}):`, error);

        // 如果不是最后一次尝试且错误可重试，则等待后重试
        if (attempt < maxAttempts && this.isRetryableError(error)) {
          const delay = this.retryConfig.retryDelay * attempt;
          console.log(`等待 ${delay}ms 后重试...`);
          await this.sleep(delay);
          continue;
        }

        break;
      }
    }

    throw lastError;
  }

  /**
   * 统一错误处理
   * @param {Error} error 原始错误
   * @returns {Object} 标准化错误对象
   */
  handleError(error) {
    console.error('API错误处理:', error);

    let errorType = API_ERROR_TYPES.UNKNOWN_ERROR;
    let message = '未知错误';
    let code = 'UNKNOWN';

    if (error.errMsg) {
      // 微信小程序网络错误
      if (error.errMsg.includes('timeout')) {
        errorType = API_ERROR_TYPES.TIMEOUT_ERROR;
        message = '请求超时，请检查网络连接';
        code = 'TIMEOUT';
      } else if (error.errMsg.includes('fail')) {
        errorType = API_ERROR_TYPES.NETWORK_ERROR;
        message = '网络连接失败，请检查网络设置';
        code = 'NETWORK_FAIL';
      }
    } else if (error.message) {
      message = error.message;
      
      if (error.message.includes('401') || error.message.includes('unauthorized')) {
        errorType = API_ERROR_TYPES.AUTH_ERROR;
        code = 'AUTH_FAIL';
      } else if (error.message.includes('400') || error.message.includes('validation')) {
        errorType = API_ERROR_TYPES.VALIDATION_ERROR;
        code = 'VALIDATION_FAIL';
      } else if (error.message.includes('500')) {
        errorType = API_ERROR_TYPES.SERVER_ERROR;
        code = 'SERVER_ERROR';
      }
    }

    const apiError = {
      type: errorType,
      code: code,
      message: message,
      originalError: error,
      timestamp: Date.now()
    };

    // 根据错误类型显示不同的用户提示
    this.showErrorToast(apiError);

    return apiError;
  }

  /**
   * 显示错误提示
   * @param {Object} error 错误对象
   */
  showErrorToast(error) {
    let title = error.message;

    switch (error.type) {
      case API_ERROR_TYPES.NETWORK_ERROR:
        title = '网络连接失败';
        break;
      case API_ERROR_TYPES.TIMEOUT_ERROR:
        title = '请求超时';
        break;
      case API_ERROR_TYPES.AUTH_ERROR:
        title = '身份验证失败';
        break;
      case API_ERROR_TYPES.SERVER_ERROR:
        title = '服务器错误';
        break;
      case API_ERROR_TYPES.VALIDATION_ERROR:
        title = '参数错误';
        break;
    }

    wx.showToast({
      title: title,
      icon: 'none',
      duration: 2000
    });
  }

  /**
   * 判断错误是否可重试
   * @param {Error} error 错误对象
   * @returns {boolean} 是否可重试
   */
  isRetryableError(error) {
    if (error.errMsg) {
      return error.errMsg.includes('timeout') || error.errMsg.includes('fail');
    }
    return false;
  }

  /**
   * 睡眠函数
   * @param {number} ms 毫秒数
   * @returns {Promise} Promise对象
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 设置缓存
   * @param {string} key 缓存键
   * @param {any} data 缓存数据
   * @param {number} ttl 过期时间（毫秒）
   */
  setCache(key, data, ttl = this.cacheConfig.defaultTTL) {
    // 清理过期缓存
    this.cleanExpiredCache();

    // 如果缓存已满，删除最旧的条目
    if (this.cache.size >= this.cacheConfig.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }

    this.cache.set(key, {
      data: data,
      expireTime: Date.now() + ttl,
      createTime: Date.now()
    });
  }

  /**
   * 获取缓存
   * @param {string} key 缓存键
   * @returns {any} 缓存数据或null
   */
  getFromCache(key) {
    const cacheItem = this.cache.get(key);
    
    if (!cacheItem) {
      return null;
    }

    if (Date.now() > cacheItem.expireTime) {
      this.cache.delete(key);
      return null;
    }

    return cacheItem.data;
  }

  /**
   * 清理过期缓存
   */
  cleanExpiredCache() {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expireTime) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * 清空所有缓存
   */
  clearCache() {
    this.cache.clear();
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 缓存统计
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      maxSize: this.cacheConfig.maxSize,
      keys: Array.from(this.cache.keys())
    };
  }
}

// 创建单例实例
const apiService = new ApiService();

module.exports = {
  ApiService,
  apiService,
  API_ERROR_TYPES
};
