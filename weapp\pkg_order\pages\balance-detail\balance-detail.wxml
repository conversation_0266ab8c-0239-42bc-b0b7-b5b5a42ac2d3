<!-- 余额明细页面 -->
<layout title="余额明细" show-back="{{true}}">
  <view class="balance-detail-container">
    <!-- 余额卡片 -->
    <!-- <view class="balance-card">
      <view class="balance-header">
        <text class="balance-title">当前余额</text>
      </view>
      <view class="balance-amount">
        <text class="currency">¥</text>
        <text class="amount">{{currentBalance}}</text>
      </view>
    </view> -->

    <!-- 筛选标签 -->
    <scroll-view scroll-x class="filter-tabs-scroll" show-scrollbar="false">
      <view class="filter-tabs">
        <view 
          class="filter-tab {{currentFilter === '' ? 'active' : ''}}"
          bindtap="onFilterChange"
          data-filter=""
        >
          全部
        </view>
        <view 
          class="filter-tab {{currentFilter === 'recharge' ? 'active' : ''}}"
          bindtap="onFilterChange"
          data-filter="recharge"
        >
          充值
        </view>
        <view 
          class="filter-tab {{currentFilter === 'consume' ? 'active' : ''}}"
          bindtap="onFilterChange"
          data-filter="consume"
        >
          消费
        </view>
        <view 
          class="filter-tab {{currentFilter === 'refund' ? 'active' : ''}}"
          bindtap="onFilterChange"
          data-filter="refund"
        >
          退款
        </view>
      </view>
    </scroll-view>

    <!-- 明细列表 -->
    <scroll-view 
      class="detail-list" 
      scroll-y="{{true}}"
      bindscrolltolower="onLoadMore"
      enable-back-to-top="{{true}}"
    >
      <view class="detail-item" wx:for="{{records}}" wx:key="id">
        <view class="detail-header">
          <view class="detail-info">
            <text class="detail-title">{{item.title}}</text>
            <text class="detail-time">{{item.timeText}}</text>
          </view>
          <view class="detail-amount {{item.type === 'recharge' || item.type === 'refund' ? 'income' : 'expense'}}">
            <text class="amount-sign">{{item.type === 'recharge' || item.type === 'refund' ? '+' : '-'}}</text>
            <text class="amount-value">¥{{item.amount}}</text>
          </view>
        </view>
        
        <view class="detail-content" wx:if="{{item.description}}">
          <text class="detail-desc">{{item.description}}</text>
        </view>
        
        <view class="detail-footer">
          <view class="detail-status {{item.status === 'success' ? 'success' : item.status === 'pending' ? 'pending' : 'failed'}}">
            <text class="status-text">{{item.statusText}}</text>
          </view>
          <view class="detail-order" wx:if="{{item.orderNo}}">
            <text class="order-text">订单号：{{item.orderNo}}</text>
          </view>
        </view>
      </view>

      <!-- 加载状态 -->
      <view class="loading-container" wx:if="{{loading}}">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 没有更多数据 -->
      <view class="no-more-container" wx:if="{{!hasMore && records.length > 0}}">
        <text class="no-more-text">没有更多记录了</text>
      </view>

      <!-- 空状态 -->
      <view class="empty-container" wx:if="{{records.length === 0 && !loading}}">
        <view class="empty-icon">💰</view>
        <text class="empty-text">暂无余额明细</text>
        <text class="empty-desc">您的余额变动记录将在这里显示</text>
      </view>
    </scroll-view>
  </view>
</layout>
