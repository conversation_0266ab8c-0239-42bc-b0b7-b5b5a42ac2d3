# 名片展开功能调试指南

## 🔍 问题排查步骤

### 1. 检查控制台日志

打开小程序开发者工具的控制台，查看以下关键日志：

#### 页面加载时：
```
收藏名片页面加载
初始化展开状态数组: []
```

#### 点击卡片时：
```
onCardTap 被调用
管理模式状态: false
点击收藏名片: {卡片数据}
提取的 cardId: 1001
当前展开的卡片IDs: []
toggleCardExpand 被调用，cardId: 1001
...
```

### 2. 使用调试按钮

页面右上角有一个红色的"调试"按钮，点击它可以：
- 查看当前卡片列表状态
- 测试展开第一张卡片
- 输出详细的调试信息

### 3. 检查数据结构

确保卡片数据包含必要的字段：
```javascript
{
  id: "1001",           // 必须有 id 或 cardId
  cardId: "1001",       
  fullName: "张三",
  isExpanded: false,    // 展开状态
  // ... 其他字段
}
```

### 4. 检查事件绑定

确保 WXML 中的事件绑定正确：
```xml
<view bindtap="onCardTap" data-item="{{item}}">
```

## 🐛 常见问题及解决方案

### 问题1: 点击没有反应

**可能原因：**
- 管理模式开启了
- 事件被其他元素阻止
- cardId 获取失败

**解决方案：**
1. 检查管理模式状态：
```javascript
console.log('管理模式:', this.data.isManageMode);
```

2. 检查事件目标：
```javascript
console.log('点击目标:', e.currentTarget);
console.log('数据集:', e.currentTarget.dataset);
```

### 问题2: 展开状态不更新

**可能原因：**
- setData 没有正确调用
- 数据绑定问题
- 条件判断错误

**解决方案：**
1. 检查 setData 调用：
```javascript
console.log('setData 前:', this.data.expandedCardIds);
this.setData({ expandedCardIds: newArray });
console.log('setData 后:', this.data.expandedCardIds);
```

2. 检查数据更新：
```javascript
console.log('卡片展开状态:', this.data.cardList.map(item => ({
  id: item.id,
  name: item.fullName,
  isExpanded: item.isExpanded
})));
```

### 问题3: 动画不生效

**可能原因：**
- CSS 类名没有正确应用
- 动画样式被覆盖
- 元素高度计算问题

**解决方案：**
1. 检查元素类名：
```javascript
// 在浏览器开发者工具中检查元素
// 应该看到类似这样的类名：
// class="card-expanded-content show"
```

2. 检查 CSS 样式是否生效

### 问题4: cardId 为空或undefined

**可能原因：**
- 数据结构不正确
- id 字段缺失
- 数据类型问题

**解决方案：**
1. 检查原始数据：
```javascript
console.log('原始卡片数据:', this.data.cardList[0]);
console.log('ID字段:', {
  id: this.data.cardList[0].id,
  cardId: this.data.cardList[0].cardId
});
```

## 🔧 调试代码片段

### 1. 添加详细日志

在 `onCardTap` 方法中添加：
```javascript
onCardTap(e) {
  console.log('=== 卡片点击调试 ===');
  console.log('事件对象:', e);
  console.log('currentTarget:', e.currentTarget);
  console.log('dataset:', e.currentTarget.dataset);
  console.log('管理模式:', this.data.isManageMode);
  
  // ... 原有代码
}
```

### 2. 检查数据更新

在 `updateCardExpandedState` 方法中添加：
```javascript
updateCardExpandedState() {
  console.log('=== 更新卡片状态调试 ===');
  console.log('当前展开IDs:', this.data.expandedCardIds);
  
  const updatedCardList = this.data.cardList.map(item => {
    const cardId = item.id || item.cardId;
    const isExpanded = this.data.expandedCardIds.includes(cardId);
    
    console.log(`卡片 ${cardId}: ${isExpanded ? '展开' : '收起'}`);
    
    return { ...item, isExpanded };
  });
  
  this.setData({ cardList: updatedCardList });
}
```

### 3. 验证 WXML 绑定

在模板中添加调试信息：
```xml
<!-- 临时调试信息 -->
<view style="font-size: 20rpx; color: #999; padding: 10rpx;">
  调试: ID={{item.id}}, 展开={{item.isExpanded}}, 管理模式={{isManageMode}}
</view>
```

## 📱 测试步骤

### 1. 基础功能测试
1. 打开页面，确保有卡片数据
2. 点击红色"调试"按钮，查看控制台输出
3. 点击任意卡片，观察控制台日志
4. 检查卡片是否有视觉变化（边框、箭头）

### 2. 状态测试
1. 点击卡片展开
2. 再次点击同一卡片收起
3. 点击不同卡片，测试多卡片展开
4. 切换到管理模式，确保点击无效

### 3. 数据测试
1. 检查展开后是否显示详细信息
2. 验证联系方式、商业简介等内容
3. 测试操作按钮是否正常工作

## 🚨 紧急修复

如果功能完全不工作，可以尝试以下快速修复：

### 1. 简化版本
临时移除复杂逻辑，使用最简单的实现：

```javascript
// 简化的点击处理
onCardTap(e) {
  const index = e.currentTarget.dataset.index;
  const key = `cardList[${index}].isExpanded`;
  const currentState = this.data.cardList[index].isExpanded;
  
  this.setData({
    [key]: !currentState
  });
}
```

### 2. 直接操作数组
```javascript
onCardTap(e) {
  const cardList = [...this.data.cardList];
  const index = e.currentTarget.dataset.index;
  cardList[index].isExpanded = !cardList[index].isExpanded;
  
  this.setData({ cardList });
}
```

## 📞 获取帮助

如果问题仍然存在，请提供以下信息：
1. 控制台完整日志
2. 卡片数据结构示例
3. 点击时的具体表现
4. 小程序开发者工具版本

---

**调试提示：** 大部分展开功能问题都与数据绑定和事件处理有关，仔细检查控制台日志通常能找到问题所在。
