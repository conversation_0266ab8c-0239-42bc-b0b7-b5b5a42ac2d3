Component({
  properties: {
    post: {
      type: Object,
      value: {}
    }
  },

  data: {
    item: {}
  },

  lifetimes: {
    attached() {
      console.log(this.properties.post)
      this.setData({
        item: this.properties.post
      });
    }
  },

  observers: {
    'post': function(newVal) {
      this.setData({
        item: newVal
      });
    }
  },

  methods: {
    // 点击卡片
    onCardTap() {
      // 跳转到帖子详情页面
      wx.navigateTo({
        url: `/pages/post-detail/post-detail?id=${this.data.item.id}`
      });
    },

    // 点击咨询按钮
    onContactTap() {
      this.triggerEvent('contact', { post: this.data.item });
    },

    // 点击点赞
    onLikeTap() {
      this.triggerEvent('like', { post: this.data.item });
    },

    // 点击图片
    onImageTap(e) {
      this.triggerEvent('image', { post: this.data.item, index: e.currentTarget.dataset.index });
    },

    // 点击分享
    onShare() {
      this.triggerEvent('share', { post: this.data.item });
    }
  }
}); 