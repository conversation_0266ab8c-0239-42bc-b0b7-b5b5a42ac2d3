// 本地名片库页面
const { request } = require('../../utils/auth');

Page({
  data: {
    // 导航栏相关
    navBarHeight: 0,

    // 名片列表数据
    cards: [],
    
    // 分页参数
    current: 1,
    size: 10,
    total: 0,
    
    // 状态控制
    loading: false,
    loadingMore: false,
    refreshing: false,
    noMore: false,
    
    // 搜索相关
    showSearch: false,
    searchKeyword: '',
    searchFocus: false,
    
    // 筛选相关
    selectedFilter: 'nearby', // 默认选中附近
    showFilterModal: false,
    selectedDistance: '',
    selectedIndustry: '',
    distanceOptions: [
      { label: '不限', value: '' },
      { label: '1公里内', value: 1 },
      { label: '3公里内', value: 3 },
      { label: '5公里内', value: 5 },
      { label: '10公里内', value: 10 }
    ],
    industryOptions: [
      '不限', '互联网', '金融', '教育', '医疗', '房地产', 
      '制造业', '服务业', '零售', '咨询', '其他'
    ],
    
    // 联系人相关
    showContactTip: false,

    // 位置信息
    userLocation: null,

    // 收藏相关
    showFavoriteModal: false,
    currentFavoriteCard: null,
    currentFavoriteIndex: -1,
    favoriteCategories: ['工作伙伴', '客户', '朋友', '其他'],
    selectedCategory: '其他',
    favoriteRemark: '',
    showAddCategoryInput: false,
    newCategoryName: ''
  },

  /**
   * 页面加载
   */
  onLoad(options) {
    console.log('名片库页面参数:', options);
    
    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '本地名片库'
    });
    
    // 获取用户位置
    this.getUserLocation();
    
    // 加载名片列表
    this.loadCards(true);
  },

  // 导航栏准备完成
  onNavReady(e) {
    const navHeight = e.detail.height;
    this.setData({
      navBarHeight: navHeight,
    });
  },


  /**
   * 页面显示
   */
  onShow() {
    // 如果从其他页面返回，刷新数据
    if (this.data.cards.length > 0) {
      this.loadCards(true);
    }
  },

  /**
   * 获取用户位置
   */
  getUserLocation() {
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        this.setData({
          userLocation: {
            latitude: res.latitude,
            longitude: res.longitude
          }
        });
        console.log('用户位置:', res);
      },
      fail: (err) => {
        console.log('获取位置失败:', err);
      }
    });
  },

  /**
   * 加载名片列表
   */
  async loadCards(refresh = false) {
    if (this.data.loading || this.data.loadingMore) return;
    
    // 设置加载状态
    if (refresh) {
      this.setData({ 
        loading: true, 
        current: 1,
        noMore: false 
      });
    } else {
      this.setData({ loadingMore: true });
    }

    try {
      const params = {
        current: refresh ? 1 : this.data.current,
        size: this.data.size
      };
      
      // 添加搜索条件
      if (this.data.searchKeyword) {
        params.keyword = this.data.searchKeyword;
      }
      
      // 添加筛选条件
      if (this.data.selectedFilter === 'nearby' && this.data.userLocation) {
        // 附近查询：传自己的位置到latitude、longitude，查询的经纬度到searchLatitude、searchLongitude
        params.latitude = this.data.userLocation.latitude;
        params.longitude = this.data.userLocation.longitude;
        params.searchLatitude = this.data.userLocation.latitude;
        params.searchLongitude = this.data.userLocation.longitude;
        params.scope = 10; // 10公里范围
      } else if (this.data.selectedFilter === 'recent') {
        // 查最新：传自己的位置到latitude、longitude，不传searchLatitude、searchLongitude
        if (this.data.userLocation) {
          params.latitude = this.data.userLocation.latitude;
          params.longitude = this.data.userLocation.longitude;
        }
        params.orderBy = 'create_time';
        params.orderDirection = 'desc';
      }

      // 距离筛选条件
      if (this.data.selectedDistance && this.data.userLocation) {
        params.latitude = this.data.userLocation.latitude;
        params.longitude = this.data.userLocation.longitude;
        params.searchLatitude = this.data.userLocation.latitude;
        params.searchLongitude = this.data.userLocation.longitude;
        params.scope = this.data.selectedDistance;
      }
      
      if (this.data.selectedIndustry && this.data.selectedIndustry !== '不限') {
        params.industry = this.data.selectedIndustry;
      }
      
      const response = await request({
        url: '/blade-chat-open/card/page',
        method: 'GET',
        data: params
      });
      
      if (response.code === 200) {
        const newCards = response.data.records || [];
        
        // 格式化数据
        const formattedCards = newCards.map(card => {
          // 处理统计数据，优先从stats对象获取
          let likeCount = 0;
          let favoriteCount = 0;
          let viewCount = 0;
          let isLiked = false;
          let isFavorited = false;

          if (card.stats) {
            likeCount = card.stats.likeCount || 0;
            favoriteCount = card.stats.favoriteCount || 0;
            viewCount = card.stats.viewCount || 0;
            isLiked = card.stats.isLiked || false;
            isFavorited = card.stats.isFavorite || card.stats.isFavorited || false;
          } else {
            // 如果没有stats对象，从根级别获取
            likeCount = card.likeCount || 0;
            favoriteCount = card.favoriteCount || 0;
            viewCount = card.viewCount || 0;
            isLiked = card.isLiked || false;
            isFavorited = card.isFavorited || false;
          }

          return {
            ...card,
            createTime: this.formatTime(card.createTime),
            likeCount: likeCount,
            favoriteCount: favoriteCount,
            viewCount: viewCount,
            isLiked: isLiked,
            isFavorited: isFavorited,
            distance: card.distance ? parseFloat(card.distance).toFixed(1) : null
          };
        });
        
        this.setData({
          cards: refresh ? formattedCards : [...this.data.cards, ...formattedCards],
          total: response.data.total,
          current: response.data.current + 1,
          noMore: newCards.length < this.data.size
        });
      } else {
        wx.showToast({
          title: response.msg || '加载失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('加载名片列表失败:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ 
        loading: false, 
        loadingMore: false,
        refreshing: false 
      });
    }
  },

  /**
   * 格式化时间
   */
  formatTime(timeStr) {
    if (!timeStr) return '';
    
    const time = new Date(timeStr);
    const now = new Date();
    const diff = now - time;
    
    const minute = 60 * 1000;
    const hour = 60 * minute;
    const day = 24 * hour;
    
    if (diff < hour) {
      return Math.floor(diff / minute) + '分钟前';
    } else if (diff < day) {
      return Math.floor(diff / hour) + '小时前';
    } else if (diff < 7 * day) {
      return Math.floor(diff / day) + '天前';
    } else {
      return time.toLocaleDateString();
    }
  },

  /**
   * 显示搜索框
   */
  onShowSearch() {
    this.setData({ 
      showSearch: true,
      searchFocus: true 
    });
  },

  /**
   * 隐藏搜索框
   */
  onHideSearch() {
    this.setData({ 
      showSearch: false,
      searchKeyword: '',
      searchFocus: false 
    });
    this.loadCards(true);
  },

  /**
   * 搜索输入
   */
  onSearchInput(e) {
    this.setData({ searchKeyword: e.detail.value });
  },

  /**
   * 执行搜索
   */
  onSearch() {
    this.loadCards(true);
  },

  /**
   * 清空搜索
   */
  onClearSearch() {
    this.setData({ searchKeyword: '' });
    this.loadCards(true);
  },

  /**
   * 筛选点击
   */
  onFilterTap(e) {
    const filter = e.currentTarget.dataset.filter;
    this.setData({ selectedFilter: filter });
    this.loadCards(true);
  },

  /**
   * 显示更多筛选
   */
  onShowMoreFilter() {
    this.setData({ showFilterModal: true });
  },

  /**
   * 隐藏筛选弹窗
   */
  onHideFilterModal() {
    this.setData({ showFilterModal: false });
  },

  /**
   * 距离选择
   */
  onDistanceSelect(e) {
    const value = e.currentTarget.dataset.value;
    this.setData({ selectedDistance: value });
  },

  /**
   * 行业选择
   */
  onIndustrySelect(e) {
    const industry = e.currentTarget.dataset.industry;
    this.setData({ selectedIndustry: industry });
  },

  /**
   * 重置筛选
   */
  onResetFilter() {
    this.setData({
      selectedDistance: '',
      selectedIndustry: ''
    });
  },

  /**
   * 确认筛选
   */
  onConfirmFilter() {
    this.setData({ showFilterModal: false });
    this.loadCards(true);
  },

  /**
   * 下拉刷新
   */
  onRefresh() {
    this.setData({ refreshing: true });
    this.loadCards(true);
  },

  /**
   * 上拉加载更多
   */
  onReachBottom() {
    if (!this.data.noMore && !this.data.loadingMore) {
      this.loadCards(false);
    }
  },

  /**
   * 名片点击事件
   */
  onCardTap(e) {
    const card = e.currentTarget.dataset.card;
    console.log('点击名片:', card);
    // 这里可以跳转到名片详情页面
    // wx.navigateTo({
    //   url: `/pages/card-detail/card-detail?id=${card.id}`
    // });
  },

  /**
   * 点赞点击事件（使用通用数据操作接口）
   */
  async onLikeTap(e) {
     // 阻止事件冒泡

    const cardId = e.currentTarget.dataset.id;
    const index = e.currentTarget.dataset.index;

    try {
      // 使用通用数据操作接口
      const response = await request({
        url: `/blade-chat/data-operate/toggle-like/2/${cardId}`,
        method: 'POST'
      });

      if (response.code === 200 && response.data.success) {
        // 更新本地数据
        const cards = [...this.data.cards];
        const card = cards[index];
        const wasLiked = card.isLiked;
        card.isLiked = response.data.currentState;
        // 根据之前的状态和当前状态更新点赞数
        if (wasLiked && !card.isLiked) {
          // 取消点赞
          card.likeCount = Math.max(0, card.likeCount - 1);
        } else if (!wasLiked && card.isLiked) {
          // 新增点赞
          card.likeCount = card.likeCount + 1;
        }

        this.setData({ cards });

        wx.showToast({
          title: response.data.message || (card.isLiked ? '点赞成功' : '取消点赞'),
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: response.msg || '操作失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('点赞操作失败:', error);
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 收藏点击事件
   */
  onFavoriteTap(e) {
    // 阻止事件冒泡
    const cardId = e.currentTarget.dataset.id;
    const index = e.currentTarget.dataset.index;
    const card = this.data.cards[index];

    if (card.isFavorited) {
      // 已收藏，提示用户是否取消收藏
      wx.showModal({
        title: '提示',
        content: '该名片已收藏，是否要取消收藏？',
        confirmText: '取消收藏',
        cancelText: '保留',
        success: (res) => {
          if (res.confirm) {
            this.unfavoriteCard(cardId, index);
          }
        }
      });
    } else {
      // 未收藏，显示收藏弹窗
      this.setData({
        showFavoriteModal: true,
        currentFavoriteCard: card,
        currentFavoriteIndex: index,
        selectedCategory: '其他',
        favoriteRemark: '',
        showAddCategoryInput: false,
        newCategoryName: ''
      });

      // 加载用户的收藏分类
      this.loadFavoriteCategories();
    }
  },

  /**
   * 加载收藏分类
   */
  async loadFavoriteCategories() {
    try {
      const response = await request({
        url: '/blade-chat/card/favorite/categories',
        method: 'GET'
      });

      if (response.code === 200 && response.data) {
        const categories = response.data.length > 0 ? response.data : ['工作伙伴', '客户', '朋友', '其他'];
        this.setData({ favoriteCategories: categories });
      }
    } catch (error) {
      console.error('加载收藏分类失败:', error);
    }
  },

  /**
   * 分类选择
   */
  onCategorySelect(e) {
    const category = e.currentTarget.dataset.category;
    this.setData({ selectedCategory: category });
  },

  /**
   * 显示新建分类输入框
   */
  onShowAddCategory() {
    this.setData({ showAddCategoryInput: true });
  },

  /**
   * 取消新建分类
   */
  onCancelAddCategory() {
    this.setData({
      showAddCategoryInput: false,
      newCategoryName: ''
    });
  },

  /**
   * 分类名称输入
   */
  onCategoryInput(e) {
    this.setData({ newCategoryName: e.detail.value });
  },

  /**
   * 确认新建分类
   */
  onConfirmAddCategory() {
    const categoryName = this.data.newCategoryName.trim();
    if (!categoryName) {
      wx.showToast({
        title: '请输入分类名称',
        icon: 'none'
      });
      return;
    }

    if (this.data.favoriteCategories.includes(categoryName)) {
      wx.showToast({
        title: '分类已存在',
        icon: 'none'
      });
      return;
    }

    const categories = [...this.data.favoriteCategories, categoryName];
    this.setData({
      favoriteCategories: categories,
      selectedCategory: categoryName,
      showAddCategoryInput: false,
      newCategoryName: ''
    });
  },

  /**
   * 备注输入
   */
  onRemarkInput(e) {
    this.setData({ favoriteRemark: e.detail.value });
  },

  /**
   * 隐藏收藏弹窗
   */
  onHideFavoriteModal() {
    this.setData({
      showFavoriteModal: false,
      currentFavoriteCard: null,
      currentFavoriteIndex: -1,
      selectedCategory: '其他',
      favoriteRemark: '',
      showAddCategoryInput: false,
      newCategoryName: ''
    });
  },

  /**
   * 确认收藏
   */
  async onConfirmFavorite() {
    if (!this.data.currentFavoriteCard) return;

    try {
      const response = await request({
        url: `/blade-chat/card/favorite/${this.data.currentFavoriteCard.id}`,
        method: 'POST',
        data: {
          category: this.data.selectedCategory,
          remark: this.data.favoriteRemark
        }
      });

      if (response.code === 200 && response.data) {
        // 更新本地数据
        const cards = [...this.data.cards];
        const card = cards[this.data.currentFavoriteIndex];
        card.isFavorited = true;
        card.favoriteCount = (card.favoriteCount || 0) + 1;

        this.setData({ cards });
        this.onHideFavoriteModal();

        wx.showToast({
          title: '收藏成功',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: response.msg || '收藏失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('收藏失败:', error);
      wx.showToast({
        title: '收藏失败，请重试',
        icon: 'none'
      });
    }
  },


  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  },

  /**
   * 添加到通讯录
   */
  async addToContacts(card, cardId, index) {
    try {
      // 构建联系人信息
      const contactInfo = {
        firstName: card.fullName || '未知',
        organization: card.company || '',
        title: card.jobTitle || '',
        mobilePhoneNumber: card.phone || '',
        addressStreet: card.address || '',
        email: card.email || '',
        url: card.website || '',
        remark: `来自名片库 - ${card.businessProfile || ''}`
      };

      // 调用微信添加联系人接口
      wx.addPhoneContact({
        ...contactInfo,
        success: async (res) => {
          console.log('添加联系人成功:', res);

          // 添加成功后，同时收藏该名片
          try {
            const response = await request({
              url: `/blade-chat/data-operate/toggle-favorite/2/${cardId}`,
              method: 'POST'
            });

            if (response.code === 200 && response.data.success) {
              // 更新本地数据
              const cards = [...this.data.cards];
              const cardItem = cards[index];
              cardItem.isFavorited = response.data.currentState;
              cardItem.favoriteCount = cardItem.isFavorited ? cardItem.favoriteCount + 1 : Math.max(0, cardItem.favoriteCount - 1);

              this.setData({ cards });

              wx.showToast({
                title: '已添加到通讯录',
                icon: 'success'
              });
            }
          } catch (error) {
            console.error('收藏失败:', error);
            // 即使收藏失败，添加联系人成功也要提示
            wx.showToast({
              title: '已添加到通讯录',
              icon: 'success'
            });
          }
        },
        fail: (err) => {
          console.error('添加联系人失败:', err);

          if (err.errMsg.includes('cancel')) {
            wx.showToast({
              title: '已取消添加',
              icon: 'none'
            });
          } else if (err.errMsg.includes('permission')) {
            wx.showModal({
              title: '权限提示',
              content: '需要获取通讯录权限才能添加联系人，请在设置中开启权限',
              confirmText: '去设置',
              success: (res) => {
                if (res.confirm) {
                  wx.openSetting();
                }
              }
            });
          } else {
            wx.showToast({
              title: '添加失败，请重试',
              icon: 'none'
            });
          }
        }
      });
    } catch (error) {
      console.error('添加联系人异常:', error);
      wx.showToast({
        title: '添加失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 取消收藏（使用通用数据操作接口）
   */
  async unfavoriteCard(cardId, index) {
    try {
      // 使用通用数据操作接口
      const response = await request({
        url: `/blade-chat/data-operate/toggle-favorite/2/${cardId}`,
        method: 'POST'
      });

      if (response.code === 200 && response.data.success) {
        // 更新本地数据
        const cards = [...this.data.cards];
        const card = cards[index];
        card.isFavorited = response.data.currentState;
        card.favoriteCount = card.isFavorited ? card.favoriteCount + 1 : Math.max(0, card.favoriteCount - 1);

        this.setData({ cards });

        wx.showToast({
          title: '已从通讯录移除',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: response.msg || '操作失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('操作失败:', error);
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      });
    }
  },



  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  }
});
