<!-- 内容卡片组件 -->
<view class="content-card {{post.isHighlight ? 'highlight' : ''}}" >
  <!-- 卡片头部 -->
  <view class="card-header">
    <view class="header-left">
      <view class="tag">{{post.tag}}</view>
      <view  
      class="tag-item"
        wx:for="{{post.tags}}" 
        wx:key="*this"
        >{{item}}
      </view>
        
    </view>
  </view>

  <!-- 描述信息 -->
  <view class="promotion-text" bindtap="onCardTap" wx:if="{{post.description}}">{{post.description}}</view>
  

  <!-- 图片区域 - 九宫格布局 -->
  <view class="image-grid"  wx:if="{{post.images && post.images.length > 0}}" bindtap="onCardTap">
    <image 
      wx:for="{{post.images}}" 
      wx:key="index" 
      src="{{item}}" 
      mode="aspectFill"
      bindtap="onImageTap"
      data-index="{{index}}"
      class="grid-image {{post.images.length === 1 ? 'single' : post.images.length === 2 ? 'double' : 'multiple'}}"
    ></image>
  </view>
  <view class="tag-container">
    <!-- 地址信息 -->
    <view class="tag-address"  wx:if="{{post.address}}">{{post.address}}</view>
    <!-- 时间信息 -->
    <view class="tag-time">
      {{post.publishTime}}
    </view>
  </view>
  <!-- 底部互动栏 -->
  <view class="interaction-bar">
    <view class="interaction-item">
      <image class="icon" src="/assets/images/detail/eye-icon.png"></image>
      <text>{{post.views}}</text>
    </view>
    <view class="interaction-item">
      <image class="icon" src="/assets/images/detail/comment-icon.png"></image>
      <text>{{post.comments}}</text>
    </view>
    <view class="interaction-item"  bindtap="onLikeTap">
      <image class="icon" src="/assets/images/detail/heart-icon.png"></image>
      <text>{{post.likes}}</text>
    </view>
  </view>
</view> 