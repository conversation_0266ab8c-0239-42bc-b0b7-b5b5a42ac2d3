# 名片附近分页查询传参规格修改说明

## 修改概述

根据要求，对名片库页面的分页查询传参规格进行了调整，明确区分了用户位置和查询位置的参数传递。

## 修改内容

### 1. 附近查询 (nearby)

**修改前：**
```javascript
if (this.data.selectedFilter === 'nearby' && this.data.userLocation) {
  params.latitude = this.data.userLocation.latitude;
  params.longitude = this.data.userLocation.longitude;
  params.scope = 10; // 10公里范围
}
```

**修改后：**
```javascript
if (this.data.selectedFilter === 'nearby' && this.data.userLocation) {
  // 附近查询：传自己的位置到latitude、longitude，查询的经纬度到searchLatitude、searchLongitude
  params.latitude = this.data.userLocation.latitude;
  params.longitude = this.data.userLocation.longitude;
  params.searchLatitude = this.data.userLocation.latitude;
  params.searchLongitude = this.data.userLocation.longitude;
  params.scope = 10; // 10公里范围
}
```

### 2. 最新查询 (recent)

**修改前：**
```javascript
else if (this.data.selectedFilter === 'recent') {
  // 最新排序
  params.orderBy = 'create_time';
  params.orderDirection = 'desc';
}
```

**修改后：**
```javascript
else if (this.data.selectedFilter === 'recent') {
  // 查最新：传自己的位置到latitude、longitude，不传searchLatitude、searchLongitude
  if (this.data.userLocation) {
    params.latitude = this.data.userLocation.latitude;
    params.longitude = this.data.userLocation.longitude;
  }
  params.orderBy = 'create_time';
  params.orderDirection = 'desc';
}
```

### 3. 距离筛选

**修改前：**
```javascript
if (this.data.selectedDistance && this.data.userLocation) {
  params.latitude = this.data.userLocation.latitude;
  params.longitude = this.data.userLocation.longitude;
  params.scope = this.data.selectedDistance;
}
```

**修改后：**
```javascript
// 距离筛选条件
if (this.data.selectedDistance && this.data.userLocation) {
  params.latitude = this.data.userLocation.latitude;
  params.longitude = this.data.userLocation.longitude;
  params.searchLatitude = this.data.userLocation.latitude;
  params.searchLongitude = this.data.userLocation.longitude;
  params.scope = this.data.selectedDistance;
}
```

## 参数说明

### 参数定义

| 参数名 | 说明 | 使用场景 |
|--------|------|----------|
| `latitude` | 用户自己的纬度 | 所有查询都传递 |
| `longitude` | 用户自己的经度 | 所有查询都传递 |
| `searchLatitude` | 查询目标的纬度 | 附近查询和距离筛选时传递 |
| `searchLongitude` | 查询目标的经度 | 附近查询和距离筛选时传递 |
| `scope` | 查询范围（公里） | 附近查询和距离筛选时传递 |
| `orderBy` | 排序字段 | 最新查询时传递 |
| `orderDirection` | 排序方向 | 最新查询时传递 |

### 查询场景

#### 1. 附近查询 (selectedFilter === 'nearby')
```javascript
{
  current: 1,
  size: 10,
  latitude: 用户纬度,           // 用户自己的位置
  longitude: 用户经度,          // 用户自己的位置
  searchLatitude: 用户纬度,     // 查询的经纬度（当前为用户位置）
  searchLongitude: 用户经度,    // 查询的经纬度（当前为用户位置）
  scope: 10                     // 查询范围10公里
}
```

#### 2. 最新查询 (selectedFilter === 'recent')
```javascript
{
  current: 1,
  size: 10,
  latitude: 用户纬度,           // 用户自己的位置
  longitude: 用户经度,          // 用户自己的位置
  orderBy: 'create_time',       // 按创建时间排序
  orderDirection: 'desc'        // 降序排列
  // 注意：不传 searchLatitude 和 searchLongitude
}
```

#### 3. 距离筛选 (selectedDistance 有值)
```javascript
{
  current: 1,
  size: 10,
  latitude: 用户纬度,           // 用户自己的位置
  longitude: 用户经度,          // 用户自己的位置
  searchLatitude: 用户纬度,     // 查询的经纬度（当前为用户位置）
  searchLongitude: 用户经度,    // 查询的经纬度（当前为用户位置）
  scope: 选择的距离值           // 用户选择的距离范围
}
```

## 业务逻辑说明

### 位置参数的作用

1. **latitude/longitude（用户位置）**：
   - 用于记录用户当前的地理位置
   - 所有查询都会传递，用于后端统计和分析
   - 在最新查询中也传递，便于后端了解用户位置

2. **searchLatitude/searchLongitude（查询位置）**：
   - 用于指定查询的目标位置
   - 只在需要基于位置进行筛选时传递
   - 当前实现中，查询位置与用户位置相同

### 扩展性考虑

这种参数设计为未来功能扩展提供了灵活性：

1. **搜索其他位置**：
   - 可以让用户选择其他位置进行查询
   - 此时 searchLatitude/searchLongitude 为目标位置
   - latitude/longitude 仍为用户实际位置

2. **位置历史记录**：
   - 后端可以基于用户位置进行数据分析
   - 了解用户的活动范围和偏好

3. **个性化推荐**：
   - 基于用户位置提供个性化内容
   - 即使在查询其他位置时也能保持个性化

## 注意事项

1. **位置权限**：确保用户已授权位置权限
2. **网络异常**：处理获取位置失败的情况
3. **参数验证**：后端需要验证位置参数的有效性
4. **性能优化**：避免频繁获取位置信息

## 测试建议

1. 测试附近查询功能，验证 searchLatitude/searchLongitude 参数传递
2. 测试最新查询功能，确认不传递 searchLatitude/searchLongitude
3. 测试距离筛选功能，验证不同距离范围的查询结果
4. 测试位置权限被拒绝的情况
5. 测试网络异常情况下的降级处理
