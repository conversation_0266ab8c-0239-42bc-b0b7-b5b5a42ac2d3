.image-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  background: transparent;
  border-radius: 12rpx;
  padding: 0;
}

.grid-image {
  border-radius: 12rpx;
  object-fit: cover;
  background-color: #f5f5f5;
  transition: all 0.3s ease;
}

.grid-image:active {
  transform: scale(0.98);
  opacity: 0.8;
}

/* 单张图片 */
.grid-image.single {
  width: 100%;
  height: 400rpx;
}

/* 两张图片 */
.grid-image.double {
  width: calc(50% - 6rpx);
  height: 300rpx;
}

/* 多张图片（3-6张） */
.grid-image.multiple {
  width: calc(33.333% - 8rpx);
  height: 200rpx;
}

.image-grid-wrapper {
  padding: 8rpx 0 8rpx 0;
} 