# 机构收藏状态修复说明

## 问题描述

用户是否收藏机构的信息在分页查询和详情查询中没有正确带出来，导致前端无法显示正确的收藏状态。

## 问题原因分析

### 1. 后端问题

1. **Mapper查询错误**：在 `InstitutionMapper.xml` 中，`getFavoriteCount` 方法错误地查询了 `urb_like` 表而不是 `urb_favorite` 表
2. **详情接口缺失收藏状态**：机构详情接口 `getInstitutionDetailWithPosts` 没有查询和返回用户的收藏状态

### 2. 前端问题

1. **数据处理不正确**：前端的 `processInstitutionDetail` 函数没有正确从后端返回的 `institutionStats` 中提取收藏状态
2. **字段映射错误**：直接从根级别查找 `isFavorite` 字段，而实际数据在 `institutionStats` 对象中

## 修复方案

### 1. 后端修复

#### 修复Mapper查询错误

**文件**: `backend/src/main/java/org/springblade/business/institution/mapper/InstitutionMapper.xml`

```xml
<!-- 修复前：错误地查询 urb_like 表 -->
<select id="getFavoriteCount" resultType="java.lang.Integer">
    select count(1) from urb_like where relevancy_id = #{id}
    AND user_id = #{userId}
    AND type = '2'
</select>

<!-- 修复后：正确查询 urb_favorite 表 -->
<select id="getFavoriteCount" resultType="java.lang.Integer">
    select count(1) from urb_favorite where relevancy_id = #{id}
    AND user_id = #{userId}
    AND type = '2'
    AND is_deleted = 0
</select>
```

#### 添加收藏状态查询

**文件**: `backend/src/main/java/org/springblade/miniapp/service/impl/WeChatInstitutionServiceImpl.java`

```java
@Override
public InstitutionVO getInstitutionDetailWithPosts(InstitutionVO institutionvo) {
    // ... 原有代码 ...
    
    // 查询用户对该机构的收藏状态
    Long userId = AuthUtil.getUserId();
    if (userId != null && userId != -1L) {
        Integer favoriteCount = institutionMapper.getFavoriteCount(institutionvo.getId(), userId);
        // 创建机构统计信息
        InstitutionStatsDTO stats = new InstitutionStatsDTO();
        stats.setIsFavorite(favoriteCount != null && favoriteCount > 0);
        institutionVO.setInstitutionStats(stats);
    }
    
    // ... 其余代码 ...
}
```

### 2. 前端修复

#### 修复数据处理逻辑

**文件**: `weapp/stores/institutionDetailStore.js` (以及其他两个同名文件)

```javascript
const processInstitutionDetail = (data) => {
  // 从机构统计信息中获取收藏状态
  const stats = data.institutionStats || {};
  
  return {
    id: data.id,
    name: data.name || '未知机构',
    // ... 其他字段 ...
    
    // 从统计信息中获取收藏和点赞状态
    isFavorite: stats.isFavorite || false,
    isLiked: stats.isLiked || false,
    likeCount: stats.likeCount || 0,
    favoriteCount: stats.favoriteCount || 0,
    viewCount: stats.viewCount || 0,
    
    // ... 其他字段 ...
  };
};
```

## 数据结构说明

### 后端返回的数据结构

```json
{
  "code": 200,
  "data": {
    "id": 123,
    "name": "测试机构",
    "description": "机构描述",
    "address": "机构地址",
    "phone": "联系电话",
    "institutionStats": {
      "isFavorite": true,      // 用户是否收藏了该机构
      "isLiked": false,        // 用户是否点赞了该机构
      "likeCount": 10,         // 总点赞数
      "favoriteCount": 25,     // 总收藏数
      "viewCount": 100,        // 总浏览数
      "distance": 1500         // 距离（米）
    },
    "posts": [...]             // 机构发布的帖子列表
  }
}
```

### 前端处理后的数据结构

```javascript
{
  id: 123,
  name: "测试机构",
  description: "机构描述",
  address: "机构地址",
  phone: "联系电话",
  isFavorite: true,          // 从 institutionStats.isFavorite 提取
  isLiked: false,            // 从 institutionStats.isLiked 提取
  likeCount: 10,             // 从 institutionStats.likeCount 提取
  favoriteCount: 25,         // 从 institutionStats.favoriteCount 提取
  viewCount: 100,            // 从 institutionStats.viewCount 提取
  // ... 其他字段
}
```

## 数据库表结构

### urb_favorite 表（收藏表）

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 主键ID |
| user_id | bigint | 用户ID |
| relevancy_id | bigint | 关联对象ID（机构ID） |
| type | varchar | 收藏类型（'2'表示机构） |
| is_deleted | tinyint | 是否删除（0-未删除，1-已删除） |
| create_time | datetime | 创建时间 |

### urb_like 表（点赞表）

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 主键ID |
| user_id | bigint | 用户ID |
| relevancy_id | bigint | 关联对象ID（机构ID） |
| type | varchar | 点赞类型（'2'表示机构） |
| is_deleted | tinyint | 是否删除（0-未删除，1-已删除） |
| create_time | datetime | 创建时间 |

## 修复效果

### 1. 机构详情页面
- 收藏按钮能正确显示当前收藏状态
- 点击收藏/取消收藏功能正常工作
- 收藏状态实时更新

### 2. 机构列表页面
- 每个机构项显示正确的收藏状态
- 收藏数量统计准确

### 3. 用户收藏列表
- 能正确显示用户收藏的机构
- 收藏状态同步更新

## 测试建议

### 1. 基本功能测试
1. 登录用户，打开机构详情页面
2. 检查收藏按钮是否显示正确状态
3. 点击收藏按钮，验证状态切换
4. 刷新页面，确认状态持久化

### 2. 数据一致性测试
1. 在机构详情页面收藏机构
2. 返回机构列表，检查收藏状态是否同步
3. 查看用户收藏列表，确认机构已添加

### 3. 边界情况测试
1. 未登录用户访问机构详情（应该不显示收藏状态）
2. 网络异常时的收藏操作
3. 并发收藏操作的处理

### 4. 性能测试
1. 大量机构列表的收藏状态查询性能
2. 机构详情页面的加载速度
3. 收藏操作的响应时间

## 相关文件

### 后端文件
- `backend/src/main/java/org/springblade/business/institution/mapper/InstitutionMapper.xml`
- `backend/src/main/java/org/springblade/miniapp/service/impl/WeChatInstitutionServiceImpl.java`
- `backend/src/main/java/org/springblade/business/institution/dto/InstitutionStatsDTO.java`

### 前端文件
- `weapp/stores/institutionDetailStore.js`
- `weapp/pkg_user/pages/user-card/stores/institutionDetailStore.js`
- `weapp/pkg_common/stores/institutionDetailStore.js`
- `weapp/pages/local/institution-detail/institution-detail.js`
- `weapp/pages/local/institution-detail/institution-detail.wxml`
