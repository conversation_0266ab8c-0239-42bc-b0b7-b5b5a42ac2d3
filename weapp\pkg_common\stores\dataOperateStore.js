/**
 * 数据操作 Store
 * 负责点赞、收藏、分享、浏览等用户行为数据的管理
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/31
 */

const { request } = require('../utils/request.js');

/**
 * API 接口配置
 */
const API = {
  // 新的数据操作接口
  toggleLike: '/blade-chat/data-operate/toggle-like',
  toggleFavorite: '/blade-chat/data-operate/toggle-favorite',
  checkLike: '/blade-chat/data-operate/check-like',
  checkFavorite: '/blade-chat/data-operate/check-favorite',
  share: '/blade-chat/data-operate/share',
  view: '/blade-chat/data-operate/view',
  
  // 兼容旧接口
  legacyLike: '/blade-chat/post/like',
  legacyFavorite: '/blade-chat/post/favorite'
};

/**
 * 数据类型常量
 */
const DATA_TYPES = {
  POST: 'post',
  INSTITUTION: 'institution',
  COMMENT: 'comment'
};

/**
 * 切换点赞状态
 * @param {string|number} id - 目标ID
 * @param {string} type - 数据类型 (post/institution/comment)
 * @returns {Promise<{success: boolean, isLiked: boolean, message: string}>}
 */
const toggleLike = async (id, type = DATA_TYPES.POST) => {
  try {
    console.log(`切换点赞状态: ID=${id}, type=${type}`);
    
    const response = await request({
      url: `${API.toggleLike}/${type}/${id}`,
      method: 'POST'
    });

    if (response.code === 200 && response.data) {
      const { currentState, message, action } = response.data;
      
      console.log(`点赞操作成功: ${action}, 当前状态: ${currentState}`);
      
      return {
        success: true,
        isLiked: currentState,
        message: message || (currentState ? '点赞成功' : '取消点赞成功'),
        action
      };
    } else {
      throw new Error(response.msg || '点赞操作失败');
    }
  } catch (error) {
    console.error('切换点赞状态失败:', error);
    return {
      success: false,
      isLiked: false,
      message: error.message || '点赞操作失败，请稍后重试'
    };
  }
};

/**
 * 切换收藏状态
 * @param {string|number} id - 目标ID
 * @param {string} type - 数据类型 (post/institution/comment)
 * @returns {Promise<{success: boolean, isFavorited: boolean, message: string}>}
 */
const toggleFavorite = async (id, type = DATA_TYPES.POST) => {
  try {
    console.log(`切换收藏状态: ID=${id}, type=${type}`);
    
    const response = await request({
      url: `${API.toggleFavorite}/${type}/${id}`,
      method: 'POST'
    });

    if (response.code === 200 && response.data) {
      const { currentState, message, action } = response.data;
      
      console.log(`收藏操作成功: ${action}, 当前状态: ${currentState}`);
      
      return {
        success: true,
        isFavorited: currentState,
        message: message || (currentState ? '收藏成功' : '取消收藏成功'),
        action
      };
    } else {
      throw new Error(response.msg || '收藏操作失败');
    }
  } catch (error) {
    console.error('切换收藏状态失败:', error);
    return {
      success: false,
      isFavorited: false,
      message: error.message || '收藏操作失败，请稍后重试'
    };
  }
};

/**
 * 检查点赞状态
 * @param {string|number} id - 目标ID
 * @param {string} type - 数据类型
 * @returns {Promise<{success: boolean, isLiked: boolean}>}
 */
const checkLikeStatus = async (id, type = DATA_TYPES.POST) => {
  try {
    const response = await request({
      url: `${API.checkLike}/${type}/${id}`,
      method: 'GET'
    });

    if (response.code === 200 && response.data) {
      return {
        success: true,
        isLiked: response.data.isLiked || false
      };
    } else {
      return { success: false, isLiked: false };
    }
  } catch (error) {
    console.error('检查点赞状态失败:', error);
    return { success: false, isLiked: false };
  }
};

/**
 * 检查收藏状态
 * @param {string|number} id - 目标ID
 * @param {string} type - 数据类型
 * @returns {Promise<{success: boolean, isFavorited: boolean}>}
 */
const checkFavoriteStatus = async (id, type = DATA_TYPES.POST) => {
  try {
    const response = await request({
      url: `${API.checkFavorite}/${type}/${id}`,
      method: 'GET'
    });

    if (response.code === 200 && response.data) {
      return {
        success: true,
        isFavorited: response.data.isFavorited || false
      };
    } else {
      return { success: false, isFavorited: false };
    }
  } catch (error) {
    console.error('检查收藏状态失败:', error);
    return { success: false, isFavorited: false };
  }
};

/**
 * 记录分享行为
 * @param {string|number} id - 目标ID
 * @param {string} type - 数据类型
 * @returns {Promise<{success: boolean, message: string}>}
 */
const recordShare = async (id, type = DATA_TYPES.POST) => {
  try {
    const response = await request({
      url: `${API.share}/${type}/${id}`,
      method: 'POST'
    });

    if (response.code === 200) {
      return {
        success: true,
        message: response.data?.message || '分享记录成功'
      };
    } else {
      throw new Error(response.msg || '分享记录失败');
    }
  } catch (error) {
    console.error('记录分享失败:', error);
    return {
      success: false,
      message: error.message || '分享记录失败'
    };
  }
};

/**
 * 记录浏览行为
 * @param {string|number} id - 目标ID
 * @param {string} type - 数据类型
 * @returns {Promise<{success: boolean, message: string}>}
 */
const recordView = async (id, type = DATA_TYPES.POST) => {
  try {
    const response = await request({
      url: `${API.view}/${type}/${id}`,
      method: 'POST'
    });

    if (response.code === 200) {
      return {
        success: true,
        message: response.data?.message || '浏览记录成功'
      };
    } else {
      throw new Error(response.msg || '浏览记录失败');
    }
  } catch (error) {
    console.error('记录浏览失败:', error);
    return {
      success: false,
      message: error.message || '浏览记录失败'
    };
  }
};

/**
 * 批量检查用户状态
 * @param {string|number} id - 目标ID
 * @param {string} type - 数据类型
 * @returns {Promise<{isLiked: boolean, isFavorited: boolean}>}
 */
const checkUserStatus = async (id, type = DATA_TYPES.POST) => {
  try {
    const [likeResult, favoriteResult] = await Promise.all([
      checkLikeStatus(id, type),
      checkFavoriteStatus(id, type)
    ]);

    return {
      isLiked: likeResult.isLiked,
      isFavorited: favoriteResult.isFavorited
    };
  } catch (error) {
    console.error('批量检查用户状态失败:', error);
    return {
      isLiked: false,
      isFavorited: false
    };
  }
};

// 导出模块
module.exports = {
  // 核心操作方法
  toggleLike,
  toggleFavorite,
  checkLikeStatus,
  checkFavoriteStatus,
  recordShare,
  recordView,
  checkUserStatus,
  
  // 常量
  DATA_TYPES,
  
  // API 配置（供其他模块使用）
  API
};
