<view class="product-list">
  <block wx:for="{{products}}" wx:key="index">
    <view class="product-item">
      <input class="product-input" placeholder="请输入商品/服务名称{{index+1}}" value="{{item}}" data-index="{{index}}" bindinput="onInputProduct" />
      <view wx:if="{{products.length > 1}}" class="delete-btn" bindtap="onDeleteProduct" data-index="{{index}}">×</view>
    </view>
  </block>
  <view class="add-btn" bindtap="onAddProduct">+ 添加商品</view>
</view> 