// pkg_user/pages/business-card/business-card.js
const { request } = require('../../../utils/request');

Page({
  data: {
    // 收藏名片列表
    cardList: [],
    // 加载状态
    loading: true,
    // 是否还有更多数据
    hasMore: true,
    // 当前页码
    currentPage: 1,
    // 每页数量
    pageSize: 10,
    // 搜索关键词
    searchKeyword: '',
    // 分类筛选
    selectedCategory: '',
    categories: [
      { id: '', name: '全部' },
      { id: '工作伙伴', name: '工作伙伴' },
      { id: '客户', name: '客户' },
      { id: '朋友', name: '朋友' },
      { id: '其他', name: '其他' }
    ],

    // 管理模式
    isManageMode: false,

    // 卡片展开状态
    expandedCardIds: [], // 存储已展开的卡片ID

    // 编辑弹窗相关
    showEditModal: false,
    editingCard: null,
    editingIndex: -1,
    editCategory: '',
    editRemark: '',
    favoriteCategories: ['工作伙伴', '客户', '朋友', '其他'],
    showAddCategoryInput: false,
    newCategoryName: ''
  },

  onLoad(options) {
    console.log('收藏名片页面加载');
    console.log('初始化展开状态数组:', this.data.expandedCardIds);
    this.loadFavoriteCategories();
    this.loadCardList();
  },

  onShow() {
    // 页面显示时刷新数据
    this.refreshData();
  },

  // 加载收藏名片列表
  async loadCardList(isRefresh = false) {
    if (this.data.loading && !isRefresh) return;

    try {
      this.setData({ loading: true });

      const params = {
        current: isRefresh ? 1 : this.data.currentPage,
        size: this.data.pageSize,
        category: this.data.selectedCategory,
        keyword: this.data.searchKeyword
      };

      console.log('加载收藏名片参数:', params);

      const response = await request({
        url: '/blade-chat/card/favorite/list',
        method: 'GET',
        data: params
      });

      if (response.code === 200 && response.data) {
        const newList = response.data.records || [];
        const cardList = isRefresh ? newList : [...this.data.cardList, ...newList];

        console.log('原始收藏名片数据:', newList);
        if (newList.length > 0) {
          console.log('第一条数据示例:', newList[0]);
          console.log('cardSnapshot内容:', newList[0].cardSnapshot);
        }

        // 格式化数据，解析cardSnapshot
        const formattedList = cardList.map(item => {
          const cardData = this.parseCardSnapshot(item);

          // 优先使用originalCard，然后是cardSnapshot，最后是item本身
          const fullName = item.originalCard?.fullName || cardData.fullName || item.fullName || '未知';
          const jobTitle = item.originalCard?.jobTitle || cardData.jobTitle || item.jobTitle || '未填写';
          const company = item.originalCard?.company || cardData.company || item.company || '未填写';
          const phone = item.originalCard?.phone || cardData.phone || item.phone || '未填写';

          const cardId = item.id || item.cardId;
          const isExpanded = this.data.expandedCardIds.includes(cardId);

          const formattedItem = {
            ...item,
            favoriteTime: this.formatTime(item.createTime),
            // 名片基本信息
            fullName: fullName,
            jobTitle: jobTitle,
            company: company,
            phone: phone,
            avatar: item.originalCard?.avatar || cardData.avatar || item.avatar,
            email: item.originalCard?.email || cardData.email || item.email,
            address: item.originalCard?.address || cardData.address || item.address,
            weixin: item.originalCard?.weixin || cardData.weixin || item.weixin,
            businessProfile: item.originalCard?.businessProfile || cardData.businessProfile || item.businessProfile,
            // 头像文字（用于没有头像时显示）
            avatarText: fullName ? fullName.charAt(0) : '?',
            // 展开状态
            isExpanded: isExpanded,
            // 保留原始数据用于详情页
            cardData: cardData
          };

          console.log(`卡片 ${cardId} 格式化完成，展开状态: ${isExpanded}`);
          return formattedItem;
        });

        this.setData({
          cardList: formattedList,
          hasMore: newList.length >= this.data.pageSize,
          currentPage: isRefresh ? 2 : this.data.currentPage + 1,
          loading: false
        });

        console.log('收藏名片列表加载完成:', formattedList.length);
        console.log('格式化后的数据示例:', formattedList[0]);
      } else {
        throw new Error(response.msg || '加载失败');
      }
    } catch (error) {
      console.error('加载收藏名片列表失败:', error);
      this.setData({ loading: false });

      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    }
  },

  // 加载收藏分类
  async loadFavoriteCategories() {
    try {
      const response = await request({
        url: '/blade-chat/card/favorite/categories',
        method: 'GET'
      });

      if (response.code === 200 && response.data) {
        const categories = response.data.length > 0 ? response.data : ['工作伙伴', '客户', '朋友', '其他'];
        this.setData({
          favoriteCategories: categories,
          categories: [
            { id: '', name: '全部' },
            ...categories.map(cat => ({ id: cat, name: cat }))
          ]
        });
      }
    } catch (error) {
      console.error('加载收藏分类失败:', error);
    }
  },

  // 解析名片快照数据
  parseCardSnapshot(item) {
    let cardData = {};

    // 解析cardSnapshot JSON字符串
    if (item.cardSnapshot) {
      try {
        if (typeof item.cardSnapshot === 'string') {
          cardData = JSON.parse(item.cardSnapshot);
        } else {
          cardData = item.cardSnapshot;
        }
      } catch (error) {
        console.error('解析cardSnapshot失败:', error, item.cardSnapshot);
        cardData = {};
      }
    }

    return cardData;
  },

  // 格式化时间
  formatTime(timeStr) {
    if (!timeStr) return '';
    const date = new Date(timeStr);
    const now = new Date();
    const diff = now - date;
    const days = Math.floor(diff / (24 * 60 * 60 * 1000));

    if (days === 0) {
      return '今天';
    } else if (days === 1) {
      return '昨天';
    } else if (days < 7) {
      return `${days}天前`;
    } else {
      return date.toLocaleDateString();
    }
  },

  // 刷新数据
  async refreshData() {
    await this.loadCardList(true);
  },

  // 加载更多
  async loadMore() {
    if (!this.data.hasMore || this.data.loading) return;
    await this.loadCardList();
  },

  // 搜索
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 执行搜索
  onSearch() {
    this.setData({
      currentPage: 1,
      cardList: []
    });
    this.loadCardList(true);
  },

  // 分类筛选
  onCategoryChange(e) {
    const categoryId = e.currentTarget.dataset.id;
    this.setData({
      selectedCategory: categoryId,
      currentPage: 1,
      cardList: []
    });
    this.loadCardList(true);
  },

  // 点击名片项
  onCardTap(e) {
    if (this.data.isManageMode) return; // 管理模式下不响应点击

    const card = e.currentTarget.dataset.item;
    const cardId = card.id || card.cardId;

    console.log('点击名片展开/收起:', cardId);

    // 切换卡片展开状态
    this.toggleCardExpand(cardId);
  },

  // 切换卡片展开状态
  toggleCardExpand(cardId) {
    if (!cardId) {
      console.error('cardId 为空，无法展开');
      return;
    }

    const expandedCardIds = [...this.data.expandedCardIds];
    const index = expandedCardIds.indexOf(cardId);

    if (index > -1) {
      // 如果已展开，则收起
      expandedCardIds.splice(index, 1);
      console.log('收起卡片:', cardId);
    } else {
      // 如果未展开，则展开
      expandedCardIds.push(cardId);
      console.log('展开卡片:', cardId);
    }

    // 更新展开状态
    this.setData({
      expandedCardIds: expandedCardIds
    });

    // 更新卡片列表中的展开状态
    this.updateCardExpandedState();
  },

  // 更新卡片展开状态
  updateCardExpandedState() {
    const updatedCardList = this.data.cardList.map(item => {
      const cardId = item.id || item.cardId;
      const isExpanded = this.data.expandedCardIds.includes(cardId);
      return {
        ...item,
        isExpanded: isExpanded
      };
    });

    this.setData({
      cardList: updatedCardList
    });
  },

  // 检查卡片是否已展开
  isCardExpanded(cardId) {
    return this.data.expandedCardIds.includes(cardId);
  },

  // 跳转到名片详情页（可以通过长按或其他方式触发）
  onCardLongPress(e) {
    const card = e.currentTarget.dataset.item;
    console.log('长按名片，跳转详情:', card);

    wx.navigateTo({
      url: `/pages/card-library/card-detail?id=${card.cardId || card.id}`
    });
  },

  // 切换管理模式
  onToggleManage() {
    this.setData({
      isManageMode: !this.data.isManageMode
    });
  },

  // 编辑名片
  onEditCard(e) {
    const card = e.currentTarget.dataset.item;
    const index = e.currentTarget.dataset.index;

    console.log('编辑名片数据:', card);

    // 为编辑卡片添加avatarText
    const editingCard = {
      ...card,
      avatarText: card.fullName ? card.fullName.charAt(0) : '?'
    };

    this.setData({
      showEditModal: true,
      editingCard: editingCard,
      editingIndex: index,
      editCategory: card.category || '其他',
      editRemark: card.remark || '',
      showAddCategoryInput: false,
      newCategoryName: ''
    });
  },

  // 删除名片
  onDeleteCard(e) {
    const card = e.currentTarget.dataset.item;
    const index = e.currentTarget.dataset.index;

    wx.showModal({
      title: '确认删除',
      content: `确定要取消收藏"${card.fullName || card.name}"的名片吗？`,
      confirmText: '删除',
      confirmColor: '#ff4757',
      success: (res) => {
        if (res.confirm) {
          this.deleteCard(card, index);
        }
      }
    });
  },

  // 执行删除操作
  async deleteCard(card, index) {
    try {
      const response = await request({
        url: `/blade-chat/card/favorite/${card.cardId || card.id}`,
        method: 'DELETE'
      });

      if (response.code === 200) {
        // 从列表中移除
        const cardList = [...this.data.cardList];
        cardList.splice(index, 1);
        this.setData({ cardList });

        wx.showToast({
          title: '已取消收藏',
          icon: 'success'
        });
      } else {
        throw new Error(response.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除收藏名片失败:', error);
      wx.showToast({
        title: '删除失败，请重试',
        icon: 'none'
      });
    }
  },

  // 去名片库
  onGoToCardLibrary() {
    wx.navigateTo({
      url: '/pages/card-library/card-library'
    });
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.refreshData().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 上拉加载更多
  onReachBottom() {
    this.loadMore();
  },

  // 编辑弹窗相关方法
  onHideEditModal() {
    this.setData({
      showEditModal: false,
      editingCard: null,
      editingIndex: -1,
      editCategory: '',
      editRemark: '',
      showAddCategoryInput: false,
      newCategoryName: ''
    });
  },

  // 分类选择
  onEditCategorySelect(e) {
    const category = e.currentTarget.dataset.category;
    this.setData({ editCategory: category });
  },

  // 显示新建分类输入框
  onShowAddCategory() {
    this.setData({ showAddCategoryInput: true });
  },

  // 取消新建分类
  onCancelAddCategory() {
    this.setData({
      showAddCategoryInput: false,
      newCategoryName: ''
    });
  },

  // 分类名称输入
  onCategoryInput(e) {
    this.setData({ newCategoryName: e.detail.value });
  },

  // 确认新建分类
  onConfirmAddCategory() {
    const categoryName = this.data.newCategoryName.trim();
    if (!categoryName) {
      wx.showToast({
        title: '请输入分类名称',
        icon: 'none'
      });
      return;
    }

    if (this.data.favoriteCategories.includes(categoryName)) {
      wx.showToast({
        title: '分类已存在',
        icon: 'none'
      });
      return;
    }

    const categories = [...this.data.favoriteCategories, categoryName];
    this.setData({
      favoriteCategories: categories,
      editCategory: categoryName,
      showAddCategoryInput: false,
      newCategoryName: ''
    });
  },

  // 备注输入
  onRemarkInput(e) {
    this.setData({ editRemark: e.detail.value });
  },

  // 确认编辑
  async onConfirmEdit() {
    if (!this.data.editingCard) return;

    try {
      const response = await request({
        url: `/blade-chat/card/favorite/${this.data.editingCard.cardId || this.data.editingCard.id}`,
        method: 'PUT',
        data: {
          category: this.data.editCategory,
          remark: this.data.editRemark
        }
      });

      if (response.code === 200) {
        // 更新本地数据
        const cardList = [...this.data.cardList];
        const card = cardList[this.data.editingIndex];
        card.category = this.data.editCategory;
        card.remark = this.data.editRemark;

        this.setData({ cardList });
        this.onHideEditModal();

        wx.showToast({
          title: '修改成功',
          icon: 'success'
        });
      } else {
        throw new Error(response.msg || '修改失败');
      }
    } catch (error) {
      console.error('修改收藏信息失败:', error);
      wx.showToast({
        title: '修改失败，请重试',
        icon: 'none'
      });
    }
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  }
});
