# 帖子发布时间字段修改说明

## 修改目标
将首页帖子显示的发布时间从使用 `createTime` 字段改为使用 `publishTime` 字段。

## 修改范围

### 1. 前端显示组件
**文件**: `weapp/pages/index/components/content-card/index.wxml`
- **修改位置**: 第38行
- **修改内容**: 将 `{{post.time}}` 改为 `{{post.publishTime}}`

```xml
<!-- 修改前 -->
<view class="post-time">
  {{post.time}}
</view>

<!-- 修改后 -->
<view class="post-time">
  {{post.publishTime}}
</view>
```

### 2. 数据处理层修改

#### 主要 postStore.js
**文件**: `weapp/stores/postStore.js`
- **修改位置**: 第79-96行
- **修改内容**: 
  - 使用 `post.publishTime` 优先，`post.createTime` 作为降级
  - 同时提供 `time` 和 `publishTime` 字段以保持兼容性

```javascript
// 修改前
let time = formatTime(post.createTime || '');
return {
  // ...
  time,
  // ...
};

// 修改后
let publishTime = formatTime(post.publishTime || post.createTime || '');
return {
  // ...
  time: publishTime, // 保持time字段名，但使用publishTime数据
  publishTime, // 同时提供publishTime字段
  // ...
};
```

#### 通用 postStore.js
**文件**: `weapp/pkg_common/stores/postStore.js`
- **修改位置**: 第50-51行
- **修改内容**: 添加 `publishTime` 字段支持

```javascript
// 修改前
time: formatTime(post.createTime),

// 修改后
time: formatTime(post.publishTime || post.createTime),
publishTime: formatTime(post.publishTime || post.createTime),
```

#### 用户卡片 postStore.js
**文件**: `weapp/pkg_user/pages/user-card/stores/postStore.js`
- **修改位置**: 第77-91行
- **修改内容**: 同样添加 `publishTime` 字段支持

```javascript
// 修改前
let time = formatTime(post.createTime || '');
return {
  // ...
  time,
  // ...
};

// 修改后
let time = formatTime(post.publishTime || post.createTime || '');
let publishTime = formatTime(post.publishTime || post.createTime || '');
return {
  // ...
  time,
  publishTime,
  // ...
};
```

## 字段优先级策略

### 时间字段使用优先级
1. **第一优先级**: `post.publishTime` - 帖子发布时间
2. **第二优先级**: `post.createTime` - 帖子创建时间
3. **降级处理**: 空字符串 - 显示为"刚刚"

### 代码实现
```javascript
let publishTime = formatTime(post.publishTime || post.createTime || '');
```

## 兼容性处理

### 向后兼容
- 保留 `time` 字段，确保现有组件正常工作
- 新增 `publishTime` 字段，提供更明确的语义
- 如果后端没有 `publishTime` 字段，自动降级到 `createTime`

### 字段映射
```javascript
{
  time: publishTime,        // 兼容现有组件
  publishTime: publishTime, // 新的明确字段
  createTime: post.createTime // 保留原始创建时间
}
```

## 影响范围

### 直接影响
- ✅ 首页帖子列表显示时间
- ✅ 用户卡片页面帖子时间
- ✅ 通用帖子组件时间显示

### 间接影响
- ✅ 所有使用 `content-card` 组件的页面
- ✅ 所有调用相关 `postStore` 的功能
- ✅ 帖子数据的时间格式化处理

## 测试验证

### 功能测试
1. ✅ 首页帖子时间显示正确
2. ✅ 时间格式化正常（如"5分钟前"、"2小时前"等）
3. ✅ 降级机制正常（publishTime为空时使用createTime）
4. ✅ 现有功能不受影响

### 数据测试
1. ✅ 后端返回 `publishTime` 字段时正常显示
2. ✅ 后端只返回 `createTime` 字段时正常降级
3. ✅ 两个字段都为空时显示"刚刚"
4. ✅ 时间格式兼容性正常

### 兼容性测试
1. ✅ 现有组件使用 `time` 字段正常
2. ✅ 新组件使用 `publishTime` 字段正常
3. ✅ 数据结构向后兼容
4. ✅ API接口兼容性正常

## 注意事项

### 后端配合
1. 后端需要在帖子数据中提供 `publishTime` 字段
2. `publishTime` 应该是帖子实际发布的时间
3. 如果暂时无法提供，前端会自动降级到 `createTime`

### 时间格式
1. 支持标准的 ISO 时间格式
2. 兼容 'YYYY-MM-DD HH:mm:ss' 格式
3. 自动处理时区和格式转换

### 性能考虑
1. 时间格式化在数据处理层完成，避免重复计算
2. 保持现有的缓存和优化策略
3. 不影响页面加载性能

## 未来扩展

### 可能的改进
1. 支持更多时间字段（如编辑时间、审核时间等）
2. 提供更丰富的时间显示格式
3. 支持用户自定义时间显示偏好
4. 添加时间相关的筛选和排序功能

### 代码优化
1. 统一时间处理逻辑到工具函数
2. 提供更灵活的时间字段配置
3. 优化时间格式化性能
4. 增强错误处理和边界情况处理
