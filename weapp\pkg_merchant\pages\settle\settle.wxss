.settle-container {
  padding: 40rpx 32rpx 0 32rpx;
  background: #fff;
}
.section-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #666;
  margin: 36rpx 0 18rpx 0;
}
.form-group {
  margin-bottom: 32rpx;
}
.label {
  font-size: 30rpx;
  margin-bottom: 10rpx;
  color: #222;
}
.required {
  color: #ff4d4f;
  margin-left: 2rpx;
}
.input, .textarea {
  width: 100%;
  border: 1rpx solid #f0f0f0;
  border-radius: 24rpx;
  padding: 0 20rpx;
  font-size: 32rpx;
  background: #fafbfc;
  margin-bottom: 2rpx;
  color: #222;
  box-sizing: border-box;
  transition: border-color 0.2s;
  height: 72rpx;
  line-height: 72rpx;
}
.input:focus, .textarea:focus {
  border-color: #ffb3b3;
}
.input::placeholder, .textarea::placeholder {
  color: #c0c4cc;
  font-size: 30rpx;
}
.textarea {
  min-height: 140rpx;
  resize: none;
  line-height: 1.6;
  padding: 16rpx 20rpx;
}
.char-count {
  text-align: right;
  color: #bbb;
  font-size: 24rpx;
}
.phone-row {
  display: flex;
  align-items: center;
}
.code-btn {
  margin-left: 12rpx;
  background: #ff7b7b;
  color: #fff;
  border-radius: 16rpx;
  font-size: 30rpx;
  height: 72rpx;
  line-height: 72rpx;
  padding: 0 32rpx;
  font-weight: 500;
  box-shadow: none;
}
.picker {
  width: 100%;
  border: 1rpx solid #f0f0f0;
  border-radius: 24rpx;
  padding: 0 20rpx;
  font-size: 32rpx;
  background: #fafbfc;
  color: #222;
  box-sizing: border-box;
  transition: border-color 0.2s;
  height: 72rpx;
  line-height: 72rpx;
}
.picker:focus {
  border-color: #ffb3b3;
}
.picker-value {
  color: #c0c4cc;
  font-size: 30rpx;
}
.submit-btn {
  width: 100%;
  margin-top: 48rpx;
  background: #ff7b7b;
  color: #fff;
  border-radius: 32rpx;
  height: 96rpx;
  font-size: 36rpx;
  font-weight: bold;
  box-shadow: none;
  margin-bottom: 40rpx;
} 