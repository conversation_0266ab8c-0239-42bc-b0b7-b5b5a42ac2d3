const GroupApi = require('./services/groupApi');

Page({
  data: {
    categories: [],
    groups: [],
    showQRCode: false,
    currentQRCode: '',
    selectedCategory: '全部', // 当前选中的分类
    loading: false, // 加载状态
    searchKeyword: '', // 搜索关键词
    hasMore: true, // 是否还有更多数据
    currentPage: 1, // 当前页码
    pageSize: 20, // 每页大小
    navBarHeight: 0 // 导航栏高度
  },
  onShowQRCode(e) {
    this.setData({
      showQRCode: true,
      currentQRCode: e.currentTarget.dataset.qr
    });
  },
  onHideQRCode() {
    this.setData({
      showQRCode: false,
      currentQRCode: ''
    });
  },
  onSaveQRCode() {
    const img = this.data.currentQRCode;
    wx.getSetting({
      success: res => {
        if (res.authSetting['scope.writePhotosAlbum']) {
          this.save(img);
        } else {
          wx.authorize({
            scope: 'scope.writePhotosAlbum',
            success: () => this.save(img),
            fail: () => wx.showToast({ title: '请授权保存到相册', icon: 'none' })
          });
        }
      }
    });
  },
  save(img) {
    wx.downloadFile({
      url: img,
      success: res => {
        wx.saveImageToPhotosAlbum({
          filePath: res.tempFilePath,
          success: () => wx.showToast({ title: '保存成功', icon: 'success' }),
          fail: () => wx.showToast({ title: '保存失败', icon: 'none' })
        });
      },
      fail: () => wx.showToast({ title: '下载失败', icon: 'none' })
    });
  },
  /**
   * 页面加载时初始化数据
   */
  onLoad() {
    this.loadCategories();
    this.loadGroups();

    // 获取custom-nav组件实例
    this.customNav = this.selectComponent('#custom-nav');
  },



  /**
   * 导航栏准备完成回调
   */
  onNavReady(e) {
    const { height } = e.detail;
    this.setData({
      navBarHeight: height
    });
  },

  /**
   * 页面滚动事件
   */
  onScroll(e) {
    const scrollTop = e.detail.scrollTop;

    // 同步传递给custom-nav组件处理滚动效果
    if (this.customNav && this.customNav.handleScroll) {
      this.customNav.handleScroll(scrollTop);
    }
  },

  /**
   * 加载分类列表
   */
  async loadCategories() {
    try {
      const response = await GroupApi.getCategoryList();
      const categories = response.data.records || [];

      // 添加"全部"分类到第一位
      const allCategories = [
        {
          id: 0,
          categoryName: '全部',
          categoryImage: '/assets/icons/discover.png'
        },
        ...categories
      ];

      this.setData({
        categories: allCategories.map(item => ({
          id: item.id,
          name: item.categoryName,
          icon: item.categoryImage || '/assets/icons/discover.png'
        }))
      });
    } catch (error) {
      console.error('加载分类失败:', error);
      // 使用默认分类数据
      this.setData({
        categories: [
          { id: 0, name: '全部', icon: '/assets/icons/discover.png' }
        ]
      });
      wx.showToast({
        title: '加载分类失败',
        icon: 'none'
      });
    }
  },

  /**
   * 加载群组列表
   */
  async loadGroups(reset = true) {
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      const params = {
        current: reset ? 1 : this.data.currentPage,
        size: this.data.pageSize
      };

      // 如果选中的不是"全部"分类，添加分类筛选
      if (this.data.selectedCategory !== '全部') {
      // 传groupType
      // 通过名字找到正确的groupType
      const category = this.data.categories.find(item => item.name === this.data.selectedCategory);
      params.groupType = category.id;
      }
      const response = await GroupApi.getGroupListByCategory(params);
      const newGroups = response.data.records || [];

      // 处理群组数据格式
      const processedGroups = newGroups.map(item => ({
        id: item.id,
        title: item.groupName,
        desc: item.groupDesc || '欢迎加入群聊',
        icon: item.groupImage,
        qr: item.groupWeixin,
        subtitle: item.groupName ? item.groupName.slice(0, 2) : '群'
      }));

      this.setData({
        groups: reset ? processedGroups : [...this.data.groups, ...processedGroups],
        currentPage: reset ? 2 : this.data.currentPage + 1,
        hasMore: newGroups.length === this.data.pageSize,
        loading: false
      });
    } catch (error) {
      console.error('加载群组失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: '加载群组失败',
        icon: 'none'
      });
    }
  },

  /**
   * 分类点击事件
   */
  onCategoryTap(e) {
    const categoryName = e.currentTarget.dataset.name;
    if (categoryName === this.data.selectedCategory) return;

    this.setData({
      selectedCategory: categoryName,
      currentPage: 1,
      hasMore: true
    });

    this.loadGroups(true);
  },

  /**
   * 搜索功能
   */
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  /**
   * 执行搜索
   */
  async onSearch() {
    const keyword = this.data.searchKeyword.trim();
    if (!keyword) {
      this.loadGroups(true);
      return;
    }

    this.setData({ loading: true });

    try {
      const params = {
        keyword,
        current: 1,
        size: this.data.pageSize
      };

      if (this.data.selectedCategory !== '全部') {
        params.groupType = this.data.selectedCategory;
      }

      const response = await GroupApi.searchGroups(params);
      const groups = response.data.records || [];

      const processedGroups = groups.map(item => ({
        id: item.id,
        title: item.groupName,
        desc: item.groupDesc || '欢迎加入群聊',
        icon: item.groupImage,
        qr: item.groupWeixin,
        subtitle: item.groupName ? item.groupName.slice(0, 2) : '群'
      }));

      this.setData({
        groups: processedGroups,
        currentPage: 2,
        hasMore: groups.length === this.data.pageSize,
        loading: false
      });
    } catch (error) {
      console.error('搜索失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: '搜索失败',
        icon: 'none'
      });
    }
  },

  /**
   * 页面触底加载更多
   */
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadGroups(false);
    }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadCategories();
    this.loadGroups(true);
    wx.stopPullDownRefresh();
  },

  stopPropagation() {}
}); 
