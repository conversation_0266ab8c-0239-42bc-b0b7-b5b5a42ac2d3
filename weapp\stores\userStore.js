// stores/userStore.js
const { request } = require('../utils/request');

/**
 * 用户数据存储服务
 */
class UserStore {
  constructor() {
    this.userInfo = null;
    this.walletBalance = 0;
    this.points = 0;
  }

  /**
   * 获取用户信息
   */
  async getUserInfo() {
    try {
      if (this.userInfo) {
        return this.userInfo;
      }

      const result = await request({
        url: '/blade-chat/user/info',
        method: 'GET'
      });

      this.userInfo = result;
      return this.userInfo;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户钱包余额
   */
  async getUserWalletBalance() {
    try {
      const result = await request({
        url: '/blade-chat/user/balance/wallet',
        method: 'GET'
      });

      this.walletBalance = parseFloat(result) || 0;
      return this.walletBalance;
    } catch (error) {
      console.error('获取钱包余额失败:', error);
      return 0;
    }
  }

  /**
   * 获取用户积分
   */
  async getUserPoints() {
    try {
      const result = await request({
        url: '/blade-chat/user/points',
        method: 'GET'
      });

      this.points = parseInt(result) || 0;
      return this.points;
    } catch (error) {
      console.error('获取用户积分失败:', error);
      return 0;
    }
  }

  /**
   * 获取用户统计信息
   */
  async getUserStats() {
    try {
      const stats = await request({
        url: '/blade-chat/user/stats',
        method: 'GET'
      });

      this.walletBalance = parseFloat(stats.walletBalance) || 0;
      this.points = parseInt(stats.points) || 0;
      return stats;
    } catch (error) {
      console.error('获取用户统计失败:', error);
      return {
        walletBalance: 0,
        points: 0,
        weeklyPosts: 0
      };
    }
  }

  /**
   * 更新用户信息
   */
  async updateUserInfo(userInfo) {
    try {
      await request({
        url: '/blade-chat/user/update',
        method: 'POST',
        data: userInfo
      });

      this.userInfo = { ...this.userInfo, ...userInfo };
      return this.userInfo;
    } catch (error) {
      console.error('更新用户信息失败:', error);
      throw error;
    }
  }

  /**
   * 刷新用户数据
   */
  async refreshUserData() {
    try {
      const [userInfo, stats] = await Promise.all([
        this.getUserInfo(),
        this.getUserStats()
      ]);

      return {
        userInfo,
        stats
      };
    } catch (error) {
      console.error('刷新用户数据失败:', error);
      throw error;
    }
  }

  /**
   * 清除用户数据缓存
   */
  clearUserData() {
    this.userInfo = null;
    this.walletBalance = 0;
    this.points = 0;
  }

  /**
   * 检查登录状态
   */
  isLoggedIn() {
    const token = wx.getStorageSync('token');
    return !!token;
  }

  /**
   * 退出登录
   */
  logout() {
    wx.removeStorageSync('token');
    wx.removeStorageSync('userInfo');
    this.clearUserData();
  }

  /**
   * 获取余额变动记录
   */
  async getBalanceRecords(page = 1, size = 20) {
    try {
      const result = await request({
        url: '/blade-chat/user/balance/records',
        method: 'GET',
        data: {
          current: page,
          size: size
        }
      });

      return result;
    } catch (error) {
      console.error('获取余额记录失败:', error);
      throw error;
    }
  }

  /**
   * 获取充值记录
   */
  async getRechargeRecords(page = 1, size = 20) {
    try {
      const result = await request({
        url: '/blade-chat/user/recharge/records',
        method: 'GET',
        data: {
          current: page,
          size: size
        }
      });

      return result;
    } catch (error) {
      console.error('获取充值记录失败:', error);
      throw error;
    }
  }
}

// 创建单例
const userStore = new UserStore();

module.exports = userStore;
