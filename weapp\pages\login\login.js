const { login, getUserInfo } = require('../../utils/auth');
const { request } = require('../../utils/request');

Page({
  data: {
    loading: false,
    checked: false,
    // 静默邀请信息
    hasPendingInvite: false,
    pendingInviteInfo: null
  },

  onLoad(options) {
    // 检查是否已登录
    const userInfo = getUserInfo();
    if (userInfo) {
      this.navigateBack();
      return;
    }

    // 检查是否有邀请码参数（扫码进入）
    if (options.inviteCode) {
      this.handleInviteCodeFromUrl(options);
    }

    // 检查是否有待处理的邀请信息
    this.checkPendingInvite();
  },

  /**
   * 处理URL中的邀请码参数
   */
  handleInviteCodeFromUrl(options) {
    try {
      const inviteManager = require('../../utils/inviteManager');
      const inviteInfo = {
        inviteCode: options.inviteCode,
        inviterUserId: options.from || options.inviterUserId,
        source: 'qrcode_scan',
        scene: options.scene,
        path: options.path,
        timestamp: Date.now()
      };

      // 保存邀请信息，等待登录时处理
      const saved = inviteManager.saveInviteInfo(inviteInfo);

      if (saved) {
        this.setData({
          hasPendingInvite: true,
          pendingInviteInfo: inviteInfo
        });

        console.log('邀请信息已保存，等待用户登录后处理:', inviteInfo);

        // 显示友好提示
        wx.showToast({
          title: '检测到邀请信息，登录后自动生效',
          icon: 'none',
          duration: 2000
        });
      }
    } catch (error) {
      console.error('处理邀请码参数失败:', error);
    }
  },

  /**
   * 检查是否有待处理的邀请信息
   */
  checkPendingInvite() {
    try {
      const inviteManager = require('../../utils/inviteManager');
      const pendingInvite = inviteManager.getPendingInvite();

      if (pendingInvite) {
        console.log('发现待处理的邀请信息:', pendingInvite);

        this.setData({
          hasPendingInvite: true,
          pendingInviteInfo: pendingInvite
        });

        // 显示邀请提示
        wx.showToast({
          title: '检测到邀请信息，登录后自动生效',
          icon: 'none',
          duration: 2000
        });
      }
    } catch (error) {
      console.error('检查邀请信息失败:', error);
    }
  },

  // 处理勾选协议
  onCheckChange(e) {
    this.setData({ checked: e.detail.value.length > 0 });
  },

  // 跳转协议页面
  onProtocolTap(e) {
    const type = e.currentTarget.dataset.type;
    wx.navigateTo({
      url: `/pages/login/protocol/protocol?type=${type}`
    });
  },

  // 处理登录
  async handleLogin() {
    if (this.data.loading) return;
    if (!this.data.checked) {
      wx.showToast({
        title: '请先同意协议',
        icon: 'none'
      });
      return;
    }

    this.setData({ loading: true });

    try {
      // 执行登录
      const loginResult = await login();
      console.log('登录成功:', loginResult);

      // 获取用户信息
      const userInfo = getUserInfo();

      // 静默处理邀请逻辑
      await this.handleInviteAfterLogin(userInfo);

      wx.showToast({
        title: '登录成功',
        icon: 'success'
      });

      // 延迟返回
      setTimeout(() => {
        this.navigateAfterLogin();
      }, 1500);

    } catch (error) {
      console.error('登录失败：', error);
      wx.showToast({
        title: error.message || '登录失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 登录后静默处理邀请逻辑
   */
  async handleInviteAfterLogin(userInfo) {
    try {
      console.log('开始静默处理邀请逻辑');

      const inviteManager = require('../../utils/inviteManager');

      // 处理邀请注册
      const result = await inviteManager.processInviteRegistration(userInfo.id);

      if (result.success && result.hasInvite) {
        console.log('邀请注册处理成功:', result);

        // 延迟显示成功提示，不干扰登录流程
        setTimeout(() => {
          this.showInviteSuccessMessage(result);
        }, 2500);

        // 通知 app.js
        const app = getApp();
        if (app.handleUserLoginSuccess) {
          app.handleUserLoginSuccess(userInfo);
        }

      } else if (result.hasInvite && !result.success) {
        console.log('邀请注册处理失败:', result.message);
        // 静默处理失败，不显示错误提示，避免干扰用户体验
      } else {
        console.log('没有邀请信息需要处理');
      }

    } catch (error) {
      console.error('处理邀请逻辑失败:', error);
      // 静默处理错误，不影响正常登录流程
    }
  },

  /**
   * 显示邀请成功提示
   */
  showInviteSuccessMessage(result) {
    const rewardPoints = result.rewardPoints || 0;
    const message = rewardPoints > 0
      ? `邀请注册成功！您和邀请人都获得了${rewardPoints}积分奖励`
      : '邀请注册成功！';

    wx.showModal({
      title: '邀请成功',
      content: message,
      showCancel: false,
      confirmText: '知道了',
      confirmColor: '#667eea'
    });
  },

  /**
   * 登录后导航
   */
  navigateAfterLogin() {
    const pages = getCurrentPages();
    const prevPage = pages[pages.length - 2];

    // 如果有上一页，则返回并刷新
    if (prevPage) {
      // 调用上一页的刷新方法
      if (typeof prevPage.onRefresh === 'function') {
        prevPage.onRefresh();
      } else if (typeof prevPage.onLoad === 'function') {
        prevPage.onLoad();
      } else if (typeof prevPage.onShow === 'function') {
        prevPage.onShow();
      }
      wx.navigateBack();
    } else {
      // 没有上一页则跳转到首页
      wx.reLaunch({
        url: '/pages/index/index'
      });
    }
  },

  // 返回上一页
  navigateBack() {
    const pages = getCurrentPages();
    if (pages.length > 1) {
      wx.navigateBack();
    } else {
      wx.switchTab({
        url: '/pages/mine/mine'
      });
    }
  },






});