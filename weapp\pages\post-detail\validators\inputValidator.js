/**
 * 输入验证器模块
 */

const { LIMITS, MESSAGES } = require('../config/constants');

/**
 * 基础验证器
 */
class BaseValidator {
  /**
   * 验证必填字段
   * @param {*} value 值
   * @param {string} fieldName 字段名
   * @returns {object} 验证结果
   */
  static validateRequired(value, fieldName) {
    const isValid = value !== null && value !== undefined && value.toString().trim().length > 0;
    return {
      isValid,
      message: isValid ? '' : `${fieldName}不能为空`
    };
  }

  /**
   * 验证字符串长度
   * @param {string} value 值
   * @param {number} maxLength 最大长度
   * @param {number} minLength 最小长度
   * @returns {object} 验证结果
   */
  static validateLength(value, maxLength, minLength = 0) {
    const length = value ? value.toString().length : 0;
    const isValid = length >= minLength && length <= maxLength;
    
    let message = '';
    if (length < minLength) {
      message = `内容至少需要${minLength}个字符`;
    } else if (length > maxLength) {
      message = `内容不能超过${maxLength}个字符`;
    }
    
    return { isValid, message };
  }

  /**
   * 验证数组长度
   * @param {Array} array 数组
   * @param {number} maxLength 最大长度
   * @param {number} minLength 最小长度
   * @returns {object} 验证结果
   */
  static validateArrayLength(array, maxLength, minLength = 0) {
    const length = Array.isArray(array) ? array.length : 0;
    const isValid = length >= minLength && length <= maxLength;
    
    let message = '';
    if (length < minLength) {
      message = `至少需要选择${minLength}项`;
    } else if (length > maxLength) {
      message = `最多只能选择${maxLength}项`;
    }
    
    return { isValid, message };
  }
}

/**
 * 反馈验证器
 */
class FeedbackValidator extends BaseValidator {
  /**
   * 验证反馈内容
   * @param {string} content 反馈内容
   * @returns {object} 验证结果
   */
  static validateContent(content) {
    // 验证必填
    const requiredResult = this.validateRequired(content, '反馈内容');
    if (!requiredResult.isValid) {
      return requiredResult;
    }

    // 验证长度
    const lengthResult = this.validateLength(content, LIMITS.FEEDBACK_CONTENT_MAX, 1);
    if (!lengthResult.isValid) {
      return lengthResult;
    }

    // 验证内容格式
    const trimmedContent = content.trim();
    if (trimmedContent.length === 0) {
      return {
        isValid: false,
        message: MESSAGES.VALIDATION.FEEDBACK_CONTENT_REQUIRED
      };
    }

    return { isValid: true, message: '' };
  }

  /**
   * 验证反馈标签
   * @param {Array} tags 标签数组
   * @returns {object} 验证结果
   */
  static validateTags(tags) {
    const arrayResult = this.validateArrayLength(tags, 5, 1);
    if (!arrayResult.isValid) {
      return {
        isValid: false,
        message: arrayResult.message || MESSAGES.VALIDATION.FEEDBACK_TAG_REQUIRED
      };
    }

    return { isValid: true, message: '' };
  }

  /**
   * 验证完整的反馈数据
   * @param {object} feedbackData 反馈数据
   * @returns {object} 验证结果
   */
  static validateFeedbackData(feedbackData) {
    const { content, tags, postId } = feedbackData;

    // 验证帖子ID
    if (!postId) {
      return {
        isValid: false,
        message: '帖子ID不能为空'
      };
    }

    // 验证内容
    const contentResult = this.validateContent(content);
    if (!contentResult.isValid) {
      return contentResult;
    }

    // 验证标签
    const tagsResult = this.validateTags(tags);
    if (!tagsResult.isValid) {
      return tagsResult;
    }

    return { isValid: true, message: '' };
  }
}

/**
 * 评论验证器
 */
class CommentValidator extends BaseValidator {
  /**
   * 验证评论内容
   * @param {string} content 评论内容
   * @returns {object} 验证结果
   */
  static validateContent(content) {
    // 验证必填
    const requiredResult = this.validateRequired(content, '评论内容');
    if (!requiredResult.isValid) {
      return {
        isValid: false,
        message: MESSAGES.VALIDATION.COMMENT_CONTENT_REQUIRED
      };
    }

    // 验证长度
    const lengthResult = this.validateLength(content, LIMITS.COMMENT_CONTENT_MAX, 1);
    if (!lengthResult.isValid) {
      return lengthResult;
    }

    return { isValid: true, message: '' };
  }

  /**
   * 验证完整的评论数据
   * @param {object} commentData 评论数据
   * @returns {object} 验证结果
   */
  static validateCommentData(commentData) {
    const { content, postId } = commentData;

    // 验证帖子ID
    if (!postId) {
      return {
        isValid: false,
        message: '帖子ID不能为空'
      };
    }

    // 验证内容
    const contentResult = this.validateContent(content);
    if (!contentResult.isValid) {
      return contentResult;
    }

    return { isValid: true, message: '' };
  }
}

/**
 * 回复验证器
 */
class ReplyValidator extends BaseValidator {
  /**
   * 验证回复内容
   * @param {string} content 回复内容
   * @returns {object} 验证结果
   */
  static validateContent(content) {
    // 验证必填
    const requiredResult = this.validateRequired(content, '回复内容');
    if (!requiredResult.isValid) {
      return {
        isValid: false,
        message: MESSAGES.VALIDATION.REPLY_CONTENT_REQUIRED
      };
    }

    // 验证长度
    const lengthResult = this.validateLength(content, LIMITS.REPLY_CONTENT_MAX, 1);
    if (!lengthResult.isValid) {
      return lengthResult;
    }

    return { isValid: true, message: '' };
  }

  /**
   * 验证完整的回复数据
   * @param {object} replyData 回复数据
   * @returns {object} 验证结果
   */
  static validateReplyData(replyData) {
    const { content, postId, parentId } = replyData;

    // 验证帖子ID
    if (!postId) {
      return {
        isValid: false,
        message: '帖子ID不能为空'
      };
    }

    // 验证父评论ID
    if (!parentId) {
      return {
        isValid: false,
        message: MESSAGES.VALIDATION.PARENT_COMMENT_NOT_FOUND
      };
    }

    // 验证内容
    const contentResult = this.validateContent(content);
    if (!contentResult.isValid) {
      return contentResult;
    }

    return { isValid: true, message: '' };
  }
}

/**
 * 举报验证器
 */
class ReportValidator extends BaseValidator {
  /**
   * 验证举报原因
   * @param {string} reason 举报原因
   * @returns {object} 验证结果
   */
  static validateReason(reason) {
    // 验证必填
    const requiredResult = this.validateRequired(reason, '举报原因');
    if (!requiredResult.isValid) {
      return {
        isValid: false,
        message: MESSAGES.VALIDATION.REPORT_REASON_REQUIRED
      };
    }

    // 验证长度
    const lengthResult = this.validateLength(reason, LIMITS.REPORT_REASON_MAX, 1);
    if (!lengthResult.isValid) {
      return lengthResult;
    }

    return { isValid: true, message: '' };
  }

  /**
   * 验证完整的举报数据
   * @param {object} reportData 举报数据
   * @returns {object} 验证结果
   */
  static validateReportData(reportData) {
    const { reason, targetId, targetType } = reportData;

    // 验证目标ID
    if (!targetId) {
      return {
        isValid: false,
        message: '举报目标不能为空'
      };
    }

    // 验证目标类型
    if (!targetType) {
      return {
        isValid: false,
        message: '举报类型不能为空'
      };
    }

    // 验证原因
    const reasonResult = this.validateReason(reason);
    if (!reasonResult.isValid) {
      return reasonResult;
    }

    return { isValid: true, message: '' };
  }
}

module.exports = {
  BaseValidator,
  FeedbackValidator,
  CommentValidator,
  ReplyValidator,
  ReportValidator
};
