<!--chat.wxml-->
<view class="chat-container">
  <!-- 聊天内容区域 -->
  <scroll-view class="chat-list" scroll-y scroll-into-view="{{scrollToMessage}}" enable-flex>
    <view class="chat-list-content">
      <!-- 消息列表 -->
      <!-- <block wx:for="{{messages}}" wx:key="id">
        <view class="message {{item.type === 'user' ? 'message-user' : 'message-ai'}}" id="msg-{{item.id}}">
          <view class="message-header" wx:if="{{item.type === 'ai'}}">
            <view class="avatar-container">
              <image class="avatar" src="/assets/images/bot-avatar.png" mode="aspectFill"></image>
              <text class="avatar-name">小易</text>
            </view>
          </view>
          <view class="message-content">{{item.content}}</view>
        </view>
      </block> -->
    </view>
  </scroll-view>

  <!-- 输入区域 -->
  <view class="input-section">
    <view class="input-box">
      <!-- 语音/键盘切换按钮 -->
      <image class="voice-icon {{isVoiceMode ? 'active' : ''}}" 
             src="/assets/images/{{isVoiceMode ? 'keyboard' : 'voice'}}.png" 
             mode="aspectFit" 
             bindtap="toggleInputMode"></image>

      <!-- 文本输入框 -->
      <input class="chat-input" 
             wx:if="{{!isVoiceMode}}"
             value="{{inputValue}}" 
             bindinput="onInput" 
             bindconfirm="sendMessage"
             placeholder="和小易对话" 
             confirm-type="send"
             cursor-spacing="20"/>

      <!-- 语音输入按钮 -->
      <view class="voice-button {{recordState === 'recording' ? 'recording' : ''}}"
            wx:if="{{isVoiceMode}}"
            bindtouchstart="startVoiceRecord"
            bindtouchend="endVoiceRecord"
            bindtouchmove="moveVoiceRecord">
        {{recordingTip}}
      </view>

      <!-- 右侧工具栏 -->
      <view class="right-tools">
        <image class="plus-icon" src="/assets/images/plus.png" mode="aspectFit" bindtap="onPlusTap"></image>
        <image class="send-icon" 
               wx:if="{{!isVoiceMode && inputValue.length > 0}}"
               src="/assets/images/send.png" 
               mode="aspectFit" 
               bindtap="sendMessage"></image>
      </view>
    </view>
  </view>
</view> 