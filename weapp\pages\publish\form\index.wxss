.form-container {
  padding: 40rpx 24rpx;
  background: #f7f8fa;
  min-height: 100vh;
}
.form-header {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 32rpx;
  color: #333;
}
.form-section {
  margin-bottom: 32rpx;
}
.form-input, .form-textarea {
  width: 100%;
  padding: 24rpx;
  font-size: 28rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  background: #fff;
  box-sizing: border-box;
}
.form-textarea {
  min-height: 180rpx;
  resize: none;
}
.form-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(to right, #ff6b6b, #ff8585);
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 44rpx;
  text-align: center;
  box-shadow: 0 4rpx 16rpx #ff6b6b33;
  transition: background 0.2s, transform 0.2s;
}
.form-btn:active {
  background: linear-gradient(to right, #ff8585, #ff6b6b);
  opacity: 0.9;
  transform: scale(0.98);
} 