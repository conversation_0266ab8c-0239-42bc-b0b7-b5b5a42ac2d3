// pages/map/map.js
const { MapApi, MapUtils } = require('./services/mapApi');
const { getPostDetail } = require('../../stores/postStore');
const { getCategoryList } = require('../../stores/categoryStore');
// 引入标记诊断工具
const MarkerDiagnostic = require('./markerDiagnostic');

/**
 * 地图页面 - 重构版本
 * 采用模块化设计，清晰的状态管理和事件处理
 */
Page({
  data: {
    // === 地图核心状态 ===
    map: {
      latitude: 39.908823,
      longitude: 116.397470,
      scale: 15, // 初始缩放级别设为4，对应50公里搜索半径
      markers: [],
      initialized: false
    },

    // === 位置状态 ===
    location: {
      user: null,
      search: null,
      ready: false
    },

    // === 数据状态 ===
    data: {
      posts: [],
      lastUpdate: null,
      searchRadius: 50000, // 初始化为50公里
      searchRadiusText: '50.0km'
    },

    // === 区域管理 ===
    region: {
      current: null,
      lastLoaded: null,
      changeThreshold: 0.001,
      loadHistory: []
    },

    // === 筛选状态 ===
    filter: {
      category: '',
      keyword: '',
      selectedCategoryId: '',
      selectedCategoryName: '全部'
    },

    // === 分类管理 ===
    category: {
      list: [],
      loaded: false,
      showFilter: false,
      filterClosing: false
    },

    // === 弹窗状态 ===
    modal: {
      showPostDetail: false,
      selectedPost: null,
      showPostList: false,
      postList: [],
      postListClosing: false
    },

    // === UI状态 ===
    ui: {
      showSearchInfo: false,
      formattedSearchLatitude: '',
      formattedSearchLongitude: '',
      scaleRangeText: '片区级'
    }
  },

  // === 生命周期方法 ===
  onLoad(options) {
    console.log('地图页面加载', options);
    this.initializeApp();
  },

  onReady() {
    console.log('地图组件准备完成');
    this.mapCtx = wx.createMapContext('map', this);

    // 初始化缩放级别显示
    const initialScaleRangeText = this.getScaleRangeText(this.data.map.scale);

    this.setData({
      'map.initialized': true,
      'ui.scaleRangeText': initialScaleRangeText
    });

    // 延迟初始化，确保地图组件完全准备好
    setTimeout(() => {
      this.initializeLocation();

      // 延迟运行标记诊断，确保数据加载完成
      setTimeout(() => {
        this.runMarkerDiagnostic();
      }, 2000);
    }, 300);
  },

  onUnload() {
    this.cleanup();
  },

  // === 初始化方法 ===
  initializeApp() {
    this.initializeTimers();
    this.initializeState();
  },

  initializeTimers() {
    this.timers = {
      regionChange: null,
      search: null,
      loadData: null
    };
    this.lastLoadTime = 0;
    this.debounceDelay = 500; // 减少防抖延迟到500毫秒，提高响应速度
  },

  initializeState() {
    // 状态已在data中初始化，这里可以添加额外的初始化逻辑
    console.log('应用状态初始化完成');
  },

  async initializeLocation() {
    console.log('开始初始化位置');
    try {
      await this.getUserLocation();
      await this.loadInitialData();
    } catch (error) {
      console.error('位置初始化失败:', error);
      this.handleLocationError(error);
    }
  },

  cleanup() {
    this.clearAllTimers();
    console.log('页面清理完成');
  },

  clearAllTimers() {
    Object.values(this.timers || {}).forEach(timer => {
      if (timer) clearTimeout(timer);
    });
  },

  // === 位置服务 ===
  async getUserLocation() {
    return new Promise((resolve, reject) => {
      wx.getLocation({
        type: 'gcj02',
        altitude: true,
        success: (res) => {
          console.log('获取位置成功:', res);

          const userLocation = {
            latitude: res.latitude,
            longitude: res.longitude,
            accuracy: res.accuracy,
            altitude: res.altitude,
            timestamp: Date.now()
          };

          this.updateLocationState(userLocation);
          resolve(userLocation);
        },
        fail: (error) => {
          console.error('获取位置失败:', error);
          reject(error);
        }
      });
    });
  },

  updateLocationState(userLocation) {
    const searchLocation = {
      latitude: userLocation.latitude,
      longitude: userLocation.longitude
    };

    this.setData({
      'map.latitude': userLocation.latitude,
      'map.longitude': userLocation.longitude,
      'location.user': userLocation,
      'location.search': searchLocation,
      'location.ready': true,
      'ui.formattedSearchLatitude': userLocation.latitude.toFixed(4),
      'ui.formattedSearchLongitude': userLocation.longitude.toFixed(4)
    });
  },

  handleLocationError(error) {
    console.error('位置服务错误:', error);
    
    const isAuthError = error.errMsg && error.errMsg.includes('auth deny');
    const title = isAuthError ? '需要位置权限' : '位置获取失败';
    const content = isAuthError 
      ? '请允许访问您的位置信息，以便显示附近的帖子。'
      : '无法获取您的位置信息，将使用默认位置（北京）显示附近内容。';

    wx.showModal({
      title,
      content,
      confirmText: '去设置',
      cancelText: '使用默认',
      success: (modalRes) => {
        if (modalRes.confirm) {
          this.openLocationSettings();
        } else {
          this.useDefaultLocation();
        }
      }
    });
  },

  openLocationSettings() {
    wx.openSetting({
      success: (settingRes) => {
        if (settingRes.authSetting['scope.userLocation']) {
          this.getUserLocation().catch(err => {
            console.error('重新获取位置失败:', err);
            this.useDefaultLocation();
          });
        }
      }
    });
  },

  useDefaultLocation() {
    const defaultLocation = {
      latitude: this.data.map.latitude,
      longitude: this.data.map.longitude,
      accuracy: 0,
      altitude: 0,
      timestamp: Date.now()
    };

    this.updateLocationState(defaultLocation);
    this.loadInitialData();
  },

  // === 数据加载服务 ===
  async loadInitialData() {
    if (!this.data.location.ready) {
      console.warn('位置未准备好，无法加载数据');
      return;
    }

    try {
      // 确保地图中心点位置与用户位置同步
      const userLocation = this.data.location.user;
      if (userLocation && userLocation.latitude && userLocation.longitude) {
        this.setData({
          'map.latitude': userLocation.latitude,
          'map.longitude': userLocation.longitude
        });
        console.log('初始化时同步地图中心点到用户位置:', {
          lat: userLocation.latitude.toFixed(6),
          lng: userLocation.longitude.toFixed(6)
        });
      }

      await this.loadNearbyPosts();
    } catch (error) {
      console.error('初始数据加载失败:', error);
      this.handleDataLoadError(error);
    }
  },

  async loadNearbyPosts() {
    const location = this.data.location.search || this.data.location.user;
    if (!location) {
      throw new Error('位置信息不可用');
    }

    // 固定使用50公里搜索半径
    const radius = 50000; // 50公里
    const params = this.buildSearchParams(location, radius);

    console.log('加载附近帖子 (固定50公里范围):', params);

    const result = await MapApi.getNearbyPosts(params);

    if (result && result.data && result.data.records) {
      const posts = this.processPostsData(result.data.records);
      const markers = this.generateMarkers(posts);

      this.updateDataState(posts, markers, radius, location);

      console.log(`加载了 ${posts.length} 个附近帖子 (50公里范围)`);
      return posts;
    } else {
      console.warn('API返回空数据');
      return [];
    }
  },

  buildSearchParams(location, radius) {
    return {
      latitude: location.latitude,
      longitude: location.longitude,
      searchLatitude: location.latitude,
      searchLongitude: location.longitude,
      scope: Math.round(radius / 1000),
      current: 1,
      size: 100,
      category: this.data.filter.category,
      keyword: this.data.filter.keyword
    };
  },

  updateDataState(posts, markers, radius, location) {
    // 验证标记数据
    const validatedMarkers = this.validateMarkers(markers);

    const currentRegion = {
      centerLocation: location,
      scale: this.data.map.scale,
      timestamp: Date.now()
    };

    console.log(`updateDataState: 更新 ${posts.length} 个帖子, ${validatedMarkers.length} 个标记`);

    this.setData({
      'data.posts': posts,
      'map.markers': validatedMarkers,
      'map.latitude': location.latitude,
      'map.longitude': location.longitude,
      'data.searchRadius': radius,
      'data.searchRadiusText': this.formatRadius(radius),
      'data.lastUpdate': Date.now(),
      'region.current': currentRegion,
      'region.lastLoaded': currentRegion
    });

    this.updateSearchInfo(radius);

    // 延迟检查标记状态，确保地图组件已更新
    setTimeout(() => {
      this.checkAndFixMarkers();
    }, 500);
  },

  /**
   * 更新数据状态但不改变地图中心位置
   * 用于用户移动地图时的数据更新
   */
  updateDataStateWithoutMapCenter(posts, markers, radius, location) {
    // 验证标记数据
    const validatedMarkers = this.validateMarkers(markers);

    const currentRegion = {
      centerLocation: location,
      scale: this.data.map.scale,
      timestamp: Date.now()
    };

    console.log(`updateDataStateWithoutMapCenter: 更新 ${posts.length} 个帖子, ${validatedMarkers.length} 个标记`);

    // 只更新数据和标记，不改变地图的中心位置
    // 注意：这里不更新 map.latitude 和 map.longitude，保持用户当前的视图位置
    this.setData({
      'data.posts': posts,
      'map.markers': validatedMarkers,
      'data.searchRadius': radius,
      'data.searchRadiusText': this.formatRadius(radius),
      'data.lastUpdate': Date.now(),
      'region.current': currentRegion,
      'region.lastLoaded': currentRegion,
      // 更新搜索中心显示信息（这是搜索的中心，不是地图显示的中心）
      'ui.formattedSearchLatitude': location.latitude.toFixed(4),
      'ui.formattedSearchLongitude': location.longitude.toFixed(4)
    });

    this.updateSearchInfo(radius);

    // 延迟检查标记状态，确保地图组件已更新
    setTimeout(() => {
      this.checkAndFixMarkers();
    }, 500);
  },

  handleDataLoadError(error) {
    console.error('数据加载错误:', error);
    wx.showToast({
      title: '加载失败，请重试',
      icon: 'none',
      duration: 2000
    });
  },

  // === 数据处理工具 ===

  /**
   * 解析图片列表
   * @param {string|Array} images 图片数据
   * @returns {Array} 图片URL数组
   */
  parseImageList(images) {
    if (!images) return [];

    // 如果已经是数组，直接返回过滤后的结果
    if (Array.isArray(images)) {
      return images.filter(img => img && typeof img === 'string' && img.trim());
    }

    // 如果是字符串，按逗号分割
    if (typeof images === 'string') {
      return images.split(',')
        .map(img => img.trim())
        .filter(img => img);
    }

    // 其他情况返回空数组
    console.warn('无法解析图片数据:', images);
    return [];
  },
  processPostsData(rawPosts) {
    if (!rawPosts || !Array.isArray(rawPosts)) {
      console.warn('processPostsData: 无效的原始数据', rawPosts);
      return [];
    }

    return rawPosts.map((post, index) => {
      // 验证必要字段
      if (!post || !post.id) {
        console.warn(`processPostsData: 帖子数据不完整 (索引 ${index}):`, post);
        return null;
      }

      // 确保图片数组存在并正确解析
      const imageList = this.parseImageList(post.images);

      // 安全获取分类名称
      let categoryName = '其他';
      let title = '无标题';

      if (post.category && post.category.name) {
        categoryName = post.category.name;
        title = post.category.name;
      } else if (post.categoryName) {
        categoryName = post.categoryName;
        title = post.categoryName;
      } else if (typeof post.category === 'string') {
        categoryName = post.category;
        title = post.category;
      }

      // 如果有内容标题，优先使用内容标题
      if (post.title && post.title.trim()) {
        title = post.title;
      }

      const processedPost = {
        ...post,
        id: post.id,
        title: title,
        content: post.content || '',
        images: imageList,
        firstImage: imageList.length > 0 ? imageList[0] : '',
        location: post.location || post.address || '位置未知',
        latitude: parseFloat(post.latitude) || 0,
        longitude: parseFloat(post.longitude) || 0,
        distance: post.distance || post.mapDistance || 0,
        distanceText: this.formatDistance(post.distance || post.mapDistance || 0),
        createTime: post.createTime,
        publishTime: post.publishTime,
        updateTime: post.updateTime,
        // 格式化的时间显示
        formattedCreateTime: this.formatTime(post.createTime),
        formattedPublishTime: this.formatTime(post.publishTime),
        formattedUpdateTime: this.formatTime(post.updateTime),
        categoryName: categoryName,
        categoryId: post.categoryId,
        // 保留原始的 category 对象，以便兼容不同的访问方式
        category: post.category || { name: categoryName },
        // 用户信息
        authorName: post.user?.nickname || post.authorName || '匿名用户',
        authorAvatar: post.user?.avatar || '',
        authorGender: post.user?.gender || '',
        authorSignature: post.user?.signature || '',
        // 统计信息
        viewCount: post.stats?.viewCount || post.viewCount || 0,
        likeCount: post.stats?.likeCount || post.likeCount || 0,
        commentCount: post.stats?.feedbackCount || post.commentCount || 0,
        favoriteCount: post.stats?.favoriteCount || 0,
        // 用户交互状态
        isLiked: post.stats?.isLiked || false,
        isFavorite: post.stats?.isFavorite || false,
        // 联系信息
        contactType: post.contactType || '',
        contactPhone: post.contactPhone || '',
        contactName: post.contactName || '',
        // 状态信息
        publishStatus: post.publishStatus || '',
        auditStatus: post.auditStatus || '',
        auditRemark: post.auditRemark || '',
        auditTime: post.auditTime || '',
        timeStatus: post.timeStatus || '',
        businessType: post.businessType || 'normal',
        isAnonymity: post.isAnonymity === '1',
        completed: post.completed || 0,
        top: post.top === '1',
        // 标签信息
        tags: post.tags || [],
        tagList: post.tagList || [],
        // 地理位置信息
        address: post.address || '',
        geoLocation: post.geoLocation || '',
        // 特殊业务类型数据
        carpool: post.carpool || {},
        jobOffer: post.jobOffer || {},
        jobSeeking: post.jobSeeking || {}
      };

      return processedPost;
    }).filter(post => post !== null); // 过滤掉无效的帖子
  },

  generateMarkers(posts) {
    if (!posts || !Array.isArray(posts)) {
      console.warn('generateMarkers: 无效的帖子数据', posts);
      return [];
    }

    const markers = posts.map((post, index) => {
      // 验证必要的字段
      if (!post || !post.id || typeof post.latitude !== 'number' || typeof post.longitude !== 'number') {
        console.warn(`generateMarkers: 帖子数据不完整 (索引 ${index}):`, post);
        return null;
      }

      // 获取分类名称，用于图标选择
      let categoryName = '其他';
      if (post.category && post.category.name) {
        categoryName = post.category.name;
      } else if (post.categoryName) {
        categoryName = post.categoryName;
      } else if (typeof post.category === 'string') {
        categoryName = post.category;
      }

      const marker = {
        id: post.id,
        latitude: post.latitude,
        longitude: post.longitude,
        iconPath: this.getMarkerIcon(categoryName),
        width: 32,
        height: 32,
        callout: {
          content: this.formatMarkerCallout(post),
          fontSize: 13,
          borderRadius: 8,
          bgColor: '#ffffff',
          color: '#333333',
          padding: 10,
          display: 'ALWAYS',
          textAlign: 'left'
        }
      };

      return marker;
    }).filter(marker => marker !== null); // 过滤掉无效的标记

    console.log(`generateMarkers: 生成了 ${markers.length} 个标记 (原始数据 ${posts.length} 个)`);

    // 如果生成的标记数量与原始数据不匹配，记录警告
    if (markers.length !== posts.length) {
      console.warn(`generateMarkers: 标记数量不匹配! 生成 ${markers.length} 个，原始 ${posts.length} 个`);
    }

    return markers;
  },

  /**
   * 验证标记数据的完整性
   * @param {Array} markers 标记数组
   * @returns {Array} 验证后的标记数组
   */
  validateMarkers(markers) {
    if (!markers || !Array.isArray(markers)) {
      console.warn('validateMarkers: 无效的标记数据', markers);
      return [];
    }

    const validMarkers = markers.filter((marker, index) => {
      // 检查必要字段
      if (!marker || typeof marker !== 'object') {
        console.warn(`validateMarkers: 标记不是对象 (索引 ${index}):`, marker);
        return false;
      }

      if (!marker.id) {
        console.warn(`validateMarkers: 标记缺少ID (索引 ${index}):`, marker);
        return false;
      }

      if (typeof marker.latitude !== 'number' || isNaN(marker.latitude)) {
        console.warn(`validateMarkers: 标记纬度无效 (索引 ${index}):`, marker);
        return false;
      }

      if (typeof marker.longitude !== 'number' || isNaN(marker.longitude)) {
        console.warn(`validateMarkers: 标记经度无效 (索引 ${index}):`, marker);
        return false;
      }

      if (!marker.iconPath) {
        console.warn(`validateMarkers: 标记缺少图标路径 (索引 ${index}):`, marker);
        return false;
      }

      // 检查callout内容
      if (marker.callout && !marker.callout.content) {
        console.warn(`validateMarkers: 标记气泡内容为空 (索引 ${index}):`, marker);
        // 不返回false，因为可以修复
        marker.callout.content = '点击查看详情';
      }

      return true;
    });

    if (validMarkers.length !== markers.length) {
      console.warn(`validateMarkers: 过滤了 ${markers.length - validMarkers.length} 个无效标记`);
    }

    return validMarkers;
  },

  // 格式化 marker 气泡内容 - 显示简短的分类和内容
  formatMarkerCallout(post) {
    // 获取分类名称，兼容不同的数据结构
    let category = '其他';
    if (post.category && post.category.name) {
      category = post.category.name;
    } else if (post.categoryName) {
      category = post.categoryName;
    } else if (typeof post.category === 'string') {
      category = post.category;
    }

    const shortCategory = category.length > 4 ? category.substring(0, 4) : category;

    // 获取内容
    const content = post.content || post.title || post.description || '';

    // 限制内容长度，保持气泡简洁
    const maxContentLength = 25;
    let shortContent = content.length > maxContentLength
      ? content.substring(0, maxContentLength) + '...'
      : content;

    // 如果内容为空，显示默认文本
    if (!shortContent.trim()) {
      shortContent = '点击查看详情';
    }

    // 格式化显示：[分类] 内容
    return `[${shortCategory}] ${shortContent}`;
  },

  getMarkerIcon(categoryName) {
    const iconMap = {
      '求助': '/assets/images/markers/help.png',
      '交友': '/assets/images/markers/friend.png',
      '活动': '/assets/images/markers/activity.png',
      '分享': '/assets/images/markers/share.png',
      '其他': '/assets/images/markers/other.png'
    };
    return iconMap[categoryName] || iconMap['其他'];
  },

  formatDistance(distance) {
    if (distance < 1000) {
      return `${Math.round(distance)}m`;
    } else {
      return `${(distance / 1000).toFixed(1)}km`;
    }
  },

  formatRadius(radius) {
    if (radius >= 1000) {
      return `${(radius / 1000).toFixed(1)}km`;
    } else {
      return `${radius}m`;
    }
  },

  /**
   * 格式化时间显示
   * @param {string} timeString 时间字符串
   * @returns {string} 格式化后的时间
   */
  formatTime(timeString) {
    if (!timeString) return '';

    try {
      const time = new Date(timeString);
      const now = new Date();
      const diff = now - time;

      // 小于1分钟
      if (diff < 60000) {
        return '刚刚';
      }

      // 小于1小时
      if (diff < 3600000) {
        const minutes = Math.floor(diff / 60000);
        return `${minutes}分钟前`;
      }

      // 小于1天
      if (diff < 86400000) {
        const hours = Math.floor(diff / 3600000);
        return `${hours}小时前`;
      }

      // 小于7天
      if (diff < 604800000) {
        const days = Math.floor(diff / 86400000);
        return `${days}天前`;
      }

      // 超过7天，显示具体日期
      const year = time.getFullYear();
      const month = String(time.getMonth() + 1).padStart(2, '0');
      const day = String(time.getDate()).padStart(2, '0');

      // 如果是今年，不显示年份
      if (year === now.getFullYear()) {
        return `${month}-${day}`;
      } else {
        return `${year}-${month}-${day}`;
      }
    } catch (error) {
      console.warn('时间格式化失败:', error);
      return timeString;
    }
  },

  updateSearchInfo(radius) {
    this.setData({
      'ui.showSearchInfo': true
    });

    // 1秒后隐藏搜索信息
    setTimeout(() => {
      this.setData({
        'ui.showSearchInfo': false
      });
    }, 1000);
  },

  // === 区域变化监听 ===
  onRegionChange(e) {
    const { type, detail } = e;

    if (type === 'begin') {
      this.clearTimer('regionChange');
      console.log('地图开始变化');
    } else if (type === 'end') {
      console.log('地图变化结束，当前区域:', {
        centerLat: detail.centerLocation.latitude.toFixed(6),
        centerLng: detail.centerLocation.longitude.toFixed(6),
        scale: detail.scale.toFixed(1)
      });
      this.handleRegionChangeEnd(detail);
    }
  },

  handleRegionChangeEnd(region) {
    const scaleRangeText = this.getScaleRangeText(region.scale);

    if (!this.isSignificantRegionChange(region)) {
      console.log('区域变化不显著，跳过处理');
      return;
    }

    console.log('地图区域变化 - 处理缩放和中心点同步:', {
      newScale: region.scale,
      newLat: region.centerLocation.latitude.toFixed(6),
      newLng: region.centerLocation.longitude.toFixed(6),
      currentMapLat: this.data.map.latitude.toFixed(6),
      currentMapLng: this.data.map.longitude.toFixed(6)
    });

    // 同步地图中心点位置
    const centerUpdated = this.syncMapCenter(region);

    // 更新缩放级别和UI显示
    const updateData = {
      'ui.scaleRangeText': scaleRangeText
    };

    this.updateScaleIfChanged(updateData, region.scale);
    this.setData(updateData);

    console.log('地图区域变化结束 - 准备加载数据', {
      centerUpdated,
      scaleChanged: this.data.map.scale !== region.scale
    });

    this.updateRegionState(region);

    if (this.shouldLoadNewData(region)) {
      this.debounceLoadRegionData(region);
    }
  },

  isSignificantRegionChange(newRegion) {
    const currentRegion = this.data.region.current;
    if (!currentRegion) return true;

    const latDiff = Math.abs(newRegion.centerLocation.latitude - currentRegion.centerLocation.latitude);
    const lngDiff = Math.abs(newRegion.centerLocation.longitude - currentRegion.centerLocation.longitude);
    const scaleDiff = Math.abs(newRegion.scale - currentRegion.scale);

    // 针对50公里搜索半径优化的阈值
    // 提高位置阈值，减少微小移动的触发
    const locationThreshold = 0.001; // 约100米
    const scaleThreshold = 0.3; // 降低缩放阈值，让缩放更容易触发

    const isSignificant = latDiff > locationThreshold ||
                         lngDiff > locationThreshold ||
                         scaleDiff > scaleThreshold;

    console.log('区域变化检查 (50公里模式):', {
      latDiff: latDiff.toFixed(6),
      lngDiff: lngDiff.toFixed(6),
      scaleDiff: scaleDiff.toFixed(2),
      isSignificant
    });

    return isSignificant;
  },

  updateRegionState(region) {
    // 固定使用50公里搜索半径，不根据缩放级别变化
    const fixedRadius = 50000; // 50公里

    // 只在缩放级别真正变化时才更新
    const updateData = {
      'region.current': region,
      'location.search': {
        latitude: region.centerLocation.latitude,
        longitude: region.centerLocation.longitude
      },
      'data.searchRadius': fixedRadius,
      'data.searchRadiusText': this.formatRadius(fixedRadius),
      'ui.formattedSearchLatitude': region.centerLocation.latitude.toFixed(4),
      'ui.formattedSearchLongitude': region.centerLocation.longitude.toFixed(4)
    };

    this.updateScaleIfChanged(updateData, region.scale);
    this.setData(updateData);

    this.updateSearchInfo(fixedRadius);
  },

  shouldLoadNewData(newRegion) {
    const lastRegion = this.data.region.lastLoaded;

    if (!lastRegion) return true;

    const centerDistance = MapUtils.calculateDistance(
      lastRegion.centerLocation.latitude,
      lastRegion.centerLocation.longitude,
      newRegion.centerLocation.latitude,
      newRegion.centerLocation.longitude
    );

    // 针对50公里搜索半径优化的阈值
    // 当用户移动地图超过25公里（搜索范围的一半）时重新加载数据
    const distanceThreshold = 25000; // 25公里（搜索范围的一半）
    const scaleThreshold = 1.0; // 降低缩放阈值，让缩放更容易触发重新加载

    // 时间间隔检查 - 减少到500毫秒，让响应更快
    const now = Date.now();
    const timeDiff = now - this.lastLoadTime;
    const timeThreshold = 500; // 500毫秒

    const shouldLoadByDistance = centerDistance > distanceThreshold;
    const scaleChange = Math.abs(newRegion.scale - lastRegion.scale);
    const shouldLoadByScale = scaleChange >= scaleThreshold;
    const shouldLoadByTime = timeDiff > timeThreshold;

    console.log('显著变化检测:', {
      距离变化: Math.round(centerDistance) + 'm',
      缩放变化: scaleChange.toFixed(1),
      时间间隔: Math.round(timeDiff) + 'ms'
    });

    const shouldLoad = (shouldLoadByDistance || shouldLoadByScale) && shouldLoadByTime;

    if (shouldLoad) {
      this.lastLoadTime = now;
    }

    return shouldLoad;
  },

  debounceLoadRegionData(region) {
    this.clearTimer('regionChange');

    this.timers.regionChange = setTimeout(() => {
      this.loadPostsInRegion(region);
    }, this.debounceDelay);
  },

  clearTimer(timerName) {
    if (this.timers[timerName]) {
      clearTimeout(this.timers[timerName]);
      this.timers[timerName] = null;
    }
  },

  async loadPostsInRegion(region) {
    try {
      console.log('🔄 地图显著变化 - 向后台获取帖子数据');

      // 固定使用50公里搜索半径
      const radius = 50000; // 50公里
      const params = this.buildSearchParams(region.centerLocation, radius);

      console.log('📡 API请求:', params);

      // 1. 向后台获取帖子数据
      const result = await MapApi.getNearbyPosts(params);

      if (result && result.data && result.data.records) {
        const posts = this.processPostsData(result.data.records);
        const markers = this.generateMarkers(posts);

        // 2. 渲染新增的标签 - 只更新帖子数据和标记
        this.setData({
          'data.posts': posts,
          'map.markers': markers,
          'data.lastUpdate': Date.now()
        });

        console.log(`✅ 渲染新增标签: ${markers.length} 个`);
      } else {
        console.warn('❌ 后台返回空数据');
      }
    } catch (error) {
      console.error('获取帖子数据失败:', error);
    }
  },

  addToLoadHistory(region, radius, postCount) {
    const loadHistory = [...this.data.region.loadHistory];
    loadHistory.push({
      center: region.centerLocation,
      scale: region.scale,
      radius: radius,
      timestamp: Date.now(),
      postCount: postCount
    });

    // 只保留最近10次记录
    if (loadHistory.length > 10) {
      loadHistory.shift();
    }

    this.setData({
      'region.loadHistory': loadHistory
    });
  },

  // === 事件处理 ===
  onMarkerTap(e) {
    
    const markerId = e.detail.markerId;
    console.log('点击标记:', markerId);

    const post = this.data.data.posts.find(p => p.id == markerId);
    if (!post) {
      console.error('未找到对应的帖子数据，markerId:', markerId);
      return;
    }

    // 防止地图点击事件关闭弹窗
    this.markerTapTime = Date.now();

    this.hidePostList();
    this.showPostDetail(post.id);
  },

  onMapTap(e) {
    // 如果刚刚点击了标记，不处理地图点击
    if (this.markerTapTime && Date.now() - this.markerTapTime < 300) {
      return;
    }

    console.log('点击地图');
    this.hideAllModals();
  },

  hideAllModals() {
    this.setData({
      'modal.showPostDetail': false,
      'modal.selectedPost': null,
      'modal.showPostList': false,
      'category.showFilter': false
    });
  },

  hidePostList() {
    if (this.data.modal.showPostList) {
      this.setData({
        'modal.postListClosing': true
      });

      setTimeout(() => {
        this.setData({
          'modal.showPostList': false,
          'modal.postListClosing': false,
          'modal.postList': []
        });
      }, 300);
    }
  },

  // === 帖子详情管理 ===
  async showPostDetail(postId) {
    try {
      console.log('显示帖子详情:', postId);

      // 首先从本地数据查找
      const localPost = this.data.data.posts.find(p => p.id == postId);

      if (localPost) {
        console.log('使用本地数据显示帖子详情');
        this.setPostDetail(localPost);
        return;
      }

      console.log('本地未找到，从API获取');
      const result = await getPostDetail(postId);

      if (result && result.data) {
        const postDetail = this.processPostsData([result.data])[0];
        this.setPostDetail(postDetail);
      } else {
        wx.showToast({
          title: '帖子不存在',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('获取帖子详情失败:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    }
  },

  setPostDetail(post) {
    setTimeout(() => {
      this.setData({
        'modal.selectedPost': post,
        'modal.showPostDetail': true
      });
    }, 50);
  },

  onClosePostDetail() {
    console.log('关闭帖子详情弹窗');
    this.setData({
      'modal.showPostDetail': false,
      'modal.selectedPost': null
    });
  },

  // 点赞帖子
  onLikePost() {
    const post = this.data.modal.selectedPost;
    if (!post) {
      console.warn('没有选中的帖子');
      return;
    }

    console.log('点赞帖子:', post.id);

    // 切换点赞状态
    const newLikedState = !post.isLiked;
    const newLikeCount = newLikedState ? post.likeCount + 1 : post.likeCount - 1;

    // 更新UI状态
    this.setData({
      'modal.selectedPost.isLiked': newLikedState,
      'modal.selectedPost.likeCount': Math.max(0, newLikeCount)
    });

    // 同时更新帖子列表中的数据
    const posts = this.data.data.posts;
    const postIndex = posts.findIndex(p => p.id === post.id);
    if (postIndex !== -1) {
      this.setData({
        [`data.posts[${postIndex}].isLiked`]: newLikedState,
        [`data.posts[${postIndex}].likeCount`]: Math.max(0, newLikeCount)
      });
    }

    wx.showToast({
      title: newLikedState ? '点赞成功' : '取消点赞',
      icon: 'success',
      duration: 1500
    });

    // TODO: 调用点赞API
    // this.updatePostLikeStatus(post.id, newLikedState);
  },

  // 收藏帖子
  onFavoritePost() {
    const post = this.data.modal.selectedPost;
    if (!post) {
      console.warn('没有选中的帖子');
      return;
    }

    console.log('收藏帖子:', post.id);

    // 切换收藏状态
    const newFavoriteState = !post.isFavorite;
    const newFavoriteCount = newFavoriteState ? post.favoriteCount + 1 : post.favoriteCount - 1;

    // 更新UI状态
    this.setData({
      'modal.selectedPost.isFavorite': newFavoriteState,
      'modal.selectedPost.favoriteCount': Math.max(0, newFavoriteCount)
    });

    // 同时更新帖子列表中的数据
    const posts = this.data.data.posts;
    const postIndex = posts.findIndex(p => p.id === post.id);
    if (postIndex !== -1) {
      this.setData({
        [`data.posts[${postIndex}].isFavorite`]: newFavoriteState,
        [`data.posts[${postIndex}].favoriteCount`]: Math.max(0, newFavoriteCount)
      });
    }

    wx.showToast({
      title: newFavoriteState ? '收藏成功' : '取消收藏',
      icon: 'success',
      duration: 1500
    });

    // TODO: 调用收藏API
    // this.updatePostFavoriteStatus(post.id, newFavoriteState);
  },

  // 查看帖子详情
  onViewPostDetail() {
    const post = this.data.modal.selectedPost;
    if (!post) {
      console.warn('没有选中的帖子');
      return;
    }

    console.log('查看帖子详情:', post.id);

    // 跳转到帖子详情页面
    wx.navigateTo({
      url: `/pages/post-detail/post-detail?id=${post.id}`
    });
  },

  // === 分类管理 ===
  async loadCategoryList() {
    if (this.data.category.loaded) {
      console.log('分类列表已加载，跳过重复请求');
      return;
    }

    try {
      console.log('开始加载分类列表...');
      const categories = await getCategoryList();
      console.log('加载分类列表成功:', categories);

      const categoryList = [
        { id: '', name: '全部', icon: '/assets/images/common/all.png' },
        ...categories
      ];

      this.setData({
        'category.list': categoryList,
        'category.loaded': true
      });
    } catch (error) {
      console.error('加载分类列表失败:', error);
      this.setData({
        'category.list': [
          { id: '', name: '全部', icon: '/assets/images/common/all.png' }
        ],
        'category.loaded': true
      });
    }
  },

  onShowCategoryFilter() {
    console.log('点击分类筛选按钮');
    this.loadCategoryList();
    this.setData({
      'category.showFilter': true,
      'category.filterClosing': false
    });
  },

  onCloseCategoryFilter() {
    this.setData({
      'category.filterClosing': true
    });

    setTimeout(() => {
      this.setData({
        'category.showFilter': false,
        'category.filterClosing': false
      });
    }, 300);
  },

  onSelectCategory(e) {
    const categoryId = e.currentTarget.dataset.categoryId;
    const categoryName = e.currentTarget.dataset.categoryName;
    console.log('选择分类:', categoryId, categoryName);

    this.setData({
      'filter.category': categoryId,
      'filter.selectedCategoryId': categoryId,
      'filter.selectedCategoryName': categoryName
    });

    this.onCloseCategoryFilter();
    this.refreshData();
  },

  async refreshData() {
    console.log('点击刷新按钮');
    try {
      // 使用当前地图中心位置刷新数据，而不是用户初始位置
      const currentRegion = {
        centerLocation: {
          latitude: this.data.map.latitude,
          longitude: this.data.map.longitude
        },
        scale: this.data.map.scale
      };

      await this.loadPostsInRegion(currentRegion);

      // 刷新后检查标记状态
      setTimeout(() => {
        this.checkAndFixMarkers();
      }, 1000);

    } catch (error) {
      console.error('刷新数据失败:', error);
      this.handleDataLoadError(error);
    }
  },

  /**
   * 手动刷新标记（用于调试和修复标记不显示问题）
   */
  onRefreshMarkers() {
    console.log('手动刷新标记');
    wx.showLoading({
      title: '刷新标记中...'
    });

    this.forceRefreshMarkers();

    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '标记已刷新',
        icon: 'success',
        duration: 1500
      });
    }, 600);
  },

  // === 其他缺失的方法 ===
  onBackToUserLocation() {
    console.log('点击回到用户位置按钮');
    if (!this.data.location.user) {
      wx.showToast({
        title: '位置信息不可用',
        icon: 'none'
      });
      return;
    }

    const userLocation = this.data.location.user;
    const targetScale = 16; // 使用适合50公里搜索半径的缩放级别

    // 只在缩放级别真正变化时才更新
    const updateData = {
      'map.latitude': userLocation.latitude,
      'map.longitude': userLocation.longitude
    };

    this.updateScaleIfChanged(updateData, targetScale);
    this.setData(updateData);

    // 重新加载数据
    this.refreshData();
  },

  clearCategoryFilter() {
    this.setData({
      'filter.category': '',
      'filter.selectedCategoryId': '',
      'filter.selectedCategoryName': '全部'
    });
    this.refreshData();
  },

  // === 帖子列表管理 ===
  showPostList() {
    console.log('显示帖子列表');
    this.setData({
      'modal.showPostList': true,
      'modal.postList': this.data.data.posts,
      'modal.postListClosing': false
    });
  },

  hidePostList() {
    console.log('隐藏帖子列表');
    this.setData({
      'modal.postListClosing': true
    });

    setTimeout(() => {
      this.setData({
        'modal.showPostList': false,
        'modal.postListClosing': false
      });
    }, 300);
  },

  onPostListItemTap(e) {
    const postId = e.currentTarget.dataset.postId;
    console.log('点击帖子列表项:', postId);

    // 隐藏帖子列表
    this.hidePostList();

    // 显示帖子详情
    setTimeout(() => {
      this.showPostDetail(postId);
    }, 100);
  },

  // === 通用方法 ===
  preventClose() {
    // 阻止事件冒泡，防止弹窗关闭
    return false;
  },

  /**
   * 智能更新地图缩放级别 - 只在真正变化时才更新
   * @param {Object} updateData - 要更新的数据对象
   * @param {number} newScale - 新的缩放级别
   */
  updateScaleIfChanged(updateData, newScale) {
    if (this.data.map.scale !== newScale) {
      updateData['map.scale'] = newScale;
      console.log(`缩放级别变化: ${this.data.map.scale} → ${newScale}`);
    }
  },

  /**
   * 同步地图中心点位置
   * 确保地图的中心点位置与当前显示区域保持一致
   * @param {Object} region - 区域信息，包含 centerLocation
   */
  syncMapCenter(region) {
    const currentLat = this.data.map.latitude;
    const currentLng = this.data.map.longitude;
    const newLat = region.centerLocation.latitude;
    const newLng = region.centerLocation.longitude;

    // 检查位置是否有显著变化（避免频繁更新）
    const latDiff = Math.abs(newLat - currentLat);
    const lngDiff = Math.abs(newLng - currentLng);
    const threshold = 0.0001; // 约10米的精度

    if (latDiff > threshold || lngDiff > threshold) {
      console.log('同步地图中心点位置:', {
        from: `${currentLat.toFixed(6)}, ${currentLng.toFixed(6)}`,
        to: `${newLat.toFixed(6)}, ${newLng.toFixed(6)}`,
        diff: `${(latDiff * 111000).toFixed(1)}m, ${(lngDiff * 111000).toFixed(1)}m`
      });

      this.setData({
        'map.latitude': newLat,
        'map.longitude': newLng
      });

      return true; // 表示位置已更新
    }

    return false; // 表示位置未更新
  },

  /**
   * 合并新帖子数据和现有数据，去重
   * @param {Array} existingPosts 现有帖子数据
   * @param {Array} newPosts 新帖子数据
   * @returns {Array} 合并去重后的帖子数据
   */
  mergeAndDeduplicatePosts(existingPosts, newPosts) {
    // 创建现有帖子的ID映射
    const existingPostIds = new Set(existingPosts.map(post => post.id));

    // 过滤出真正的新帖子
    const uniqueNewPosts = newPosts.filter(post => !existingPostIds.has(post.id));

    // 合并数据
    let mergedPosts = [...existingPosts, ...uniqueNewPosts];

    // 如果数据量过大，清理远离当前视图的数据
    if (mergedPosts.length > 500) {
      mergedPosts = this.cleanupDistantPosts(mergedPosts);
    }

    console.log(`数据合并: 现有 ${existingPosts.length} 个，新增 ${uniqueNewPosts.length} 个，合并后 ${mergedPosts.length} 个`);

    return mergedPosts;
  },

  /**
   * 清理远离当前视图的帖子数据
   * @param {Array} posts 帖子数据
   * @returns {Array} 清理后的帖子数据
   */
  cleanupDistantPosts(posts) {
    const currentLat = this.data.map.latitude;
    const currentLng = this.data.map.longitude;
    const maxDistance = 100000; // 100公里，超过这个距离的帖子将被清理

    const nearbyPosts = posts.filter(post => {
      const distance = MapUtils.calculateDistance(
        currentLat, currentLng,
        post.latitude, post.longitude
      );
      return distance <= maxDistance;
    });

    console.log(`数据清理: 原有 ${posts.length} 个帖子，清理后保留 ${nearbyPosts.length} 个`);

    return nearbyPosts;
  },

  /**
   * 根据缩放级别获取对应的显示范围文本
   * @param {number} scale 缩放级别
   * @returns {string} 范围文本
   */
  getScaleRangeText(scale) {
    if (scale >= 18) return '街道级';
    if (scale >= 17) return '小区级';
    if (scale >= 16) return '社区级';
    if (scale >= 15) return '片区级';
    if (scale >= 14) return '区域级';
    if (scale >= 13) return '大区域';
    if (scale >= 12) return '城区级';
    if (scale >= 11) return '城市级';
    if (scale >= 10) return '大城市';
    if (scale >= 9) return '都市圈';
    if (scale >= 8) return '城市群';
    if (scale >= 7) return '省市级';
    return '省际级';
  },

  /**
   * 获取当前地图中心点位置信息（用于调试）
   * @returns {Object} 中心点位置信息
   */
  getCurrentMapCenter() {
    return {
      latitude: this.data.map.latitude,
      longitude: this.data.map.longitude,
      scale: this.data.map.scale,
      scaleRangeText: this.data.ui.scaleRangeText,
      searchLocation: this.data.location.search,
      userLocation: this.data.location.user
    };
  },

  /**
   * 调试方法：打印当前地图状态
   */
  debugMapState() {
    const mapCenter = this.getCurrentMapCenter();
    console.log('=== 当前地图状态 ===');
    console.log('地图中心点:', `${mapCenter.latitude.toFixed(6)}, ${mapCenter.longitude.toFixed(6)}`);
    console.log('缩放级别:', `${mapCenter.scale} (${mapCenter.scaleRangeText})`);
    console.log('搜索位置:', mapCenter.searchLocation ?
      `${mapCenter.searchLocation.latitude.toFixed(6)}, ${mapCenter.searchLocation.longitude.toFixed(6)}` : '未设置');
    console.log('用户位置:', mapCenter.userLocation ?
      `${mapCenter.userLocation.latitude.toFixed(6)}, ${mapCenter.userLocation.longitude.toFixed(6)}` : '未获取');
    console.log('标记数量:', this.data.map.markers ? this.data.map.markers.length : 0);
    console.log('==================');
  },

  /**
   * 强制刷新地图标记
   * 用于解决标记不显示的问题
   */
  forceRefreshMarkers() {
    console.log('强制刷新地图标记');

    const currentMarkers = this.data.map.markers || [];
    if (currentMarkers.length === 0) {
      console.log('没有标记需要刷新');
      return;
    }

    // 先清空标记，然后重新设置
    this.setData({
      'map.markers': []
    }, () => {
      // 在下一个事件循环中重新设置标记
      setTimeout(() => {
        const validatedMarkers = this.validateMarkers(currentMarkers);
        this.setData({
          'map.markers': validatedMarkers
        });
        console.log(`强制刷新完成: ${validatedMarkers.length} 个标记`);
      }, 100);
    });
  },

  /**
   * 检查并修复标记显示问题
   */
  checkAndFixMarkers() {
    console.log('检查标记显示状态');

    const posts = this.data.data.posts || [];
    const markers = this.data.map.markers || [];

    console.log(`当前状态: ${posts.length} 个帖子, ${markers.length} 个标记`);

    // 如果有帖子但没有标记，重新生成标记
    if (posts.length > 0 && markers.length === 0) {
      console.log('发现标记丢失，重新生成标记');
      const newMarkers = this.generateMarkers(posts);
      const validatedMarkers = this.validateMarkers(newMarkers);

      this.setData({
        'map.markers': validatedMarkers
      });

      console.log(`重新生成了 ${validatedMarkers.length} 个标记`);
      return;
    }

    // 如果标记数量不匹配，重新生成
    if (posts.length !== markers.length) {
      console.log(`标记数量不匹配 (帖子: ${posts.length}, 标记: ${markers.length})，重新生成标记`);
      const newMarkers = this.generateMarkers(posts);
      const validatedMarkers = this.validateMarkers(newMarkers);

      this.setData({
        'map.markers': validatedMarkers
      });

      console.log(`重新生成了 ${validatedMarkers.length} 个标记`);
      return;
    }

    console.log('标记状态正常');
  },

  /**
   * 运行标记诊断
   */
  runMarkerDiagnostic() {
    console.log('运行标记诊断工具');
    try {
      const report = MarkerDiagnostic.generateDiagnosticReport(this);
      console.log('诊断报告:', report);

      if (report.summary.status === 'issues_found') {
        console.log('发现问题，尝试自动修复...');
        const fixed = MarkerDiagnostic.autoFixMarkerIssues(this);

        if (fixed) {
          wx.showToast({
            title: '已修复标记问题',
            icon: 'success'
          });
        } else {
          wx.showToast({
            title: '无法自动修复',
            icon: 'none'
          });
        }
      } else {
        wx.showToast({
          title: '标记状态正常',
          icon: 'success'
        });
      }

      return report;
    } catch (error) {
      console.error('诊断工具运行失败:', error);
      wx.showToast({
        title: '诊断失败',
        icon: 'none'
      });
    }
  },

  /**
   * 测试数据解析功能
   * @param {Object} testData 测试数据
   */
  testDataParsing(testData) {
    console.log('=== 测试数据解析 ===');
    console.log('原始数据:', testData);

    try {
      const processedPosts = this.processPostsData([testData]);
      const processedPost = processedPosts[0];

      console.log('处理后数据:', processedPost);

      // 验证关键字段
      const validationResults = {
        基本信息: {
          ID: processedPost.id === testData.id,
          标题: !!processedPost.title,
          内容: processedPost.content === testData.content,
          位置: !!processedPost.latitude && !!processedPost.longitude
        },
        分类信息: {
          分类名称: processedPost.categoryName === testData.category.name,
          分类ID: processedPost.categoryId === testData.categoryId
        },
        用户信息: {
          用户名: processedPost.authorName === testData.user.nickname,
          头像: processedPost.authorAvatar === testData.user.avatar,
          性别: processedPost.authorGender === testData.user.gender
        },
        统计信息: {
          点赞数: processedPost.likeCount === testData.stats.likeCount,
          评论数: processedPost.commentCount === testData.stats.feedbackCount,
          收藏数: processedPost.favoriteCount === testData.stats.favoriteCount,
          浏览数: processedPost.viewCount === testData.stats.viewCount
        },
        状态信息: {
          已点赞: processedPost.isLiked === testData.stats.isLiked,
          已收藏: processedPost.isFavorite === testData.stats.isFavorite
        },
        时间信息: {
          创建时间: processedPost.createTime === testData.createTime,
          发布时间: processedPost.publishTime === testData.publishTime,
          格式化时间: !!processedPost.formattedPublishTime
        }
      };

      console.log('验证结果:', validationResults);

      // 检查是否有验证失败的项
      let hasErrors = false;
      for (const category in validationResults) {
        for (const field in validationResults[category]) {
          if (!validationResults[category][field]) {
            console.warn(`验证失败: ${category} - ${field}`);
            hasErrors = true;
          }
        }
      }

      if (!hasErrors) {
        console.log('✅ 所有字段验证通过');
        wx.showToast({
          title: '数据解析正确',
          icon: 'success'
        });
      } else {
        console.log('❌ 部分字段验证失败');
        wx.showToast({
          title: '数据解析有问题',
          icon: 'none'
        });
      }

      return {
        success: !hasErrors,
        processedPost,
        validationResults
      };

    } catch (error) {
      console.error('数据解析测试失败:', error);
      wx.showToast({
        title: '测试失败',
        icon: 'none'
      });
      return {
        success: false,
        error: error.message
      };
    }
  },













  // === 性能优化方法 ===

  /**
   * 防抖函数 - 通用版本
   * @param {Function} func 要防抖的函数
   * @param {number} delay 延迟时间
   * @param {string} key 防抖键名
   */
  debounce(func, delay, key = 'default') {
    if (!this.debounceTimers) {
      this.debounceTimers = {};
    }

    if (this.debounceTimers[key]) {
      clearTimeout(this.debounceTimers[key]);
    }

    this.debounceTimers[key] = setTimeout(() => {
      func.call(this);
      delete this.debounceTimers[key];
    }, delay);
  },

  /**
   * 批量更新数据 - 减少setData调用
   * @param {Object} updates 要更新的数据对象
   */
  batchUpdate(updates) {
    if (!this.pendingUpdates) {
      this.pendingUpdates = {};
    }

    // 合并更新
    Object.assign(this.pendingUpdates, updates);

    // 防抖批量更新
    this.debounce(() => {
      if (Object.keys(this.pendingUpdates).length > 0) {
        this.setData(this.pendingUpdates);
        this.pendingUpdates = {};
      }
    }, 16, 'batchUpdate'); // 16ms约等于60fps
  },

  /**
   * 优化的标记生成 - 只在必要时重新生成
   * @param {Array} posts 帖子数据
   * @returns {Array} 标记数组
   */
  optimizedGenerateMarkers(posts) {
    // 检查是否需要重新生成标记
    const postsHash = this.generatePostsHash(posts);
    if (this.lastPostsHash === postsHash) {
      console.log('帖子数据未变化，跳过标记重新生成');
      return this.data.map.markers;
    }

    this.lastPostsHash = postsHash;
    return this.generateMarkers(posts);
  },

  /**
   * 生成帖子数据哈希值
   * @param {Array} posts 帖子数据
   * @returns {string} 哈希值
   */
  generatePostsHash(posts) {
    return posts.map(post => `${post.id}_${post.latitude}_${post.longitude}`).join('|');
  },

  /**
   * 智能缓存清理
   */
  smartCacheCleanup() {
    const now = Date.now();
    const maxAge = 10 * 60 * 1000; // 10分钟

    // 清理加载历史
    const validHistory = this.data.region.loadHistory.filter(
      item => now - item.timestamp < maxAge
    );

    if (validHistory.length !== this.data.region.loadHistory.length) {
      this.setData({
        'region.loadHistory': validHistory
      });
    }
  },

  /**
   * 页面可见性变化处理
   */
  onShow() {
    console.log('地图页面显示');
    // 页面显示时恢复定时器
    if (this.data.location.ready && !this.data.data.loading) {
      // 可以在这里添加数据刷新逻辑
    }
  },

  onHide() {
    console.log('地图页面隐藏');
    // 页面隐藏时清理定时器，节省资源
    this.clearAllTimers();

    // 执行内存清理
    this.smartCacheCleanup();
  },

  /**
   * 错误边界处理
   * @param {Error} error 错误对象
   * @param {string} context 错误上下文
   */
  handleError(error, context = 'unknown') {
    console.error(`地图页面错误 [${context}]:`, error);

    // 显示用户友好的错误提示
    wx.showToast({
      title: '操作失败，请重试',
      icon: 'none',
      duration: 2000
    });
  }
});
