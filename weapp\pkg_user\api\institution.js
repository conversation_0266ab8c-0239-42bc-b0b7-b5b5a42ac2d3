const { request } = require('../../utils/request.js')

/**
 * 申请机构入驻
 * @param {Object} data 机构申请数据
 */
const applyInstitution = (data) => {
  return request({
    url: '/blade-chat/institution/apply',
    method: 'POST',
    data: data
  })
}

/**
 * 获取机构分类列表
 */
const getInstitutionTypes = () => {
  return request({
    url: '/blade-chat/institution/types',
    method: 'GET'
  })
}

/**
 * 获取我的机构列表
 * @param {Object} params 查询参数
 */
const getMyInstitutions = (params = {}) => {
  return request({
    url: '/blade-chat/institution/my-institution',
    method: 'GET',
    data: params
  })
}

/**
 * 更新机构信息
 * @param {Object} data 机构数据
 */
const updateInstitution = (data) => {
  return request({
    url: '/blade-chat/institution/update',
    method: 'POST',
    data: data
  })
}

module.exports = {
  applyInstitution,
  getInstitutionTypes,
  getMyInstitutions,
  updateInstitution
}
